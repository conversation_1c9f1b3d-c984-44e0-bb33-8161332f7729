#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无痕浏览器自动化注册系统
每次打开全新的无痕浏览器窗口进行注册
完全绕过cookie和会话限制
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import random
import string
import json
import threading
from concurrent.futures import ThreadPoolExecutor
import os

class IncognitoAutoRegister:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.success_accounts = []
        self.failed_attempts = []
        
        print("🕵️ 无痕浏览器自动化注册系统")
        print("🚀 每次使用全新的浏览器环境")
        print("=" * 60)

    def create_incognito_driver(self):
        """创建无痕浏览器实例"""
        chrome_options = Options()
        
        # 无痕模式
        chrome_options.add_argument('--incognito')
        
        # 反检测配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # 禁用图片加载，提高速度
        
        # 高级反检测
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 随机User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        # 窗口大小随机化
        window_sizes = ['1920,1080', '1366,768', '1440,900', '1536,864']
        chrome_options.add_argument(f'--window-size={random.choice(window_sizes)}')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']})")
            
            return driver
            
        except Exception as e:
            print(f"❌ 浏览器创建失败: {e}")
            return None

    def generate_account_info(self):
        """生成随机账号信息"""
        def generate_phone():
            prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                       '150', '151', '152', '153', '155', '156', '157', '158', '159',
                       '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return prefix + suffix
        
        def generate_password(length=8):
            chars = string.ascii_letters + string.digits
            return ''.join(random.choice(chars) for _ in range(length))
        
        return generate_phone(), generate_password()

    def inject_crypto_and_register(self, driver, phone, password):
        """注入加密脚本并执行注册"""
        print(f"💉 注入加密脚本到浏览器...")
        
        # 读取JavaScript注入脚本
        try:
            with open('browser_injection.js', 'r', encoding='utf-8') as f:
                js_code = f.read()
        except FileNotFoundError:
            print("❌ 找不到 browser_injection.js 文件")
            return False
        
        try:
            # 注入JavaScript代码
            driver.execute_script(js_code)
            
            # 等待初始化完成
            time.sleep(3)
            
            # 验证注入是否成功
            result = driver.execute_script("return typeof window.AutoRegister !== 'undefined'")
            if not result:
                print("❌ JavaScript注入失败")
                return False
            
            print("✅ JavaScript注入成功")
            
            # 执行自动注册（修改为使用指定的手机号和密码）
            register_script = f"""
                return new Promise(async (resolve) => {{
                    try {{
                        console.log('🎯 开始自动注册流程');
                        
                        const phone = '{phone}';
                        const password = '{password}';
                        
                        console.log('📱 使用账号信息:');
                        console.log('手机号:', phone);
                        console.log('密码:', password);
                        
                        // 步骤1: 获取图片验证码
                        console.log('📸 步骤1: 获取图片验证码');
                        const captchaResult = await window.AutoRegister.makeAPIRequest(
                            '/dev-api/api/login/authccode.html',
                            {{}},
                            '获取图片验证码'
                        );
                        
                        if (captchaResult) {{
                            console.log('✅ 验证码获取成功');
                            
                            // 步骤2: 发送短信验证码
                            console.log('📱 步骤2: 发送短信验证码');
                            const smsResult = await window.AutoRegister.makeAPIRequest(
                                '/dev-api/api/login/smscode.html',
                                {{
                                    phone: phone,
                                    code: ''
                                }},
                                `发送短信验证码到 ${{phone}}`
                            );
                            
                            if (smsResult) {{
                                console.log('✅ 短信发送成功！');
                                resolve({{
                                    success: true, 
                                    phone: phone, 
                                    password: password,
                                    step: 'sms_sent',
                                    message: '短信验证码已发送'
                                }});
                            }} else {{
                                resolve({{
                                    success: false, 
                                    error: '短信发送失败',
                                    phone: phone
                                }});
                            }}
                        }} else {{
                            resolve({{
                                success: false, 
                                error: '验证码获取失败',
                                phone: phone
                            }});
                        }}
                    }} catch (error) {{
                        resolve({{
                            success: false, 
                            error: error.message,
                            phone: phone
                        }});
                    }}
                }});
            """
            
            # 执行注册脚本
            result = driver.execute_script(register_script)
            
            return result
            
        except Exception as e:
            print(f"❌ 脚本执行失败: {e}")
            return {'success': False, 'error': str(e), 'phone': phone}

    def single_incognito_register(self, attempt_id):
        """单次无痕浏览器注册"""
        print(f"\n🚀 开始第 {attempt_id} 次注册尝试")
        print("-" * 50)
        
        driver = None
        try:
            # 1. 创建无痕浏览器
            print("🕵️ 创建无痕浏览器...")
            driver = self.create_incognito_driver()
            
            if not driver:
                return {'success': False, 'error': '浏览器创建失败', 'attempt_id': attempt_id}
            
            # 2. 生成账号信息
            phone, password = self.generate_account_info()
            print(f"📱 生成账号: {phone}")
            print(f"🔐 生成密码: {password}")
            
            # 3. 访问登录页面
            login_url = f"{self.base_url}/pages/login/login?code=********"
            print(f"🌐 访问页面: {login_url}")
            
            driver.get(login_url)
            
            # 4. 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(random.uniform(3, 6))  # 随机延时
            
            # 5. 注入脚本并执行注册
            result = self.inject_crypto_and_register(driver, phone, password)
            
            if result and result.get('success'):
                print(f"✅ 第 {attempt_id} 次尝试成功！")
                print(f"📱 账号: {result['phone']}")
                print(f"🔐 密码: {result['password']}")
                print(f"📝 状态: {result.get('message', '成功')}")
                
                # 保存成功账号
                success_info = {
                    'attempt_id': attempt_id,
                    'phone': result['phone'],
                    'password': result['password'],
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'step': result.get('step', 'completed')
                }
                self.success_accounts.append(success_info)
                
                return success_info
            else:
                error_msg = result.get('error', '未知错误') if result else '执行失败'
                print(f"❌ 第 {attempt_id} 次尝试失败: {error_msg}")
                
                # 保存失败信息
                fail_info = {
                    'attempt_id': attempt_id,
                    'phone': result.get('phone', phone) if result else phone,
                    'error': error_msg,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                self.failed_attempts.append(fail_info)
                
                return fail_info
                
        except Exception as e:
            print(f"❌ 第 {attempt_id} 次尝试异常: {e}")
            
            fail_info = {
                'attempt_id': attempt_id,
                'error': str(e),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            self.failed_attempts.append(fail_info)
            
            return fail_info
            
        finally:
            # 6. 关闭浏览器
            if driver:
                try:
                    driver.quit()
                    print(f"🧹 第 {attempt_id} 次浏览器已关闭")
                except:
                    pass

    def batch_incognito_register(self, count=5, max_workers=3):
        """批量无痕浏览器注册"""
        print(f"\n🎯 开始批量无痕注册")
        print(f"📊 目标数量: {count}")
        print(f"🔄 并发数量: {max_workers}")
        print("=" * 60)
        
        # 使用线程池并发执行
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = []
            for i in range(1, count + 1):
                future = executor.submit(self.single_incognito_register, i)
                futures.append(future)
                
                # 错开启动时间，避免同时启动太多浏览器
                time.sleep(random.uniform(2, 5))
            
            # 等待所有任务完成
            print("\n⏳ 等待所有注册任务完成...")
            for future in futures:
                try:
                    result = future.result(timeout=120)  # 每个任务最多等待2分钟
                except Exception as e:
                    print(f"❌ 任务执行异常: {e}")

    def save_results(self):
        """保存注册结果"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        
        # 保存成功账号
        if self.success_accounts:
            success_file = f"success_accounts_{timestamp}.json"
            with open(success_file, 'w', encoding='utf-8') as f:
                json.dump(self.success_accounts, f, ensure_ascii=False, indent=2)
            print(f"✅ 成功账号已保存到: {success_file}")
        
        # 保存失败记录
        if self.failed_attempts:
            failed_file = f"failed_attempts_{timestamp}.json"
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_attempts, f, ensure_ascii=False, indent=2)
            print(f"📝 失败记录已保存到: {failed_file}")

    def print_summary(self):
        """打印总结报告"""
        print("\n" + "=" * 60)
        print("📊 批量注册总结报告")
        print("=" * 60)
        
        total_attempts = len(self.success_accounts) + len(self.failed_attempts)
        success_count = len(self.success_accounts)
        failed_count = len(self.failed_attempts)
        
        print(f"📈 总尝试次数: {total_attempts}")
        print(f"✅ 成功次数: {success_count}")
        print(f"❌ 失败次数: {failed_count}")
        
        if total_attempts > 0:
            success_rate = (success_count / total_attempts) * 100
            print(f"📊 成功率: {success_rate:.1f}%")
        
        if self.success_accounts:
            print(f"\n🎉 成功注册的账号:")
            for account in self.success_accounts:
                print(f"  📱 {account['phone']} | 🔐 {account['password']} | ⏰ {account['timestamp']}")
        
        if self.failed_attempts:
            print(f"\n💔 失败原因统计:")
            error_counts = {}
            for attempt in self.failed_attempts:
                error = attempt.get('error', '未知错误')
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"  ❌ {error}: {count} 次")

    def run_continuous_register(self, target_success=10, max_attempts=50):
        """持续注册直到达到目标成功数量"""
        print(f"\n🎯 持续注册模式")
        print(f"🎯 目标成功数量: {target_success}")
        print(f"📊 最大尝试次数: {max_attempts}")
        print("=" * 60)
        
        attempt_count = 0
        
        while len(self.success_accounts) < target_success and attempt_count < max_attempts:
            attempt_count += 1
            
            print(f"\n🔄 第 {attempt_count} 轮尝试 (已成功: {len(self.success_accounts)}/{target_success})")
            
            result = self.single_incognito_register(attempt_count)
            
            # 每次尝试后的延时
            if attempt_count < max_attempts:
                delay = random.uniform(10, 20)  # 10-20秒随机延时
                print(f"⏳ 等待 {delay:.1f} 秒后继续...")
                time.sleep(delay)
        
        print(f"\n🏁 持续注册完成！")
        print(f"✅ 成功注册: {len(self.success_accounts)} 个账号")
        print(f"📊 总尝试次数: {attempt_count}")


def main():
    """主函数"""
    print("🕵️ 无痕浏览器自动化注册系统")
    print("🚀 每次使用全新的浏览器环境，完全绕过会话限制")
    print("=" * 60)
    
    # 检查JavaScript文件
    if not os.path.exists('browser_injection.js'):
        print("❌ 找不到 browser_injection.js 文件")
        print("💡 请确保 browser_injection.js 文件在当前目录")
        return
    
    register = IncognitoAutoRegister()
    
    print("\n📋 选择运行模式:")
    print("1. 单次注册测试")
    print("2. 批量并发注册")
    print("3. 持续注册模式")
    
    choice = input("\n请选择模式 (1-3): ").strip()
    
    try:
        if choice == '1':
            # 单次测试
            result = register.single_incognito_register(1)
            print(f"\n📊 测试结果: {result}")
            
        elif choice == '2':
            # 批量注册
            count = input("请输入注册数量 (默认5): ").strip()
            count = int(count) if count.isdigit() else 5
            
            workers = input("请输入并发数量 (默认3): ").strip()
            workers = int(workers) if workers.isdigit() else 3
            
            register.batch_incognito_register(count, workers)
            
        elif choice == '3':
            # 持续注册
            target = input("请输入目标成功数量 (默认10): ").strip()
            target = int(target) if target.isdigit() else 10
            
            max_attempts = input("请输入最大尝试次数 (默认50): ").strip()
            max_attempts = int(max_attempts) if max_attempts.isdigit() else 50
            
            register.run_continuous_register(target, max_attempts)
            
        else:
            print("❌ 无效选择")
            return
        
        # 打印总结和保存结果
        register.print_summary()
        register.save_results()
        
        print("\n🎊 程序执行完成！")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断程序")
        register.print_summary()
        register.save_results()
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        register.print_summary()
        register.save_results()


if __name__ == "__main__":
    main()
