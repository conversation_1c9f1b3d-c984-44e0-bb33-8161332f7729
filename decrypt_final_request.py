#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 最终解密：使用固定的全局密钥解密第二种注册请求

import base64
import json
import urllib.parse
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# --- 从最终Hook日志中确定的全局加密参数 ---
# 这个密钥同时用于HMAC签名和第二种注册流程的AES加密
GLOBAL_KEY = bytes.fromhex('947989c9aadc9fad7f21ebc026373f24')

# 这个IV同时用于登录密码加密和第二种注册流程的AES加密
GLOBAL_IV = bytes.fromhex('0102030405060708090a0b0c0d0e0f10')


# --- 第二种注册请求的URL ---
REQUEST_URL_FLOW_B = "http://ggregesd.kes337f.shop/user/register/v1?data=7n%2BvhCrZycbsXKq%2FIGcg3M1%2FNXuSByAG6aS5ORj%2FuWSOgTnPiABWd%2FSo1C5wdQNb2FtRQK7nplKMCQBa5esRYSGeNK%2FQbc5Oc%2BtvhFdvrB04gSPLR%2Fi4%2BqleFhdbl51zbHyTkSS4y7F6t2wWsdfue9h2j5LugG3NxzfkOhlAty051W2VDsctQviwPwXWPjODXnmlcOL9yFHtMbQLERfyu1g%2B0IfkbIf2KeFEwxkSL89azMZaGnNY3EntWJrr%2FYgVJjhgpyb7Nn9feREaQvs9NiRezzWRhlBTaqA5D0gChBax%2BQnshXHmGYeDnDseq%2Bzd0ysYOjK2E5tzBcJVzoujja5ruYe4LjYYu04aiQsWwiB8X8yJLos5bpVZT7VSyUhazfoczP8QSaI3IOSkRrDEZTHBYPiOdXOQ78y2lMZcp%2BKjLiPEucCWvRJOKG0kgPAPrbS6iQe6918rBvVuYBqkbXvYlReCqX0iWGU6z5zV4qWUkpNntwaXYCYIomfVP5XIEQNRA%2Bq%2Bbq7z06WZn%2FBMJxBk%2FhjEHoxo8yxOcBEDV4yrSoOtkV5Y6JakMODHkqhTmltZlctonvmaI5ELouxOUw%3D%3D&deviceId=android&language=zh&salt=1753522482092&secret=CUV1vJFN1khFIHWLNXmNUA%3D%3D"


def decrypt_final_request():
    """
    使用全局固定密钥和IV，解密第二种注册请求。
    """
    print("--- 最终解密：第二种注册请求 ---")
    
    try:
        # 从URL中解析出 'data' 参数
        parsed_url = urllib.parse.urlparse(REQUEST_URL_FLOW_B)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        encrypted_data_b64 = query_params.get('data', [''])[0]

        print(f"密钥 (全局固定): {GLOBAL_KEY.hex()}")
        print(f"IV (全局固定):   {GLOBAL_IV.hex()}")

        # Base64解码密文
        ciphertext = base64.b64decode(encrypted_data_b64)

        # 执行AES解密
        cipher = AES.new(GLOBAL_KEY, AES.MODE_CBC, GLOBAL_IV)
        decrypted_bytes = unpad(cipher.decrypt(ciphertext), AES.block_size)
        
        # 解码为JSON
        decrypted_json = json.loads(decrypted_bytes.decode('utf-8'))
        
        print("\n>>> 解密成功! <<<\n")
        print("请求的明文内容是:")
        print(json.dumps(decrypted_json, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"\n解密失败: {e}")

if __name__ == '__main__':
    decrypt_final_request() 