#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Playwright绕过阿里云WAF反爬虫
更现代的无头浏览器解决方案
"""

import asyncio
import requests
from playwright.async_api import async_playwright
from decrypt_analysis import EncryptionSystem

class PlaywrightBypass:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.crypto = EncryptionSystem()
        
        print("🎭 Playwright绕过方案启动")
        print("🚀 使用现代无头浏览器技术")
        print("=" * 50)

    async def get_cookies_with_playwright(self):
        """使用Playwright获取有效cookie"""
        print("🍪 使用Playwright获取cookie...")
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser = await p.chromium.launch(
                    headless=False,  # 设置为True可以无头运行
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )
                
                # 创建上下文
                context = await browser.new_context(
                    user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                    viewport={'width': 375, 'height': 667}  # iPhone尺寸
                )
                
                # 反检测设置
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                    
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });
                """)
                
                # 创建页面
                page = await context.new_page()
                
                # 访问登录页面
                login_url = f"{self.base_url}/pages/login/login?code=21328050"
                print(f"📱 访问页面: {login_url}")
                
                await page.goto(login_url, wait_until='networkidle')
                
                # 等待页面完全加载
                print("⏳ 等待页面加载...")
                await asyncio.sleep(5)
                
                # 获取所有cookie
                cookies = await context.cookies()
                cookie_dict = {}
                
                print("🍪 获取到的cookie:")
                for cookie in cookies:
                    cookie_dict[cookie['name']] = cookie['value']
                    print(f"  {cookie['name']}: {cookie['value'][:20]}...")
                
                await browser.close()
                
                # 检查关键cookie
                if 'acw_tc' in cookie_dict and 'cdn_sec_tc' in cookie_dict:
                    print("✅ 成功获取关键cookie")
                else:
                    print("⚠️ 未获取到关键cookie")
                
                return cookie_dict
                
            except Exception as e:
                print(f"❌ Playwright获取cookie失败: {e}")
                return None

    async def test_api_with_playwright_cookies(self, cookies):
        """使用Playwright获取的cookie测试API"""
        print("\n🚀 使用Playwright cookie测试API")
        print("-" * 40)
        
        if not cookies:
            print("❌ 没有可用的cookie")
            return False
        
        try:
            # 准备测试数据
            test_data = {}
            encrypted = self.crypto.encrypt_data(test_data)
            sign = self.crypto.generate_sign(test_data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return False
            
            # 格式化cookie
            cookie_header = "; ".join([f"{k}={v}" for k, v in cookies.items()])
            
            # 构造请求头
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': cookie_header,
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'token': '',
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            print(f"🔐 加密数据: {encrypted[:30]}...")
            print(f"✍️ 签名: {sign}")
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/dev-api/api/login/authccode.html",
                headers=headers,
                json=request_body,
                timeout=15,
                verify=False
            )
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"📏 响应长度: {len(response.text)}")
            
            # 检查响应
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("❌ 仍然返回HTML页面")
                return False
            else:
                print("✅ 返回API响应")
                print(f"响应内容: {response.text}")
                
                # 尝试解密
                try:
                    decrypted = self.crypto.decrypt_data(response.text)
                    if decrypted:
                        print(f"🔓 解密成功: {decrypted}")
                    else:
                        print("⚠️ 响应可能是明文")
                except:
                    print("⚠️ 响应不是加密格式")
                
                return True
                
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            return False

    async def playwright_register_process(self):
        """使用Playwright的完整注册流程"""
        print("\n🎯 Playwright注册流程")
        print("=" * 50)
        
        # 1. 获取cookie
        cookies = await self.get_cookies_with_playwright()
        if not cookies:
            print("❌ 无法获取cookie")
            return False
        
        # 2. 测试API
        api_success = await self.test_api_with_playwright_cookies(cookies)
        
        if api_success:
            print("\n🎉 Playwright方案成功！")
            print("可以继续实现完整的注册流程")
            return True
        else:
            print("\n❌ API测试失败")
            return False

def run_playwright_bypass():
    """运行Playwright绕过方案"""
    bypass = PlaywrightBypass()
    
    # 运行异步函数
    result = asyncio.run(bypass.playwright_register_process())
    
    if result:
        print("\n🎊 Playwright绕过成功！")
    else:
        print("\n💔 Playwright绕过失败")
        print("\n💡 安装Playwright:")
        print("pip install playwright")
        print("playwright install chromium")

if __name__ == "__main__":
    run_playwright_bypass()
