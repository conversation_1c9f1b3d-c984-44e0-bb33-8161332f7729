import type nodeUtilTypes from "node:util/types";
export declare const isExternal: typeof nodeUtilTypes.isExternal;
export declare const isDate: typeof nodeUtilTypes.isDate;
export declare const isArgumentsObject: unknown;
export declare const isBigIntObject: (val: any) => val is bigint;
export declare const isBooleanObject: typeof nodeUtilTypes.isBooleanObject;
export declare const isNumberObject: typeof nodeUtilTypes.isNumberObject;
export declare const isStringObject: typeof nodeUtilTypes.isStringObject;
export declare const isSymbolObject: typeof nodeUtilTypes.isSymbolObject;
export declare const isNativeError: unknown;
export declare const isRegExp: typeof nodeUtilTypes.isRegExp;
export declare const isAsyncFunction: unknown;
export declare const isGeneratorFunction: unknown;
export declare const isGeneratorObject: unknown;
export declare const isPromise: typeof nodeUtilTypes.isPromise;
export declare const isMap: typeof nodeUtilTypes.isMap;
export declare const isSet: typeof nodeUtilTypes.isSet;
export declare const isMapIterator: unknown;
export declare const isSetIterator: unknown;
export declare const isWeakMap: typeof nodeUtilTypes.isWeakMap;
export declare const isWeakSet: typeof nodeUtilTypes.isWeakSet;
export declare const isArrayBuffer: typeof nodeUtilTypes.isArrayBuffer;
export declare const isDataView: typeof nodeUtilTypes.isDataView;
export declare const isSharedArrayBuffer: typeof nodeUtilTypes.isSharedArrayBuffer;
export declare const isProxy: unknown;
export declare const isModuleNamespaceObject: unknown;
export declare const isAnyArrayBuffer: unknown;
export declare const isBoxedPrimitive: unknown;
export declare const isArrayBufferView: unknown;
export declare const isTypedArray: unknown;
export declare const isUint8Array: unknown;
export declare const isUint8ClampedArray: unknown;
export declare const isUint16Array: unknown;
export declare const isUint32Array: unknown;
export declare const isInt8Array: unknown;
export declare const isInt16Array: unknown;
export declare const isInt32Array: unknown;
export declare const isFloat32Array: unknown;
export declare const isFloat64Array: unknown;
export declare const isBigInt64Array: unknown;
export declare const isBigUint64Array: unknown;
export declare const isKeyObject: unknown;
export declare const isCryptoKey: unknown;
