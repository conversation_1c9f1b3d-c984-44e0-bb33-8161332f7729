#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# “鲲鹏通讯” - 终极红包工具 (V4 - 最终正确版)
#
# 集成所有最终破解的逻辑：两步流程、正确的动态签名密钥、正确的签名原文。

import requests
import json
import base64
import random
import string
import time
import hmac
import hashlib

# --- 1. 全局配置 ---
BASE_URL = "http://23.248.226.178:8095" # 更新为新的服务器地址
ACCOUNT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"
API_KEY_STRING = "212i919292901"

# --- 2. 核心算法函数 (已升级为最终正确版) ---
def generate_api_signature(key_b64: str, user_id: str, access_token: str, **kwargs) -> (str, str):
    """
    [Hook验证正确] 基于Hook分析的签名算法
    Hook示例: 212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh155831591981753676978158
    格式: API_KEY + user_id + access_token + language + phone + salt
    """
    salt = str(int(time.time() * 1000))
    sign_key = base64.b64decode(key_b64)

    # 基于Hook分析的正确顺序: API_KEY + user_id + access_token + language + 其他参数 + salt
    content_parts = [API_KEY_STRING, str(user_id), access_token]

    # 添加language参数 (Hook中观察到zh)
    if 'language' in kwargs:
        content_parts.append(str(kwargs['language']))
    else:
        content_parts.append('zh')  # 默认语言

    # 添加其他参数 (按key排序，但language已处理)
    for key in sorted(kwargs.keys()):
        if key != 'language':  # language已经添加过了
            content_parts.append(str(kwargs[key]))

    content_parts.append(salt)
    content_to_sign = "".join(content_parts)

    print(f"🔐 Hook验证签名 - 用户{user_id}:")
    print(f"   签名内容: {content_to_sign}")
    print(f"   内容长度: {len(content_to_sign)}")

    secret = base64.b64encode(hmac.new(sign_key, content_to_sign.encode('utf-8'), hashlib.md5).digest()).decode('utf-8')
    print(f"   生成签名: {secret}")

    return salt, secret

# --- 3. 功能模块 ---
def get_latest_messages_mock(user_id):
    """(模拟功能) 返回一个固定红包ID用于测试"""
    print(f"\n--- [模拟] 账号 {user_id} 发现新红包... ---")
    return ["6885ccf90e75c97d1d5a7231"]

def open_red_packet(session, phone, user_data: dict, packet_id: str):
    try:
        user_id = str(user_data.get("userId"))
        access_token = user_data.get("access_token")
        http_key_b64 = user_data.get("httpKey")
        pay_key_b64 = user_data.get("payKey") # 我们将用payKey来领红包
        
        if not all([user_id, access_token, http_key_b64, pay_key_b64]):
            print(f"\033[91m账号 {phone} 数据不完整, 跳过。\033[0m"); return

        language = "zh"
        headers = {"User-Agent": "chat_im/2.1.8 (Linux; U; Android 10; 22041216UC Build/UP1A.231005.007)"}

        print(f"🔍 账号信息: 用户ID={user_id}, 手机={phone}")

        # --- [步骤 A: /getRedPacket “摸红包”] ---
        print(f"--- 账号 {user_id} 正在“摸红包”... ---")
        # 签名参数 (基于Hook分析，包含手机号): id, language, phone
        params_get_sign = {'id': packet_id, 'language': language, 'phone': phone}
        salt_get, secret_get = generate_api_signature(http_key_b64, user_id, access_token, **params_get_sign)
        params_get_full = {"id": packet_id, "language": language, "access_token": access_token, "salt": salt_get, "secret": secret_get}

        print(f"🔑 摸红包签名: salt={salt_get}, secret={secret_get}")

        response_get = session.get(f"{BASE_URL}/redPacket/getRedPacket", params=params_get_full, headers=headers, timeout=15)
        print(f"📤 摸红包请求URL: {response_get.url}")

        try:
            resp_get_json = response_get.json()
        except:
            print(f"\033[91m>>> 摸红包响应解析失败: {response_get.text[:200]}\033[0m")
            return

        if resp_get_json.get("resultCode") != 1:
            error_code = resp_get_json.get("resultCode")
            error_msg = resp_get_json.get('resultMsg', '未知')

            if error_code == 1030102:
                print(f"\033[93m>>> 令牌失效 (账号需要重新登录)\033[0m")
            elif error_code == 100102:
                print(f"\033[93m>>> 红包不存在或已过期\033[0m")
            elif error_code == 104004:
                print(f"\033[93m>>> 红包已被领完\033[0m")
            else:
                print(f"\033[91m>>> “摸红包”失败: {error_msg} (错误码: {error_code}) <<<\033[0m")
                print(f"完整响应: {json.dumps(resp_get_json, ensure_ascii=False)}")
            return
        
        print(">>> “摸红包”成功，已获得许可。")
        time.sleep(random.uniform(0.5, 1.2))

        # --- [步骤 B: /openRedPacket “拆红包”] ---
        print(f"--- 账号 {user_id} 正在“拆红包”... ---")
        params_open_sign = {'id': packet_id, 'language': language, 'phone': phone}
        salt_open, secret_open = generate_api_signature(pay_key_b64, user_id, access_token, **params_open_sign)
        params_open_full = {"id": packet_id, "language": language, "access_token": access_token, "salt": salt_open, "secret": secret_open}
        
        response_open = session.get(f"{BASE_URL}/redPacket/openRedPacket", params=params_open_full, headers=headers, timeout=15)
        resp_open_json = response_open.json()
        print("--- 服务器响应 ---\n", json.dumps(resp_open_json, indent=2, ensure_ascii=False))

        if resp_open_json.get("resultCode") == 1: print(f"\033[92m>>> 领取成功！ <<<\033[0m")
        elif resp_open_json.get("resultCode") in [100102, 104004]: print(f"\033[93m>>> 红包已领完。\033[0m")
        else: print(f"\033[91m>>> 领取失败: {resp_open_json.get('resultMsg', '未知')} <<<\033[0m")

    except Exception as e: print(f"\033[91m!! 账号 {phone} 发生未知错误: {e}\033[0m")


# --- 4. 启动器 ---
if __name__ == "__main__":
    try:
        print("="*60); print(" "*12 + "“鲲鹏通讯”红包监控与领取工具 V4 (最终版)"); print("="*60)
        try:
            with open(ACCOUNT_FILE, "r", encoding="utf-8") as f: lines = f.readlines()
            print(f"成功加载 {len(lines)} 个账号。")
        except: print(f"\033[91m错误：找不到账号文件 '{ACCOUNT_FILE}'！\033[0m"); exit()

        packet_id_input = input("请输入要领取的【红包ID】: ").strip()
        
        if packet_id_input and len(packet_id_input) > 10:
            print(f"\033[95m*** 准备使用所有账号领取红包 {packet_id_input} ***\033[0m")
            session = requests.Session()
            for i, line in enumerate(lines):
                print("\n" + "="*50 + f"\n--- [任务 {i + 1} / {len(lines)}] ---")
                try:
                    phone, pwd, data_str = line.strip().split(':', 2); data = json.loads(data_str)
                    open_red_packet(session, phone, data, packet_id_input)
                except Exception as e: print(f"第 {i+1} 行数据格式错误，已跳过: {e}")
                if i < len(lines) - 1: time.sleep(random.uniform(1.2, 2.5))
            print("\033[95m\n*** 所有账号领取完毕 ***\033[0m")
        else:
            print("输入的红包ID无效。")
    except KeyboardInterrupt: pass
    print("\n\n程序已退出。")