@echo off
chcp 65001 >nul
title 51代理自动签到工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    51代理自动签到工具                          ║
echo ║                                                              ║
echo ║  Windows批处理启动脚本                                        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo × Python未安装或未添加到PATH
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✓ Python已安装
echo.

REM 运行启动脚本
python start.py

pause
