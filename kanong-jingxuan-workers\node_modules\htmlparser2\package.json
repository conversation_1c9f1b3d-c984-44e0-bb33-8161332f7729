{"name": "htmlparser2", "version": "10.0.0", "description": "Fast & forgiving HTML/XML parser", "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./WritableStream": {"import": {"types": "./dist/esm/WritableStream.d.ts", "default": "./dist/esm/WritableStream.js"}, "require": {"types": "./dist/commonjs/WritableStream.d.ts", "default": "./dist/commonjs/WritableStream.js"}}}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["WritableStream.js", "dist", "src"], "scripts": {"build": "tshy", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "lint": "npm run lint:es && npm run lint:ts && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "lint:ts": "tsc --noEmit", "prepare": "npm run build", "test": "npm run test:vi && npm run lint", "test:vi": "vitest run"}, "prettier": {"tabWidth": 4}, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.2.1", "entities": "^6.0.0"}, "devDependencies": {"@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitest/coverage-v8": "^2.1.8", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-n": "^17.15.1", "eslint-plugin-unicorn": "^56.0.1", "prettier": "^3.4.2", "tshy": "^3.0.2", "typescript": "^5.7.2", "vitest": "^2.0.2"}, "tshy": {"exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"], "exports": {".": "./src/index.ts", "./WritableStream": "./src/WritableStream.ts"}}}