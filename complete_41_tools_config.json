{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"command": "C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\.venv-mcp\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\ida-pro-mcp-new\\src\\ida_pro_mcp\\server.py", "--unsafe", "--transport", "stdio"], "timeout": 3600, "disabled": false, "env": {"IDA_MCP_ENABLE_ALL_TOOLS": "true", "IDA_MCP_UNSAFE_MODE": "true", "IDA_MCP_DEBUG": "true"}}}, "all_41_tools": {"basic_connection": ["check_connection", "get_metadata", "get_current_address", "get_current_function"], "function_operations": ["get_function_by_name", "get_function_by_address", "list_functions", "decompile_function", "disassemble_function", "rename_function", "set_function_prototype"], "data_operations": ["convert_number", "list_strings", "list_strings_filter", "search_strings", "list_globals", "list_globals_filter", "list_local_types", "get_defined_structures"], "analysis_tools": ["get_xrefs_to", "get_xrefs_to_field", "get_entry_points", "read_memory_bytes", "data_read_byte", "data_read_word", "data_read_dword", "data_read_qword", "data_read_string"], "modification_tools": ["set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "set_local_variable_type", "declare_c_type", "get_stack_frame_variables"], "unsafe_debug_tools": ["dbg_get_registers", "dbg_get_call_stack", "dbg_list_breakpoints", "dbg_start_process", "dbg_exit_process", "dbg_continue_process", "dbg_run_to", "dbg_set_breakpoint", "dbg_delete_breakpoint", "dbg_enable_breakpoint"]}}