#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 验证码识别重试机制测试脚本

import sys
import os
import time
from io import BytesIO

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_captcha_recognition():
    """测试验证码识别功能"""
    print("🧪 验证码识别功能测试")
    print("=" * 40)
    
    try:
        from zhuccc1 import recognize_captcha, OCR_AVAILABLE
        
        if not OCR_AVAILABLE:
            print("❌ ddddocr未安装，无法测试OCR功能")
            return False
        
        print("✅ ddddocr已安装，开始测试...")
        
        # 创建模拟验证码图片数据
        print("📝 创建模拟验证码数据...")
        
        # 这里我们创建一些测试数据
        test_cases = [
            {
                'name': '空数据测试',
                'data': b'',
                'expected': 'need_new_captcha'
            },
            {
                'name': '无效图片数据测试',
                'data': b'invalid_image_data',
                'expected': 'need_new_captcha'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 测试用例 {i}: {test_case['name']}")
            
            try:
                result = recognize_captcha(test_case['data'], max_retries=2)
                print(f"   结果: {result}")
                
                if result == test_case['expected']:
                    print(f"   ✅ 测试通过")
                else:
                    print(f"   ⚠️ 结果不符合预期 (期望: {test_case['expected']})")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print("\n✅ 验证码识别功能测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_captcha_retry_logic():
    """测试验证码重试逻辑"""
    print("\n🧪 验证码重试逻辑测试")
    print("=" * 40)
    
    try:
        # 模拟验证码识别的各种返回值
        test_results = [
            ('1234', '4位数字', True),
            ('12345', '5位数字', True),  # 应该截取前4位
            ('123', '3位数字', False),
            ('abcd', '字母', False),
            ('12a4', '混合字符', False),
            ('', '空字符串', False),
            ('need_new_captcha', '需要新验证码', False),
            ('retry', '用户重试', False)
        ]
        
        print("📋 测试各种验证码识别结果的处理:")
        
        for result, desc, should_accept in test_results:
            print(f"\n   测试: {desc} -> '{result}'")
            
            # 模拟主程序中的验证逻辑
            if result == 'retry':
                print("     🔄 用户选择重新获取验证码")
                accepted = False
            elif result == 'need_new_captcha':
                print("     🔄 OCR识别失败，需要新验证码")
                accepted = False
            elif result and len(result) == 4 and result.isdigit():
                print(f"     ✅ 验证码有效: {result}")
                accepted = True
            elif result and len(result) > 4 and result.isdigit():
                truncated = result[:4]
                print(f"     ✅ 验证码有效 (截取): {truncated}")
                accepted = True
            else:
                print(f"     ❌ 验证码无效: {result}")
                accepted = False
            
            if accepted == should_accept:
                print("     ✅ 处理逻辑正确")
            else:
                print(f"     ⚠️ 处理逻辑异常 (期望: {should_accept}, 实际: {accepted})")
        
        print("\n✅ 验证码重试逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_retry_mechanism():
    """测试重试机制"""
    print("\n🧪 重试机制测试")
    print("=" * 40)
    
    try:
        print("📋 模拟验证码获取和识别的重试流程:")
        
        # 模拟重试场景
        scenarios = [
            {
                'name': '第1次获取失败，第2次成功',
                'attempts': [
                    {'get_success': False, 'reason': '网络错误'},
                    {'get_success': True, 'captcha_result': '1234'}
                ]
            },
            {
                'name': '获取成功但识别失败，需要重新获取',
                'attempts': [
                    {'get_success': True, 'captcha_result': 'need_new_captcha'},
                    {'get_success': True, 'captcha_result': '5678'}
                ]
            },
            {
                'name': '多次识别失败，最终成功',
                'attempts': [
                    {'get_success': True, 'captcha_result': 'abc'},
                    {'get_success': True, 'captcha_result': '12'},
                    {'get_success': True, 'captcha_result': '9876'}
                ]
            }
        ]
        
        for scenario in scenarios:
            print(f"\n🎯 场景: {scenario['name']}")
            
            for i, attempt in enumerate(scenario['attempts'], 1):
                print(f"   第{i}次尝试:")
                
                if not attempt['get_success']:
                    print(f"     ❌ 获取验证码失败: {attempt['reason']}")
                    print("     🔄 等待后重试...")
                    continue
                
                result = attempt['captcha_result']
                print(f"     📷 获取验证码成功")
                print(f"     🤖 识别结果: {result}")
                
                if result == 'need_new_captcha':
                    print("     🔄 识别失败，获取新验证码...")
                elif result and len(result) == 4 and result.isdigit():
                    print(f"     ✅ 识别成功，验证码: {result}")
                    break
                else:
                    print(f"     ❌ 识别结果无效，重新获取...")
            
            print(f"   📊 场景完成")
        
        print("\n✅ 重试机制测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 验证码识别重试机制测试套件")
    print("🔧 测试验证码识别失败时的自动重试逻辑")
    print("=" * 60)
    
    tests = [
        ("验证码识别功能", test_captcha_recognition),
        ("验证码重试逻辑", test_captcha_retry_logic),
        ("重试机制模拟", test_retry_mechanism)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        result = test_func()
        results.append(result)
        
        if result:
            print(f"✅ {test_name}通过")
        else:
            print(f"❌ {test_name}失败")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过! 验证码重试机制正常!")
        print("💡 新的重试逻辑特点:")
        print("   - OCR识别失败时自动获取新验证码")
        print("   - 支持4位数字验证码的严格验证")
        print("   - 增加重试次数到5次")
        print("   - 智能处理各种识别结果")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
    
    return passed_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
