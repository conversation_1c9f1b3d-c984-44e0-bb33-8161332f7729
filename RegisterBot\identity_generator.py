import random
import datetime

class IdentityGenerator:
    """
    用于生成随机的中国公民身份信息
    包括姓名和身份证号
    """
    
    # 常见姓氏
    SURNAMES = [
        "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
        "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧",
        "程", "曹", "袁", "邓", "许", "傅", "沈", "曾", "彭", "吕",
        "苏", "卢", "蒋", "蔡", "贾", "丁", "魏", "薛", "叶", "阎",
        "余", "潘", "杜", "戴", "夏", "钟", "汪", "田", "任", "姜",
        "范", "方", "石", "姚", "谭", "廖", "邹", "熊", "金", "陆",
        "郝", "孔", "白", "崔", "康", "毛", "邱", "秦", "江", "史",
        "顾", "侯", "邵", "孟", "龙", "万", "段", "雷", "钱", "汤",
        "尹", "黎", "易", "常", "武", "乔", "贺", "赖", "龚", "文"
    ]
    
    # 常见名字用字
    NAME_CHARS = [
        "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
        "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞",
        "平", "刚", "桂英", "玲", "桂兰", "建华", "文", "斌", "玉兰", "华",
        "建国", "建军", "小红", "小明", "小刚", "小芳", "小霞", "小军", "小华", "小燕",
        "国强", "国栋", "国华", "国辉", "国安", "国庆", "国良", "国梁", "国荣", "国龙",
        "建", "文华", "文娟", "文静", "文龙", "文明", "文强", "文彬", "文艳", "文杰",
        "宇", "宁", "安", "佳", "卓", "昊", "天", "文轩", "文宇", "子墨",
        "浩宇", "浩然", "皓轩", "绍辉", "绍齐", "绍武", "绍玉", "绍祖", "绍钧", "绍辉",
        "家伟", "家杰", "家豪", "家铭", "家骏", "家驹", "家栋", "家乐", "家明", "家兴",
        "俊杰", "俊彦", "俊楠", "俊伟", "俊达", "俊峰", "俊辉", "俊杰", "俊良", "俊迈"
    ]
    
    # 地区编码（前6位）- 简化版，仅包含部分常见地区
    REGION_CODES = {
        "北京市": ["110100", "110101", "110102", "110105", "110106", "110107", "110108", "110109"],
        "上海市": ["310100", "310101", "310104", "310105", "310106", "310107", "310109", "310110"],
        "广州市": ["440100", "440103", "440104", "440105", "440106", "440111", "440112", "440113"],
        "深圳市": ["440300", "440303", "440304", "440305", "440306", "440307", "440308"],
        "成都市": ["510100", "510104", "510105", "510106", "510107", "510108", "510112", "510113"],
        "重庆市": ["500100", "500101", "500102", "500103", "500104", "500105", "500106", "500107"],
        "杭州市": ["330100", "330102", "330103", "330104", "330105", "330106", "330108", "330109"],
        "南京市": ["320100", "320102", "320104", "320105", "320106", "320111", "320113", "320114"],
        "武汉市": ["420100", "420102", "420103", "420104", "420105", "420106", "420107", "420111"],
        "西安市": ["610100", "610102", "610103", "610104", "610111", "610112", "610113", "610114"]
    }
    
    @classmethod
    def generate_name(cls):
        """生成随机中文姓名"""
        surname = random.choice(cls.SURNAMES)
        
        # 有时用单字名，有时用双字名
        if random.random() < 0.4:  # 40%概率用单字名
            name = random.choice(cls.NAME_CHARS)
        else:  # 60%概率用双字名
            name = random.choice(cls.NAME_CHARS) + random.choice(cls.NAME_CHARS)
            
            # 确保双字名不是重复的字
            while name[0] == name[1]:
                name = random.choice(cls.NAME_CHARS) + random.choice(cls.NAME_CHARS)
        
        return surname + name
    
    @classmethod
    def generate_id_card(cls):
        """生成随机身份证号"""
        # 1. 随机选择一个地区
        region = random.choice(list(cls.REGION_CODES.keys()))
        region_code = random.choice(cls.REGION_CODES[region])
        
        # 2. 生成出生日期 (1960-2000年)
        start_date = datetime.date(1960, 1, 1)
        end_date = datetime.date(2000, 12, 31)
        time_between_dates = end_date - start_date
        days_between_dates = time_between_dates.days
        random_days = random.randrange(days_between_dates)
        birth_date = start_date + datetime.timedelta(days=random_days)
        birth_date_str = birth_date.strftime("%Y%m%d")
        
        # 3. 生成顺序码（3位数）
        sequence_code = str(random.randint(1, 999)).zfill(3)
        
        # 4. 前17位
        id_card_17 = region_code + birth_date_str + sequence_code
        
        # 5. 计算校验码
        factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_value = 0
        for i in range(17):
            sum_value += int(id_card_17[i]) * factors[i]
        
        check_code = check_codes[sum_value % 11]
        
        # 6. 完整的身份证号
        id_card = id_card_17 + check_code
        
        return id_card
    
    @classmethod
    def generate_identity(cls):
        """生成一个完整的身份（姓名和身份证号）"""
        name = cls.generate_name()
        id_card = cls.generate_id_card()
        return {"name": name, "id_card": id_card}


# 测试代码
if __name__ == "__main__":
    print("生成10个随机身份信息:")
    for i in range(10):
        identity = IdentityGenerator.generate_identity()
        print(f"{i+1}. 姓名: {identity['name']}, 身份证号: {identity['id_card']}") 