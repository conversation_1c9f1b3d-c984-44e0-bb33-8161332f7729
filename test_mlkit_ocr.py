#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MLKit OCR 测试脚本
用于测试身份证识别功能
"""

import os
import sys
import time
from pathlib import Path

# 导入MLKit OCR模块
try:
    from mlkit_ocr import MLKitOCR, recognize_id_card_simple
    from mlkit_config import setup_google_cloud_credentials, get_credentials_setup_guide
    print("✓ MLKit OCR 模块导入成功")
except ImportError as e:
    print(f"❌ MLKit OCR 模块导入失败: {e}")
    sys.exit(1)

def test_single_image(image_path: str, credentials_path: str = None):
    """
    测试单张身份证图片识别
    
    Args:
        image_path: 身份证图片路径
        credentials_path: Google Cloud凭证文件路径
    """
    print(f"\n🧪 测试图片: {image_path}")
    print("=" * 60)
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return False
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行识别
    try:
        name, id_number = recognize_id_card_simple(image_path, credentials_path)
        
        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️ 处理时间: {processing_time:.2f} 秒")
        
        if name and id_number:
            print(f"✅ 识别成功!")
            print(f"   👤 姓名: {name}")
            print(f"   🆔 身份证号: {id_number}")
            
            # 验证身份证号格式
            if validate_id_format(id_number):
                print(f"   ✅ 身份证号格式正确")
            else:
                print(f"   ⚠️ 身份证号格式可能有误")
            
            return True
        else:
            print(f"❌ 识别失败")
            print(f"   姓名: {name or '未识别'}")
            print(f"   身份证号: {id_number or '未识别'}")
            return False
            
    except Exception as e:
        print(f"❌ 识别过程出错: {e}")
        return False

def validate_id_format(id_number: str) -> bool:
    """验证身份证号格式"""
    if not id_number:
        return False
    
    if len(id_number) == 15:
        return id_number.isdigit()
    elif len(id_number) == 18:
        return id_number[:-1].isdigit() and id_number[-1] in '0123456789Xx'
    
    return False

def test_batch_images(image_dir: str, credentials_path: str = None):
    """
    批量测试身份证图片识别
    
    Args:
        image_dir: 身份证图片目录
        credentials_path: Google Cloud凭证文件路径
    """
    print(f"\n🧪 批量测试目录: {image_dir}")
    print("=" * 60)
    
    if not os.path.exists(image_dir):
        print(f"❌ 目录不存在: {image_dir}")
        return
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    # 查找图片文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(Path(image_dir).glob(f"*{ext}"))
        image_files.extend(Path(image_dir).glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"❌ 目录中没有找到图片文件")
        return
    
    print(f"📁 找到 {len(image_files)} 张图片")
    
    # 统计结果
    success_count = 0
    total_count = len(image_files)
    results = []
    
    # 逐个测试
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{total_count}] {image_file.name}")
        print("-" * 40)
        
        success = test_single_image(str(image_file), credentials_path)
        if success:
            success_count += 1
        
        results.append({
            'file': image_file.name,
            'success': success
        })
        
        # 避免API调用过快
        if i < total_count:
            time.sleep(1)
    
    # 显示统计结果
    print(f"\n📊 批量测试结果:")
    print("=" * 60)
    print(f"总计: {total_count} 张")
    print(f"成功: {success_count} 张")
    print(f"失败: {total_count - success_count} 张")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    # 显示失败的文件
    failed_files = [r['file'] for r in results if not r['success']]
    if failed_files:
        print(f"\n❌ 识别失败的文件:")
        for file in failed_files:
            print(f"   - {file}")

def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置MLKit OCR测试环境")
    print("=" * 60)
    
    # 检查凭证文件
    credentials_files = [
        'google-cloud-credentials.json',
        'credentials.json',
        'service-account-key.json'
    ]
    
    credentials_path = None
    for file in credentials_files:
        if os.path.exists(file):
            credentials_path = file
            break
    
    if credentials_path:
        if setup_google_cloud_credentials(credentials_path):
            print(f"✅ 使用凭证文件: {credentials_path}")
            return credentials_path
        else:
            print(f"❌ 凭证文件无效: {credentials_path}")
    else:
        print("❌ 未找到Google Cloud凭证文件")
        print("\n" + get_credentials_setup_guide())
        return None
    
    return credentials_path

def main():
    """主函数"""
    print("🎯 MLKit OCR 身份证识别测试")
    print("=" * 60)
    
    # 设置测试环境
    credentials_path = setup_test_environment()
    if not credentials_path:
        print("\n❌ 环境设置失败，请先配置Google Cloud凭证")
        return
    
    # 测试选项
    print(f"\n📋 测试选项:")
    print(f"1. 测试单张图片")
    print(f"2. 批量测试目录")
    print(f"3. 测试默认目录")
    print(f"4. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择测试选项 (1-4): ").strip()
            
            if choice == '1':
                image_path = input("请输入身份证图片路径: ").strip()
                if image_path:
                    test_single_image(image_path, credentials_path)
                
            elif choice == '2':
                image_dir = input("请输入图片目录路径: ").strip()
                if image_dir:
                    test_batch_images(image_dir, credentials_path)
                
            elif choice == '3':
                # 测试默认目录
                default_dirs = [
                    'id_card_sorted/正面',
                    'test_images',
                    'images',
                    '.'
                ]
                
                for dir_path in default_dirs:
                    if os.path.exists(dir_path):
                        print(f"📁 使用默认目录: {dir_path}")
                        test_batch_images(dir_path, credentials_path)
                        break
                else:
                    print("❌ 未找到默认测试目录")
                
            elif choice == '4':
                print("👋 测试结束")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，测试结束")
            break
        except Exception as e:
            print(f"❌ 测试过程出错: {e}")

if __name__ == "__main__":
    main()
