#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import time
import random
import json
import logging
import os
import sys
from typing import Dict, Tuple, Optional, Union, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("PhoneSmsModule")

class SmsApiConfig:
    """短信验证码API配置"""
    # API平台配置
    API_BASE_URL = "http://api.my531.com"
    USERNAME = "a263263147"
    PASSWORD = "520@txbmm"
    
    # 项目配置
    DEFAULT_PROJECT_ID = ""
    
    # 重试配置
    MAX_SMS_RETRIES = 60  # 获取短信验证码的最大重试次数
    SMS_RETRY_INTERVAL = 5  # 获取验证码重试间隔(秒)
    DEFAULT_TIMEOUT = 300  # 默认超时时间(秒)
    
    # 会话配置
    USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"

class SmsApiClient:
    """短信API客户端"""
    
    def __init__(self, username: str = "", password: str = "", project_id: str = "", config: SmsApiConfig = None):
        """
        初始化客户端
        :param username: API用户名
        :param password: API密码
        :param project_id: 项目ID
        :param config: 配置对象，如果不传则使用默认配置
        """
        self.config = config or SmsApiConfig()
        
        # 如果提供了参数，则覆盖配置中的值
        if username:
            self.config.USERNAME = username
        if password:
            self.config.PASSWORD = password
        if project_id:
            self.config.DEFAULT_PROJECT_ID = project_id
            
        self.token = None
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": self.config.USER_AGENT,
            "Content-Type": "application/json"
        })
        
        logger.info(f"短信API客户端已初始化，服务器: {self.config.API_BASE_URL}")
        
    def login(self) -> bool:
        """
        登录API平台获取token
        :return: 是否登录成功
        """
        if not self.config.USERNAME or not self.config.PASSWORD:
            logger.error("未配置用户名或密码")
            return False
            
        logger.info(f"正在登录短信平台，用户名: {self.config.USERNAME}")
        try:
            url = f"{self.config.API_BASE_URL}/login"
            data = {
                "username": self.config.USERNAME,
                "password": self.config.PASSWORD
            }
            response = self.session.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 0:
                        self.token = result.get("data", {}).get("token")
                        if self.token:
                            logger.info(f"登录成功，token: {self.token[:10]}...")
                            return True
                        else:
                            logger.error("登录成功但未返回token")
                            return False
                    else:
                        logger.error(f"登录失败，错误码: {result.get('code')}, 信息: {result.get('message')}")
                        return False
                except ValueError:
                    logger.error(f"登录响应解析失败: {response.text}")
                    return False
            else:
                logger.error(f"登录请求失败，HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"登录过程发生异常: {e}")
            return False
    
    def check_balance(self) -> Optional[float]:
        """
        查询账户余额
        :return: 成功返回余额，失败返回None
        """
        if not self.token and not self.login():
            return None
        
        try:
            url = f"{self.config.API_BASE_URL}/balance"
            headers = {"token": self.token}
            response = self.session.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    balance = result.get("data", {}).get("balance", 0)
                    logger.info(f"余额查询成功，余额: {balance}")
                    return float(balance)
                else:
                    logger.error(f"余额查询失败，错误码: {result.get('code')}, 信息: {result.get('message')}")
                    return None
            else:
                logger.error(f"余额查询请求失败，HTTP状态码: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"余额查询过程发生异常: {e}")
            return None
    
    def get_phone_number(self, project_id: str = "") -> Tuple[bool, Union[Tuple[str, str], str]]:
        """
        获取手机号
        :param project_id: 项目ID，不传则使用默认项目ID
        :return: (success, result)
                 success为True，result为(手机号, "归属地")
                 success为False，result为错误信息
        """
        if not project_id:
            project_id = self.config.DEFAULT_PROJECT_ID
            
        if not project_id:
            return False, "未指定项目ID"
            
        if not self.token and not self.login():
            return False, "未登录或登录失败"
        
        try:
            url = f"{self.config.API_BASE_URL}/get_mobile"
            headers = {"token": self.token}
            data = {"project_id": project_id}
            response = self.session.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    phone = result.get("data", {}).get("mobile")
                    if phone:
                        logger.info(f"成功获取手机号: {phone}")
                        return True, (phone, "未知")
                    else:
                        logger.error("获取手机号成功但未返回号码")
                        return False, "获取手机号成功但未返回号码"
                else:
                    error_msg = result.get("message", "未知错误")
                    logger.error(f"获取手机号失败: {error_msg}")
                    return False, error_msg
            else:
                logger.error(f"获取手机号请求失败，HTTP状态码: {response.status_code}")
                return False, f"HTTP错误: {response.status_code}"
        except Exception as e:
            logger.error(f"获取手机号过程发生异常: {e}")
            return False, str(e)
    
    def get_sms_code(self, phone_number: str, project_id: str = "", timeout: int = None) -> Optional[str]:
        """
        获取短信验证码
        :param phone_number: 手机号
        :param project_id: 项目ID，不传则使用默认项目ID
        :param timeout: 超时时间(秒)，不传则使用默认值
        :return: 成功返回验证码，失败返回None
        """
        if not project_id:
            project_id = self.config.DEFAULT_PROJECT_ID
            
        if not project_id:
            logger.error("未指定项目ID")
            return None
            
        if not self.token and not self.login():
            return None
        
        # 提取验证码的函数
        def extract_code(message: str) -> Optional[str]:
            """从短信内容中提取验证码，默认提取4-6位数字"""
            import re
            # 尝试匹配4-6位数字作为验证码
            patterns = [
                r'验证码[为是:：\s]*([0-9]{4,6})',  # 常见形式：验证码为1234
                r'码[为是:：\s]*([0-9]{4,6})',      # 简化形式：码1234
                r'code[为是:：\s]*([0-9]{4,6})',    # 英文形式：code 1234
                r'([0-9]{4,6})',                   # 直接匹配4-6位数字
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, message, re.IGNORECASE)
                if matches:
                    return matches[0]
            
            return None
        
        logger.info(f"正在获取手机号 {phone_number} 的短信验证码")
        start_time = time.time()
        retries = 0
        
        timeout_val = timeout or self.config.DEFAULT_TIMEOUT
        
        while time.time() - start_time < timeout_val and retries < self.config.MAX_SMS_RETRIES:
            try:
                url = f"{self.config.API_BASE_URL}/get_message"
                headers = {"token": self.token}
                data = {
                    "project_id": project_id,
                    "phone_num": phone_number
                }
                response = self.session.post(url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        message = result.get("data", {}).get("message", "")
                        if message:
                            logger.info(f"获取到短信内容: {message}")
                            
                            # 尝试从短信内容中提取验证码
                            code = extract_code(message)
                            if code:
                                logger.info(f"成功提取验证码: {code}")
                                return code
                            else:
                                logger.warning(f"未能从短信内容中提取验证码，原始内容: {message}")
                        else:
                            logger.debug("短信内容为空")
                    else:
                        error_msg = result.get("message", "未知错误")
                        if "没有" in error_msg or "不存在" in error_msg or "无短信" in error_msg:
                            # 如果是还没收到短信，则继续轮询
                            logger.debug(f"短信尚未到达 ({retries+1}/{self.config.MAX_SMS_RETRIES}): {error_msg}")
                        else:
                            # 其他错误则记录
                            logger.error(f"获取短信失败: {error_msg}")
                else:
                    logger.error(f"获取短信请求失败，HTTP状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"获取短信过程发生异常: {e}")
            
            # 每次轮询间隔随机时间，避免触发平台限制
            sleep_time = self.config.SMS_RETRY_INTERVAL + random.uniform(0, 1)
            logger.debug(f"等待 {sleep_time:.2f} 秒后重试...")
            time.sleep(sleep_time)
            retries += 1
        
        logger.error(f"在 {timeout_val} 秒内未能获取到验证码，已尝试 {retries} 次")
        return None
    
    def add_to_blacklist(self, phone_number: str, project_id: str = "") -> bool:
        """
        将号码加入黑名单
        :param phone_number: 手机号
        :param project_id: 项目ID，不传则使用默认项目ID
        :return: 是否成功
        """
        if not project_id:
            project_id = self.config.DEFAULT_PROJECT_ID
            
        if not project_id:
            logger.error("未指定项目ID")
            return False
            
        if not self.token and not self.login():
            return False
        
        try:
            url = f"{self.config.API_BASE_URL}/add_blacklist"
            headers = {"token": self.token}
            data = {
                "project_id": project_id,
                "phone_num": phone_number
            }
            response = self.session.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    logger.info(f"手机号 {phone_number} 已成功加入黑名单")
                    return True
                else:
                    error_msg = result.get("message", "未知错误")
                    logger.error(f"将手机号 {phone_number} 加入黑名单失败: {error_msg}")
                    return False
            else:
                logger.error(f"加入黑名单请求失败，HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"加入黑名单过程发生异常: {e}")
            return False
    
    def release_phone_number(self, phone_number: str, project_id: str = "") -> bool:
        """
        释放手机号
        :param phone_number: 手机号
        :param project_id: 项目ID，不传则使用默认项目ID
        :return: 是否成功
        """
        if not project_id:
            project_id = self.config.DEFAULT_PROJECT_ID
            
        if not project_id:
            logger.error("未指定项目ID")
            return False
            
        if not self.token and not self.login():
            return False
        
        try:
            url = f"{self.config.API_BASE_URL}/release_mobile"
            headers = {"token": self.token}
            data = {
                "project_id": project_id,
                "phone_num": phone_number
            }
            response = self.session.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    logger.info(f"手机号 {phone_number} 已成功释放")
                    return True
                else:
                    error_msg = result.get("message", "未知错误")
                    logger.error(f"释放手机号 {phone_number} 失败: {error_msg}")
                    return False
            else:
                logger.error(f"释放手机号请求失败，HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"释放手机号过程发生异常: {e}")
            return False

def get_phone_and_sms(username: str, password: str, project_id: str) -> Dict[str, Any]:
    """
    获取手机号和短信验证码的独立函数，供主脚本调用
    
    :param username: API用户名
    :param password: API密码
    :param project_id: 项目ID
    :return: 包含结果的字典：
        {
            "success": True/False,  # 是否成功获取手机号和验证码
            "phone": "手机号",      # 成功时返回获取的手机号
            "code": "验证码",       # 成功时返回获取的验证码
            "message": "消息内容",   # 成功或失败的详细信息
            "client": client对象    # 成功时返回已登录的client对象，方便后续操作
        }
    """
    # 创建客户端对象
    client = SmsApiClient(username, password, project_id)
    
    # 登录
    if not client.login():
        return {
            "success": False,
            "phone": None,
            "code": None,
            "message": "登录失败",
            "client": client
        }
    
    # 检查余额
    balance = client.check_balance()
    if balance is None:
        return {
            "success": False,
            "phone": None,
            "code": None,
            "message": "无法获取账户余额",
            "client": client
        }
    elif balance <= 0:
        return {
            "success": False,
            "phone": None,
            "code": None,
            "message": f"账户余额不足，当前余额: {balance}",
            "client": client
        }
    
    # 获取手机号
    success, result = client.get_phone_number(project_id)
    if not success:
        return {
            "success": False,
            "phone": None,
            "code": None,
            "message": f"获取手机号失败: {result}",
            "client": client
        }
    
    phone_number, _ = result
    
    # 获取验证码
    verification_code = client.get_sms_code(phone_number, project_id)
    if not verification_code:
        # 获取验证码失败时，尝试释放手机号
        client.release_phone_number(phone_number, project_id)
        return {
            "success": False,
            "phone": phone_number,
            "code": None,
            "message": "获取验证码超时或失败",
            "client": client
        }
    
    # 成功获取验证码
    return {
        "success": True,
        "phone": phone_number,
        "code": verification_code,
        "message": "成功获取手机号和验证码",
        "client": client
    }

# 直接运行模块进行测试
if __name__ == "__main__":
    print("=" * 60)
    print("手机号和短信验证码获取模块测试")
    print("=" * 60)
    
    # 获取用户输入
    username = input("请输入API用户名: ")
    password = input("请输入API密码: ")
    project_id = input("请输入项目ID: ")
    
    print("\n开始测试获取手机号和短信验证码...")
    
    # 测试获取手机号和验证码
    result = get_phone_and_sms(username, password, project_id)
    
    if result["success"]:
        print(f"\n✅ 成功获取手机号和验证码:")
        print(f"手机号: {result['phone']}")
        print(f"验证码: {result['code']}")
        
        # 释放手机号
        print("\n尝试释放手机号...")
        if result["client"].release_phone_number(result["phone"], project_id):
            print(f"✅ 手机号 {result['phone']} 已成功释放")
        else:
            print(f"❌ 释放手机号 {result['phone']} 失败")
    else:
        print(f"\n❌ 获取失败: {result['message']}")
    
    print("\n测试完成!") 