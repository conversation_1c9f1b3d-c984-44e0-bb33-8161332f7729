const Deal = require('../models/dealModel');
const User = require('../models/userModel');
const Category = require('../models/categoryModel');

// @desc    搜索活动
// @route   GET /api/search
// @access  Public
exports.search = async (req, res) => {
  try {
    const { q, type = 'all' } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        message: '请提供搜索关键词'
      });
    }

    // 分页
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // 搜索结果
    let result = {};
    let total = 0;

    // 根据类型搜索
    switch (type) {
      case 'deals':
        // 搜索活动
        result = await Deal.find(
          { 
            $text: { $search: q },
            status: 'active'
          },
          { score: { $meta: 'textScore' } }
        )
        .populate({
          path: 'author',
          select: 'username avatar creditScore level'
        })
        .populate('category', 'name')
        .sort({ score: { $meta: 'textScore' } })
        .skip(startIndex)
        .limit(limit);

        total = await Deal.countDocuments({ 
          $text: { $search: q },
          status: 'active'
        });
        break;

      case 'users':
        // 搜索用户
        result = await User.find(
          { 
            $or: [
              { username: { $regex: q, $options: 'i' } },
              { bio: { $regex: q, $options: 'i' } }
            ]
          }
        )
        .select('username avatar bio creditScore level')
        .skip(startIndex)
        .limit(limit);

        total = await User.countDocuments({
          $or: [
            { username: { $regex: q, $options: 'i' } },
            { bio: { $regex: q, $options: 'i' } }
          ]
        });
        break;

      case 'all':
      default:
        // 搜索活动
        const deals = await Deal.find(
          { 
            $text: { $search: q },
            status: 'active'
          },
          { score: { $meta: 'textScore' } }
        )
        .populate({
          path: 'author',
          select: 'username avatar creditScore level'
        })
        .populate('category', 'name')
        .sort({ score: { $meta: 'textScore' } })
        .limit(5);

        // 搜索用户
        const users = await User.find(
          { 
            $or: [
              { username: { $regex: q, $options: 'i' } },
              { bio: { $regex: q, $options: 'i' } }
            ]
          }
        )
        .select('username avatar bio creditScore level')
        .limit(5);

        // 搜索分类
        const categories = await Category.find({
          name: { $regex: q, $options: 'i' }
        }).limit(5);

        result = {
          deals,
          users,
          categories
        };

        total = deals.length + users.length + categories.length;
        break;
    }

    // 分页结果
    const pagination = {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: Array.isArray(result) ? result.length : Object.values(result).flat().length,
      pagination: type !== 'all' ? pagination : undefined,
      data: result
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
}; 