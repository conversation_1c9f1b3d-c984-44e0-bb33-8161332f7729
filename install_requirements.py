#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站注册脚本 - 依赖安装脚本

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {package_name} 安装过程中发生错误: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主安装函数"""
    print("🚀 环球网站注册脚本 - 依赖安装")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        ("requests", "HTTP请求库"),
        ("pycryptodome", "AES加密库"),
        ("pillow", "图像处理库"),
        ("ddddocr", "验证码和OCR识别库"),
    ]
    
    # 可选的包
    optional_packages = [
        ("opencv-python", "图像预处理库（提高OCR准确率）"),
    ]
    
    print("📋 检查已安装的包...")
    
    # 检查必需包
    missing_required = []
    for package, desc in required_packages:
        # 特殊处理某些包名
        check_name = package
        if package == "pycryptodome":
            check_name = "Crypto"
        elif package == "pillow":
            check_name = "PIL"
        
        if check_package(check_name):
            print(f"✅ {package} 已安装")
        else:
            print(f"❌ {package} 未安装")
            missing_required.append((package, desc))
    
    # 检查可选包
    missing_optional = []
    for package, desc in optional_packages:
        check_name = package
        if package == "opencv-python":
            check_name = "cv2"
        
        if check_package(check_name):
            print(f"✅ {package} 已安装")
        else:
            print(f"⚠️ {package} 未安装 (可选)")
            missing_optional.append((package, desc))
    
    # 安装缺失的必需包
    if missing_required:
        print(f"\n📦 安装缺失的必需包 ({len(missing_required)}个):")
        success_count = 0
        
        for package, desc in missing_required:
            if install_package(package, desc):
                success_count += 1
        
        if success_count == len(missing_required):
            print(f"\n✅ 所有必需包安装完成!")
        else:
            print(f"\n⚠️ {len(missing_required) - success_count} 个包安装失败")
    else:
        print(f"\n✅ 所有必需包已安装!")
    
    # 询问是否安装可选包
    if missing_optional:
        print(f"\n📦 发现 {len(missing_optional)} 个可选包未安装:")
        for package, desc in missing_optional:
            print(f"   - {package}: {desc}")
        
        install_optional = input("\n是否安装可选包? (Y/N，默认Y): ").strip().upper()
        if install_optional != 'N':
            print("\n📦 安装可选包:")
            for package, desc in missing_optional:
                install_package(package, desc)
    
    # 创建必要的目录
    print(f"\n📁 检查必要目录...")
    
    directories = [
        r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data",
        r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\正面",
        r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted\反面"
    ]
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"✅ 创建目录: {directory}")
            else:
                print(f"✅ 目录已存在: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
    
    # 最终检查
    print(f"\n🔍 最终检查...")
    
    all_good = True
    for package, _ in required_packages:
        check_name = package
        if package == "pycryptodome":
            check_name = "Crypto"
        elif package == "pillow":
            check_name = "PIL"
        
        if not check_package(check_name):
            print(f"❌ {package} 仍未正确安装")
            all_good = False
    
    if all_good:
        print(f"\n🎉 环境配置完成!")
        print(f"💡 现在可以运行:")
        print(f"   python test_ocr.py      # 测试OCR功能")
        print(f"   python test_huanqiu.py  # 测试基本功能")
        print(f"   python zhuccc1.py       # 开始注册")
        
        print(f"\n📋 使用前请确保:")
        print(f"   1. 将身份证图片放入对应目录")
        print(f"   2. 正面图片: C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\id_card_sorted\\正面\\")
        print(f"   3. 反面图片: C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\id_card_sorted\\反面\\")
    else:
        print(f"\n⚠️ 环境配置不完整，请手动安装缺失的包")
    
    return all_good

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 安装过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
