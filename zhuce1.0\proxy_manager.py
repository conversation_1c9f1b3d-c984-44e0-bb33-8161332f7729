# proxy_manager.py
# 一个用于从 51Daili.com 获取和管理代理IP的独立模块。

import requests
import time
from datetime import datetime, timedelta
import json
from typing import Optional, Dict
import subprocess
import platform

class ProxyManager:
    """
    51代理 (51daili.com) 的客户端。

    功能:
    1. 根据配置从API获取一个代理IP。
    2. 自动管理IP的生命周期，如果当前IP过期，会自动获取新的IP。
    3. 将获取到的IP格式化为 Python requests 库可以直接使用的字典。
    """
    API_URL = "http://bapi.51daili.com/getapi2"

    def __init__(self, packid: str, uid: str, access_name: str, access_password: str, protocol: str = 'http', time_duration: int = 3):
        """
        初始化代理管理器。
        
        参数:
            packid (str): 您的套餐ID。
            uid (str): 您的用户ID (uid)。
            access_name (str): 权限校验名称 (accessName)。
            access_password (str): 权限校验密码 (accessPassword)。
            protocol (str): 代理协议, 'http' 或 'socks5'。默认为 'http'。
            time_duration (int): 期望的代理稳定使用时长（分钟）。默认为3分钟。
        """
        # --- 请在这里配置您的API参数 ---
        self.api_params = {
            'linePoolIndex': -1,          # 线路池索引
            'packid': packid,             # 套餐ID
            'time': time_duration,        # 稳定使用时长(分钟)
            'qty': 1,                     # 每次只获取一个IP
            'port': 1 if protocol.lower() == 'http' else 2,  # 1 for HTTP, 2 for SOCKS5
            'format': 'json',             # 返回格式为JSON
            'field': 'ipport,expiretime,regioncode,isptype',  # 需要返回的字段
            'dt': 4,                      # 每日提取去重
            'ct': 1,                      # 未知参数，根据URL保持一致
            'dtc': 2,                     # 新增参数，根据用户提供的有效URL
            'usertype': 17,               # 用户类型
            'uid': uid,                   # 用户ID
            'accessName': access_name,    # 权限校验名称
            'accessPassword': access_password  # 权限校验密码
        }
        
        self.protocol = protocol.lower()
        self.current_proxy_data = None   # 用于存储API返回的原始数据
        print(f"[成功] 51代理管理器已初始化 (协议: {self.protocol.upper()})。")

    def _ping_test(self, ip: str, timeout: int = 3) -> bool:
        """
        测试IP的连通性

        参数:
            ip (str): 要测试的IP地址
            timeout (int): 超时时间（秒），默认3秒

        返回:
            bool: True表示连通，False表示不连通
        """
        try:
            # 根据操作系统选择ping命令
            system = platform.system().lower()
            if system == "windows":
                # Windows系统使用 ping -n 1 -w timeout_ms IP
                cmd = ["ping", "-n", "1", "-w", str(timeout * 1000), ip]
            else:
                # Linux/Mac系统使用 ping -c 1 -W timeout IP
                cmd = ["ping", "-c", "1", "-W", str(timeout), ip]

            # 执行ping命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 2)

            # 检查返回码，0表示成功
            if result.returncode == 0:
                print(f"[连通性测试] IP {ip} 连通正常")
                return True
            else:
                print(f"[连通性测试] IP {ip} 连通失败")
                return False

        except subprocess.TimeoutExpired:
            print(f"[连通性测试] IP {ip} 测试超时")
            return False
        except Exception as e:
            print(f"[连通性测试] IP {ip} 测试出错: {e}")
            return False

    def _fetch_new_proxy(self) -> bool:
        """
        [内部方法] 调用API获取一个新的IP，并存储它。
        """
        print("正在从 51Daili API 获取新代理IP...")
        try:
            # 显示完整请求URL，便于调试
            full_url = self.API_URL + "?" + "&".join([f"{k}={v}" for k, v in self.api_params.items()])
            print(f"请求URL: {full_url}")
            
            response = requests.get(self.API_URL, params=self.api_params, timeout=15)
            response.raise_for_status()  # 如果HTTP状态码不是2xx，则抛出异常
            
            # 显示原始响应内容
            print(f"API原始响应: {response.text}")
            
            # 51代理的API可能返回纯文本或JSON，需要判断格式
            response_text = response.text.strip()
            
            # 如果响应是纯文本格式，可能是错误信息或IP:Port格式
            if response_text and not (response_text.startswith('{') or response_text.startswith('[')):
                # 检查是否是错误信息
                if ':' not in response_text or len(response_text) > 30:
                    print(f"[错误] API返回错误信息: {response_text}")
                    self.current_proxy_data = None
                    return False
                
                # 如果包含冒号，可能是直接返回IP:Port格式
                if ':' in response_text:
                    ip, port = response_text.split(':')
                    # 设置默认过期时间为30分钟后
                    expire_time = datetime.now().replace(microsecond=0) + timedelta(minutes=30)
                    expire_time_str = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # Ping测试仅作为参考，不作为失败的决定性依据，因为很多代理禁ping
                    self._ping_test(ip)
                    
                    self.current_proxy_data = {
                        "ip": ip,
                        "port": port,
                        "expire_time": expire_time
                    }

                    print(f"[成功] 成功获取新代理: {ip}:{port}，将在 {expire_time_str} 过期。")
                    return True
            
            # 尝试解析为JSON格式
            try:
                data = response.json()
                
                # 检查是否有错误信息
                if isinstance(data, dict) and data.get('code') != 0:
                    error_msg = data.get('msg', '未知错误')
                    print(f"[错误] API返回错误: {error_msg}")
                    print(f"完整API响应: {data}")
                    self.current_proxy_data = None
                    return False
                
                # 正常情况下，应该有代理IP信息
                if isinstance(data, dict) and data.get('code') == 0 and 'data' in data and len(data['data']) > 0:
                    proxy_info = data['data'][0]
                    # API实际返回'ip'字段而不是'ipport'字段
                    ip_port = proxy_info.get('ip') or proxy_info.get('ipport')
                    if not ip_port or ':' not in ip_port:
                        print(f"[错误] API返回的代理数据格式不正确: {proxy_info}")
                        return False
                    
                    ip, port = ip_port.split(':')
                    expire_time_str = proxy_info.get('ExpireTime', '')
                    
                    try:
                        expire_time = datetime.strptime(expire_time_str, "%Y-%m-%d %H:%M:%S")
                    except (ValueError, TypeError):
                        # 如果过期时间格式错误，设置默认30分钟
                        expire_time = datetime.now().replace(microsecond=0) + timedelta(minutes=30)
                        expire_time_str = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 测试IP连通性，但不再作为阻塞条件
                    self._ping_test(ip)

                    self.current_proxy_data = {
                        "ip": ip,
                        "port": port,
                        "expire_time": expire_time
                    }

                    print(f"[成功] 成功获取新代理: {ip}:{port}，将在 {expire_time_str} 过期。")
                    return True
                
                print(f"[错误] 未能从API响应中解析出代理信息: {data}")
                return False
                
            except json.JSONDecodeError:
                print(f"[错误] API响应不是有效的JSON格式: {response_text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"[错误] 网络错误: 请求51Daili API失败: {e}")
            self.current_proxy_data = None
            return False
        except Exception as e:
            print(f"[错误] 处理API响应时出现未知错误: {str(e)}")
            self.current_proxy_data = None
            return False
            
    def _test_http_connectivity(self, proxy_dict: Dict[str, str], timeout: int = 10) -> bool:
        """
        [内部方法] 使用代理尝试访问一个测试网站，验证其可用性。

        参数:
            proxy_dict (dict): requests库格式的代理字典
            timeout (int): 超时时间（秒），默认10秒

        返回:
            bool: True表示可用，False表示不可用
        """
        test_url = "https://httpbin.org/ip"
        ip_to_test = proxy_dict['http'].split('@')[1] if '@' in proxy_dict['http'] else 'Unknown IP'
        print(f"[HTTP连通性测试] 正在使用代理 {ip_to_test} 访问 {test_url}...")
        try:
            # verify=False 用于忽略HTTPS证书验证, 某些代理需要
            response = requests.get(test_url, proxies=proxy_dict, timeout=timeout, verify=False)
            response.raise_for_status()
            # 简单检查响应内容
            if "origin" in response.json():
                print(f"[HTTP连通性测试] 代理 {ip_to_test} 可用。")
                return True
            else:
                print(f"[HTTP连通性测试] 代理 {ip_to_test} 返回了无效的响应: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"[HTTP连通性测试] 代理 {ip_to_test} 测试失败: {e}")
            return False

    def get_proxy(self, force_new=True, max_retries=3) -> Optional[Dict[str, str]]:
        """
        获取一个【经过HTTP连通性测试】的可用的代理字典。

        参数:
            force_new (bool): 是否强制获取新代理，默认为True。
            max_retries (int): 在返回失败前，最多尝试获取和测试多少个不同的代理。

        如果当前没有代理或当前代理已过期或force_new为True，它会自动获取一个新代理。
        获取后会进行HTTP连通性测试，如果失败则自动更换，直到找到可用的代理或达到最大尝试次数。

        返回:
            一个类似 {'http': '*******************:port', 'https': '...'} 的字典，或者 None。
        """
        for attempt in range(max_retries):
            print(f"\n--- 获取并测试代理 (第 {attempt + 1}/{max_retries} 次尝试) ---")
            is_expired = False
            if self.current_proxy_data:
                # 检查当前代理是否已经过期
                if datetime.now() >= self.current_proxy_data["expire_time"]:
                    print("[提示] 当前代理已过期。")
                    is_expired = True
            
            # 如果没有代理或代理已过期或强制获取新代理
            if not self.current_proxy_data or is_expired or force_new:
                if force_new and self.current_proxy_data and not is_expired:
                    print("[提示] 强制获取新的代理IP，不重用未过期的代理。")
                
                if not self._fetch_new_proxy():
                    print("获取新代理失败，稍后重试...")
                    time.sleep(2)
                    continue  # 获取新代理失败，进入下一次循环

            # 确保current_proxy_data不为None
            if not self.current_proxy_data:
                continue
                
            # 格式化代理为requests库所需的格式
            ip = self.current_proxy_data['ip']
            port = self.current_proxy_data['port']

            # 根据您的示例URL, 代理似乎需要权限验证, 格式为 protocol://user:pass@host:port
            # accessName 和 accessPassword 就是这里的 user 和 pass
            user = self.api_params['accessName']
            pwd = self.api_params['accessPassword']
            
            proxy_url = f"{self.protocol}://{user}:{pwd}@{ip}:{port}"
            
            proxy_dict = {
                'http': proxy_url,
                'https': proxy_url  # HTTPS请求也通过同一个代理
            }

            # 对格式化好的代理进行HTTP连通性测试
            if self._test_http_connectivity(proxy_dict):
                print(f"✅ 代理 {ip}:{port} 测试通过，准备使用。")
                return proxy_dict # 测试通过，返回可用的代理
            else:
                print(f"❌ 代理 {ip}:{port} HTTP连通性测试失败，将释放并更换代理。")
                self.release_proxy() # 测试失败，释放代理
                time.sleep(1) # 短暂休眠后重试

        print(f"❌ 经过 {max_retries} 次尝试后，仍未能获取到可用的代理IP。")
        return None

    def release_proxy(self):
        """
        释放当前代理IP的引用。
        在代理无效时调用此方法可以立即标记当前代理为无效，
        这样下次调用get_proxy时会获取新的代理。
        
        返回:
            bool: 是否成功释放代理
        """
        if self.current_proxy_data:
            ip_port = f"{self.current_proxy_data['ip']}:{self.current_proxy_data['port']}"
            print(f"[提示] 释放代理IP: {ip_port}")
            self.current_proxy_data = None
            return True
        return False

# ==============================================================================
# -------------------- 使用示例 (当直接运行此文件时) ---------------------------
# ==============================================================================
if __name__ == '__main__':
    print("--- 正在测试 ProxyManager 模块 ---")

    # --- 1. 请在这里填入您的51代理API凭证 ---
    # 注意：这些是测试信息，使用前请替换成您自己的真实信息
    MY_PACK_ID = "2"                  # 您的套餐ID
    MY_UID = "42477"                  # 您的UID
    MY_ACCESS_NAME = "ab263263"        # 您的 accessName
    MY_ACCESS_PASSWORD = "8C84B9BFE1774BFB2443DD34797EE4FB" # 您的 accessPassword

    # --- 2. 创建代理管理器实例 ---
    proxy_client = ProxyManager(
        packid=MY_PACK_ID,
        uid=MY_UID,
        access_name=MY_ACCESS_NAME,
        access_password=MY_ACCESS_PASSWORD,
        protocol='http',  # 'http' 或 'socks5'
        time_duration=3   # 代理时长3分钟
    )

    # --- 3. 获取代理 ---
    # 第一次调用会从API获取新IP
    print("\n[第一次获取代理]")
    current_proxies = proxy_client.get_proxy(force_new=True)

    if current_proxies:
        print(f"获取到的代理字典: {current_proxies}")

        # --- 4. 使用获取到的代理访问一个测试网站 ---
        test_url = "https://httpbin.org/ip"
        print(f"\n正在使用代理访问: {test_url}")
        try:
            # `verify=False` 用于忽略HTTPS证书验证，某些代理需要
            # `timeout=10` 设置10秒超时
            response = requests.get(test_url, proxies=current_proxies, verify=False, timeout=10)
            print("--- 访问成功! ---")
            print(f"状态码: {response.status_code}")
            print(f"对方服务器看到的IP是: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"--- 使用代理访问失败! ---")
            print(f"错误: {e}")

        # --- 5. 再次获取代理 ---
        # 这次调用会强制获取新IP，不再重用之前的代理
        print("\n[第二次获取代理 (强制获取新IP)]")
        new_proxies = proxy_client.get_proxy(force_new=True)
        print(f"再次获取到的代理字典: {new_proxies}")
        
        # 比较两次获取的代理是否不同
        if new_proxies != current_proxies:
            print("[成功] 成功验证：第二次获取到了不同的代理IP")
        else:
            print("[警告] 警告：两次获取到了相同的代理IP，可能是API限制或配额不足")
        
    else:
        print("\n未能成功获取代理IP，请检查您的API配置或网络。")
        print("\n可能的原因:")
        print("1. API凭证错误 (packid, uid, accessName, accessPassword)")
        print("2. 套餐余额不足")
        print("3. API地址变更")
        print("4. 网络连接问题")
        print("\n解决方案:")
        print("1. 检查51代理后台，确认API参数是否正确")
        print("2. 联系51代理客服，确认API调用方式")
        print("3. 尝试使用不同的网络环境测试") 