import requests
import time
import json
import random

class Proxy91Config:
    """
    91代理的配置类。
    请在此处填写您从91代理官方获取的API参数。
    """
    # 您的订单号 (trade_no)
    TRADE_NO = "B228657142146"
    # 您的密钥 (secret)
    SECRET = "HBAOD3XOGsDQX21V"
    
    # --- 其他API参数 (可根据需求修改) ---
    # 每次获取的IP数量
    NUM = 1
    # 协议: 1=HTTP/HTTPS, 2=SOCKS5
    PROTOCOL = 1
    # 返回格式: 'json' 或 'text'
    FORMAT = "json"
    # IP和端口的分隔符: 1=冒号(:), 2=下划线(_)
    SEP = 1
    # 代理时长 (分钟), 根据API文档，对于某些套餐此值固定为 1
    TIME = 1
    # 是否过滤已使用过的: 1=是, 0=否
    FILTER = 1


class Proxy91Manager:
    """
    用于对接91代理的管理器。
    负责获取、管理和测试从91代理API获取的IP。
    """
    def __init__(self, config: Proxy91Config):
        self.config = config
        self.api_url = "http://api.91http.com/v1/get-ip"
        self.current_proxy = None
        self.proxy_info = {}
        self.last_fetch_time = 0
        self.max_retries = 3
        print("✅ 91代理管理器已初始化。")

    def pre_check(self):
        """
        预检查91代理服务是否可用
        尝试获取一个代理并测试连接，以确认配置和服务状态
        """
        print("ℹ️ [91代理] 正在进行预检查...")
        try:
            proxy = self.get_proxy(force_new=True)
            if proxy:
                print("✅ [91代理] 预检查通过！成功获取并测试了代理。")
                return True
            else:
                print("❌ [91代理] 预检查失败：无法获取代理。")
                return False
        except Exception as e:
            print(f"❌ [91代理] 预检查过程中发生异常: {e}")
            return False

    def get_proxy(self, force_new=False):
        """
        获取一个代理IP。
        如果已有未过期的IP且不强制刷新，则返回当前IP。
        否则，调用API获取新IP。
        """
        if self.current_proxy and not force_new:
            current_time = time.time()
            if current_time - self.last_fetch_time < 600:
                print("ℹ️ [91代理] 返回已缓存的代理。")
                return self.current_proxy
            else:
                print("ℹ️ [91代理] 缓存的代理已超过10分钟，将获取新代理。")
        
        print("ℹ️ [91代理] 正在从API获取新代理IP...")
        
        for retry in range(self.max_retries):
            try:
                if retry > 0:
                    print(f"ℹ️ [91代理] 第 {retry+1}/{self.max_retries} 次重试获取代理...")
                    time.sleep(random.uniform(1, 3))
                
                proxy = self._fetch_proxy_from_api()
                if proxy:
                    self.last_fetch_time = time.time()
                    return proxy
            except Exception as e:
                print(f"❌ [91代理] 获取代理时发生异常 (尝试 {retry+1}/{self.max_retries}): {e}")
                if retry == self.max_retries - 1:
                    print("❌ [91代理] 达到最大重试次数，无法获取代理。")
                    return None
        
        return None

    def _fetch_proxy_from_api(self):
        """从API获取代理IP"""
        params = {
            "trade_no": self.config.TRADE_NO,
            "secret": self.config.SECRET,
            "num": self.config.NUM,
            "protocol": self.config.PROTOCOL,
            "format": self.config.FORMAT,
            "sep": self.config.SEP,
            "time": self.config.TIME,
            "filter": self.config.FILTER
        }
        
        try:
            response = requests.get(self.api_url, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0:
                response_data = data.get("data")
                if not response_data or not isinstance(response_data, dict):
                    print(f"❌ [91代理] 获取代理失败: API返回的 'data' 字段为空或格式不正确。")
                    return None

                proxy_list = response_data.get("proxy_list")
                if not proxy_list or not isinstance(proxy_list, list) or len(proxy_list) == 0:
                    print(f"❌ [91代理] 获取代理失败: API返回的 'proxy_list' 字段为空或格式不正确。")
                    return None
                
                first_proxy_item = proxy_list[0]
                ip = first_proxy_item.get("ip")
                port = first_proxy_item.get("port")

                if ip and port:
                    ip_port = f"{ip}:{port}"
                    proxy_url = f"http://{ip_port}"
                    self.current_proxy = {'http': proxy_url, 'https': proxy_url}
                    self.proxy_info = first_proxy_item
                    
                    expire_time = self.proxy_info.get('expire_time', '未知')
                    print(f"✅ [91代理] 成功获取新代理: {ip_port} (过期时间: {expire_time})")
                    
                    if self._test_proxy(self.current_proxy):
                        return self.current_proxy
                    else:
                        print("❌ [91代理] 获取的代理无法正常使用，尝试重新获取。")
                        self.current_proxy = None
                        return None
                else:
                    print(f"❌ [91代理] 获取代理失败: 未能从API响应中解析出IP和端口。")
                    return None
            else:
                error_msg = data.get('msg', '未知API错误')
                print(f"❌ [91代理] 获取代理失败: {error_msg}")
                if data.get("code") == 2009:
                    print("    -> 解决方案: 请将您当前机器的公网IP地址添加到91代理后台的API使用白名单中。")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ [91代理] 网络错误: 请求API时失败: {e}")
            return None
        except (json.JSONDecodeError, Exception) as e:
            print(f"❌ [91代理] 解析代理时发生未知错误: {e}")
            return None

    def _test_proxy(self, proxy_dict, timeout=10):
        """测试代理是否可用，优先测试目标网站"""
        if not proxy_dict:
            return False
            
        target_test_url = "https://m.cnagriculturer.icu/" 
        
        try:
            print(f"ℹ️ [91代理] 正在对目标网站进行连接测试 ({target_test_url})...")
            start_time = time.time()
            response = requests.head(target_test_url, proxies=proxy_dict, timeout=timeout, headers={"User-Agent": "Mozilla/5.0"})
            elapsed = time.time() - start_time
            
            if response.status_code < 500:
                print(f"✅ [91代理] 代理连接目标网站测试成功！响应时间: {elapsed:.2f}秒")
                return True
            else:
                print(f"⚠️ [91代理] 代理连接目标网站测试返回服务器错误: {response.status_code}")
        except Exception as e:
            print(f"⚠️ [91代理] 代理连接目标网站测试失败: {e}")
                
        print("❌ [91代理] 此代理无法访问目标网站，将被丢弃。")
        return False

    def release_proxy(self):
        """
        释放(清除)当前持有的代理。
        """
        self.current_proxy = None
        self.proxy_info = {}
        print("ℹ️ [91代理] 已清除当前代理。下次将获取新代理。")

if __name__ == '__main__':
    """
    当此文件被直接运行时，将执行以下测试代码。
    """
    print("="*60)
    print("            ▶▶▶ 91代理 (91HTTP) 模块测试工具 ◀◀◀")
    print("="*60 + "\n")
    
    config = Proxy91Config()
    manager = Proxy91Manager(config)
    
    proxy = manager.get_proxy(force_new=True)
    
    if proxy:
        print(f"\n--- 成功获取代理: {proxy.get('http')} ---")
    else:
        print("\n未能成功获取代理IP，请检查您的API配置或网络。")

    print("\n--- 测试完毕 ---")
