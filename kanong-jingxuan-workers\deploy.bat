@echo off
chcp 65001 >nul
echo.
echo 🚀 卡农精选网站 - 一键部署脚本
echo ====================================
echo 📍 目标平台: Cloudflare Workers
echo 💰 费用: 完全免费
echo ⚡ 性能: 全球边缘计算
echo 🤖 功能: AI智能分析
echo ====================================
echo.

echo 🔍 检查环境...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装
    echo 💡 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

echo.
echo 📦 安装依赖...
call npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功

echo.
echo 🔐 登录 Cloudflare...
echo 💡 即将打开浏览器进行登录，请在浏览器中完成授权
pause

call npx wrangler login

if %errorlevel% neq 0 (
    echo ❌ 登录失败
    pause
    exit /b 1
)

echo ✅ 登录成功

echo.
echo 🚀 开始部署...
call npx wrangler deploy

if %errorlevel% neq 0 (
    echo ❌ 部署失败
    pause
    exit /b 1
)

echo.
echo 🎉 部署成功！
echo ====================================
echo 🌐 您的网站已成功部署到 Cloudflare Workers
echo 📱 可以通过浏览器访问您的网站
echo ⚡ 享受全球边缘计算加速
echo 💰 完全免费使用
echo ====================================
echo.

echo 📋 获取访问地址...
call npx wrangler whoami

echo.
echo 🎊 恭喜！卡农精选网站部署完成！
echo.
echo 📚 接下来您可以:
echo   1. 🌐 访问网站查看效果
echo   2. 🔧 在 Cloudflare 控制台管理
echo   3. 🎨 自定义域名绑定
echo   4. 📊 查看使用统计
echo.
echo 🎉 享受您的专属卡农精选网站！
echo.
pause 