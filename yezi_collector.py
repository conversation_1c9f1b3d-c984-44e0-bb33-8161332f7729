#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
椰子云手机号收集器 - 专门从椰子云获取手机号并保存
"""

import requests
import json
import time
import random
import os
import sys
from datetime import datetime

class YeziCollector:
    def __init__(self):
        # 椰子云API配置
        self.base_url = "http://api.sqhyw.net:90"
        self.username = ""
        self.password = ""
        self.project_id = ""
        self.token = None
        
        # 保存配置
        self.save_file = "scan_results/yezi.txt"
        self.save_interval = 100
        self.auto_release = True
        
        # 确保目录存在
        os.makedirs("scan_results", exist_ok=True)
        
        # 统计信息
        self.collected_phones = []
        self.error_count = 0
    
    def login(self):
        """登录并获取token"""
        print("正在登录椰子云平台...")
        url = f"{self.base_url}/api/logins"
        params = {
            "username": self.username,
            "password": self.password
        }
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if "token" in data and data["token"]:
                self.token = data["token"]
                money = "未知"
                if "data" in data and isinstance(data["data"], list) and len(data["data"]) > 0:
                    money = data["data"][0].get("money", "未知")
                print(f"✅ 登录成功！账户余额: {money}")
                return True
            else:
                error_msg = data.get('message', '未知错误')
                print(f"❌ 登录失败: {error_msg}")
                print(f"   完整服务器响应: {json.dumps(data, ensure_ascii=False)}")
                return False
        except Exception as e:
            print(f"❌ 登录时发生网络错误: {e}")
            return False
    
    def get_phone_number(self):
        """
        获取一个手机号码
        返回: (bool, result) -> (是否成功, (手机号, 归属地) 或 错误信息)
        """
        if not self.token:
            return False, "错误：未登录，请先调用login()"

        print(f"正在为项目ID「{self.project_id}」获取手机号码...")
        url = f"{self.base_url}/api/get_mobile"
        params = {
            "token": self.token,
            "project_id": self.project_id,
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()

            # 检查速率限制
            remaining = int(data.get("1分钟内剩余取卡数", 999))
            if remaining < 10:
                error_msg = f"警告：剩余取卡数过低 ({remaining})，为避免封号，暂停获取。"
                print(f"❌ {error_msg}")
                return False, error_msg
            
            if data.get("message") == "ok" and "mobile" in data:
                phone_number = data["mobile"]
                # 提取归属地信息
                location_data = data.get("data", [{}])
                location = location_data[0].get("isp", "未知归属地") if location_data else "未知归属地"
                print(f"✅ 成功获取手机号: {phone_number} ({location})")
                return True, (phone_number, location)
            else:
                error_msg = data.get('message', '返回信息错误')
                print(f"❌ 获取手机号失败: {error_msg}")
                print(f"   完整服务器响应: {json.dumps(data, ensure_ascii=False)}")
                return False, error_msg
        except Exception as e:
            error_msg = f"获取手机号时发生网络错误: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg
    
    def release_phone_number(self, phone_number):
        """释放手机号"""
        if not self.token:
            return
            
        print(f"正在释放手机号: {phone_number}...")
        url = f"{self.base_url}/api/free_mobile"
        params = {"token": self.token, "phone_num": phone_number}
        try:
            response = requests.get(url, params=params, timeout=15)
            if response.json().get("message") == "ok":
                print(f"✅ 号码 {phone_number} 已成功释放。")
                return True
            return False
        except Exception as e:
            print(f"释放号码时出错: {e}")
            return False
    
    def save_phones(self, append=True):
        """保存手机号到文件"""
        if not self.collected_phones:
            print("没有手机号需要保存")
            return
        
        mode = 'a' if append else 'w'
        try:
            with open(self.save_file, mode, encoding='utf-8') as f:
                for phone in self.collected_phones:
                    f.write(f"{phone}\n")
            
            print(f"✅ 已保存 {len(self.collected_phones)} 个手机号到 {self.save_file}")
            
            # 清空已保存的列表
            self.collected_phones = []
            
        except Exception as e:
            print(f"❌ 保存手机号失败: {e}")
    
    def collect_phones(self, count, delay_range=(1, 3)):
        """
        批量收集手机号
        
        Args:
            count (int): 要收集的手机号数量
            delay_range (tuple): 请求间隔时间范围(秒)
        """
        if not self.login():
            print("登录失败，无法继续")
            return
        
        print(f"🚀 开始收集 {count} 个手机号...")
        print(f"⏱️  延迟范围: {delay_range[0]}-{delay_range[1]}秒")
        print(f"💾 保存间隔: 每{self.save_interval}个")
        if self.auto_release:
            print("✅ 自动释放: 已启用")
        print("=" * 60)
        
        start_time = time.time()
        collected = 0
        
        try:
            while collected < count:
                # 获取手机号
                success, result = self.get_phone_number()
                
                if success:
                    phone_number, location = result
                    self.collected_phones.append(phone_number)
                    collected += 1
                    
                    # 自动释放
                    if self.auto_release:
                        self.release_phone_number(phone_number)
                    
                    # 计算速度和预计时间
                    elapsed = time.time() - start_time
                    speed = collected / elapsed * 60 if elapsed > 0 else 0
                    eta = (count - collected) / (speed / 60) if speed > 0 else 0
                    
                    status = "已释放" if self.auto_release else "已获取"
                    print(f"[{collected}/{count}] {status}: {phone_number} ({location}) | 速度: {speed:.1f}/分钟 | 剩余: {eta/60:.1f}分钟")
                    
                    # 定期保存
                    if len(self.collected_phones) >= self.save_interval:
                        self.save_phones()
                else:
                    self.error_count += 1
                    error_msg = result
                    print(f"[{collected}/{count}] 获取失败: {error_msg}")
                    
                    # 如果是余额不足或其他严重错误，可以选择退出
                    if "余额不足" in str(error_msg) or "账户异常" in str(error_msg):
                        print("检测到严重错误，停止获取")
                        break
                
                # 添加延迟
                if collected < count:
                    time.sleep(random.uniform(delay_range[0], delay_range[1]))
        
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断，正在保存已获取的手机号...")
        except Exception as e:
            print(f"\n❌ 发生异常: {e}")
        finally:
            # 保存剩余的手机号
            if self.collected_phones:
                self.save_phones()
            
            # 显示统计信息
            total_time = time.time() - start_time
            print("\n" + "=" * 60)
            print("📊 收集统计:")
            print(f"✅ 成功获取: {collected}")
            print(f"❌ 失败次数: {self.error_count}")
            print(f"⏱️  总耗时: {total_time/60:.1f}分钟")
            if collected > 0:
                print(f"📈 平均速度: {collected/total_time*60:.1f}个/分钟")
            print(f"💾 保存位置: {self.save_file}")
            print("=" * 60)

def main():
    print("🌴 椰子云手机号收集器")
    print("=" * 50)
    
    collector = YeziCollector()
    
    # 设置椰子云账号
    collector.username = input("椰子云用户名: ").strip()
    collector.password = input("椰子云密码: ").strip()
    collector.project_id = input("项目ID: ").strip()
    
    if not all([collector.username, collector.password, collector.project_id]):
        print("❌ 账号信息不完整")
        return
    
    # 设置保存文件
    save_file = input("保存文件路径 (默认scan_results/yezi.txt): ").strip()
    if save_file:
        collector.save_file = save_file
    
    # 设置保存间隔
    save_interval = input("保存间隔 (默认100): ").strip()
    if save_interval and save_interval.isdigit():
        collector.save_interval = int(save_interval)
    
    # 设置自动释放
    auto_release = input("是否自动释放手机号 (y/n, 默认y): ").strip().lower()
    collector.auto_release = auto_release != 'n'
    
    # 设置收集数量
    count = int(input("要收集的手机号数量: ").strip() or "100")
    
    # 设置延迟
    delay_input = input("请求延迟范围 (格式: 最小,最大 默认0.1,0.3): ").strip()
    if delay_input:
        try:
            # 支持中文逗号和小数点
            delay_input = delay_input.replace('，', ',').replace('。', '.')
            parts = delay_input.split(',')
            if len(parts) == 2:
                min_delay = float(parts[0].strip())
                max_delay = float(parts[1].strip())
                delay_range = (min_delay, max_delay)
            else:
                print("延迟格式错误，使用默认值")
                delay_range = (0.1, 0.3)
        except:
            print("延迟格式错误，使用默认值")
            delay_range = (0.1, 0.3)
    else:
        delay_range = (0.1, 0.3)
    
    # 开始收集
    collector.collect_phones(count, delay_range)

if __name__ == "__main__":
    main()
