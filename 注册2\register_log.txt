2025-07-10 17:55:30,216 - <PERSON>FO - OCR初始化成功
2025-07-10 17:55:30,217 - INFO - 开始注册流程...
2025-07-10 17:55:30,217 - INFO - 随机生成手机号: 15036451747, 密码: EKdQwwp9
2025-07-10 17:55:30,217 - INFO - 获取验证码...
2025-07-10 17:55:30,218 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 17:55:31,100 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214132943784"},"count":0}
2025-07-10 17:55:31,106 - ERROR - 发生异常: 'dict' object has no attribute 'startswith'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 385, in main
    auth_code = registrator.get_auth_code()
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 142, in get_auth_code
    if image_data_str.startswith("data:image/jpeg;base64,"):
       ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'startswith'
2025-07-10 17:56:00,743 - INFO - OCR初始化成功
2025-07-10 17:56:00,744 - INFO - 开始注册流程...
2025-07-10 17:56:00,744 - INFO - 随机生成手机号: 18887974706, 密码: xjelReUV
2025-07-10 17:56:00,744 - INFO - 获取验证码...
2025-07-10 17:56:00,746 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 17:56:01,719 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214136083747"},"count":0}
2025-07-10 17:56:01,721 - ERROR - 发生异常: 'dict' object has no attribute 'startswith'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 385, in main
    auth_code = registrator.get_auth_code()
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 142, in get_auth_code
    if image_data_str.startswith("data:image/jpeg;base64,"):
       ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'startswith'
2025-07-10 17:58:12,248 - INFO - OCR初始化成功
2025-07-10 17:58:12,248 - INFO - 开始注册流程...
2025-07-10 17:58:12,249 - INFO - 随机生成手机号: 15257372248, 密码: SktgFjKC
2025-07-10 17:58:12,249 - INFO - 获取验证码...
2025-07-10 17:58:12,250 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 17:58:13,475 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214149121569"},"count":0}
2025-07-10 17:58:13,477 - INFO - 获取到验证码key: authccode175214149121569
2025-07-10 17:58:13,477 - INFO - 获取验证码成功
2025-07-10 17:58:13,486 - INFO - 验证码图片已保存: 注册2/captcha.jpg
2025-07-10 17:58:13,518 - INFO - OCR识别结果: 7522
2025-07-10 18:00:00,149 - INFO - 正在注册账号: 15257372248
2025-07-10 18:00:00,150 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:00:00,150 - INFO - 注册数据: {"mobile": "15257372248", "password": "SktgFjKC", "code": "7522", "key": "authccode175214149121569"}
2025-07-10 18:00:00,850 - ERROR - 解密失败: string argument should contain only ASCII characters
2025-07-10 18:00:00,850 - ERROR - 注册请求失败，无响应
2025-07-10 18:03:52,473 - INFO - OCR初始化成功
2025-07-10 18:03:52,474 - INFO - 开始注册流程...
2025-07-10 18:03:52,474 - INFO - 随机生成手机号: 13436359630, 密码: Wb8Ia7SV
2025-07-10 18:03:52,474 - INFO - 使用邀请码: 555555
2025-07-10 18:03:52,474 - INFO - 获取验证码...
2025-07-10 18:03:52,477 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:03:52,955 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214183288422"},"count":0}
2025-07-10 18:03:52,957 - INFO - 获取到验证码key: authccode175214183288422
2025-07-10 18:03:52,957 - INFO - 获取验证码成功
2025-07-10 18:03:52,962 - INFO - 验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:03:52,978 - INFO - OCR识别结果: 7337
2025-07-10 18:03:52,978 - INFO - 正在注册账号: 13436359630
2025-07-10 18:03:52,978 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:03:52,978 - INFO - 注册数据: {"mobile": "13436359630", "password": "Wb8Ia7SV", "code": "7337", "key": "authccode175214183288422", "inviteCode": "555555"}
2025-07-10 18:03:53,686 - INFO - 注册响应原始数据: <!DOCTYPE html><html lang="zh-cmn-Hans" class="font-n"><head><meta charSet="utf-8"/><meta name="view
2025-07-10 18:03:53,687 - ERROR - Base64解码失败，尝试直接处理响应
2025-07-10 18:03:53,687 - ERROR - JSON解析失败: <!DOCTYPE html><html lang="zh-cmn-Hans" class="font-n"><head><meta charSet="utf-8"/><meta name="view
2025-07-10 18:03:53,687 - ERROR - 注册失败: {'code': 500, 'message': '响应解析失败'}
2025-07-10 18:08:06,796 - INFO - OCR初始化成功
2025-07-10 18:08:06,815 - INFO - 开始注册流程...
2025-07-10 18:08:06,815 - INFO - 随机生成手机号: 13874943873, 密码: QxYP1t3e
2025-07-10 18:08:06,816 - INFO - 使用邀请码: 555555
2025-07-10 18:08:06,816 - INFO - 获取验证码...
2025-07-10 18:08:06,817 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:08:07,279 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214208694251"},"count":0}
2025-07-10 18:08:07,282 - INFO - 获取到验证码key: authccode175214208694251
2025-07-10 18:08:07,282 - INFO - 获取验证码成功
2025-07-10 18:08:07,287 - INFO - 验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:08:07,304 - INFO - OCR识别结果: 5375
2025-07-10 18:08:07,304 - INFO - 正在注册账号: 13874943873
2025-07-10 18:08:07,304 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:08:07,304 - INFO - 注册数据: {"mobile": "13874943873", "password": "QxYP1t3e", "code": "5375", "key": "authccode175214208694251", "inviteCode": "555555"}
2025-07-10 18:08:07,986 - INFO - 注册响应状态码: 200
2025-07-10 18:08:07,987 - ERROR - 解密失败: Data must be aligned to block boundary in ECB mode
2025-07-10 18:08:07,987 - ERROR - 注册请求失败，无响应
2025-07-10 18:09:24,258 - INFO - OCR初始化成功
2025-07-10 18:09:24,259 - INFO - 开始注册流程...
2025-07-10 18:09:24,259 - INFO - 随机生成手机号: 13466852663, 密码: RZl1rXAd
2025-07-10 18:09:24,259 - INFO - 使用邀请码: 555555
2025-07-10 18:09:24,259 - INFO - 获取验证码...
2025-07-10 18:09:24,261 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:09:25,065 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,\/9j\/4AAQSkZJRgABAQEAYABgAAD\/\/gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2NjIpLCBxdWFsaXR5ID0gOTAK\/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU\/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU\/8AAEQgAHQBkAwEiAAIRAQMRAf\/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC\/\/EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29\/j5+v\/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC\/\/EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29\/j5+v\/aAAwDAQACEQMRAD8A\/OKiiiuI9sKKK7638VeEJPC2g6PP4RlvdQgSUXepxXRhnkkeVioQAMrKqFANwyTnpxVxipXu7Et26HA0V6X8TPh94c+GXi3StNm1G\/1MSWcd3qVisUcM9i7jctuX3OrMFwScDGRxmvc9e8D+F\/COo2jeGtFstNvIPALaskepNHOVvJ50jWSV5vkyodduQAOTxjnqjhZSck3axm6i0t1Plu48JanZaANXudPvIrN3CJO0DCLJ9WIxk9h1rFr1X4j+FviTJ4f0\/Vdf1GTXtEu5\/Kt5rLUUvLYTdNo8pigb0xXo3gD4UeGNJ+G3xLtNWtU1PxrpuhC+lctmPTiWyIlx1kAA3HsePWlHCynPlWml9fS45VYpXPmSiiiuM1CiiigAooooAKKKKACvXvgT4ZttNt9Y+IutQiTRvDahreOQfLc3rf6mP3wfmPsB6ivIavjX9SGhnRhf3A0kzfaTZeYfKMuMb9vTOOM1rSmqcuZq9vz6ESTkrI0Bqd34v8XS3t7vvtU1O63b3+bdLI\/Ujvyele1ftSaB4h8V\/HHXdO0PTL\/UI9H0myiuI7SJm2RJBGWLgdt5P414d4W8X6z4J1Mahod\/Jp16BtE0QGQOvcGtvV\/jP4613UFv73xXqst4qsizrcsjBWxkZXH91fyFbQqU1TlGd7tp\/df\/ADJkpNq2yPa\/h94+f4G\/s6z\/ANp2lvda3quspeaZpOoK2UiRCrTFQQy8n5TxzyOlXf2eta8P+J9A+MMh0TUNLefwrd3F7cRXv2gSKvLeWrqMMMk8uc+1fLmoald6tdPdX11Pe3LnLTXEhkdvqScmus8FfGTxh8PLG4sdC1mS0sLhWSe0aNJIpVb7ysGB4OTke9dNPGKM4qXwxVtkzKVG6dt2crqgsl1Ccac07WW790bkASFf9oLkA\/SqtWNRvW1K\/uLt44onmcyFIU2IpJzhR2HtVevMe+h0oKKKKQwooooA\/9k=","key":"authccode175214216455537"},"count":0}
2025-07-10 18:09:25,066 - INFO - 获取到验证码key: authccode175214216455537
2025-07-10 18:09:25,066 - INFO - 获取验证码成功
2025-07-10 18:09:25,072 - INFO - 验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:09:25,089 - INFO - OCR识别结果: 7154
2025-07-10 18:09:25,089 - INFO - 正在注册账号: 13466852663
2025-07-10 18:09:25,089 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:09:25,089 - INFO - 注册数据: {"mobile": "13466852663", "password": "RZl1rXAd", "code": "7154", "key": "authccode175214216455537", "inviteCode": "555555"}
2025-07-10 18:09:26,084 - INFO - 注册响应状态码: 200
2025-07-10 18:09:26,084 - ERROR - Base64解码失败，尝试直接处理响应
2025-07-10 18:09:26,085 - ERROR - JSON解析失败: 

<!doctype html><html><head><script src="//g.alicdn.com/mtb/wpk/2.0.3/wpk.js"></script><script>try{
2025-07-10 18:09:26,085 - ERROR - 注册失败: {'code': 500, 'message': '响应解析失败'}
2025-07-10 18:12:24,748 - INFO - OCR初始化成功
2025-07-10 18:12:24,749 - INFO - 开始注册流程...
2025-07-10 18:12:24,749 - INFO - 随机生成手机号: 13597496953, 密码: naap2DFM
2025-07-10 18:12:24,749 - INFO - 使用邀请码: 555555
2025-07-10 18:12:24,750 - INFO - 获取验证码...
2025-07-10 18:12:24,751 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:12:25,459 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214234498479"},"count":0}
2025-07-10 18:12:25,462 - INFO - 获取到验证码key: authccode175214234498479
2025-07-10 18:12:25,462 - INFO - 获取验证码成功
2025-07-10 18:12:25,467 - INFO - 验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:12:25,484 - INFO - OCR识别结果: 2236
2025-07-10 18:12:25,484 - INFO - 正在注册账号: 13597496953
2025-07-10 18:12:25,484 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:12:25,485 - INFO - 注册数据: {"mobile": "13597496953", "password": "naap2DFM", "code": "2236", "key": "authccode175214234498479", "inviteCode": "555555"}
2025-07-10 18:12:25,815 - INFO - 注册响应状态码: 200
2025-07-10 18:12:25,815 - ERROR - 解密失败: Data must be aligned to block boundary in ECB mode
2025-07-10 18:12:25,815 - ERROR - 解密响应失败或响应为空
2025-07-10 18:12:25,816 - ERROR - 注册失败: {'code': 500, 'message': '解密失败'}
2025-07-10 18:19:34,779 - INFO - ==================================================
2025-07-10 18:19:34,779 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:19:34,779 - INFO - ==================================================
2025-07-10 18:19:34,779 - INFO - 使用随机User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Mobile/15E148 Safari/604.1
2025-07-10 18:19:34,835 - INFO - OCR初始化成功
2025-07-10 18:19:34,836 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:19:34,836 - INFO - [任务 1] 随机生成手机号: 15192403084, 密码: ZNM079Ew
2025-07-10 18:19:34,836 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:19:34,837 - INFO - [任务 1] 获取验证码...
2025-07-10 18:19:34,838 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:19:36,735 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,\/9j\/4AAQSkZJRgABAQEAYABgAAD\/\/gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2NjIpLCBxdWFsaXR5ID0gOTAK\/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU\/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU\/8AAEQgAHQBkAwEiAAIRAQMRAf\/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC\/\/EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29\/j5+v\/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC\/\/EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29\/j5+v\/aAAwDAQACEQMRAD8AipPF\/iXWPh18Bfin468Pvawa3oNhYCyuruxgvFgkm1O1iLCOdHQkxmVclTjcSMHmlrr9I+HifGj4K\/FP4eXOsR+FdM1i0sry98TXdsJbXTIbO5Fy7zMZI1RSsbfMW\/hJ7cAmd18X\/AXhPwFeX8fxp+Ol54svZ9IiN1oHjLxiuh6ReAMuy4g0TTUV5U3ROmxyQxD5DkBhy+o+GZfGl5ceLRrdj4s0nWWkubbV\/Cl7YSw3dwXTfCpurq1jR18wkxu4cbCApwSN7w58P\/hL4x8a6vbat4H8O+LvCet2EviPxB8QPF0SS6lPbXNkJ\/tzXm1BYp5bRlUhWJIuSoQ5I+ev2BPhl4u8YfBLWY4I57bw94m8S6Ymi211I6xtNbWVxHqV6kZ4KbmhVpVzuaLYeUAoGemap4d0TRZLSHXtY1L4czzXs+nFviPoVxo9n58au4CajH59hIHSKRkIufm2\/LnIrDsj4aur+O3l+JXw4s4mfabubxtpZjQf3iEnZsfRSfao\/EvxEs9e0vV\/jXY6NqWu\/AH4IX1nH4X0M6mNPj8U+ITeRRSarI6pI+yIzhkDDBGxV2hriKvU\/Cfxx8e\/Hj4XeH\/EOk6jc+HvEXjrwfrS2Om2WrSJax6wovbWAwvIw8rdLHEyqWOxmABOAaAPLrPxv8PdS0jUoPBNj4z+PHiix80Xdn8OtKmTT9PKk7BPeTwHIlVJTGYo33GJgM5UtL4Y8V\/D\/wCLWj+KJPBzeIfD3ibwk0aa74R8ZRQw6hBl\/KkkiCNl0jmIjcFFZCy7gu5QeN+AnxU8b\/ssWPiz4SfEL4SeMvGWtQai\/jrV7jwprkeoXUU0trEqR34tjL5cbyQea0jyhwro+wjG4\/ZF8Q+A\/jJ4o+LOu2er3118dvHFq13Jpl9Ypp9g0LSx3l5aaaEnnNy8bxRn96UkMaghMrKaAPQtE8BeJvE1obvSPD2qanahihns7OSVNw6jcoIzyOPeoda8H694a2f2vomo6WH+79ttJId303AZqaz+Hth8UZPBttHrPjjwz4k8N6pe3C3vgKQwX89jdW8cc8ayKjMjrJBCwJBUo0ikDcGXE\/aRs\/Gf7KPgPwR4\/wBK+PHxU8P+JtR1G8tR4d8d+Io9bWWFbKdvOW1eNVcCQQxnzI22tcRt8pCkgDaK7n4oR3U1l4D1PV9Mh0bxTq\/hXTtS13T4bY232e\/kjJlRoSAYmBxlDyDmuGoAKKKKACrV34f8JfETwNrvgTxwuqQaDq8kFyuo6NOUubK5g3+VJ5ZPlzJ+8dWRweGypDKpqrRQB0d3bT6\/oNr4X+Kfxz8Y\/GXwNa28ePDNtokGgC+kjAWNb6\/SZ7mePbuLKSSz7HLFk50\/GniPxB8WfhL4y8FeH\/EHhf4ealq9jb6Hp0d3ZS2+m2mlFm+02kBgDC3DrsBZo3BUFflO1k4migDq\/jiPE1\/+yt4\/+G9h8BNNsfh94e8NskHiLwF8QbO+sbc20kd75pt5kSYoZIS8rMhnk2sMsW8wUf2Sdb1xv2YF0Lw14A8aR+KvAHg3xXcaV4xfTQun3eptdTG2gsVAk+1ybpJv7u1oSNj7xt7P4VfDO\/8Ajh4P8aeBNI8XX\/ga81LSrm3udRtNOsLyK5tZxHFJBKk1uZ9u3djyriLBbOMjNdDY\/sreJ\/2YfgdqqT\/EKz8U2+m6hJq1jcw6Lcadf2t1dXECyGOVb54ljwCdnk9WJBBJyAfOP7Lf7Vvwn8Gfs0eFPD+pfFy4+HvjebVb\/U\/Fc6aTqN1dajNLcP5UzXEMbb28nyxgvyevK89nYeEPEf7Sn7bvhn4vaD4Y1n4f\/DrQptKmu\/HmvW\/9mXPiV7ZJgssUJEbOt2jpb7UDDykUsFLCId2n7TnxLjgaJfEpAYYZ\/sVvvbjGS3l5JwBznPFcRrfjfxB4kvYbvVdbv9QuYG3xSXNwzmI5z8uT8vPpQIv6ZqHwo+K3iPx14Yt\/Emm+P\/Osb271bwrYwarZMtpDOsr\/AOliFFjKNGnDNgnCkMGwfMfFngXRPgf4F8d\/HT4Ra94q8LfEPw+LTz\/+EgWy8QW0lpPPDassU9xbeZDNmRTvJYlEdBkOdn0b4N+CE\/7QfiO7+KPgrxLN8Hvi9ZD+z9T8S6RZx3tnrkEsYDC80+UiJ2AjUgggbsMVJSPZ1nxf\/Yl8dfHfw5beD9c+LGh+GPAwvIp9W0PwR4JGmf2tsZGBlke9mwwx8vBQMFYoxRcINj5y+HPxV8V\/Hb4I654i8erDq3ibw54wfw7H4qt7SG0\/tSAwNIUkiiULviKphgANkqDkgsYK6fxd4f0T4X2sHww8I6YNI8K+FbieCJWk824vrksBNeXMmBvlkKjsFRQEUKoArmKYwooooA\/\/2Q==","key":"authccode175214277496276"},"count":0}
2025-07-10 18:19:36,737 - INFO - 获取到验证码key: authccode175214277496276
2025-07-10 18:19:36,738 - INFO - 获取验证码成功
2025-07-10 18:19:36,743 - INFO - 原始验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:19:36,754 - INFO - 验证码图片预处理完成
2025-07-10 18:19:36,766 - INFO - OCR识别结果(处理后): 5483
2025-07-10 18:19:36,766 - INFO - [任务 1] 正在注册账号: 15192403084
2025-07-10 18:19:36,766 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:19:36,766 - INFO - 注册数据: {"mobile": "15192403084", "password": "ZNM079Ew", "code": "5483", "key": "authccode175214277496276", "inviteCode": "555555"}
2025-07-10 18:19:37,601 - INFO - 注册响应状态码: 200
2025-07-10 18:19:37,602 - ERROR - Base64解码失败: string argument should contain only ASCII characters，尝试直接处理响应
2025-07-10 18:19:37,602 - ERROR - 解密后数据不是有效JSON: <!doctype html><html lang="en" class=""><head><meta charset="utf-8"><title>【快手短视频App】快手，拥抱每一种生活</tit
2025-07-10 18:19:37,603 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '解密后响应不是JSON格式'}
2025-07-10 18:19:37,603 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:19:37,605 - INFO - ==================================================
2025-07-10 18:19:37,605 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:19:37,605 - INFO - ==================================================
2025-07-10 18:22:41,824 - INFO - ==================================================
2025-07-10 18:22:41,825 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:22:41,825 - INFO - ==================================================
2025-07-10 18:22:41,825 - INFO - 使用随机User-Agent: Mozilla/5.0 (Linux; Android 11; XQ-AT52) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
2025-07-10 18:22:41,876 - INFO - OCR初始化成功
2025-07-10 18:22:41,876 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:22:41,876 - INFO - [任务 1] 随机生成手机号: 13419380110, 密码: nRbar0qE
2025-07-10 18:22:41,877 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:22:41,877 - INFO - [任务 1] 获取验证码...
2025-07-10 18:22:41,879 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:22:42,936 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214296125172"},"count":0}
2025-07-10 18:22:42,938 - INFO - 获取到验证码key: authccode175214296125172
2025-07-10 18:22:42,938 - INFO - 获取验证码成功
2025-07-10 18:22:42,943 - INFO - 原始验证码图片已保存: 注册2/captcha.jpg
2025-07-10 18:22:42,952 - INFO - 验证码图片预处理完成
2025-07-10 18:22:42,966 - INFO - OCR识别结果(处理后): 4365
2025-07-10 18:22:42,966 - INFO - [任务 1] 正在注册账号: 13419380110
2025-07-10 18:22:42,966 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:22:42,966 - INFO - 注册数据: {"mobile": "13419380110", "password": "nRbar0qE", "code": "4365", "key": "authccode175214296125172", "inviteCode": "555555"}
2025-07-10 18:22:43,644 - INFO - 注册响应状态码: 200
2025-07-10 18:22:43,645 - INFO - 注册接口返回了HTML页面，而不是预期的JSON
2025-07-10 18:22:43,646 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '接口返回了HTML页面，可能是防护机制或接口变更', 'html': '<!DOCTYPE html><html lang="zh-cmn-Hans" class="font-n"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"/><meta name="format-detection" content="telephone=no"/><meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/><link rel="dns-prefetch" href="//mat1.gtimg.com"/><link rel="dns-prefetch" href="//pacaio.match.qq.com"/><link rel="dns-prefetch" href="//inews.gtimg.com"/><l'}
2025-07-10 18:22:43,646 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:22:43,648 - INFO - ==================================================
2025-07-10 18:22:43,648 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:22:43,649 - INFO - ==================================================
2025-07-10 18:24:22,240 - INFO - ==================================================
2025-07-10 18:24:22,240 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:24:22,240 - INFO - ==================================================
2025-07-10 18:24:22,240 - INFO - 使用随机User-Agent: Mozilla/5.0 (Linux; Android 12; M2102J20SG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
2025-07-10 18:24:22,286 - INFO - OCR初始化成功
2025-07-10 18:24:22,286 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:24:22,286 - INFO - [任务 1] 随机生成手机号: 15800996566, 密码: JthRdQLe
2025-07-10 18:24:22,287 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:24:22,287 - INFO - [任务 1] 获取验证码...
2025-07-10 18:24:22,289 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:24:23,846 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214306240978"},"count":0}
2025-07-10 18:24:23,847 - INFO - 获取到验证码key: authccode175214306240978
2025-07-10 18:24:23,847 - INFO - 获取验证码成功
2025-07-10 18:24:23,856 - INFO - 验证码图片预处理完成
2025-07-10 18:24:23,863 - INFO - OCR识别结果(处理后): 8293
2025-07-10 18:24:23,863 - INFO - [任务 1] 正在注册账号: 15800996566
2025-07-10 18:24:23,864 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:24:23,864 - INFO - 注册数据: {"mobile": "15800996566", "password": "JthRdQLe", "code": "8293", "key": "authccode175214306240978", "inviteCode": "555555"}
2025-07-10 18:24:24,933 - INFO - 注册响应状态码: 200
2025-07-10 18:24:24,934 - INFO - 注册接口返回了HTML页面，而不是预期的JSON
2025-07-10 18:24:24,934 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '接口返回了HTML页面，可能是防护机制或接口变更', 'html': '<!doctype html><html lang="en" class=""><head><meta charset="utf-8"><title>【快手短视频App】快手，拥抱每一种生活</title><meta name="referrer" content="always"><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover"><meta name="format-detection" content="telephone=no"><meta name="keywords" content="快手,快手官网,快手下载,kuaishou,快手直播伴侣,快手app,短视频app,快手直播"><meta name="description" , itemprop="description" , content="快手是一款国民级短视频App。在快手，了解真实的世界，认识有'}
2025-07-10 18:24:24,935 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:24:24,938 - INFO - ==================================================
2025-07-10 18:24:24,938 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:24:24,938 - INFO - ==================================================
2025-07-10 18:25:56,643 - INFO - ==================================================
2025-07-10 18:25:56,643 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:25:56,643 - INFO - ==================================================
2025-07-10 18:25:56,644 - INFO - 使用随机User-Agent: Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
2025-07-10 18:25:56,698 - INFO - OCR初始化成功
2025-07-10 18:25:56,698 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:25:56,698 - INFO - [任务 1] 随机生成手机号: 15245740134, 密码: Wh3lHCsE
2025-07-10 18:25:56,699 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:25:56,699 - INFO - [任务 1] 获取验证码...
2025-07-10 18:25:56,700 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:25:57,306 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214315612054"},"count":0}
2025-07-10 18:25:57,308 - INFO - 获取到验证码key: authccode175214315612054
2025-07-10 18:25:57,309 - INFO - 获取验证码成功
2025-07-10 18:25:57,317 - INFO - 验证码图片预处理完成
2025-07-10 18:25:57,327 - INFO - OCR识别结果(处理后): 9787
2025-07-10 18:25:57,327 - INFO - [任务 1] 正在注册账号: 15245740134
2025-07-10 18:25:57,327 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:25:57,327 - INFO - 注册数据: {"mobile": "15245740134", "password": "Wh3lHCsE", "code": "9787", "key": "authccode175214315612054", "inviteCode": "555555"}
2025-07-10 18:25:58,259 - INFO - 注册响应状态码: 200
2025-07-10 18:25:58,259 - INFO - 注册响应原始数据: 

<!doctype html><html><head><script src="//g.alicdn.com/mtb/wpk/2.0.3/wpk.js"></script><script>try{...
2025-07-10 18:25:58,260 - INFO - 尝试手动解密字符串: 

<!doctype html><html><head><...
2025-07-10 18:25:58,260 - ERROR - 手动解密Base64解码失败: string argument should contain only ASCII characters
2025-07-10 18:25:58,261 - ERROR - 无法解密响应
2025-07-10 18:25:58,261 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '解密失败', 'raw': '\r\n<!doctype html><html><head><script src="//g.alicdn.com/mtb/wpk/2.0.3/wpk.js"></script><script>try{getLego2WPK({"bid":"alpvis","env":"","rate":0.01})}catch(e){window.getLego2WPK=function(){return {report:noop,reportFlow:noop,reportError:noop,reportApiError:noop,setConfig:noop,install:noop,installAll:noop,uninstall:noop,diagnose:noop};function noop(){}}}</script><meta name="spm-id" content="a311n"><title>爱淘宝首页</title><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial'}
2025-07-10 18:25:58,261 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:25:58,263 - INFO - ==================================================
2025-07-10 18:25:58,264 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:25:58,264 - INFO - ==================================================
2025-07-10 18:27:23,210 - INFO - ==================================================
2025-07-10 18:27:23,210 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:27:23,210 - INFO - ==================================================
2025-07-10 18:27:23,211 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:27:23,530 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662917521432430055284e126a2c591354bc0815b48b491634d45c', 'cdn_sec_tc': 'b482662917521432430055284e126a2c591354bc0815b48b491634d45c'}
2025-07-10 18:27:23,575 - INFO - OCR初始化成功
2025-07-10 18:27:23,575 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:27:23,576 - INFO - [任务 1] 随机生成手机号: 18471697077, 密码: bgq0sD0J
2025-07-10 18:27:23,576 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:27:23,576 - INFO - [任务 1] 获取验证码...
2025-07-10 18:27:23,578 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:27:29,040 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,\/9j\/4AAQSkZJRgABAQEAYABgAAD\/\/gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2NjIpLCBxdWFsaXR5ID0gOTAK\/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU\/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU\/8AAEQgAHQBkAwEiAAIRAQMRAf\/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC\/\/EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29\/j5+v\/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC\/\/EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29\/j5+v\/aAAwDAQACEQMRAD8A\/NyiiivEP1MKKKKACinGNxGrlSEYkBscEjGRn8R+deq\/Dn4eeF\/EHw98Z69eX95d6xoenrdx2CQ+XbKzSbFDybtzn2AUD+8e3LiMRDDQ9pO9rpaa6t2\/PuNI8pxxntSV7n8R7qHxj+zh4D1uDSrDTZrDW7\/SpY9NtxErF0ikTI5JO1QMkknqeSaxvGfgXTvg3omn2WpWo1jxvqNot3NBKpNtpULj5AR\/y0mI+bn5V44PWuOnmEZpJxtNykuX\/C7N37W1v521dhpXPJaKKK9YkKKKKACiiigAooooAK3PCPheXxVqvkCQW1nAhnvLt\/u28K\/ec\/yA7kgVh1v+G\/G2o+F7HUbG2S0uLDUfL+1Wt5bJKsuwkpyRuXBJ+6RnvnArCv7T2b9j8X9f0vMatfU9a+O82lz\/AAR+Dj6Rp6afZt\/a5RAPncCeFQ7nuxCgk1m\/COF4vgT8Z70KxC2+mW+4D+\/O+f8A0Gtf40+JHn+A\/wAI4YrDT7SG7ttQZlitVJjxcJ\/q2bLJnHO0jPfivL9I+LXi\/wAP6KdI0vXbnTtLZdslpa7Y45eMHeoGHPu2TXz2Go1a+AVOCXxt6t7Ko32eulvx8h6XueteB9Gubz9mi4tUksry9s\/FEGu2ulw3kT3c0QhWOVhCG3YAA4I7H0rrtR8YeJm\/aktdRtNTu5vBOrPDenfIzafJYmFfMLKfkwgDK2eQVIr5KJyST1NXY9d1KLTH01NRu005zua0WdhEx9SmcH8q1qZS6k5zck+bmWq25rba7q116v5F0WfF93Y3\/ivWbnTIhBps15NJbRgY2xFyVH5YrIoor6GEVCKiuhL1CiiiqAKKKKAP\/9k=","key":"authccode175214324387649"},"count":0}
2025-07-10 18:27:29,041 - INFO - 获取到验证码key: authccode175214324387649
2025-07-10 18:27:29,041 - INFO - 获取验证码成功
2025-07-10 18:27:29,050 - INFO - 验证码图片预处理完成
2025-07-10 18:27:29,059 - INFO - OCR识别结果(处理后): 416
2025-07-10 18:27:29,068 - INFO - OCR识别结果(原始图): 4116
2025-07-10 18:27:29,068 - INFO - [任务 1] 正在注册账号: 18471697077
2025-07-10 18:27:29,068 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:27:29,069 - INFO - 注册数据: {"mobile": "18471697077", "password": "bgq0sD0J", "code": "4116", "key": "authccode175214324387649", "inviteCode": "555555"}
2025-07-10 18:27:29,757 - INFO - 注册响应状态码: 200
2025-07-10 18:27:29,757 - INFO - 注册响应原始数据: <html><head><meta charset="UTF-8" /></head><body></body> <script> var glb;(glb="undefined"==typeof w...
2025-07-10 18:27:29,757 - INFO - 尝试手动解密字符串: <html><head><meta charset="UTF...
2025-07-10 18:27:29,757 - ERROR - 手动解密过程出错: Data must be aligned to block boundary in ECB mode
2025-07-10 18:27:29,758 - ERROR - 无法解密响应
2025-07-10 18:27:29,758 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '解密失败', 'raw': '<html><head><meta charset="UTF-8" /></head><body></body> <script> var glb;(glb="undefined"==typeof window?global:window)._$jsvmprt=function(b,e,f){function a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(b){return!1}}function d(b,e,f){return(d=a()?Reflect.construct:function(b,e,f){var a=[null];a.push.apply(a,e);var d=n'}
2025-07-10 18:27:29,758 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:27:29,761 - INFO - ==================================================
2025-07-10 18:27:29,761 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:27:29,761 - INFO - ==================================================
2025-07-10 18:35:04,785 - INFO - ==================================================
2025-07-10 18:35:04,785 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:35:04,785 - INFO - ==================================================
2025-07-10 18:35:04,785 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:35:05,114 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521437046206075e9907f11373a570099479ed0c037c7961', 'cdn_sec_tc': 'b482662c17521437046206075e9907f11373a570099479ed0c037c7961'}
2025-07-10 18:35:05,171 - INFO - OCR初始化成功
2025-07-10 18:35:05,171 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:35:05,172 - INFO - [任务 1] 随机生成手机号: 18469948848, 密码: rI0YA4dw
2025-07-10 18:35:05,172 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:35:05,172 - INFO - [任务 1] 获取验证码...
2025-07-10 18:35:05,174 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:35:05,998 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214370547153"},"count":0}
2025-07-10 18:35:06,000 - INFO - 获取到验证码key: authccode175214370547153
2025-07-10 18:35:06,001 - INFO - 获取验证码成功
2025-07-10 18:35:06,009 - INFO - 验证码图片预处理完成
2025-07-10 18:35:06,016 - INFO - OCR识别结果(处理后): 361
2025-07-10 18:35:06,025 - INFO - OCR识别结果(原始图): 3611
2025-07-10 18:35:06,025 - INFO - [任务 1] 正在注册账号: 18469948848
2025-07-10 18:35:06,026 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:35:06,026 - INFO - 注册数据: {"mobile": "18469948848", "password": "rI0YA4dw", "code": "3611", "key": "authccode175214370547153", "inviteCode": "555555"}
2025-07-10 18:35:06,660 - INFO - 注册响应状态码: 200
2025-07-10 18:35:06,660 - INFO - 注册响应原始数据: <!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <link rel="dns-prefetch" h...
2025-07-10 18:35:06,661 - INFO - 尝试手动解密字符串: <!DOCTYPE html>
<html lang="zh...
2025-07-10 18:35:06,661 - ERROR - 手动解密Base64解码失败: string argument should contain only ASCII characters
2025-07-10 18:35:06,661 - ERROR - 无法解密响应
2025-07-10 18:35:06,661 - ERROR - [任务 1] 注册失败: {'code': 500, 'message': '解密失败', 'raw': '<!DOCTYPE html>\n<html lang="zh-cn">\n<head>\n    <meta charset="utf-8">\n    <link rel="dns-prefetch" href="//h5.sinaimg.cn">\n    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover">\n    <meta name="format-detection" content="telephone=no">\n    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">\n    <title>微博</title>\n    <meta content="随时随地发现新鲜事！微博带你欣赏世界上每一个精彩瞬间，了解每一个幕后故事。分享你想表达的，让全世界都能听到你的心声！" name="description">\n        <link rel="'}
2025-07-10 18:35:06,662 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:35:06,664 - INFO - ==================================================
2025-07-10 18:35:06,665 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:35:06,665 - INFO - ==================================================
2025-07-10 18:36:22,867 - INFO - ==================================================
2025-07-10 18:36:22,868 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:36:22,868 - INFO - ==================================================
2025-07-10 18:36:22,868 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:36:23,919 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521437827115255e2e394fdae2bbf9ba77885664eef4b9f8', 'cdn_sec_tc': 'b482662c17521437827115255e2e394fdae2bbf9ba77885664eef4b9f8'}
2025-07-10 18:36:23,971 - INFO - OCR初始化成功
2025-07-10 18:36:23,971 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:36:23,972 - INFO - [任务 1] 随机生成手机号: 15101161175, 密码: DYfgLLzy
2025-07-10 18:36:23,972 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:36:23,973 - INFO - [任务 1] 获取验证码...
2025-07-10 18:36:23,974 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:36:25,167 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214378359885"},"count":0}
2025-07-10 18:36:25,168 - INFO - 获取到验证码key: authccode175214378359885
2025-07-10 18:36:25,169 - INFO - 获取验证码成功
2025-07-10 18:36:25,177 - INFO - 验证码图片预处理完成
2025-07-10 18:36:25,186 - INFO - OCR识别结果(处理后): 2617
2025-07-10 18:36:25,187 - INFO - [任务 1] 正在注册账号: 15101161175
2025-07-10 18:36:25,187 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:36:25,187 - INFO - 注册数据: {"mobile": "15101161175", "password": "DYfgLLzy", "code": "2617", "key": "authccode175214378359885", "inviteCode": "555555"}
2025-07-10 18:36:25,864 - INFO - 注册响应状态码: 200
2025-07-10 18:36:25,864 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 18:36:25,865 - INFO - 成功提取JS代码(前100字符):  var glb;(glb="undefined"==typeof window?global:window)._$jsvmprt=function(b,e,f){function a(){if("u...
2025-07-10 18:36:25,865 - INFO - 正在尝试使用Node.js执行JS代码...
2025-07-10 18:36:25,925 - ERROR - 执行JavaScript质询时发生严重错误: TypeError: S[R] is not a constructor
2025-07-10 18:36:25,926 - ERROR - [任务 1] 注册失败: {'code': 503, 'message': '收到JavaScript质询，已尝试执行但流程未完成。'}
2025-07-10 18:36:25,926 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:36:25,928 - INFO - ==================================================
2025-07-10 18:36:25,928 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:36:25,928 - INFO - ==================================================
2025-07-10 18:37:44,675 - INFO - ==================================================
2025-07-10 18:37:44,675 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:37:44,675 - INFO - ==================================================
2025-07-10 18:37:44,676 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:37:44,973 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521438645245702e1aa8656a24aff6482aea37c20dcf7013', 'cdn_sec_tc': 'b482662c17521438645245702e1aa8656a24aff6482aea37c20dcf7013'}
2025-07-10 18:37:45,023 - INFO - OCR初始化成功
2025-07-10 18:37:45,024 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:37:45,024 - INFO - [任务 1] 随机生成手机号: 18711012050, 密码: hkGfzrTL
2025-07-10 18:37:45,024 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:37:45,024 - INFO - [任务 1] 获取验证码...
2025-07-10 18:37:45,026 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:37:45,740 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214386452601"},"count":0}
2025-07-10 18:37:45,741 - INFO - 获取到验证码key: authccode175214386452601
2025-07-10 18:37:45,741 - INFO - 获取验证码成功
2025-07-10 18:37:45,750 - INFO - 验证码图片预处理完成
2025-07-10 18:37:45,757 - INFO - OCR识别结果(处理后): 3864
2025-07-10 18:37:45,757 - INFO - [任务 1] 正在注册账号: 18711012050
2025-07-10 18:37:45,758 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:37:45,758 - INFO - 注册数据: {"mobile": "18711012050", "password": "hkGfzrTL", "code": "3864", "key": "authccode175214386452601", "inviteCode": "555555"}
2025-07-10 18:37:48,168 - INFO - 注册响应状态码: 200
2025-07-10 18:37:48,168 - INFO - 尝试手动解密字符串: <!DOCTYPE html>
<html>

<head>...
2025-07-10 18:37:48,169 - ERROR - 手动解密Base64解码失败: string argument should contain only ASCII characters
2025-07-10 18:37:48,169 - ERROR - 无法解密响应
2025-07-10 18:37:48,170 - ERROR - [任务 1] 注册请求失败，无响应
2025-07-10 18:37:49,170 - INFO - [任务 1] 第2次尝试注册...
2025-07-10 18:37:49,170 - INFO - [任务 1] 获取验证码...
2025-07-10 18:37:49,171 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:39:08,210 - INFO - ==================================================
2025-07-10 18:39:08,210 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:39:08,210 - INFO - ==================================================
2025-07-10 18:39:08,210 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:39:08,498 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521439480566621ebe99cf47244855d3a16a156bb6ff949c', 'cdn_sec_tc': 'b482662c17521439480566621ebe99cf47244855d3a16a156bb6ff949c'}
2025-07-10 18:39:08,546 - INFO - OCR初始化成功
2025-07-10 18:39:08,547 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:39:08,548 - INFO - [任务 1] 随机生成手机号: 15879307706, 密码: k7bAVE8h
2025-07-10 18:39:08,548 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:39:08,548 - INFO - [任务 1] 获取验证码...
2025-07-10 18:39:08,550 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:39:09,542 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214394822001"},"count":0}
2025-07-10 18:39:09,544 - INFO - 获取到验证码key: authccode175214394822001
2025-07-10 18:39:09,544 - INFO - 获取验证码成功
2025-07-10 18:39:09,553 - INFO - 验证码图片预处理完成
2025-07-10 18:39:09,563 - INFO - OCR识别结果(处理后): 73s9
2025-07-10 18:39:09,571 - INFO - OCR识别结果(原始图): 7339
2025-07-10 18:39:09,571 - INFO - [任务 1] 正在注册账号: 15879307706
2025-07-10 18:39:09,571 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:39:09,571 - INFO - 注册数据: {"mobile": "15879307706", "password": "k7bAVE8h", "code": "7339", "key": "authccode175214394822001", "inviteCode": "555555"}
2025-07-10 18:39:10,578 - INFO - 注册响应状态码: 200
2025-07-10 18:39:10,578 - INFO - 尝试手动解密字符串: <!DOCTYPE html><html lang="zh-...
2025-07-10 18:39:10,578 - ERROR - 手动解密Base64解码失败: string argument should contain only ASCII characters
2025-07-10 18:39:10,579 - ERROR - 无法解密响应
2025-07-10 18:39:10,579 - ERROR - [任务 1] 注册请求失败，无响应
2025-07-10 18:39:11,580 - INFO - [任务 1] 第2次尝试注册...
2025-07-10 18:39:11,580 - INFO - [任务 1] 获取验证码...
2025-07-10 18:39:11,581 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:39:12,270 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214395190694"},"count":0}
2025-07-10 18:39:12,272 - INFO - 获取到验证码key: authccode175214395190694
2025-07-10 18:39:12,272 - INFO - 获取验证码成功
2025-07-10 18:39:12,273 - INFO - 验证码图片预处理完成
2025-07-10 18:39:12,281 - INFO - OCR识别结果(处理后): 835
2025-07-10 18:39:12,290 - INFO - OCR识别结果(原始图): 8351
2025-07-10 18:39:12,290 - INFO - [任务 1] 正在注册账号: 15879307706
2025-07-10 18:39:12,290 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:39:12,290 - INFO - 注册数据: {"mobile": "15879307706", "password": "k7bAVE8h", "code": "8351", "key": "authccode175214395190694", "inviteCode": "555555"}
2025-07-10 18:39:12,772 - INFO - 注册响应状态码: 403
2025-07-10 18:39:12,772 - ERROR - 注册请求失败，状态码: 403
2025-07-10 18:39:12,772 - ERROR - [任务 1] 注册失败: {'code': 403, 'message': '请求失败'}
2025-07-10 18:39:12,773 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:39:12,775 - INFO - ==================================================
2025-07-10 18:39:12,775 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:39:12,775 - INFO - ==================================================
2025-07-10 18:40:13,726 - INFO - ==================================================
2025-07-10 18:40:13,726 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:40:13,726 - INFO - ==================================================
2025-07-10 18:40:13,727 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:40:24,343 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662917521440235802657eed1010c639ea87d4c917e5fa6f9382b4', 'cdn_sec_tc': 'b482662917521440235802657eed1010c639ea87d4c917e5fa6f9382b4'}
2025-07-10 18:40:24,395 - INFO - OCR初始化成功
2025-07-10 18:40:24,395 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:40:24,396 - INFO - [任务 1] 随机生成手机号: 18730125523, 密码: L61UzBQI
2025-07-10 18:40:24,396 - INFO - [任务 1] 使用邀请码: 555555
2025-07-10 18:40:24,396 - INFO - [任务 1] 获取验证码...
2025-07-10 18:40:24,398 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:40:25,721 - INFO - 解密响应: {"code":200,"message":"请求成功","data":{"img":"data:image\/jpeg;base64,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","key":"authccode175214402489798"},"count":0}
2025-07-10 18:40:25,723 - INFO - 获取到验证码key: authccode175214402489798
2025-07-10 18:40:25,724 - INFO - 获取验证码成功
2025-07-10 18:40:25,732 - INFO - 验证码图片预处理完成
2025-07-10 18:40:25,741 - INFO - OCR识别结果(处理后): 2862
2025-07-10 18:40:25,741 - INFO - [任务 1] 正在注册账号: 18730125523
2025-07-10 18:40:25,741 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:40:25,741 - INFO - 注册数据: {"mobile": "18730125523", "password": "L61UzBQI", "code": "2862", "key": "authccode175214402489798", "inviteCode": "555555"}
2025-07-10 18:40:26,401 - INFO - 注册响应状态码: 200
2025-07-10 18:40:26,402 - INFO - 收到200 OK响应，原始数据(前100字符): <html><head><meta charset="UTF-8" /></head><body></body> <script> var glb;(glb="undefined"==typeof w...
2025-07-10 18:40:26,402 - INFO - 尝试手动解密字符串: <html><head><meta charset="UTF...
2025-07-10 18:40:26,402 - ERROR - 手动解密过程出错: Data must be aligned to block boundary in ECB mode
2025-07-10 18:40:26,402 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 18:40:26,403 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 18:40:26,403 - INFO - 成功提取JS代码(前100字符):  var glb;(glb="undefined"==typeof window?global:window)._$jsvmprt=function(b,e,f){function a(){if("u...
2025-07-10 18:40:26,403 - INFO - 正在尝试使用Node.js执行JS代码...
2025-07-10 18:40:26,464 - ERROR - 执行JavaScript质询时发生严重错误: TypeError: S[R] is not a constructor
2025-07-10 18:40:26,464 - ERROR - [任务 1] 注册失败: {'code': 503, 'message': '收到JavaScript质询，解密失败。'}
2025-07-10 18:40:26,464 - ERROR - [任务 1] 达到最大尝试次数(5)，注册失败
2025-07-10 18:40:26,467 - INFO - ==================================================
2025-07-10 18:40:26,467 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:40:26,467 - INFO - ==================================================
2025-07-10 18:42:52,796 - INFO - 将使用邀请码: 5960823
2025-07-10 18:42:52,796 - INFO - ==================================================
2025-07-10 18:42:52,796 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:42:52,796 - INFO - ==================================================
2025-07-10 18:42:52,797 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:42:52,797 - ERROR - [任务 1] 发生异常: 'str' object has no attribute 'generate_random_phone'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 796, in register_single_account
    phone = registrator.generate_random_phone()
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'generate_random_phone'
2025-07-10 18:42:52,799 - INFO - ==================================================
2025-07-10 18:42:52,799 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:42:52,799 - INFO - ==================================================
2025-07-10 18:44:56,389 - INFO - 将使用邀请码: 555555
2025-07-10 18:44:56,389 - INFO - ==================================================
2025-07-10 18:44:56,389 - INFO - 开始注册任务 - 总计 1 个账号, 1 个并发线程
2025-07-10 18:44:56,389 - INFO - ==================================================
2025-07-10 18:44:56,390 - INFO - [任务 1] 开始注册流程...
2025-07-10 18:44:56,390 - ERROR - [任务 1] 发生异常: 'str' object has no attribute 'generate_random_phone'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\hook\注册机\注册2\注册机2.py", line 796, in register_single_account
    phone = registrator.generate_random_phone()
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'generate_random_phone'
2025-07-10 18:44:56,391 - INFO - ==================================================
2025-07-10 18:44:56,391 - INFO - 注册任务完成: 成功 0 个, 失败 1 个
2025-07-10 18:44:56,391 - INFO - ==================================================
2025-07-10 18:48:42,237 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 18:48:42,237 - MainThread - INFO - ==================================================
2025-07-10 18:48:42,237 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 18:48:42,237 - MainThread - INFO - ==================================================
2025-07-10 18:48:42,238 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 18:48:42,238 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:48:42,526 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662917521445221137395e09b706f34b6136ae3e8b20ea8c2e4e8e', 'cdn_sec_tc': 'b482662917521445221137395e09b706f34b6136ae3e8b20ea8c2e4e8e'}
2025-07-10 18:48:42,578 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 18:48:42,578 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 18:48:42,580 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:48:42,964 - RegThread_0 - INFO - 获取到验证码key: authccode175214452217829
2025-07-10 18:48:42,965 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 18:48:42,974 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 18:48:42,982 - RegThread_0 - INFO - OCR识别结果(处理后): k2633
2025-07-10 18:48:42,990 - RegThread_0 - INFO - OCR识别结果(原始图): 9633
2025-07-10 18:48:42,990 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:48:42,990 - RegThread_0 - INFO - 注册数据: {"mobile": "13429613399", "password": "dSZqV5t5", "code": "9633", "key": "authccode175214452217829", "inviteCode": "555555"}
2025-07-10 18:48:43,564 - RegThread_0 - INFO - 注册响应状态码: 403
2025-07-10 18:48:43,564 - RegThread_0 - ERROR - 注册请求失败，状态码: 403
2025-07-10 18:48:43,564 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 403, 'message': '请求失败'}
2025-07-10 18:48:43,567 - MainThread - INFO - ==================================================
2025-07-10 18:48:43,567 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 18:48:43,567 - MainThread - INFO - ==================================================
2025-07-10 18:49:27,298 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 18:49:27,298 - MainThread - INFO - ==================================================
2025-07-10 18:49:27,298 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 18:49:27,298 - MainThread - INFO - ==================================================
2025-07-10 18:49:27,298 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 18:49:27,298 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:49:27,575 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521445671858452e2f75530ae39db964888e3f12e79a9d38', 'cdn_sec_tc': 'b482662c17521445671858452e2f75530ae39db964888e3f12e79a9d38'}
2025-07-10 18:49:27,624 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 18:49:27,625 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 18:49:27,626 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:49:28,012 - RegThread_0 - INFO - 获取到验证码key: authccode175214456794165
2025-07-10 18:49:28,012 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 18:49:28,021 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 18:49:28,029 - RegThread_0 - INFO - OCR识别结果(处理后): 4816
2025-07-10 18:49:28,029 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:49:28,030 - RegThread_0 - INFO - 注册数据: {"mobile": "15980034962", "password": "fMrZi5zj", "code": "4816", "key": "authccode175214456794165", "inviteCode": "555555"}
2025-07-10 18:49:28,800 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 18:49:28,800 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!doctype html><html lang="en" class=""><head><meta charset="utf-8"><title>【快手短视频App】快手，拥抱每一种生活</tit...
2025-07-10 18:49:28,801 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 18:49:28,801 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 500, 'message': '响应无法解密，且不是预期的HTML格式。', 'raw': '<!doctype html><html lang="en" class=""><head><meta charset="utf-8"><title>【快手短视频App】快手，拥抱每一种生活</title><meta name="referrer" content="always"><meta name="viewport" content="width=device-width,initial-'}
2025-07-10 18:49:28,804 - MainThread - INFO - ==================================================
2025-07-10 18:49:28,804 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 18:49:28,804 - MainThread - INFO - ==================================================
2025-07-10 18:54:02,903 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 18:54:02,903 - MainThread - INFO - ==================================================
2025-07-10 18:54:02,903 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 18:54:02,903 - MainThread - INFO - ==================================================
2025-07-10 18:54:02,904 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 18:54:02,904 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:54:03,199 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521448428113870e63d700fb31aa0c1ba34a883ee884f952', 'cdn_sec_tc': 'b482662c17521448428113870e63d700fb31aa0c1ba34a883ee884f952'}
2025-07-10 18:54:03,244 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 18:54:03,245 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 18:54:03,247 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:54:03,608 - RegThread_0 - INFO - 获取到验证码key: authccode175214484398285
2025-07-10 18:54:03,608 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 18:54:03,617 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 18:54:03,627 - RegThread_0 - INFO - OCR识别结果(处理后): 7583
2025-07-10 18:54:03,628 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:54:03,628 - RegThread_0 - INFO - 注册数据: {"mobile": "17843681802", "password": "QwAmQFwz", "code": "7583", "key": "authccode175214484398285", "inviteCode": "555555"}
2025-07-10 18:54:04,218 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 18:54:04,218 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!DOCTYPE html>
<html>

<head>
    <meta charset="utf8" version='1'/>
    <title>京东(JD.COM)-正品低价、品质保...
2025-07-10 18:54:04,219 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 18:54:04,219 - RegThread_0 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 18:54:04,223 - RegThread_0 - INFO - 成功提取JS代码(前100字符): 
        window.point = {}
        window.point.start = new Date().getTime()
    ...
2025-07-10 18:54:04,224 - RegThread_0 - INFO - 尝试在Node环境中执行JS以获取cookie...
2025-07-10 18:54:04,270 - RegThread_0 - WARNING - 未能从JS质询中解析出cookie。
2025-07-10 18:54:04,270 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 503, 'message': '收到JavaScript质询，已尝试解决但未成功。'}
2025-07-10 18:54:04,272 - MainThread - INFO - ==================================================
2025-07-10 18:54:04,273 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 18:54:04,273 - MainThread - INFO - ==================================================
2025-07-10 18:56:02,985 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 18:56:02,985 - MainThread - INFO - ==================================================
2025-07-10 18:56:02,985 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 18:56:02,985 - MainThread - INFO - ==================================================
2025-07-10 18:56:02,986 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 18:56:02,986 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 18:56:03,624 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662c17521449628987930e31da377547bece850b4d1b3427a712b7', 'cdn_sec_tc': 'b482662c17521449628987930e31da377547bece850b4d1b3427a712b7'}
2025-07-10 18:56:03,672 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 18:56:03,672 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 18:56:03,674 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 18:56:04,123 - RegThread_0 - INFO - 获取到验证码key: authccode175214496346805
2025-07-10 18:56:04,124 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 18:56:04,132 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 18:56:04,142 - RegThread_0 - INFO - OCR识别结果(处理后): 5354
2025-07-10 18:56:04,143 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 18:56:04,143 - RegThread_0 - INFO - 注册数据: {"mobile": "15229308070", "password": "CfV0npym", "code": "5354", "key": "authccode175214496346805", "inviteCode": "555555"}
2025-07-10 18:56:04,940 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 18:56:04,940 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!doctype html><html lang="en" class=""><head><meta charset="utf-8"><title>【快手短视频App】快手，拥抱每一种生活</tit...
2025-07-10 18:56:04,941 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 18:56:04,941 - RegThread_0 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 18:56:04,941 - RegThread_0 - INFO - 成功提取JS代码(前100字符): !function e(){var n=Math.min(document.documentElement.clientWidth,750)/7.5;document.documentElement....
2025-07-10 18:56:04,941 - RegThread_0 - INFO - 尝试在Node环境中执行JS以获取cookie...
2025-07-10 18:56:04,987 - RegThread_0 - ERROR - Node环境执行JS质询失败: TypeError: Cannot read properties of undefined (reading 'clientWidth')
2025-07-10 18:56:04,987 - RegThread_0 - WARNING - 未能从JS质询中解析出cookie。
2025-07-10 18:56:04,988 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 503, 'message': '收到JavaScript质询，已尝试解决但未成功。'}
2025-07-10 18:56:04,990 - MainThread - INFO - ==================================================
2025-07-10 18:56:04,990 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 18:56:04,990 - MainThread - INFO - ==================================================
2025-07-10 19:13:04,192 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 19:13:04,192 - MainThread - INFO - ==================================================
2025-07-10 19:13:04,192 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 19:13:04,193 - MainThread - INFO - ==================================================
2025-07-10 19:13:04,193 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 19:13:04,193 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 19:13:04,488 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': 'b482662917521459841443771e91cef26e6a82c127eb36b292978b4e6d', 'cdn_sec_tc': 'b482662917521459841443771e91cef26e6a82c127eb36b292978b4e6d'}
2025-07-10 19:13:04,551 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 19:13:04,551 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 19:13:04,553 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 19:13:10,157 - RegThread_0 - INFO - 获取到验证码key: authccode175214598436261
2025-07-10 19:13:10,157 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 19:13:10,166 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 19:13:10,175 - RegThread_0 - INFO - OCR识别结果(处理后): 2982
2025-07-10 19:13:10,176 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 19:13:10,176 - RegThread_0 - INFO - 注册数据: {"mobile": "15730731646", "password": "9rpK7hll", "code": "2982", "key": "authccode175214598436261", "inviteCode": "555555"}
2025-07-10 19:13:11,105 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 19:13:11,105 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <link rel="dns-prefetch" h...
2025-07-10 19:13:11,105 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 19:13:11,106 - RegThread_0 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 19:13:11,106 - RegThread_0 - INFO - 成功提取JS代码(前100字符): !function(e){var a,i=navigator.userAgent.toLowerCase(),n=document.documentElement,t=parseInt(n.clien...
2025-07-10 19:13:11,106 - RegThread_0 - INFO - 尝试在Node环境中执行JS以获取cookie...
2025-07-10 19:13:11,153 - RegThread_0 - ERROR - Node环境执行JS质询失败: TypeError: Cannot read properties of undefined (reading 'clientWidth')
2025-07-10 19:13:11,154 - RegThread_0 - WARNING - 未能从JS质询中解析出cookie。
2025-07-10 19:13:11,154 - RegThread_0 - WARNING - 未安装 undetected-chromedriver，无法自动获取 WAF Cookie
2025-07-10 19:13:11,154 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 503, 'message': '收到JavaScript质询，已尝试解决但未成功。'}
2025-07-10 19:13:11,156 - MainThread - INFO - ==================================================
2025-07-10 19:13:11,156 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 19:13:11,156 - MainThread - INFO - ==================================================
2025-07-10 19:31:29,018 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 19:31:29,019 - MainThread - INFO - ==================================================
2025-07-10 19:31:29,019 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 19:31:29,019 - MainThread - INFO - ==================================================
2025-07-10 19:31:29,019 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 19:31:29,020 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 19:31:32,157 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': '2ff6179b17521470911362020e717413cd620344bab584237e249c33ca', 'cdn_sec_tc': '2ff6179b17521470911362020e717413cd620344bab584237e249c33ca'}
2025-07-10 19:31:32,223 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 19:31:32,223 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 19:31:32,225 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 19:31:33,011 - RegThread_0 - INFO - 获取到验证码key: authccode175214709261015
2025-07-10 19:31:33,012 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 19:31:33,020 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 19:31:33,029 - RegThread_0 - INFO - OCR识别结果(处理后): 2932
2025-07-10 19:31:33,029 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 19:31:33,029 - RegThread_0 - INFO - 注册数据: {"mobile": "15084672034", "password": "Ion8cnux", "code": "2932", "key": "authccode175214709261015", "inviteCode": "555555"}
2025-07-10 19:31:34,291 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 19:31:34,292 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!DOCTYPE html>
<html class=""><!--STATUS OK--><head><meta name="referrer" content="always" /><meta ...
2025-07-10 19:31:34,292 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 19:31:34,293 - RegThread_0 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 19:31:34,293 - RegThread_0 - INFO - 成功提取JS代码(前100字符): window._performanceTimings=[['firstLine',+new Date()]];...
2025-07-10 19:31:34,293 - RegThread_0 - INFO - 尝试在Node环境中执行JS以获取cookie...
2025-07-10 19:31:34,340 - RegThread_0 - WARNING - 未能从JS质询中解析出cookie。
2025-07-10 19:31:34,341 - RegThread_0 - WARNING - 未安装 undetected-chromedriver，无法自动获取 WAF Cookie
2025-07-10 19:31:34,341 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 503, 'message': '收到JavaScript质询，已尝试解决但未成功。'}
2025-07-10 19:31:34,344 - MainThread - INFO - ==================================================
2025-07-10 19:31:34,344 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 19:31:34,344 - MainThread - INFO - ==================================================
2025-07-10 19:51:23,446 - MainThread - INFO - 将使用邀请码: 555555
2025-07-10 19:51:23,446 - MainThread - INFO - ==================================================
2025-07-10 19:51:23,447 - MainThread - INFO - 开始注册任务 - 账号: 1, 线程: 1, 重试: 5
2025-07-10 19:51:23,447 - MainThread - INFO - ==================================================
2025-07-10 19:51:23,447 - RegThread_0 - INFO - 任务 1 开始...
2025-07-10 19:51:23,448 - RegThread_0 - INFO - 正在初始化会话并从 https://bmqb-app-1.ddzety.com/pages/login/login 获取动态Cookie...
2025-07-10 19:51:24,693 - RegThread_0 - INFO - 成功获取到动态Cookie: {'acw_tc': '2ff6179517521482843578277ef67a1880c45dcb916a8801fafb35d721', 'cdn_sec_tc': '2ff6179517521482843578277ef67a1880c45dcb916a8801fafb35d721'}
2025-07-10 19:51:24,746 - RegThread_0 - INFO - OCR初始化成功
2025-07-10 19:51:24,747 - RegThread_0 - INFO - 任务 1: 尝试注册 (第 1/5 次)...
2025-07-10 19:51:24,749 - RegThread_0 - INFO - 请求验证码: https://bmqb-app-1.ddzety.com/dev-api/api/login/authccode.html
2025-07-10 19:51:25,154 - RegThread_0 - INFO - 获取到验证码key: authccode175214828440165
2025-07-10 19:51:25,155 - RegThread_0 - INFO - 获取验证码成功
2025-07-10 19:51:25,163 - RegThread_0 - INFO - 验证码图片预处理完成
2025-07-10 19:51:25,172 - RegThread_0 - INFO - OCR识别结果(处理后): 3312
2025-07-10 19:51:25,172 - RegThread_0 - INFO - 请求注册: https://bmqb-app-1.ddzety.com/dev-api/api/login/register.html
2025-07-10 19:51:25,173 - RegThread_0 - INFO - 注册数据: {"mobile": "15794324625", "password": "FYduDsUQ", "code": "3312", "key": "authccode175214828440165", "inviteCode": "555555"}
2025-07-10 19:51:25,610 - RegThread_0 - INFO - 注册响应状态码: 200
2025-07-10 19:51:25,611 - RegThread_0 - INFO - 收到200 OK响应，原始数据(前100字符): <!DOCTYPE html>
<html>

<head>
    <meta charset="utf8" version='1'/>
    <title>京东(JD.COM)-正品低价、品质保...
2025-07-10 19:51:25,611 - RegThread_0 - WARNING - 解密响应失败或响应不是JSON。现在检查是否为HTML/JS质询。
2025-07-10 19:51:25,612 - RegThread_0 - INFO - 检测到JavaScript质询，正在尝试解决...
2025-07-10 19:51:25,613 - RegThread_0 - INFO - 成功提取JS代码(前100字符): 
        window.point = {}
        window.point.start = new Date().getTime()
    ...
2025-07-10 19:51:25,613 - RegThread_0 - INFO - 尝试在Node环境中执行JS以获取cookie...
2025-07-10 19:51:25,661 - RegThread_0 - WARNING - 未能从JS质询中解析出cookie。
2025-07-10 19:51:29,671 - RegThread_0 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-07-10 19:51:30,883 - RegThread_0 - ERROR - 使用浏览器获取 WAF Cookie 失败: Message: session not created: cannot connect to chrome at 127.0.0.1:21267
from session not created: This version of ChromeDriver only supports Chrome version 138
Current browser version is 137.0.7151.69
Stacktrace:
	GetHandleVerifier [0x0x12744a3+62419]
	GetHandleVerifier [0x0x12744e4+62484]
	(No symbol) [0x0x10b2133]
	(No symbol) [0x0x10ebfab]
	(No symbol) [0x0x10eafc9]
	(No symbol) [0x0x10e132f]
	(No symbol) [0x0x10e1166]
	(No symbol) [0x0x112af3e]
	(No symbol) [0x0x112a82a]
	(No symbol) [0x0x111f266]
	(No symbol) [0x0x10ee852]
	(No symbol) [0x0x10ef6f4]
	GetHandleVerifier [0x0x14e4793+2619075]
	GetHandleVerifier [0x0x14dfbaa+2599642]
	GetHandleVerifier [0x0x129b04a+221050]
	GetHandleVerifier [0x0x128b2c8+156152]
	GetHandleVerifier [0x0x1291c7d+183213]
	GetHandleVerifier [0x0x127c388+94904]
	GetHandleVerifier [0x0x127c512+95298]
	GetHandleVerifier [0x0x126766a+9626]
	BaseThreadInitThunk [0x0x760a6839+25]
	RtlGetFullPathName_UEx [0x0x77b0906f+1215]
	RtlGetFullPathName_UEx [0x0x77b0903d+1165]

2025-07-10 19:51:30,885 - RegThread_0 - ERROR - 任务 1: 注册失败，非验证码错误: {'code': 503, 'message': '收到JavaScript质询，已尝试解决但未成功。'}
2025-07-10 19:51:30,887 - MainThread - INFO - ==================================================
2025-07-10 19:51:30,888 - MainThread - INFO - 注册任务完成: 成功 0, 失败 1
2025-07-10 19:51:30,888 - MainThread - INFO - ==================================================
