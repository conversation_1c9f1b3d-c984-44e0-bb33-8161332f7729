import requests
import random
import string
import time
import os
import json
import re

# --- 1. 配置模块 ---
BANK_URL = "https://cpdd.stchina17.com/api/v1/user/bank"
ACCOUNT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\注册实名成功.txt"
GOLDEN_CARD_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\RegisterBot\黄金卡.txt"
BIND_SUCCESS_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\绑卡成功.txt"

# 全局变量
GOLDEN_BANK_CARDS = []
ACCOUNTS = []

# --- 2. 数据加载模块 ---
def load_accounts_from_file():
    """从文件加载已注册实名的账号信息"""
    global ACCOUNTS
    if not os.path.exists(ACCOUNT_FILE):
        print(f"❌ 找不到账号文件: {ACCOUNT_FILE}")
        return False
    
    print(f"正在从 {ACCOUNT_FILE} 加载账号信息...")
    with open(ACCOUNT_FILE, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 格式：手机号:密码:姓名:身份证:token:注册时间
                parts = line.split(':')
                if len(parts) >= 6:
                    account = {
                        "phone": parts[0],
                        "password": parts[1], 
                        "name": parts[2],
                        "id_number": parts[3],
                        "token": parts[4],
                        "register_time": parts[5]
                    }
                    ACCOUNTS.append(account)
                else:
                    print(f"⚠️  第{line_num}行格式不正确，跳过")
            except Exception as e:
                print(f"❌ 第{line_num}行解析失败: {e}")
    
    if ACCOUNTS:
        print(f"✅ 成功加载 {len(ACCOUNTS)} 个账号信息")
        return True
    else:
        print("❌ 没有找到有效的账号信息")
        return False

def load_golden_cards_from_file():
    """从黄金卡文件加载银行卡信息"""
    global GOLDEN_BANK_CARDS
    if not os.path.exists(GOLDEN_CARD_FILE):
        print(f"ℹ️  提示: 未找到黄金卡文件: {GOLDEN_CARD_FILE}")
        return
    
    print(f"正在从 {GOLDEN_CARD_FILE} 加载黄金卡...")
    loaded_cards = []
    with open(GOLDEN_CARD_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            # 解析格式：银行: xxx, 卡号: xxx
            if "银行:" in line and "卡号:" in line:
                bank_match = re.search(r"银行: (.*?),", line)
                card_match = re.search(r"卡号: (\d+)", line)
                
                if bank_match and card_match:
                    bank_name = bank_match.group(1).strip()
                    bank_no = card_match.group(1).strip()
                    
                    card = {
                        "bank_name": bank_name, 
                        "bank_no": bank_no
                    }
                    
                    # 去重检查
                    if not any(c["bank_no"] == bank_no for c in loaded_cards):
                        loaded_cards.append(card)
    
    if loaded_cards:
        GOLDEN_BANK_CARDS = loaded_cards
        print(f"✅ 成功加载 {len(GOLDEN_BANK_CARDS)} 张不重复的黄金银行卡！")

def generate_random_bank_card():
    """生成随机银行卡"""
    bank_bins = [
        {'name': '工商银行', 'bin': '622202', 'len': 19}, {'name': '农业银行', 'bin': '622848', 'len': 19},
        {'name': '中国银行', 'bin': '621661', 'len': 19}, {'name': '建设银行', 'bin': '621700', 'len': 16},
        {'name': '交通银行', 'bin': '601428', 'len': 19}, {'name': '招商银行', 'bin': '622588', 'len': 16},
        {'name': '邮政储蓄银行', 'bin': '622188', 'len': 19}, {'name': '光大银行', 'bin': '622666', 'len': 16},
        {'name': '民生银行', 'bin': '622622', 'len': 16}, {'name': '平安银行', 'bin': '622155', 'len': 16},
        {'name': '浦发银行', 'bin': '622521', 'len': 16}, {'name': '中信银行', 'bin': '622688', 'len': 16},
        {'name': '兴业银行', 'bin': '622908', 'len': 18}, {'name': '华夏银行', 'bin': '622637', 'len': 16},
        {'name': '广发银行', 'bin': '622568', 'len': 16}, {'name': '深发银行', 'bin': '622526', 'len': 16},
        {'name': '上海银行', 'bin': '622892', 'len': 16}, {'name': '北京银行', 'bin': '602969', 'len': 19},
    ]
    card_info = random.choice(bank_bins)
    bank_name, bin_prefix, card_len = card_info['name'], card_info['bin'], card_info['len']
    card_middle = "".join([random.choice(string.digits) for _ in range(card_len - len(bin_prefix) - 1)])
    card_prefix_for_luhn = f"{bin_prefix}{card_middle}"
    
    def luhn_checksum(card_number):
        digits = [int(d) for d in str(card_number)]
        odd_digits, even_digits = digits[-1::-2], digits[-2::-2]
        checksum = sum(odd_digits) + sum(sum(divmod(d * 2, 10)) for d in even_digits)
        return (10 - (checksum % 10)) % 10
    
    card_no = f"{card_prefix_for_luhn}{luhn_checksum(card_prefix_for_luhn)}"
    return bank_name, card_no

# --- 3. 核心API请求模块 ---
def get_common_headers(token=None):
    headers = { 
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1', 
        'Accept': 'application/json, text/plain, */*', 
        'Content-Type': 'application/json', 
        'Origin': 'https://www.wmjd5.com', 
        'Referer': 'https://www.wmjd5.com/'
    }
    if token: 
        headers['Authorization'] = f'Bearer {token}'
    return headers

def bind_bank_card_api(session, token, account_name, bank_name, bank_no):
    payload = {"name": bank_name, "account_name": account_name, "account": bank_no}
    try:
        response = session.post(BANK_URL, headers=get_common_headers(token), json=payload, timeout=10)
        return response.json().get("status", False), response.json().get("message", "未知错误")
    except Exception as e: 
        return False, str(e)

def save_bind_success(account_info, bank_info):
    """保存绑卡成功的信息"""
    try:
        output_dir = os.path.dirname(BIND_SUCCESS_FILE)
        if not os.path.exists(output_dir) and output_dir: 
            os.makedirs(output_dir)
        
        # 保存格式：手机号:密码:姓名:身份证:token:银行名:卡号:绑卡时间
        output_line = f"{account_info['phone']}:{account_info['password']}:{account_info['name']}:{account_info['id_number']}:{account_info['token']}:{bank_info['bank_name']}:{bank_info['bank_no']}:{time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        with open(BIND_SUCCESS_FILE, 'a', encoding='utf-8') as f: 
            f.write(output_line)
        print(f"  💾 \033[92m绑卡成功信息已保存至 {BIND_SUCCESS_FILE}\033[0m")
    except Exception as e: 
        print(f"  ❌ 保存绑卡信息时出错: {e}")

# --- 4. 主逻辑模块 ---
def main():
    print("=" * 60)
    print("🎯 绑卡测试模块 v1.0")
    print(f"--- 账号信息来源: {os.path.abspath(ACCOUNT_FILE)} ---")
    print(f"--- 黄金卡数据来源: {os.path.abspath(GOLDEN_CARD_FILE)} ---")
    print(f"--- 绑卡成功记录: {os.path.abspath(BIND_SUCCESS_FILE)} ---")
    print("=" * 60)
    
    # 加载数据
    if not load_accounts_from_file():
        return
    
    load_golden_cards_from_file()
    
    print(f"\n当前可用账号: {len(ACCOUNTS)} 个")
    print(f"当前黄金卡池: {len(GOLDEN_BANK_CARDS)} 张")
    
    # 选择要测试的账号
    print("\n--- 选择要测试绑卡的账号 ---")
    for i, account in enumerate(ACCOUNTS, 1):
        print(f"{i:2d}. {account['name']} ({account['phone']}) - {account['register_time']}")
    
    while True:
        try:
            choice = input(f"\n请选择账号 (1-{len(ACCOUNTS)}) 或输入 'all' 测试所有账号: ").strip()
            if choice.lower() == 'all':
                selected_accounts = ACCOUNTS
                break
            else:
                choice_num = int(choice)
                if 1 <= choice_num <= len(ACCOUNTS):
                    selected_accounts = [ACCOUNTS[choice_num - 1]]
                    break
                else:
                    print(f"请输入 1-{len(ACCOUNTS)} 之间的数字")
        except ValueError:
            print("请输入有效的数字或 'all'")
    
    # 开始绑卡测试
    for account_index, account in enumerate(selected_accounts, 1):
        print(f"\n\n--- 🚀 开始为账号 {account_index}/{len(selected_accounts)} 进行绑卡测试 ---")
        print(f"账号信息: {account['name']} ({account['phone']})")
        
        session = requests.Session()
        bound_success = False
        
        # 尝试黄金卡
        if GOLDEN_BANK_CARDS:
            print(f"  💎 [收割模式] 检测到 {len(GOLDEN_BANK_CARDS)} 张黄金卡...")
            for i, card in enumerate(GOLDEN_BANK_CARDS):
                print(f"    尝试黄金卡 {i+1}/{len(GOLDEN_BANK_CARDS)}: {card['bank_name']} - {card['bank_no']} (开户名: {account['name']})")
                success, msg = bind_bank_card_api(session, account['token'], account['name'], card['bank_name'], card['bank_no'])
                if success:
                    print(f"    🎉 \033[92m绑卡成功！\033[0m")
                    save_bind_success(account, card)
                    bound_success = True
                    break
                else:
                    print(f"    - 失败: {msg}")
                    time.sleep(0.5)
        
        # 如果黄金卡都失败，尝试生成新卡
        if not bound_success:
            print(f"  🔍 [狩猎模式] 黄金卡均失败，开始生成新卡...")
            attempt_count = 0
            max_attempts = 20  # 限制尝试次数
            
            while not bound_success and attempt_count < max_attempts:
                attempt_count += 1
                b_name, b_no = generate_random_bank_card()
                print(f"    尝试新卡 {attempt_count}/{max_attempts}: {b_name} - {b_no} (开户名: {account['name']})")
                success, msg = bind_bank_card_api(session, account['token'], account['name'], b_name, b_no)
                if success:
                    new_card = {"bank_name": b_name, "bank_no": b_no}
                    print(f"    🎉 \033[92m狩猎成功！发现新的黄金卡！\033[0m")
                    save_bind_success(account, new_card)
                    # 将成功的卡加入黄金卡池
                    if not any(c["bank_no"] == b_no for c in GOLDEN_BANK_CARDS):
                        GOLDEN_BANK_CARDS.append(new_card)
                    bound_success = True
                    break
                else:
                    print(f"    - 失败: {msg}")
                    time.sleep(1)
        
        if not bound_success:
            print(f"  ❌ \033[91m账号 {account['name']} 绑卡失败，已尝试所有方法\033[0m")
        
        # 如果还有下一个账号，稍作休息
        if account_index < len(selected_accounts):
            print(f"--- {random.randint(2,4)}秒后测试下一个账号... ---")
            time.sleep(random.randint(2, 4))
    
    print("\n\n==================== 绑卡测试完成 ====================")
    print(f"测试账号总数: {len(selected_accounts)}")
    print(f"当前黄金卡池: {len(GOLDEN_BANK_CARDS)} 张")
    print(f"绑卡成功记录保存在: {os.path.abspath(BIND_SUCCESS_FILE)}")
    print("="*60)

if __name__ == "__main__":
    main()
