import requests
import random
import string
import base64
import time
import ddddocr
import os
import json
import re

# --- 1. 配置模块 ---
CAPTCHA_URL = "https://cpdd.stchina17.com/api/v1/captcha"
REGISTER_URL = "https://cpdd.stchina17.com/api/v1/register"
VERIFY_URL = "https://cpdd.stchina17.com/api/v1/user/verify-identity"
BANK_URL = "https://cpdd.stchina17.com/api/v1/user/bank"
ACCOUNT_SAVE_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\token.txt"
IDENTITY_DATA_FILE = r"C:\Users\<USER>\Desktop\40.txt"
# 【新增】黄金卡数据文件路径
GOLDEN_CARD_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\RegisterBot\黄金卡.txt"


# 全局变量
GOLDEN_BANK_CARDS = []
IDENTITIES = []
CURRENT_IDENTITY_INDEX = 0


# --- 2. 数据与文件处理模块 ---

def load_golden_cards_from_file():
    """【新增】从黄金卡文件加载已知的银行卡信息"""
    global GOLDEN_BANK_CARDS
    if not os.path.exists(GOLDEN_CARD_FILE):
        print(f"ℹ️  提示: 未找到黄金卡文件: {GOLDEN_CARD_FILE}，将从零开始狩猎。")
        return

    print(f"正在从 {GOLDEN_CARD_FILE} 加载已发现的黄金卡...")
    loaded_cards = []
    with open(GOLDEN_CARD_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            # 解析格式：银行: xxx, 卡号: xxx
            if "银行:" in line and "卡号:" in line:
                bank_match = re.search(r"银行: (.*?),", line)
                card_match = re.search(r"卡号: (\d+)", line)

                if bank_match and card_match:
                    bank_name = bank_match.group(1).strip()
                    bank_no = card_match.group(1).strip()

                    card = {
                        "bank_name": bank_name,
                        "bank_no": bank_no
                    }

                    # 去重检查
                    if not any(c["bank_no"] == bank_no for c in loaded_cards):
                        loaded_cards.append(card)

    if loaded_cards:
        GOLDEN_BANK_CARDS = loaded_cards
        print(f"✅ 成功加载 {len(GOLDEN_BANK_CARDS)} 张不重复的黄金银行卡！")

def add_manual_golden_card():
    """手动添加黄金卡"""
    global GOLDEN_BANK_CARDS
    print("\n--- 手动添加黄金卡 ---")
    while True:
        bank_name = input("请输入银行名称 (直接回车结束添加): ").strip()
        if not bank_name:
            break
        bank_no = input("请输入银行卡号: ").strip()
        if not bank_no:
            print("银行卡号不能为空！")
            continue

        card = {"bank_name": bank_name, "bank_no": bank_no}

        # 检查是否已存在相同卡号
        if not any(c["bank_no"] == bank_no for c in GOLDEN_BANK_CARDS):
            GOLDEN_BANK_CARDS.append(card)
            print(f"✅ 已添加: {bank_name} - {bank_no}")
        else:
            print("⚠️  该银行卡号已存在！")

    if GOLDEN_BANK_CARDS:
        print(f"当前黄金卡池共有 {len(GOLDEN_BANK_CARDS)} 张银行卡")

def load_identities_from_file():
    """从 40.txt 文件加载身份信息"""
    global IDENTITIES
    if not os.path.exists(IDENTITY_DATA_FILE):
        print(f"❌ 关键错误: 找不到身份信息文件: {IDENTITY_DATA_FILE}")
        return False
    with open(IDENTITY_DATA_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if '----' in line:
                try:
                    name, id_number = line.split('----', 1)
                    if name.strip() and id_number.strip(): IDENTITIES.append({"name": name.strip(), "id_number": id_number.strip()})
                except ValueError: continue
    if not IDENTITIES:
        print(f"❌ 错误: {IDENTITY_DATA_FILE} 文件为空或格式不正确。"); return False
    print(f"✅ 已成功从 {IDENTITY_DATA_FILE} 加载 {len(IDENTITIES)} 条身份信息。"); return True

def generate_random_phone():
    prefixes = ['134', '135', '136', '137', '138', '139', '150', '151', '152', '155', '157', '158', '159', '182', '183', '187', '188', '178', '198']
    return random.choice(prefixes) + ''.join(random.choices(string.digits, k=8))
def generate_random_password(length=8):
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def generate_random_identity():
    """生成随机的姓名和身份证号码"""
    # 常见姓氏
    surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎']

    # 常见名字
    given_names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平', '刚', '桂英', '建华', '建国', '建军', '志强', '志明', '秀兰', '秀珍', '春梅', '海燕', '雪梅', '国华', '国强', '国庆', '建平', '建设', '建新', '建中', '建民', '建文', '建武', '建业', '建英', '建忠', '建州']

    # 生成随机姓名
    name = random.choice(surnames) + random.choice(given_names)

    # 生成随机身份证号码 (18位)
    # 地区码 (前6位) - 使用一些常见的地区码
    area_codes = ['110101', '110102', '110105', '110106', '110107', '110108', '110109', '110111', '120101', '120102', '120103', '120104', '120105', '120106', '120110', '120111', '130101', '130102', '130104', '130105', '130107', '130108', '130109', '130111', '140101', '140105', '140106', '140107', '140108', '140109', '140110', '140121']
    area_code = random.choice(area_codes)

    # 出生日期 (8位) - 生成1970-2000年之间的日期
    year = random.randint(1970, 2000)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 使用28避免月份天数问题
    birth_date = f"{year:04d}{month:02d}{day:02d}"

    # 顺序码 (3位) - 随机生成
    sequence = f"{random.randint(1, 999):03d}"

    # 前17位
    id_17 = area_code + birth_date + sequence

    # 计算校验码 (第18位)
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]

    id_number = id_17 + check_code

    return {"name": name, "id_number": id_number}
def generate_random_bank_card():
    # 【已恢复】您提供的最完整的银行卡生成列表
    bank_bins = [
        {'name': '工商银行', 'bin': '622202', 'len': 19}, {'name': '农业银行', 'bin': '622848', 'len': 19},
        {'name': '中国银行', 'bin': '621661', 'len': 19}, {'name': '建设银行', 'bin': '621700', 'len': 16},
        {'name': '交通银行', 'bin': '601428', 'len': 19}, {'name': '招商银行', 'bin': '622588', 'len': 16},
        {'name': '邮政储蓄银行', 'bin': '622188', 'len': 19}, {'name': '光大银行', 'bin': '622666', 'len': 16},
        {'name': '民生银行', 'bin': '622622', 'len': 16}, {'name': '平安银行', 'bin': '622155', 'len': 16},
        {'name': '浦发银行', 'bin': '622521', 'len': 16}, {'name': '中信银行', 'bin': '622688', 'len': 16},
        {'name': '兴业银行', 'bin': '622908', 'len': 18}, {'name': '华夏银行', 'bin': '622637', 'len': 16},
        {'name': '广发银行', 'bin': '622568', 'len': 16}, {'name': '深发银行', 'bin': '622526', 'len': 16},
        {'name': '上海银行', 'bin': '622892', 'len': 16}, {'name': '北京银行', 'bin': '602969', 'len': 19},
    ]
    card_info = random.choice(bank_bins)
    bank_name, bin_prefix, card_len = card_info['name'], card_info['bin'], card_info['len']
    card_middle = "".join([random.choice(string.digits) for _ in range(card_len - len(bin_prefix) - 1)])
    card_prefix_for_luhn = f"{bin_prefix}{card_middle}"
    def luhn_checksum(card_number):
        digits = [int(d) for d in str(card_number)]
        odd_digits, even_digits = digits[-1::-2], digits[-2::-2]
        checksum = sum(odd_digits) + sum(sum(divmod(d * 2, 10)) for d in even_digits)
        return (10 - (checksum % 10)) % 10
    card_no = f"{card_prefix_for_luhn}{luhn_checksum(card_prefix_for_luhn)}"
    return bank_name, card_no

# --- 3. 核心API请求模块 ---
def get_common_headers(token=None):
    headers = { 'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1', 'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'Origin': 'https://www.wmjd5.com', 'Referer': 'https://www.wmjd5.com/'}
    if token: headers['Authorization'] = f'Bearer {token}'
    return headers
def get_and_solve_captcha(session, ocr_instance):
    print("  正在获取验证码...")
    try:
        response = session.get(CAPTCHA_URL, headers=get_common_headers(), timeout=10)
        data = response.json()
        if not data.get("status"): print(f"  ❌ 验证码获取失败: {data.get('message')}"); return None, None
        captcha_key, image_b64 = data['data']['captcha_key'], data['data']['captcha_image'].split(',')[1]
        captcha_code = ocr_instance.classification(base64.b64decode(image_b64))
        print(f"  🤖 AI识别结果: {captcha_code}"); return captcha_key, captcha_code
    except Exception as e: print(f"  ❌ 验证码请求异常: {e}"); return None, None
def register_account(session, payload):
    print(f"  正在使用邀请码 {payload['invitation_code']} 进行注册...");
    try:
        response = session.post(REGISTER_URL, headers=get_common_headers(), json=payload, timeout=15)
        data = response.json()
        if data.get("status"): print("  ✅ \033[92m注册成功!\033[0m"); return data["data"]["token"]
        else: print(f"  ❌ \033[91m注册失败: {data.get('message', '未知错误')}\033[0m"); return None
    except Exception as e: print(f"  ❌ 注册请求异常: {e}"); return None
def verify_identity(session, token, name, id_number):
    print(f"    ...正在使用【{name}】进行实名认证...")
    payload = {"name": name, "idNo": id_number}
    try:
        response = session.post(VERIFY_URL, headers=get_common_headers(token), json=payload, timeout=10)
        data = response.json()
        if data.get("status"): print(f"    ✅ \033[92m【{name}】实名认证成功!\033[0m"); return True
        else: print(f"    -  \033[91m认证失败: {data.get('message', '未知错误')}，将尝试下一个身份。\033[0m"); return False
    except Exception as e: print(f"    -  \033[91m认证请求异常: {e}，将尝试下一个身份。\033[0m"); return False
def bind_bank_card_api(session, token, account_name, bank_name, bank_no):
    payload = {"name": bank_name, "account_name": account_name, "account": bank_no}
    try:
        response = session.post(BANK_URL, headers=get_common_headers(token), json=payload, timeout=10)
        return response.json().get("status", False), response.json().get("message", "未知错误")
    except Exception as e: return False, str(e)
def save_account_info(info):
    try:
        output_dir = os.path.dirname(ACCOUNT_SAVE_FILE)
        if not os.path.exists(output_dir) and output_dir: os.makedirs(output_dir)
        phone, password = info.get("phone", ""), info.get("password", "")
        json_string = json.dumps(info, ensure_ascii=False)
        output_line = f"{phone}:{password}:{json_string}\n"
        with open(ACCOUNT_SAVE_FILE, 'a', encoding='utf-8') as f: f.write(output_line)
        print(f"  💾 \033[92m账号信息已成功保存至 {ACCOUNT_SAVE_FILE}\033[0m")
    except Exception as e: print(f"  ❌ 保存文件时出错: {e}")

# --- 4. 主逻辑模块 ---
def main():
    print("=" * 60); print("🎯 鲲鹏 全自动注册 | 实名 | 绑卡 v3.9 (随机身份生成版)")
    print(f"--- 账号记录将保存在: {os.path.abspath(ACCOUNT_SAVE_FILE)} ---")
    print(f"--- 身份信息将随机生成 ---")
    print(f"--- 黄金卡数据将从: {os.path.abspath(GOLDEN_CARD_FILE)} 读取 ---"); print("=" * 60)
    
    try: ocr = ddddocr.DdddOcr(); print("✅ ddddocr库初始化成功。")
    except Exception as e: print(f"❌ ddddocr库初始化失败: {e}"); return

    print("✅ 将使用随机生成的身份信息进行认证。")
    print("ℹ️  绑卡策略：黄金卡(银行名+卡号) + 实名姓名 = 绑卡组合")
    load_golden_cards_from_file() # 尝试加载黄金卡,即使失败也不影响主流程

    # 询问是否手动添加黄金卡
    manual_add = input("是否手动添加黄金卡？(y/n): ").strip().lower()
    if manual_add in ['y', 'yes', '是']:
        add_manual_golden_card()

    invitation_code = input("请输入您的邀请码: ").strip();
    if not invitation_code: print("邀请码不能为空！"); return
    
    while True:
        try: num_to_register = int(input(f"您希望成功注册多少个账号? ").strip()); break
        except ValueError: print("请输入一个有效的数字。")
            
    successful_accounts_created = 0
    while successful_accounts_created < num_to_register:
        print(f"\n\n--- 🚀 开始执行任务，目标：成功创建第 {successful_accounts_created + 1}/{num_to_register} 个账号 ---")
        session = requests.Session()
        
        phone, password = generate_random_phone(), generate_random_password()
        captcha_key, captcha_code = get_and_solve_captcha(session, ocr)
        if not captcha_key or not captcha_code: print("   获取验证码失败，2秒后重试..."); time.sleep(2); continue
        register_payload = {"phone": phone, "password": password, "password_confirmation": password, "transaction_passcode": "147258", "transaction_passcode_confirmation": "147258", "invitation_code": invitation_code, "captcha_key": captcha_key, "captcha_value": captcha_code}
        token = register_account(session, register_payload)
        if not token: print("   注册失败，2秒后重试..."); time.sleep(2); continue
        
        # 认证与绑卡循环
        verified_identity = None
        bound_card_info = None

        print("  ▶️ [身份认证循环] 注册成功，开始使用随机生成的身份信息...")
        max_attempts = 50  # 最大尝试次数
        attempt_count = 0

        while True:
            attempt_count += 1
            if attempt_count > max_attempts:
                print(f"\n❌ 严重错误：已尝试 {max_attempts} 次随机身份生成，均认证失败！程序终止。");
                return

            # 生成随机身份信息
            identity_to_try = generate_random_identity()
            name, id_number = identity_to_try["name"], identity_to_try["id_number"]

            if verify_identity(session, token, name, id_number):
                verified_identity = identity_to_try
                break
            time.sleep(1)
        
        print("  ▶️ [无限绑卡循环] 实名认证成功，开始寻找可用的银行卡...")

        # 尝试黄金卡（使用实名认证的姓名作为开户名）
        tried_golden_cards = False
        if GOLDEN_BANK_CARDS:
            print(f"    💎 [收割模式] 检测到 {len(GOLDEN_BANK_CARDS)} 张黄金卡...")
            tried_golden_cards = True
            for i, card in enumerate(GOLDEN_BANK_CARDS):
                # 统一使用当前实名认证成功的姓名作为开户名
                account_name = verified_identity['name']
                print(f"      尝试已知卡 {i+1}/{len(GOLDEN_BANK_CARDS)}: {card['bank_name']} - {card['bank_no']} (开户名: {account_name})")
                success, msg = bind_bank_card_api(session, token, account_name, card['bank_name'], card['bank_no'])
                if success:
                    bound_card_info = card
                    print(f"    🎉 \033[92m收割成功！\033[0m")
                    break
                else:
                    print(f"      - 失败: {msg}")
                    time.sleep(0.5)
            else:
                # 如果所有黄金卡都失败，进入狩猎模式
                print(f"    ℹ️  所有黄金卡均失败，切换到狩猎模式...")
                bound_card_info = None

        # 如果黄金卡没成功，进入狩猎模式
        if 'bound_card_info' not in locals() or bound_card_info is None:
            while True:
                print(f"    🔍 [狩猎模式] {'继续' if tried_golden_cards else ''}正在生成新卡...")
                b_name, b_no = generate_random_bank_card()
                print(f"      尝试新卡: {b_name} - {b_no} (开户名: {verified_identity['name']})")
                success, msg = bind_bank_card_api(session, token, verified_identity["name"], b_name, b_no)
                if success:
                    bound_card_info = {"bank_name": b_name, "bank_no": b_no}
                    # 将成功的卡加入黄金卡池
                    if not any(c["bank_no"] == b_no for c in GOLDEN_BANK_CARDS):
                        GOLDEN_BANK_CARDS.append(bound_card_info)
                    print(f"    🎉 \033[92m狩猎成功！发现新的黄金卡，已加入卡池！\033[0m")
                    break
                else:
                    print(f"      - 失败: {msg}，1秒后继续狩猎...")
                    time.sleep(1)
        
        successful_accounts_created += 1
        print("  🎉🎉🎉 \033[92m恭喜！一个账号已完成注册、实名、绑卡全流程！\033[0m")
        account_details = {
            "phone": phone, "password": password, "name": verified_identity["name"], "id_number": verified_identity["id_number"],
            "token": token.split('|')[-1] if '|' in token else token, "full_token": token, "register_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "bound_bank_name": bound_card_info["bank_name"], "bound_bank_no": bound_card_info["bank_no"], "bound_bank_card_id": bound_card_info["id"]
        }
        save_account_info(account_details)
        if successful_accounts_created < num_to_register:
            print(f"--- 任务完成，{random.randint(3,6)}秒后开始下一个任务... ---"); time.sleep(random.randint(3, 6))

    print("\n\n==================== 所有任务已完成 ====================")
    print(f"目标注册总数: {num_to_register}\n已成功创建总数: {successful_accounts_created}")
    print(f"当前黄金卡池数量: {len(GOLDEN_BANK_CARDS)}")
    if GOLDEN_BANK_CARDS:
        for card in GOLDEN_BANK_CARDS: print(f"  - {card['bank_name']}: {card['bank_no']}")
    print(f"所有成功记录已保存至: {os.path.abspath(ACCOUNT_SAVE_FILE)}"); print("="*60)

if __name__ == "__main__":
    main()