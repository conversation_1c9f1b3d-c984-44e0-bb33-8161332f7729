#!/usr/bin/env python3
"""
IDA Pro MCP Server - Standard MCP Protocol Implementation
"""
import json
import sys
import http.client
from typing import Any, List, Dict

# Import MCP library
try:
    from mcp.server.fastmcp import FastMCP
    MCP_AVAILABLE = True
except ImportError:
    print("Error: MCP library not available. Install with: pip install mcp")
    MCP_AVAILABLE = False
    sys.exit(1)

# IDA connection settings
IDA_HOST = "127.0.0.1"
IDA_PORT = 13337

def make_ida_request(method: str, params: List[Any] = None) -> Any:
    """Make a JSON-RPC request to IDA plugin"""
    if params is None:
        params = []
    
    try:
        conn = http.client.HTTPConnection(IDA_HOST, IDA_PORT, timeout=10)
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params,
            "id": 1
        }
        
        conn.request("POST", "/mcp", json.dumps(request), {
            "Content-Type": "application/json"
        })
        
        response = conn.getresponse()
        data = json.loads(response.read().decode())
        conn.close()
        
        if "error" in data:
            return f"IDA Error: {data['error']['message']}"
        
        result = data.get("result")
        return result if result is not None else "success"
        
    except Exception as e:
        return f"Connection failed: {str(e)}"

# Create MCP server
mcp = FastMCP("IDA Pro MCP")

@mcp.tool()
def check_connection() -> str:
    """Check if the IDA plugin is running"""
    result = make_ida_request("check_connection")
    return str(result)

@mcp.tool()
def get_metadata() -> str:
    """Get metadata about the current IDB"""
    result = make_ida_request("get_metadata")
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_function_by_name(name: str) -> str:
    """Get a function by its name"""
    result = make_ida_request("get_function_by_name", [name])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_function_by_address(address: str) -> str:
    """Get a function by its address"""
    result = make_ida_request("get_function_by_address", [address])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_current_address() -> str:
    """Get the address currently selected by the user"""
    result = make_ida_request("get_current_address")
    return str(result)

@mcp.tool()
def get_current_function() -> str:
    """Get the function currently selected by the user"""
    result = make_ida_request("get_current_function")
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_functions(offset: int = 0, count: int = 10) -> str:
    """List all functions in the database (paginated)"""
    result = make_ida_request("list_functions", [offset, count])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def convert_number(text: str, size: int = 4) -> str:
    """Convert a number (decimal, hexadecimal) to different representations"""
    result = make_ida_request("convert_number", [text, size])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_globals_filter(offset: int = 0, count: int = 10, filter: str = "") -> str:
    """List matching globals in the database (paginated, filtered)"""
    result = make_ida_request("list_globals_filter", [offset, count, filter])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_globals(offset: int = 0, count: int = 10) -> str:
    """List all globals in the database (paginated)"""
    result = make_ida_request("list_globals", [offset, count])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_strings_filter(offset: int = 0, count: int = 10, filter: str = "") -> str:
    """List matching strings in the database (paginated, filtered)"""
    result = make_ida_request("list_strings_filter", [offset, count, filter])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_strings(offset: int = 0, count: int = 10) -> str:
    """List all strings in the database (paginated)"""
    result = make_ida_request("list_strings", [offset, count])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def list_local_types() -> str:
    """List all Local types in the database"""
    result = make_ida_request("list_local_types")
    if isinstance(result, list):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def decompile_function(address: str) -> str:
    """Decompile a function at the given address"""
    result = make_ida_request("decompile_function", [address])
    return str(result)

@mcp.tool()
def disassemble_function(start_address: str) -> str:
    """Get assembly code for a function"""
    result = make_ida_request("disassemble_function", [start_address])
    if isinstance(result, dict):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_xrefs_to(address: str) -> str:
    """Get all cross references to the given address"""
    result = make_ida_request("get_xrefs_to", [address])
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_xrefs_to_field(struct_name: str, field_name: str) -> str:
    """Get all cross references to a named struct field (member)"""
    result = make_ida_request("get_xrefs_to_field", [struct_name, field_name])
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def get_entry_points() -> str:
    """Get all entry points in the database"""
    result = make_ida_request("get_entry_points")
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def set_comment(address: str, comment: str) -> str:
    """Set a comment for a given address in the function disassembly and pseudocode"""
    result = make_ida_request("set_comment", [address, comment])
    return str(result)

@mcp.tool()
def rename_local_variable(function_address: str, old_name: str, new_name: str) -> str:
    """Rename a local variable in a function"""
    result = make_ida_request("rename_local_variable", [function_address, old_name, new_name])
    return str(result)

@mcp.tool()
def rename_global_variable(old_name: str, new_name: str) -> str:
    """Rename a global variable"""
    result = make_ida_request("rename_global_variable", [old_name, new_name])
    return str(result)

@mcp.tool()
def set_global_variable_type(variable_name: str, new_type: str) -> str:
    """Set a global variable's type"""
    result = make_ida_request("set_global_variable_type", [variable_name, new_type])
    return str(result)

@mcp.tool()
def rename_function(function_address: str, new_name: str) -> str:
    """Rename a function"""
    result = make_ida_request("rename_function", [function_address, new_name])
    return str(result)

@mcp.tool()
def set_function_prototype(function_address: str, prototype: str) -> str:
    """Set a function's prototype"""
    result = make_ida_request("set_function_prototype", [function_address, prototype])
    return str(result)

@mcp.tool()
def declare_c_type(c_declaration: str) -> str:
    """Create or update a local type from a C declaration"""
    result = make_ida_request("declare_c_type", [c_declaration])
    return str(result)

@mcp.tool()
def set_local_variable_type(function_address: str, variable_name: str, new_type: str) -> str:
    """Set a local variable's type"""
    result = make_ida_request("set_local_variable_type", [function_address, variable_name, new_type])
    return str(result)

# Unsafe debugging functions
@mcp.tool()
def dbg_get_registers() -> str:
    """Get all registers and their values. This function is only available when debugging."""
    result = make_ida_request("dbg_get_registers")
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def dbg_get_call_stack() -> str:
    """Get the current call stack"""
    result = make_ida_request("dbg_get_call_stack")
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def dbg_list_breakpoints() -> str:
    """List all breakpoints in the program"""
    result = make_ida_request("dbg_list_breakpoints")
    if isinstance(result, (dict, list)):
        return json.dumps(result, indent=2)
    return str(result)

@mcp.tool()
def dbg_start_process() -> str:
    """Start the debugger"""
    result = make_ida_request("dbg_start_process")
    return str(result)

@mcp.tool()
def dbg_exit_process() -> str:
    """Exit the debugger"""
    result = make_ida_request("dbg_exit_process")
    return str(result)

@mcp.tool()
def dbg_continue_process() -> str:
    """Continue the debugger"""
    result = make_ida_request("dbg_continue_process")
    return str(result)

@mcp.tool()
def dbg_run_to(address: str) -> str:
    """Run the debugger to the specified address"""
    result = make_ida_request("dbg_run_to", [address])
    return str(result)

@mcp.tool()
def dbg_set_breakpoint(address: str) -> str:
    """Set a breakpoint at the specified address"""
    result = make_ida_request("dbg_set_breakpoint", [address])
    return str(result)

@mcp.tool()
def dbg_delete_breakpoint(address: str) -> str:
    """Delete a breakpoint at the specified address"""
    result = make_ida_request("dbg_delete_breakpoint", [address])
    return str(result)

@mcp.tool()
def dbg_enable_breakpoint(address: str, enable: bool) -> str:
    """Enable or disable a breakpoint at the specified address"""
    result = make_ida_request("dbg_enable_breakpoint", [address, enable])
    return str(result)

def main():
    """Run the MCP server"""
    print("Starting IDA Pro MCP Server...")
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server stopped")
    except Exception as e:
        print(f"Server error: {e}")

if __name__ == "__main__":
    main()
