#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 账号查看脚本 - 用于查看和管理保存的账号信息

import os
from datetime import datetime

def view_accounts():
    """查看保存的账号信息"""
    account_file = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"
    
    print("📋 环球网站注册账号查看器")
    print("=" * 50)
    
    if not os.path.exists(account_file):
        print(f"❌ 账号文件不存在: {account_file}")
        print("💡 请先运行注册脚本创建账号")
        return
    
    try:
        with open(account_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("📝 账号文件为空，暂无注册账号")
            return
        
        print(f"📊 共找到 {len(lines)} 个账号记录")
        print("=" * 50)
        
        # 统计信息
        success_count = 0
        failed_count = 0
        real_auth_count = 0
        proxy_count = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 解析账号信息
                # 格式: 手机号:密码:设备名称:用户ID:Token前缀:代理IP:状态
                parts = line.split(':')
                
                if len(parts) >= 6:
                    phone = parts[0]
                    password = parts[1]
                    device = parts[2]
                    user_id = parts[3] if parts[3] != '未知' else '未获取'
                    token = parts[4]
                    proxy_ip = parts[5] if len(parts) > 5 else '直连'
                    status = parts[6] if len(parts) > 6 else '未知状态'
                    
                    print(f"📱 账号 #{i}")
                    print(f"   手机号: {phone}")
                    print(f"   密码: {password}")
                    print(f"   设备: {device}")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token}")
                    print(f"   代理IP: {proxy_ip}")
                    print(f"   状态: {status}")
                    print("-" * 40)
                    
                    # 统计
                    if '成功' in status:
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    if '实名认证成功' in status:
                        real_auth_count += 1
                    
                    if proxy_ip != '直连':
                        proxy_count += 1
                        
                else:
                    # 兼容旧格式
                    print(f"📱 账号 #{i} (旧格式)")
                    print(f"   原始数据: {line}")
                    print("-" * 40)
                    
            except Exception as e:
                print(f"❌ 解析第{i}行数据失败: {e}")
                print(f"   原始数据: {line}")
                print("-" * 40)
        
        # 显示统计信息
        print("📊 统计信息:")
        print(f"   总账号数: {len(lines)}")
        print(f"   成功账号: {success_count}")
        print(f"   失败账号: {failed_count}")
        print(f"   实名认证: {real_auth_count}")
        print(f"   使用代理: {proxy_count}")
        print(f"   直连账号: {len(lines) - proxy_count}")
        
        if success_count > 0:
            success_rate = (success_count / len(lines)) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        if real_auth_count > 0:
            auth_rate = (real_auth_count / success_count) * 100 if success_count > 0 else 0
            print(f"   实名率: {auth_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 读取账号文件失败: {e}")

def export_accounts():
    """导出账号信息为CSV格式"""
    account_file = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"
    
    if not os.path.exists(account_file):
        print("❌ 账号文件不存在")
        return
    
    try:
        # 生成导出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_file = f"accounts_export_{timestamp}.csv"
        
        with open(account_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        with open(export_file, 'w', encoding='utf-8-sig') as f:
            # 写入CSV头部
            f.write("手机号,密码,设备名称,用户ID,Token前缀,代理IP,状态\n")
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    parts = line.split(':')
                    if len(parts) >= 6:
                        # 处理包含冒号的字段
                        phone = parts[0]
                        password = parts[1]
                        device = parts[2]
                        user_id = parts[3]
                        token = parts[4]
                        proxy_ip = parts[5] if len(parts) > 5 else '直连'
                        status = ':'.join(parts[6:]) if len(parts) > 6 else '未知状态'
                        
                        f.write(f'"{phone}","{password}","{device}","{user_id}","{token}","{proxy_ip}","{status}"\n')
                    else:
                        # 旧格式，直接写入
                        f.write(f'"{line}","","","","","","旧格式数据"\n')
                        
                except Exception as e:
                    print(f"⚠️ 导出行数据失败: {e}")
        
        print(f"✅ 账号信息已导出到: {export_file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")

def clean_failed_accounts():
    """清理失败的账号记录"""
    account_file = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"
    
    if not os.path.exists(account_file):
        print("❌ 账号文件不存在")
        return
    
    try:
        with open(account_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤成功的账号
        success_lines = []
        failed_count = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为成功账号
            if '成功' in line or '注册+登录成功' in line:
                success_lines.append(line + '\n')
            else:
                failed_count += 1
        
        if failed_count == 0:
            print("✅ 没有找到失败的账号记录")
            return
        
        # 确认清理
        confirm = input(f"⚠️ 将删除 {failed_count} 个失败账号记录，是否继续? (Y/N): ").strip().upper()
        if confirm != 'Y':
            print("❌ 清理操作已取消")
            return
        
        # 备份原文件
        backup_file = account_file + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        print(f"💾 原文件已备份到: {backup_file}")
        
        # 写入清理后的数据
        with open(account_file, 'w', encoding='utf-8') as f:
            f.writelines(success_lines)
        
        print(f"✅ 清理完成，删除了 {failed_count} 个失败记录")
        print(f"📊 剩余 {len(success_lines)} 个成功账号")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def main():
    """主函数"""
    while True:
        print("\n🔧 环球网站账号管理工具")
        print("=" * 30)
        print("1. 查看所有账号")
        print("2. 导出账号信息")
        print("3. 清理失败账号")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            view_accounts()
        elif choice == '2':
            export_accounts()
        elif choice == '3':
            clean_failed_accounts()
        elif choice == '4':
            print("👋 退出账号管理工具")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
