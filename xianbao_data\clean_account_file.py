#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 整理账号密码文件 - 只保留手机号和密码
#
# 功能说明:
# - 从复杂的账号文件中提取手机号和密码
# - 删除所有JSON数据，只保留 "手机号:密码" 格式
# - 自动备份原文件
# - 支持交互式和命令行两种模式
#
# 使用方法:
# 1. 交互式模式: python clean_account_file.py
# 2. 命令行模式: python clean_account_file.py <输入文件> [输出文件]
#    例如: python clean_account_file.py "C:\Users\<USER>\Desktop\账号密码.txt"
#    例如: python clean_account_file.py "input.txt" "output.txt"

import json
import os
from datetime import datetime

def clean_account_file(input_file, output_file=None):
    """
    整理账号文件，只保留手机号和密码
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则覆盖原文件
    """
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    # 如果没有指定输出文件，先备份原文件
    if output_file is None:
        backup_file = input_file + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"📋 备份原文件到: {backup_file}")
        try:
            with open(input_file, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        except Exception as e:
            print(f"❌ 备份文件失败: {e}")
            return False
        output_file = input_file
    
    cleaned_accounts = []
    error_lines = []
    
    print(f"📖 正在读取文件: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 总共 {len(lines)} 行数据")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # 解析格式: 手机号:密码:JSON数据
                parts = line.split(':', 2)
                if len(parts) >= 2:
                    phone = parts[0].strip()
                    password = parts[1].strip()
                    
                    # 验证手机号格式 (11位数字)
                    if phone.isdigit() and len(phone) == 11:
                        cleaned_accounts.append(f"{phone}:{password}")
                        print(f"✅ 第{line_num}行: {phone}:{password}")
                    else:
                        print(f"⚠️ 第{line_num}行: 手机号格式不正确 - {phone}")
                        error_lines.append(line_num)
                else:
                    print(f"⚠️ 第{line_num}行: 格式不正确，跳过")
                    error_lines.append(line_num)
                    
            except Exception as e:
                print(f"❌ 第{line_num}行解析失败: {e}")
                error_lines.append(line_num)
        
        # 写入清理后的数据
        print(f"\n💾 正在写入清理后的数据到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            for account in cleaned_accounts:
                f.write(account + '\n')
        
        # 统计结果
        print(f"\n📊 整理完成!")
        print(f"✅ 成功处理: {len(cleaned_accounts)} 个账号")
        print(f"❌ 错误行数: {len(error_lines)} 行")
        if error_lines:
            print(f"错误行号: {error_lines}")
        
        # 显示前几个清理后的账号
        if cleaned_accounts:
            print(f"\n📋 清理后的账号示例 (前5个):")
            for i, account in enumerate(cleaned_accounts[:5], 1):
                print(f"  {i}. {account}")
            if len(cleaned_accounts) > 5:
                print(f"  ... 还有 {len(cleaned_accounts) - 5} 个账号")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理文件失败: {e}")
        return False

def interactive_clean():
    """交互式整理文件"""
    print("🧹 账号密码文件整理工具")
    print("=" * 50)
    
    # 默认文件路径
    default_file = r"C:\Users\<USER>\Desktop\账号密码.txt"
    
    print(f"默认文件路径: {default_file}")
    input_file = input("请输入文件路径 (直接回车使用默认路径): ").strip()
    
    if not input_file:
        input_file = default_file
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    print(f"\n📁 选择的文件: {input_file}")
    print("整理选项:")
    print("  1. 覆盖原文件 (会自动备份)")
    print("  2. 保存到新文件")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "2":
        output_file = input("请输入输出文件路径: ").strip()
        if not output_file:
            print("❌ 输出文件路径不能为空")
            return
    else:
        output_file = None
    
    # 执行整理
    success = clean_account_file(input_file, output_file)
    
    if success:
        print("\n🎉 文件整理完成!")
    else:
        print("\n❌ 文件整理失败!")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None

        print(f"🧹 命令行模式 - 整理文件: {input_file}")
        if output_file:
            print(f"📁 输出到: {output_file}")
        else:
            print("📁 覆盖原文件 (会自动备份)")

        success = clean_account_file(input_file, output_file)
        if success:
            print("\n🎉 文件整理完成!")
        else:
            print("\n❌ 文件整理失败!")
    else:
        # 交互式模式
        interactive_clean()
