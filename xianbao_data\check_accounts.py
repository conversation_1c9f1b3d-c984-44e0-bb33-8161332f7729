#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯 - 账号状态检查工具

import json
import time
from datetime import datetime

ACCOUNT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"

def check_account_status():
    """检查账号文件状态"""
    print("🔍 鲲鹏通讯 - 账号状态检查")
    print("=" * 50)
    
    try:
        with open(ACCOUNT_FILE, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        print(f"📋 总账号数: {len(lines)}")
        
        valid_accounts = 0
        invalid_accounts = 0
        token_info = []
        
        for i, line in enumerate(lines[:10], 1):  # 只检查前10个账号作为示例
            try:
                phone, password, data_str = line.strip().split(':', 2)
                data = json.loads(data_str)
                
                user_id = data.get("userId", "未知")
                access_token = data.get("access_token", "")
                http_key = data.get("httpKey", "")
                pay_key = data.get("payKey", "")
                
                print(f"\n📱 账号 {i}: {phone}")
                print(f"   用户ID: {user_id}")
                print(f"   访问令牌: {access_token[:20]}..." if access_token else "   访问令牌: 无")
                print(f"   HTTP密钥: {'有' if http_key else '无'}")
                print(f"   支付密钥: {'有' if pay_key else '无'}")
                
                if access_token and http_key and pay_key:
                    valid_accounts += 1
                    token_info.append({
                        "phone": phone,
                        "user_id": user_id,
                        "token_length": len(access_token),
                        "has_keys": True
                    })
                else:
                    invalid_accounts += 1
                    print(f"   ❌ 数据不完整")
                    
            except Exception as e:
                invalid_accounts += 1
                print(f"\n❌ 账号 {i} 数据格式错误: {e}")
        
        print(f"\n📊 统计信息 (前10个账号):")
        print(f"   有效账号: {valid_accounts}")
        print(f"   无效账号: {invalid_accounts}")
        
        if token_info:
            print(f"\n🔑 令牌信息:")
            for info in token_info:
                print(f"   {info['phone']}: 用户ID={info['user_id']}, 令牌长度={info['token_length']}")
        
        # 显示完整的第一个账号数据作为示例
        if lines:
            print(f"\n📋 第一个账号的完整数据示例:")
            try:
                phone, password, data_str = lines[0].strip().split(':', 2)
                data = json.loads(data_str)
                print(f"   手机号: {phone}")
                print(f"   密码: {password}")
                print(f"   数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print("   数据格式错误")
                
    except FileNotFoundError:
        print(f"❌ 找不到账号文件: {ACCOUNT_FILE}")
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def show_sample_format():
    """显示正确的账号文件格式"""
    print("\n📝 正确的账号文件格式:")
    print("=" * 40)
    
    sample_data = {
        "userId": "112746",
        "access_token": "ceeab2460f4e4e98a9ec0791adf5cec9",
        "httpKey": "base64编码的HTTP密钥",
        "payKey": "base64编码的支付密钥",
        "nickname": "用户昵称",
        "avatar": "头像URL"
    }
    
    sample_line = f"***********:password123:{json.dumps(sample_data, ensure_ascii=False)}"
    print(sample_line)
    
    print(f"\n格式说明:")
    print(f"   手机号:密码:JSON数据")
    print(f"   JSON数据必须包含: userId, access_token, httpKey, payKey")

if __name__ == "__main__":
    check_account_status()
    show_sample_format()
