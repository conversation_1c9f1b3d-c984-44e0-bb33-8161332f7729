{"version": 3, "file": "subselects.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/subselects.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,sDAAgC;AAOhC,sCAAyC;AAEzC,gFAAgF;AACnE,QAAA,mBAAmB,GAAG,EAAE,CAAC;AAEtC,SAAgB,WAAW,CACvB,IAAgC,EAChC,OAAmC;IAEnC,IAAI,IAAI,KAAK,kBAAQ,CAAC,SAAS;QAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;IAC3D,OAAO,UAAC,IAAU,IAAK,OAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAjC,CAAiC,CAAC;AAC7D,CAAC;AAND,kCAMC;AAUD,SAAgB,eAAe,CAC3B,IAAU,EACV,OAAmC;IAEnC,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IACpC,IAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,EAAE,CAAC;IAClE,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AATD,0CASC;AAED,SAAS,WAAW,CAChB,OAA2C;IAE3C,gCAAgC;IAChC,OAAO;QACH,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO;QAC1B,uBAAuB,EAAE,CAAC,CAAC,OAAO,CAAC,uBAAuB;QAC1D,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa;QACtC,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU;QAChC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY;QACpC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM;KACzB,CAAC;AACN,CAAC;AAED,IAAM,EAAE,GAAc,UAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY;IAC9D,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;IAEhE,OAAO,IAAI,KAAK,kBAAQ,CAAC,QAAQ;QAC7B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAC7B,CAAC,CAAC,kBAAQ,CAAC,SAAS;YACpB,CAAC,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAxB,CAAwB,CAAC;AAC7C,CAAC,CAAC;AAEF;;;;GAIG;AACU,QAAA,UAAU,GAA8B;IACjD,EAAE,IAAA;IACF;;OAEG;IACH,OAAO,EAAE,EAAE;IACX,KAAK,EAAE,EAAE;IACT,GAAG,YAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY;QAC3C,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;QAEhE,OAAO,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAC9B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,KAAK,kBAAQ,CAAC,QAAQ;gBAC5B,CAAC,CAAC,kBAAQ,CAAC,SAAS;gBACpB,CAAC,CAAC,UAAC,IAAI,IAAK,OAAA,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAzB,CAAyB,CAAC;IAC9C,CAAC;IACD,GAAG,EAAH,UACI,IAAgC,EAChC,SAAuB,EACvB,OAA2C,EAC3C,QAA4B,EAC5B,YAA6C;QAErC,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QAE5B,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAE7B,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,qBAAW,CAAC,EAAnB,CAAmB,CAAC;YACtD,CAAC,CAAC,mEAAmE;gBAClE,CAAC,2BAAmB,CAA8B;YACrD,CAAC,CAAC,SAAS,CAAC;QAEhB,IAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,QAAQ,KAAK,kBAAQ,CAAC,SAAS;YAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;QAE/D,IAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAElD,iDAAiD;QACjD,IAAI,OAAO,IAAI,QAAQ,KAAK,kBAAQ,CAAC,QAAQ,EAAE;YAC3C;;;eAGG;YACK,IAAA,KAAmC,QAAQ,uBAAb,EAA9B,wBAAsB,mBAAG,KAAK,KAAA,CAAc;YAEpD,OAAO,UAAC,IAAI;gBACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAE,OAAO,KAAK,CAAC;gBAE9B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAClB,IAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAM,YAAY,GAAG,wBAAsB;oBACvC,CAAC,iCAAK,MAAM,SAAK,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,QAC/C,CAAC,CAAC,MAAM,CAAC;gBAEb,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YACvD,CAAC,CAAC;SACL;QAED,OAAO,UAAC,IAAI;YACR,OAAA,IAAI,CAAC,IAAI,CAAC;gBACV,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QADxD,CACwD,CAAC;IACjE,CAAC;CACJ,CAAC"}