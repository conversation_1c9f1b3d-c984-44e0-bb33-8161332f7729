import os
from datetime import datetime

# ======================= 增强版配置文件 =======================

# --- 爬虫配置 ---
FORUM_URLS = [
    {"name": "推荐热帖", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=225&filter=lastpost&orderby=lastpost", "priority": "high"},
    {"name": "羊毛专区", "url": "https://www.51kanong.com/yh-282-1.htm", "priority": "high"},
    {"name": "老哥生活", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=2603", "priority": "medium"},
    {"name": "数藏专区", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=15", "priority": "low"},
    {"name": "下款线报", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=23", "priority": "high"},
    {"name": "羊毛交流", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=169&filter=lastpost&orderby=lastpost", "priority": "medium"},
]

# --- 高级爬虫配置 ---
# 请求间隔配置（秒）
REQUEST_DELAYS = {
    "between_posts": 2,      # 帖子间延时
    "between_forums": 5,     # 板块间延时
    "ai_analysis": 10,       # AI分析间延时
}

# 内容过滤配置
CONTENT_FILTERS = {
    "min_content_length": 50,        # 最小内容长度
    "max_content_length": 10000,     # 最大内容长度
    "skip_keywords": ["广告", "推广", "代理"],  # 跳过包含这些关键词的帖子
    "priority_keywords": ["羊毛", "优惠", "活动", "福利", "免费"],  # 优先处理包含这些关键词的帖子
}

# --- AI 分析配置 ---
GEMINI_API_HOST = "https://generativelanguage.googleapis.com"
GEMINI_API_KEY = "AIzaSyBAapmvw5zq9s3ro2EjGH4NgY0BNhAODgw"

# 增强版AI提示词 - 更精准的分析
AI_PROMPT_TEMPLATE = """
作为专业的羊毛价值分析师，请分析以下论坛帖子并提供结构化评估。

**分析维度:**
1. **价值等级**: 🔥极高价值 | ⭐高价值 | 👍中等价值 | 👎低价值 | ❌无价值
2. **活动类型**: 现金红包/优惠券/实物奖品/积分兑换/其他
3. **参与难度**: 简单(1-2步) | 中等(3-5步) | 复杂(5+步)
4. **时效性**: 长期有效 | 近期截止 | 即将过期 | 已过期
5. **适用人群**: 所有用户 | 新用户专享 | 特定地区 | 特定条件

**输出格式:**
```
🎯 **核心价值**: [一句话总结]
📊 **价值评分**: [0-10分] - [评分理由]
🎁 **福利详情**: [具体奖励内容]
📱 **参与方式**: [详细步骤]
⚠️  **限制条件**: [参与限制]
⏰ **时效提醒**: [有效期信息]
💡 **操作建议**: [具体建议]
```

**原始内容:**
标题: {title}
内容: {content}
"""

# --- 邮件配置 ---
SMTP_SERVER = "smtp.qq.com"
SMTP_PORT = 465
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "djppyahldzyqdcdb"
RECEIVER_EMAIL = "<EMAIL>"

# 邮件模板配置
EMAIL_TEMPLATES = {
    "daily_report": {
        "subject_template": "🎯 每日羊毛简报 ({date}) - {count}个优质机会",
        "priority_threshold": 6,  # 只有评分>=6的才发送邮件
    },
    "urgent_alert": {
        "subject_template": "🔥 紧急羊毛提醒 - {title}",
        "score_threshold": 8,     # 评分>=8的立即发送
    }
}

# --- 数据存储配置 ---
DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

SEEN_POSTS_FILE = os.path.join(DATA_DIR, 'seen_posts_51kanong.txt')
ANALYSIS_LOG_FILE = os.path.join(DATA_DIR, f'analysis_log_{datetime.now().strftime("%Y%m")}.json')
STATS_FILE = os.path.join(DATA_DIR, 'monthly_stats.json')

# --- 性能优化配置 ---
PERFORMANCE_CONFIG = {
    "max_concurrent_requests": 3,    # 最大并发请求数
    "request_timeout": 30,           # 请求超时时间
    "retry_attempts": 3,             # 重试次数
    "cache_duration": 3600,          # 缓存时间（秒）
}

# --- 调试配置 ---
DEBUG_CONFIG = {
    "enable_debug": False,           # 是否启用调试模式
    "save_raw_html": False,          # 是否保存原始HTML
    "verbose_logging": True,         # 详细日志
}
