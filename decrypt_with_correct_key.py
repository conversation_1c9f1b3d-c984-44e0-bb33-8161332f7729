#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 使用正确密钥解密sign数据

import json
import base64
from urllib.parse import unquote
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def decrypt_sign_with_key():
    """使用正确的密钥解密sign数据"""
    print("🔍 使用正确密钥解密sign数据")
    print("=" * 50)
    
    # 你提供的sign数据
    raw_data = "sign=%7B%22ct%22%3A%22d8%2B1K3VFqOWPoZxFeaY4GePDAkLSXRsLIxmO%2FJOwRUgNcmwpHmUCxqtSpUrppoGm%22%2C%22iv%22%3A%22fc959df3b5cbe47782e6c267450b5795%22%2C%22s%22%3A%228802485aa551eada%22%7D"
    
    # URL解码
    decoded_data = unquote(raw_data)
    sign_part = decoded_data.replace('sign=', '')
    
    print(f"📝 Sign数据: {sign_part}")
    
    # 解析JSON
    sign_json = json.loads(sign_part)
    ct = sign_json['ct']
    iv = sign_json['iv']
    s = sign_json['s']
    
    print(f"📝 加密参数:")
    print(f"   ct (密文): {ct}")
    print(f"   iv (初始向量): {iv}")
    print(f"   s (盐值): {s}")
    
    # 从JavaScript代码中找到的密钥
    key = "v4NTEx37"
    print(f"🔐 使用密钥: {key}")
    
    try:
        # 密钥需要是16字节，如果不足则填充
        key_bytes = key.encode('utf-8')
        if len(key_bytes) < 16:
            key_bytes = key_bytes.ljust(16, b'0')  # 用0填充到16字节
        elif len(key_bytes) > 16:
            key_bytes = key_bytes[:16]  # 截取前16字节
        
        print(f"🔐 处理后的密钥: {key_bytes} (长度: {len(key_bytes)})")
        
        # 解密
        iv_bytes = bytes.fromhex(iv)
        ct_bytes = base64.b64decode(ct)
        
        cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)
        decrypted = unpad(cipher.decrypt(ct_bytes), AES.block_size)
        decrypted_text = decrypted.decode('utf-8')
        
        print(f"✅ 解密成功!")
        print(f"📝 解密结果: {decrypted_text}")
        
        # 解析JSON
        try:
            decrypted_json = json.loads(decrypted_text)
            print(f"📋 解密的JSON数据:")
            print(f"   {json.dumps(decrypted_json, ensure_ascii=False, indent=2)}")
            
            # 分析密码
            if 'password' in decrypted_json:
                password = decrypted_json['password']
                username = decrypted_json.get('username', '未知')
                
                print(f"\n🔐 账号信息分析:")
                print(f"   用户名: {username}")
                print(f"   密码: {password}")
                print(f"   密码长度: {len(password)}")
                print(f"   包含小写字母: {any(c.islower() for c in password)}")
                print(f"   包含大写字母: {any(c.isupper() for c in password)}")
                print(f"   包含数字: {any(c.isdigit() for c in password)}")
                print(f"   只包含字母数字: {password.isalnum()}")
                
                # 分析密码格式
                print(f"\n💡 密码格式分析:")
                if password.isdigit():
                    print(f"   格式: 纯数字")
                elif password.isalpha():
                    print(f"   格式: 纯字母")
                elif password.isalnum():
                    has_upper = any(c.isupper() for c in password)
                    has_lower = any(c.islower() for c in password)
                    has_digit = any(c.isdigit() for c in password)
                    
                    format_parts = []
                    if has_upper:
                        format_parts.append("大写字母")
                    if has_lower:
                        format_parts.append("小写字母")
                    if has_digit:
                        format_parts.append("数字")
                    
                    print(f"   格式: {' + '.join(format_parts)}")
                else:
                    print(f"   格式: 包含特殊字符")
                
                return password
            else:
                print("⚠️ 解密结果中没有找到password字段")
                return None
                
        except json.JSONDecodeError:
            print(f"⚠️ 解密结果不是有效的JSON格式")
            return decrypted_text
            
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return None

def compare_with_our_password():
    """对比我们生成的密码"""
    print(f"\n🔍 密码格式对比")
    print("=" * 50)
    
    # 我们当前生成的密码示例
    our_password = "y7ojohhkAt"
    
    print(f"📝 我们的密码: {our_password}")
    print(f"   长度: {len(our_password)}")
    print(f"   格式: 小写字母 + 大写字母 + 数字")
    
    print(f"\n💡 如果解密成功，可以看到真实的密码格式")
    print(f"💡 然后调整我们的密码生成策略")

def update_password_generation_strategy(real_password):
    """根据真实密码更新生成策略"""
    if not real_password:
        return
    
    print(f"\n🔧 密码生成策略建议")
    print("=" * 50)
    
    if real_password.isdigit():
        print(f"✅ 建议策略: 生成6-11位纯数字密码")
        print(f"   示例: 123456, 1234567890")
    elif real_password.isalpha() and real_password.islower():
        print(f"✅ 建议策略: 生成6-11位纯小写字母密码")
        print(f"   示例: abcdef, password")
    elif real_password.isalnum():
        has_upper = any(c.isupper() for c in real_password)
        has_lower = any(c.islower() for c in real_password)
        has_digit = any(c.isdigit() for c in real_password)
        
        if has_lower and has_digit and not has_upper:
            print(f"✅ 建议策略: 生成小写字母+数字混合密码")
            print(f"   示例: abc123, password1")
        elif has_upper and has_lower and has_digit:
            print(f"✅ 建议策略: 生成大小写字母+数字混合密码")
            print(f"   示例: Abc123, Password1")
        else:
            print(f"✅ 建议策略: 模仿真实密码的格式")
    
    print(f"\n🔧 修改建议:")
    print(f"1. 在zhuccc1.py中修改generate_random_password()函数")
    print(f"2. 按照真实密码的格式生成密码")
    print(f"3. 测试新的密码格式")

def main():
    """主函数"""
    print("🔍 Sign数据解密 - 使用正确密钥")
    print("🔧 密钥来源: JavaScript代码中的 'v4NTEx37'")
    print("=" * 60)
    
    # 解密数据
    real_password = decrypt_sign_with_key()
    
    # 对比密码
    compare_with_our_password()
    
    # 更新策略建议
    update_password_generation_strategy(real_password)
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    if real_password:
        print("✅ 解密成功，找到了真实的密码格式")
        print("💡 现在可以根据真实格式调整密码生成策略")
        print("🔧 修改generate_random_password()函数")
    else:
        print("❌ 解密失败，可能需要进一步分析")
        print("💡 检查密钥是否正确或加密算法实现")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 解密被用户中断")
    except Exception as e:
        print(f"\n💥 解密过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
