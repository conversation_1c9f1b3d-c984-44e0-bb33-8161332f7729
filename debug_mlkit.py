#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MLKit OCR 调试脚本
用于诊断Google Cloud Vision API连接问题
"""

import os
import sys

def test_google_cloud_vision():
    """测试Google Cloud Vision API"""
    print("🔍 测试Google Cloud Vision API...")
    
    try:
        from google.cloud import vision
        print("✅ google.cloud.vision 导入成功")
    except ImportError as e:
        print(f"❌ google.cloud.vision 导入失败: {e}")
        print("💡 请运行: pip install google-cloud-vision")
        return False
    
    # 检查凭证文件
    credentials_path = "google-cloud-credentials.json"
    if os.path.exists(credentials_path):
        print(f"✅ 凭证文件存在: {credentials_path}")
    else:
        print(f"❌ 凭证文件不存在: {credentials_path}")
        return False
    
    # 设置环境变量
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
    print(f"✅ 环境变量已设置: GOOGLE_APPLICATION_CREDENTIALS")
    
    # 测试客户端初始化
    try:
        client = vision.ImageAnnotatorClient()
        print("✅ Vision API 客户端初始化成功")
    except Exception as e:
        print(f"❌ Vision API 客户端初始化失败: {e}")
        return False
    
    return True

def test_mlkit_ocr_module():
    """测试MLKit OCR模块"""
    print("\n🔍 测试MLKit OCR模块...")
    
    try:
        from mlkit_ocr import MLKitOCR
        print("✅ MLKitOCR 类导入成功")
    except ImportError as e:
        print(f"❌ MLKitOCR 类导入失败: {e}")
        return False
    
    # 初始化MLKit OCR
    try:
        ocr = MLKitOCR()
        print(f"✅ MLKitOCR 初始化成功，可用状态: {ocr.is_available}")
        return ocr.is_available
    except Exception as e:
        print(f"❌ MLKitOCR 初始化失败: {e}")
        return False

def test_simple_ocr():
    """测试简单OCR功能"""
    print("\n🔍 测试简单OCR功能...")
    
    # 查找测试图片
    test_images = [
        "id_card_sorted/正面/孟巧英甘肃村1956.jpg",
        "id_card_sorted/正面",
    ]
    
    test_image = None
    for img_path in test_images:
        if os.path.exists(img_path):
            if os.path.isfile(img_path):
                test_image = img_path
                break
            elif os.path.isdir(img_path):
                # 查找目录中的第一张图片
                for file in os.listdir(img_path):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        test_image = os.path.join(img_path, file)
                        break
                if test_image:
                    break
    
    if not test_image:
        print("❌ 未找到测试图片")
        return False
    
    print(f"📷 使用测试图片: {test_image}")
    
    try:
        from mlkit_ocr import recognize_id_card_simple
        name, id_number = recognize_id_card_simple(test_image)
        
        if name and id_number:
            print(f"✅ OCR识别成功:")
            print(f"   姓名: {name}")
            print(f"   身份证号: {id_number}")
            return True
        else:
            print(f"⚠️ OCR识别不完整:")
            print(f"   姓名: {name or '未识别'}")
            print(f"   身份证号: {id_number or '未识别'}")
            return False
            
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 MLKit OCR 调试诊断")
    print("=" * 50)
    
    # 测试1: Google Cloud Vision API
    vision_ok = test_google_cloud_vision()
    
    # 测试2: MLKit OCR模块
    mlkit_ok = test_mlkit_ocr_module()
    
    # 测试3: 简单OCR功能
    if vision_ok and mlkit_ok:
        ocr_ok = test_simple_ocr()
    else:
        ocr_ok = False
    
    # 总结
    print("\n📊 诊断结果:")
    print("=" * 50)
    print(f"Google Cloud Vision API: {'✅ 正常' if vision_ok else '❌ 异常'}")
    print(f"MLKit OCR 模块: {'✅ 正常' if mlkit_ok else '❌ 异常'}")
    print(f"OCR 识别功能: {'✅ 正常' if ocr_ok else '❌ 异常'}")
    
    if vision_ok and mlkit_ok and ocr_ok:
        print("\n🎉 所有测试通过！MLKit OCR 可以正常使用")
    else:
        print("\n❌ 存在问题，请检查上述错误信息")
        
        # 提供解决建议
        print("\n💡 解决建议:")
        if not vision_ok:
            print("1. 安装Google Cloud Vision API: pip install google-cloud-vision")
            print("2. 检查凭证文件是否正确")
            print("3. 确保Google Cloud项目已启用Vision API")
        
        if not mlkit_ok:
            print("4. 检查mlkit_ocr.py文件是否存在")
            print("5. 检查模块导入路径")
        
        if not ocr_ok:
            print("6. 检查测试图片是否存在")
            print("7. 检查网络连接")

if __name__ == "__main__":
    main()
