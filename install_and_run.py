#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动安装依赖并运行注册脚本
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖包"""
    print("🔧 开始安装依赖包...")
    
    # 升级pip
    subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
    
    # 安装依赖
    requirements = [
        "requests==2.31.0",
        "pycryptodome==3.19.0", 
        "selenium==4.15.2",
        "webdriver-manager==4.0.1",
        "opencv-python==********",
        "pillow==10.0.1",
        "numpy==1.24.3",
        "pyautogui==0.9.54",
        "ddddocr==1.4.11"
    ]
    
    for package in requirements:
        try:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except Exception as e:
            print(f"❌ {package} 安装失败: {e}")
    
    print("✅ 依赖包安装完成！")

def download_chromedriver():
    """下载ChromeDriver"""
    print("🚗 检查ChromeDriver...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # 自动下载并设置ChromeDriver
        service = Service(ChromeDriverManager().install())
        print("✅ ChromeDriver 准备完成")
        return True
    except Exception as e:
        print(f"❌ ChromeDriver 设置失败: {e}")
        return False

def main():
    print("🎯 自动注册系统安装程序")
    print("=" * 50)
    
    # 1. 安装依赖
    try:
        install_requirements()
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        return
    
    # 2. 设置ChromeDriver
    if not download_chromedriver():
        print("⚠️ ChromeDriver设置失败，请手动下载Chrome浏览器")
        return
    
    # 3. 运行主程序
    print("\n🚀 启动自动注册程序...")
    try:
        from auto_register_gui import AutoRegisterGUI
        
        auto_register = AutoRegisterGUI()
        
        while True:
            print("\n🎮 请选择操作:")
            print("1. 单个账号注册")
            print("2. 批量注册")
            print("3. 查看已注册账号")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == '1':
                auto_register.auto_register()
            elif choice == '2':
                count = int(input("请输入注册数量: "))
                auto_register.batch_register(count)
            elif choice == '3':
                try:
                    import json
                    with open('registered_accounts.json', 'r', encoding='utf-8') as f:
                        accounts = json.load(f)
                    print(f"\n📋 已注册账号 ({len(accounts)} 个):")
                    for i, account in enumerate(accounts, 1):
                        print(f"{i}. 手机号: {account['phone']}, 密码: {account['password']}, 时间: {account.get('register_time', '未知')}")
                except:
                    print("📋 暂无已注册账号")
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")

if __name__ == "__main__":
    main()
