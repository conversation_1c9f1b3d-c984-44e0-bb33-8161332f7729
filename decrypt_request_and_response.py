#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 解密鲲鹏通讯登录流程的三个请求与响应

import base64
import json
import urllib.parse
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# 登录流程的三个请求数据
LOGIN_REQUESTS = {
    "request1": {
        "name": "获取登录验证码",
        "url": "http://**************:8095/auth/getLoginCode",
        "method": "POST",
        "params": {
            "areaCode": "92",
            "deviceId": "android",
            "account": "***********",
            "mac": "8mHfI4nGgZ5GnhO4o+G3EA==",
            "language": "zh",
            "salt": "*************",
            "secret": "Gv1xpOZ3OW+W2BOuN4MWsA=="
        },
        "response": {
            "currentTime": *************,
            "data": {
                "code": "yZmXRAV1z0ggOkNBNr9hRO+HZCT4lokG5TYIqo6SeeyDSUGeOg0c0e0N5Q/nWRENCRUyooo3QJPctlh4LZOVYwzpFr/V/mqXJUb9gnvjRCOW0v1joynccpTUZyhP95m0VIAIo84kaw3tHhTXsa7dBsWheUImWfLJvm/NcqxHIIk=",
                "userId": "107477"
            },
            "resultCode": 1
        }
    },
    "request2": {
        "name": "获取登录私钥",
        "url": "http://**************:8095/authkeys/getLoginPrivateKey",
        "method": "POST",
        "params": {
            "userId": "107477",
            "mac": "MEm0lcY/LeDJUqouB/Efdw==",
            "language": "zh",
            "salt": "*************",
            "secret": "o9Ay9s56Oe0CghSyODARKw=="
        },
        "response": {
            "currentTime": 1753674049627,
            "data": {
                "privateKey": "Ua9Eq5K+Ik05osXtW/vANyxPK3uiAXh4DlKi/sTLdiHzOZWsLab3Vusu0xbNR6tqzbJ9wTIh+/xBdz/+3grsLcSFr5l8w6F9dUr13hp+9QccauYMHPF/HWiZQoipUtBGAUZw9igMGSXJD62uDRYgIjcUxGk5DloTVr2NgKC/iq6BsOoLYupmO47ohGU6HQnzNsrL6WJkNZrpBsFayeeLE+q6F/oS89Yt6cvv2cKZx38klYoR2KDEoOGOTk0ANsX9P6JNtmvxpLiRvZhzHwA3uLrykzW8fHTKQPu7PGjO1PRguNqXu7mdX4BYuDugaERlvZGjB1qiAxeg5V053rSDyDkrQTw1UhiOXz69944YeDhrDle/x3k9JLGqupuNNrN3DUnzuyBJ9qohWDkpRW+IzMxI9t/UZ2WQKQv8dNO52pR8LOL3OU8E0aHmgAhHwdM6Ksvtf9y6nJ5d1CEAvXpPpFp3bOUWiqWKokkWNn9FMjXbqecRcZrzvIjZ3BoiN0oEOrnzZllAUJ8vfNy25tebJIJxFjMvCXOo/SRyBSvBFkkY1+lci0jHEUhgJA1yInSEH2bS4vlgei3b9Ng/UdsJGWC1WXis+QJLadEwCnDhFZe19THJ6d1QUpyXdbAjpP61nDuRv266h74ZnMbMUo3TDIw56lBdHG9w9qZi3FtCquxOM8tELc1RybZGPRYt+LGBxHI7kXXe54TBHXeuUIvZ6/bk1GjltMAgkFHdFoi0TjHYZBm+FNAqOnCNfcNHBkOmaStF5824imQ/b9Pu6BchXCpjYIhKMoabHTCYiWf2IB8VZRdhXbMo3XGQkQsoVaIe"
            },
            "resultCode": 1
        }
    },
    "request3": {
        "name": "执行登录",
        "url": "http://**************:8095/user/login/v1?data=60GQxFZcKvJa67P7uo1ujiwnK70P0YpV1y63XtlhsjyQGqybTChHe7kAFyX0d%2Fn9%2BAUeBqp7Yf%2FtygavwlI6yOMfzpVZ3n755yNzp4iRMzJciq%2BPTM4Hq8HTXL8ijK6BHgEVA%2BwUTnrodEIyKCB%2FzkgBkNp6IylW1a4dmQAJrkRC5xjkwvb8E%2F8%2B6iFVNMm2&userId=107477&deviceId=android&language=zh&salt=1753674050662&secret=amVchC6qgzROOrvRk42b4A%3D%3D",
        "method": "GET",
        "response": {
            "currentTime": 1753674049833,
            "data": {
                "data": "M48ih3EwPHysqVzgNQAJFsqj8NKt7GQH0mTPafIACncIP524OicHs6ZqA/QxZhlAfaa8zm1RJ/tC9LgSxiEQwLOo5lMMkPnPa+0gseeK4R6hhy5YBVR9U6oMMs1jxbmkSFOv2X3D/7SsOi0tB3+DmNYWso4+LM7euciD1qX+EjgBe5/Hhxho9oXU7O0fBryRUN70sqHp+8LFPa6kW95c/qok+1HBpjzm208ZKe8OwKHybq5+P1TYeSmHfI3yMRTLXNY5ExrHEHdni4yhmXvrDsGS6pGHzKmzBYCeiUMZg1pT6LKenPUCUcnZ+AZI7H3YcwkjqAdLksvGBvvN1LBlPLcD1TnAu6vE6SJnoZJlk5Yi4FyipuE8xVBdsLa/6R1FXzZOEFpHcXoCQm8U0V5epDYxNEPm+H8QrbGfJf79/vm0tIpkLM8jU//jxLuJXF4YGpPODnRNuwvTiMDlk2q3zMKqvO6mZi+kzY+47Bdj/H8HYBoa2syfopWWMXtz/cdX9f+tK17FuaWxBf5VePEPZ/UH1Wy00nLup0j4N/NQCbocf3zo7BA8dybwaTVzMdkXa9qYKaszAaO1UyVr7cuVwIGG1rjiUGHkk0toM68wO/KKAapJ8SgYUyqBus82iq8JEjupFIfQNhspiW6lxCXtPTAURmLd7jTADIGxqi6YWlOE3Dt6lniPwoYgDQNCJcdYGgLWHFiP+NxJl86L5NkJA3m7MbssGelP9vMMAhae9Sz+o/hGk6UkEfyAqOCc7fJukz3dP9JSRGPU95zte/wMpDfw4VXN9uLHcdAoVV7q/ZquR4Q2HS7tXKCo/45z5fo6c+II4nuIyHoRPuJqMdWbDEAlGC5Dgx72lK+KA68kMMIBwoI0YrdpU6LQroKEWSJMufzSTmc5uz9FgydbvVzYKm838moGIXgTN8fMviQ2v6ZSyRGRagRAKPBhG/tPbgPJ+rGF/CkPExFRAUrxcN/withChR46Haywheg5NQYpMBm+HygBuWbPmsLakTLWsvwKEFKv9wNQl0ZuxmQbOSpTxGbxM09xNY7jKVo+87RoDViufqtHWBRfrGn9IWr156zdhbQH6bAAolZy72tLWXFex/VSwSKK9beS/nIiQobkYiYyc8KxWbgVaOrWVB6Ei6obQ8UngwdciVQSJwnybzW5Uu3kDz18M61y9jU8blvmagH0tEZIXefw74scI6tqzYJMFKfD/89Pt/QqYeLFo1rl6XiW06oLZQOHbl/mHt015zn2RCzUuLvoZSGJiw5lGcD9+AJe0iDcluOpGsXlqm5+eXpofI0wblejlZLlnE4CD+5mUQyY+fnV13D3Xx3rjGNwbBvVg1YH8li0eKUJcvTeX0558f/KzUSAnVCdrES0SFW6lVq3/MD0DNEXf6biyqJM1otTel1IJ3ZLptYpYVUwnlE9eVvndENpLIbrkONtk1yzJ/vmsNYdbMkn1tsVihlsIaXdMaK8WVwd9EOriOFkMqXi5xJDDDwUGq1aOJ5Goq8QPJjA8NhB5nUUdNjb71KhjyZ0vNOCveBMY9ps2UazNkWy4FOqPzENwG/Glguyl10="
            },
            "resultCode": 1
        }
    }
}

def decrypt_aes_data(encrypted_data_b64, description="数据"):
    """使用AES-CBC解密数据"""
    try:
        # 使用全局固定密钥和IV
        key = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
        iv = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")

        print(f"密钥 (全局固定): {key.hex()}")
        print(f"IV (全局固定): {iv.hex()}")

        ciphertext = base64.b64decode(encrypted_data_b64)
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_padded_bytes = cipher.decrypt(ciphertext)
        decrypted_bytes = unpad(decrypted_padded_bytes, AES.block_size)

        # 尝试解析为JSON
        try:
            result = json.loads(decrypted_bytes.decode('utf-8'))
            print(f"\n>>> {description}解密成功! <<<\n")
            print(f"{description}明文内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result
        except:
            # 如果不是JSON，返回原始字符串
            text = decrypted_bytes.decode('utf-8', errors='ignore')
            print(f"\n>>> {description}解密成功! <<<\n")
            print(f"{description}明文内容:")
            print(text)
            return text

    except Exception as e:
        print(f"\n{description}解密失败: {e}")
        return None

def analyze_request_1():
    """分析请求1: 获取登录验证码"""
    print("🔍 请求1: 获取登录验证码")
    print("=" * 60)

    request_data = LOGIN_REQUESTS["request1"]

    print("📤 请求参数:")
    for key, value in request_data["params"].items():
        print(f"   {key}: {value}")

    print("\n📥 响应数据:")
    response = request_data["response"]
    print(f"   resultCode: {response['resultCode']}")
    print(f"   userId: {response['data']['userId']}")
    print(f"   code: {response['data']['code'][:50]}... (已截断)")

    # 尝试解密响应中的code
    print(f"\n🔐 尝试解密响应中的验证码...")
    encrypted_code = response['data']['code']
    return decrypt_aes_data(encrypted_code, "验证码")

def analyze_request_2():
    """分析请求2: 获取登录私钥"""
    print(f"\n🔍 请求2: 获取登录私钥")
    print("=" * 60)

    request_data = LOGIN_REQUESTS["request2"]

    print("📤 请求参数:")
    for key, value in request_data["params"].items():
        print(f"   {key}: {value}")

    print("\n📥 响应数据:")
    response = request_data["response"]
    print(f"   resultCode: {response['resultCode']}")
    print(f"   privateKey: {response['data']['privateKey'][:50]}... (已截断)")

    # 尝试解密响应中的privateKey
    print(f"\n🔐 尝试解密响应中的私钥...")
    encrypted_key = response['data']['privateKey']
    return decrypt_aes_data(encrypted_key, "私钥")

def analyze_request_3():
    """分析请求3: 执行登录"""
    print(f"\n🔍 请求3: 执行登录")
    print("=" * 60)

    request_data = LOGIN_REQUESTS["request3"]

    # 从URL中提取参数
    parsed = urllib.parse.urlparse(request_data["url"])
    params = urllib.parse.parse_qs(parsed.query)

    print("📤 请求参数:")
    for key, values in params.items():
        value = values[0] if values else ""
        if key == "data":
            print(f"   {key}: {value[:50]}... (已截断)")
        else:
            print(f"   {key}: {value}")

    print("\n📥 响应数据:")
    response = request_data["response"]
    print(f"   resultCode: {response['resultCode']}")
    print(f"   data: {response['data']['data'][:50]}... (已截断)")

    # 尝试解密请求中的data参数
    print(f"\n🔐 尝试解密请求中的data参数...")
    encrypted_request_data = urllib.parse.unquote(params['data'][0])
    request_result = decrypt_aes_data(encrypted_request_data, "请求数据")

    # 尝试解密响应中的data
    print(f"\n🔐 尝试解密响应中的data...")
    encrypted_response_data = response['data']['data']
    response_result = decrypt_aes_data(encrypted_response_data, "响应数据")

    return request_result, response_result

def main():
    print("🔍 鲲鹏通讯 - 登录流程解密工具")
    print("=" * 60)

    # 分析三个请求
    code_result = analyze_request_1()
    key_result = analyze_request_2()
    login_request_result, login_response_result = analyze_request_3()

    print(f"\n🎯 解密总结:")
    print(f"   请求1 (验证码): {'✅ 成功' if code_result else '❌ 失败'}")
    print(f"   请求2 (私钥): {'✅ 成功' if key_result else '❌ 失败'}")
    print(f"   请求3 (登录请求): {'✅ 成功' if login_request_result else '❌ 失败'}")
    print(f"   请求3 (登录响应): {'✅ 成功' if login_response_result else '❌ 失败'}")

    if login_response_result:
        print(f"\n💡 重要发现:")
        print(f"   如果请求3的响应解密成功，那么里面应该包含新的")
        print(f"   access_token、httpKey、payKey等信息，可以用来")
        print(f"   更新过期的令牌！")

if __name__ == '__main__':
    main()