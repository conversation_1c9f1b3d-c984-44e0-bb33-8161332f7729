#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# --- “鲲鹏通讯”响应数据独立解密工具 (最终修正版 V2) ---
#
# 描述:
#   修复了由于错误假设导致的“Incorrect padding”问题。
#   本脚本实现了最终确定的、混合模式的解密逻辑。
#
# 使用方法:
#   1. 确保已安装 pycryptodome 库 (pip install pycryptodome)
#   2. 将下面 ENCRYPTED_DATA_B64 和 REQUEST_SECRET_B64 的值，
#      替换成您成功注册那次日志中对应的值。
#   3. 运行此脚本 (python decrypt_final.py)
#
#############################################################

import base64
import json
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import unpad

# --- 请在这里填入您成功注册那次的数据 ---

# 1. 这是服务器返回的、需要解密的 data.data 的值
# (来自您的日志: "currentTime": 1753531343481)
ENCRYPTED_DATA_B64 = "ZNP7H0h33DspulnqkOTV11qpkHMt4ucvVWw7V1CCMEraB1r/k6zqCv6ViTEl+vuoqFsPWu84kkMUlD4Y47VvVXqMdd+mqmDFRBoPU07VFwm5n/Ifovif94WMpHcmC1kZM4XMKwTgaQzTc0XUjizokCdXohqH0e0pmddSSi1XmDiKEHi7vVhniIXPSDZ04YK+PWij0jLs1z20IEWC8Bs6GW3El8p11wB4oRDxpyBmFy6Fk4W7rgYSV8or+pvF9t8l5ES9kP0J91IRuGo4QKBI3ah7aly9ztY0/ZkEYaBwbCwj3ifoxZB581odN3e1uC5rJJpCieI2Vwn0mQuwLnVpOzFW2y8rtd3NrNk75DZft+t49z7xuXbmj7EPPR1ty1/Egfv0gWiiMCm6qVrh/PdVz4WA8H/leJRMtao6fxBhnjMbQUjfWgLEC0KoTXHovjn/KR3N9t2BY9ujOvyutSCXfUkhyuHMdC+ydO6+/wLD2eh9Wh8fkG7A+E1w9P6/RNqGWv8ml0kMPKogaJyi7m15QR++pmOBQy+TK1oWm/LfLRuNe7of/2MQHqhfq7DZOGlLfkJwdLcR+xQ7x/t+oQ8EsAtSMx/+k8oAFQ6FkPCyr4H/dZmq1X9wEFVJ+rvzgMglGJ2U/Hwi+pp+zpGiGazEelFgWeZrklnS3CdALE9Z+7gWzUFYHkS0KfX614A1cpOVx/3Nz5JLcGh5Fel5PlBTamDo6L+BV00tgfdgMh1fTair0NDxmncN37vB1x+GtaDCyf+dXyusZtxGtyQGwZ/Vv1zkoUAngIrowRDaAl+Ik/KHeTC9Im9WndmG1V+oQUMhKLZ+lbkRU3sPkWeN5e0kVZ6NEq4lIUqhK8mylUusk1xMgu0t2ZOf8jDACauty5ViQ55uQEl+pB0IWyXm8o9mSLnHRUm6NyUdi34jFkSMzVTIU6CMVB/zbx5vTxR8uSexCx4MYWYZM7Nbx/a3X7H0AAjEvdhCNkxitChWn3hxd7JSLwVPDdVHYnYpIaGLF2r/9QdBhmRe8oI2y+r5RVRA/pgWsFoL/Ct4UCb2YEtEJTvMz6Ij5sqVDdHlz5pYHwwi5D+Qy4NA0PctXV3bNBRcZv7JQcxaUB0I+08wpTNJDhtD7WB/Ak4oZcawBC7xZSLpWUXhopAiVxDRaZA7P3/cnMZWFnHRclQpdJ6sBQuPfe9PUWjmtMlpPjDBYlQFbehUnVKjLapfjFxk21A6gt2N7BAej2JOdRf8TbKY+7oG2LDc6VsOfr9Xj3dvPuglg/GFi1YK0S3m+NukgYxpy9wD3u78aIk9kLdrJsZXhNZmgxd9NdU0fs2Iy7OcYHm5UnEk/CgqExD/MmT5MycHQs793v1U+4JJIDbHtGGUFU5gA99g3v3I2iaG+RYoxO2NRBNJVQ+RS8CTMWHxITc1fgGXldwb7be8cvrznuSNZd6csQQRhYJQKiJL77SZvZ4IjwovRGPdngcro3HbCOlEhOCVS8JMCkB27X6SveZOtgiMMIlogGXbO7j/MYmS+u+E/zd"

# 2. 这是您发送那次成功请求时，URL里的 secret 参数的值。
#    由于您的脚本每次都随机生成，我无法知道确切值。
#    但幸运的是，您之前的日志脚本里，正好保留了一份！
#    它就是 `lambda$secureRegister$19` 被调用时，使用的那个 secret
REQUEST_SECRET_B64 = "CUV1vJFN1khFIHWLNXmNUA==" # 这只是一个示例，如果上面的密文是来自不同请求，请找到对应的secret

def decrypt_kunpeng_response(encrypted_b64: str, secret_b64: str):
    """
    [最终正确逻辑] 解密服务器响应
    - Key: 来自请求的secret参数
    - IV:  来自响应密文的前16字节
    """
    # 1. Base64解码请求的secret，得到解密密钥
    key = base64.b64decode(secret_b64)
    
    # 2. Base64解码响应的完整数据
    full_decoded_bytes = base64.b64decode(encrypted_b64)
    
    # 3. 从响应数据中分离出IV和真实密文
    iv = full_decoded_bytes[:16]           # 前16字节是IV
    encrypted_bytes = full_decoded_bytes[16:] # 剩余的是密文
    
    # 4. 初始化AES解密器
    cipher = AES.new(key, AES.MODE_CBC, iv)
    
    # 5. 执行解密并去除填充
    decrypted_bytes = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
    
    return json.loads(decrypted_bytes.decode('utf-8'))


if __name__ == "__main__":
    print("--- 正在使用最终修正的混合模式算法解密 ---")
    print(f"将使用请求中的 Secret (前10位): {REQUEST_SECRET_B64[:10]}... 作为Key")
    
    try:
        # 调用最终的解密函数
        decrypted_data = decrypt_kunpeng_response(ENCRYPTED_DATA_B64, REQUEST_SECRET_B64)
        
        print("\n\033[92m>>> 解密成功! 完整的明文数据如下: <<<\033[0m\n")
        print(json.dumps(decrypted_data, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"\n\033[91m>>> 解密失败! <<<\033[0m")
        print(f"错误原因: {e}")