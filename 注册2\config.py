#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os

# 账号配置
ACCOUNT = os.environ.get("DAILI_ACCOUNT") or "ab263263"
PASSWORD = os.environ.get("DAILI_PASSWORD") or "1111qqqq"

# 浏览器配置
HEADLESS = True  # 是否无头模式运行
WINDOW_SIZE = (1920, 1080)  # 浏览器窗口大小

# 延迟配置
LOGIN_DELAY = (2, 4)  # 登录页面加载延迟范围(秒)
TYPE_DELAY = (0.05, 0.15)  # 打字延迟范围(秒)
SLIDER_DELAY = (0.01, 0.02)  # 滑块移动延迟范围(秒)

# 重试配置
MAX_RETRY = 3  # 最大重试次数
RETRY_DELAY = 5  # 重试间隔(秒)

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "51daili_advanced.log"

# URL配置
MOBILE_BASE_URL = "https://m.51daili.com"
PC_BASE_URL = "https://www.51daili.com"
LOGIN_URL = f"{PC_BASE_URL}/user/login.html"
SIGNIN_URL = f"{PC_BASE_URL}/user/signin.html"
USER_INDEX_URL = f"{PC_BASE_URL}/user/index.html"

# 反爬虫配置
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
]

# Chrome选项
CHROME_OPTIONS = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-blink-features=AutomationControlled',
    '--disable-extensions',
    '--disable-plugins',
    '--disable-images',  # 禁用图片加载以提高速度
    '--disable-javascript',  # 可选：禁用JS（如果网站允许）
]

# 滑块验证配置
SLIDER_CONFIG = {
    'max_attempts': 3,  # 最大尝试次数
    'track_margin': 50,  # 轨道边距
    'shake_range': (-2, 2),  # 抖动范围
    'shake_count': 3,  # 抖动次数
}
