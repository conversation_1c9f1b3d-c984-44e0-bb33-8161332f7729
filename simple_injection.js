// 🚀 简化版浏览器注入脚本
// 专门用于无痕浏览器自动化

console.log('🎯 注入简化版自动注册脚本...');

// 等待CryptoJS加载的简化版本
function waitForCrypto() {
    return new Promise((resolve) => {
        if (typeof CryptoJS !== 'undefined') {
            resolve(true);
            return;
        }
        
        let attempts = 0;
        const checkInterval = setInterval(() => {
            attempts++;
            if (typeof CryptoJS !== 'undefined') {
                clearInterval(checkInterval);
                resolve(true);
            } else if (attempts > 20) { // 10秒超时
                clearInterval(checkInterval);
                resolve(false);
            }
        }, 500);
    });
}

// 简化的自动注册对象
window.AutoRegister = {
    aesKey: 'wtBfKcqAMug1wbH8',
    signKey: 'xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY',
    
    encryptData: function(data) {
        if (typeof CryptoJS === 'undefined') {
            console.error('❌ CryptoJS未加载');
            return null;
        }
        
        try {
            const jsonStr = JSON.stringify(data);
            const key = CryptoJS.enc.Utf8.parse(this.aesKey);
            const encrypted = CryptoJS.AES.encrypt(jsonStr, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            return encrypted.toString();
        } catch (e) {
            console.error('❌ 加密失败:', e);
            return null;
        }
    },
    
    generateSign: function(data) {
        if (typeof CryptoJS === 'undefined') {
            console.error('❌ CryptoJS未加载');
            return null;
        }
        
        try {
            const sortedKeys = Object.keys(data).sort();
            const sortedParams = sortedKeys.map(key => `${key}=${data[key]}`).join('&');
            const signString = `${sortedParams}&key=${this.signKey}`;
            return CryptoJS.MD5(signString).toString();
        } catch (e) {
            console.error('❌ 签名生成失败:', e);
            return null;
        }
    },
    
    makeAPIRequest: async function(apiPath, data, description = '') {
        console.log(`🚀 ${description}`);
        
        try {
            const encrypted = this.encryptData(data);
            const sign = this.generateSign(data);
            
            if (!encrypted || !sign) {
                console.error('❌ 加密或签名失败');
                return null;
            }
            
            console.log('🔐 加密完成');
            console.log('✍️ 签名完成');
            
            const response = await fetch(`https://ds-web1.yrpdz.com${apiPath}`, {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'content-type': 'application/json',
                    'is_app': 'false',
                    'origin': 'https://ds-web1.yrpdz.com',
                    'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                    'user-agent': navigator.userAgent,
                    'token': '',
                    'sign': sign,
                    'transfersecret': encrypted
                },
                body: JSON.stringify({[encrypted]: encrypted})
            });
            
            console.log('📥 响应状态:', response.status);
            
            const responseText = await response.text();
            
            if (responseText && !responseText.startsWith('<!DOCTYPE html>')) {
                console.log('✅ API调用成功');
                console.log('📄 响应:', responseText.substring(0, 100) + '...');
                return responseText;
            } else {
                console.error('❌ 返回HTML页面，可能被拦截');
                return null;
            }
            
        } catch (error) {
            console.error('❌ 请求失败:', error);
            return null;
        }
    }
};

// 初始化
waitForCrypto().then(success => {
    if (success) {
        console.log('✅ CryptoJS加载成功');
        console.log('✅ 自动注册脚本准备就绪');
    } else {
        console.error('❌ CryptoJS加载失败');
    }
});
