import requests
import hashlib
import time
import random
import string
import json
import os
import collections
from proxy_manager import ProxyManager
from typing import Optional

# --- 1. 全局及API配置 ---
# ==============================================================================

# API URLs
API_BASE_URL = "http://206.119.6.100:30340/api"
LOGIN_URL = f"{API_BASE_URL}/user/login"
RESET_PASS_URL = f"{API_BASE_URL}/wallet/resetPass"
SIGN_IN_URL = f"{API_BASE_URL}/sign/globalsign"
WALLET_DETAIL_URL = f"{API_BASE_URL}/wallet/detail"

ACCOUNTS_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\accounts.txt" # 固定文件路径
API_VERSION = "1.5.0"
SECRET_KEY = "*************" # 全局签名密钥

# --- 51代理API凭证 ---
# !!! 使用前请务必替换成您自己的真实信息 !!!
PROXY_CONFIG = {
    "packid": "2",
    "uid": "42477",
    "access_name": "ab263263",
    "access_password": "8C84B9BFE1774BFB2443DD34797EE4FB",
    "protocol": 'http',
    "time_duration": 31
}

# ==============================================================================

def calculate_sign(params: dict, secret_key: str, is_login_or_register: bool = False) -> str:
    """
    计算请求签名 - 智能区分不同接口的签名规则。
    """
    filtered_params = {k: v for k, v in params.items() if k != 'sign' and v is not None and v != ''}
    sorted_params = collections.OrderedDict(sorted(filtered_params.items()))
    values_string = "".join(sorted_params.values())
    
    # 注册和登录接口的签名规则与其他接口不同
    if is_login_or_register:
        account = params.get('account', '')
        timestamp = params.get('timestamp', '')
        string_to_sign = f"{values_string}{account}{timestamp}{secret_key}"
    else:
         string_to_sign = f"{values_string}{secret_key}"

    return hashlib.md5(string_to_sign.encode('utf-8')).hexdigest().lower()

class LoginBot:
    """
    处理账号登录及后续操作（修改密码、签到）的机器人。
    """

    def __init__(self, proxy_manager):
        self.proxy_manager = proxy_manager
        self.accounts = self.load_accounts()
        self.total_balance = 0.0 # 用于累加总余额

    def _update_account_in_file(self, updated_account: dict):
        """在文件中更新单个账户的信息，主要用于保存token"""
        # 注意：这里直接读取文件而不是使用 self.accounts，是为了防止并发修改问题
        all_accounts = []
        if os.path.exists(ACCOUNTS_FILE):
            with open(ACCOUNTS_FILE, "r", encoding="utf-8") as f:
                all_accounts = [json.loads(line) for line in f if line.strip()]

        # 查找并替换
        account_found = False
        for i, acc in enumerate(all_accounts):
            if acc['account'] == updated_account['account']:
                all_accounts[i] = updated_account
                account_found = True
                break
        
        # 如果文件中没找到（理论上不应该发生），则追加
        if not account_found:
            all_accounts.append(updated_account)
        
        # 将更新后的完整列表写回文件
        try:
            with open(ACCOUNTS_FILE, "w", encoding="utf-8") as f:
                for acc in all_accounts:
                    f.write(json.dumps(acc, ensure_ascii=False) + "\n")
            # print(f"ℹ️ 已更新账号 {updated_account['account']} 的token信息。")
        except IOError as e:
            print(f"❌ 更新账号文件失败: {e}")

    def load_accounts(self) -> list:
        """从 accounts.txt 加载账号信息，并校验关键字段"""
        if not os.path.exists(ACCOUNTS_FILE):
            print(f"❌ 错误: 账号文件 '{ACCOUNTS_FILE}' 不存在。请先运行注册脚本。")
            return []
        
        valid_accounts = []
        try:
            with open(ACCOUNTS_FILE, "r", encoding="utf-8") as f:
                for i, line in enumerate(f):
                    if not line.strip():
                        continue
                    try:
                        account = json.loads(line)
                        # 验证关键字段是否存在
                        if 'account' in account and 'password' in account and 'deviceNo' in account:
                            valid_accounts.append(account)
                        else:
                            print(f"⚠️ 警告: 第 {i+1} 行账号数据不完整 (缺少 account, password, 或 deviceNo)，已跳过。")
                    except json.JSONDecodeError:
                        print(f"⚠️ 警告: 第 {i+1} 行不是有效的JSON格式，已跳过。")

            print(f"✅ 成功加载 {len(valid_accounts)} 个有效账号。")
            return valid_accounts
        except IOError as e:
            print(f"❌ 加载或解析账号文件失败: {e}")
            return []

    def login(self, account_info: dict, proxy_ip: str, proxies: dict):
        """使用单个账号信息进行登录"""
        print("\n--- 步骤1: 登录 ---")
        params = {
            'password': account_info['password'],
            'os': 'android',
            'v': API_VERSION,
            'ip': proxy_ip,
            'deviceNo': account_info['deviceNo'],
            'account': account_info['account'],
            'token': '', # 登录时token为空或任意
            'timestamp': str(int(time.time() * 1000)),
            'sign': ''
        }
        params['sign'] = calculate_sign(params, SECRET_KEY, is_login_or_register=True)

        try:
            response = requests.post(LOGIN_URL, data=params, proxies=proxies, timeout=20)
            result = response.json()
            if result.get('code') == 0:
                print(f"✅ 登录成功: {account_info['account']}")
                # 登录成功后，更新账号信息并保存
                session_data = result['data']
                account_info['token'] = session_data.get('token')
                account_info['user_id'] = session_data.get('id')
                # 设置token过期时间为23小时后
                account_info['token_expiry'] = int(time.time()) + 23 * 3600 
                self._update_account_in_file(account_info)
                return session_data # 返回包含 token 和 id 的 data 字典
            else:
                print(f"❌ 登录失败: {account_info['account']} - {result.get('msg')}")
                return None
        except Exception as e:
            print(f"❌ 登录请求异常: {e}")
            return None

    def reset_wallet_password(self, account_info: dict, session_data: dict, new_password: str, proxies: Optional[dict] = None):
        """修改钱包密码"""
        print("--- 步骤2: 修改钱包密码 ---")
        params = {
            'password': new_password,
            'os': 'android',
            'v': API_VERSION,
            'account': account_info['account'],
            'token': session_data['token'],
            'timestamp': str(int(time.time() * 1000)),
            'sign': ''
        }
        params['sign'] = calculate_sign(params, SECRET_KEY)

        try:
            response = requests.post(RESET_PASS_URL, data=params, proxies=proxies, timeout=20)
            result = response.json()
            if result.get('code') == 0:
                print(f"✅ 钱包密码修改成功: {account_info['account']}")
                return "SUCCESS"
            
            if "token" in result.get('msg', '').lower() or "失效" in result.get('msg', ''):
                print(f"⚠️ Token失效: {account_info['account']}")
                return "TOKEN_EXPIRED"

            print(f"❌ 钱包密码修改失败: {account_info['account']} - {result.get('msg')}")
            return "FAILED"
        except Exception as e:
            print(f"❌ 修改密码请求异常: {e}")
            return "FAILED"

    def get_wallet_balance(self, account_info: dict, session_data: dict, proxies: Optional[dict] = None):
        """查询钱包余额"""
        print("--- 步骤: 查询钱包余额 ---")
        params = {
            'os': 'android',
            'v': API_VERSION,
            'token': session_data['token'],
            'timestamp': str(int(time.time() * 1000)),
            'sign': ''
        }
        params['sign'] = calculate_sign(params, SECRET_KEY)

        try:
            response = requests.post(WALLET_DETAIL_URL, data=params, proxies=proxies, timeout=20)
            result = response.json()
            
            if result.get('code') == 0:
                balance_data = result.get('data', {})
                print(f"✅ 查询成功: {account_info['account']}")
                print(f"   - 可用余额: {balance_data.get('balance', 'N/A')}")
                print(f"   - 冻结余额: {balance_data.get('balance_lock', 'N/A')}")
                print(f"   - gent余额: {balance_data.get('balance_gent', 'N/A')}")
                print(f"   - 积分余额: {balance_data.get('balance_point', 'N/A')}")
                
                # 累加余额到总计
                try:
                    balance_str = balance_data.get('balance', '0.00')
                    self.total_balance += float(balance_str)
                    print(f"   - (已累加到总额: {self.total_balance:.2f})")
                except (ValueError, TypeError):
                    print(f"   - ⚠️ 无法解析余额 '{balance_str}'，总额未累加。")

                return "SUCCESS"
            
            if "token" in result.get('msg', '').lower() or "失效" in result.get('msg', ''):
                print(f"⚠️ Token失效: {account_info['account']}")
                return "TOKEN_EXPIRED"

            print(f"❌ 查询余额失败: {account_info['account']} - {result.get('msg')}")
            return "FAILED"
        except Exception as e:
            print(f"❌ 查询余额请求异常: {e}")
            return "FAILED"

    def sign_in(self, account_info: dict, session_data: dict, proxies: Optional[dict] = None):
        """每日签到"""
        print("--- 步骤3: 每日签到 ---")
        params = {
            'os': 'android',
            'user_id': session_data['id'],
            'v': API_VERSION,
            'token': session_data['token'],
            'timestamp': str(int(time.time() * 1000)),
            'sign': ''
        }
        params['sign'] = calculate_sign(params, SECRET_KEY)

        try:
            response = requests.post(SIGN_IN_URL, data=params, proxies=proxies, timeout=20)
            result = response.json()
            
            # 检查是否成功
            if result.get('code') == 0:
                print(f"✅ 签到成功: {account_info['account']}")
                return "SUCCESS"
            
            # 检查是否已签到
            if "已签到" in result.get('msg', ''):
                print(f"✅ 今日已签到: {account_info['account']}")
                return "SUCCESS"
            
            # 检查是否是token失效
            if "token" in result.get('msg', '').lower() or "失效" in result.get('msg', ''):
                print(f"⚠️ Token失效，需要重新登录: {account_info['account']}")
                return "TOKEN_EXPIRED"

            print(f"❌ 签到失败: {account_info['account']} - {result.get('msg')}")
            return "FAILED"

        except Exception as e:
            print(f"❌ 签到请求异常: {e}")
            return "FAILED"


    def run(self):
        """执行所有账号的完整流程"""
        if not self.accounts:
            return

        print("\n" + "="*50)
        print("请选择要执行的操作:")
        print("  1. 批量修改钱包密码")
        print("  2. 批量每日签到")
        print("  3. 查询所有账号余额")
        print("  4. 执行全部任务 (修改密码 + 签到 + 查余额)")
        print("="*50)

        choice = input("请输入选项 (1, 2, 3, or 4): ").strip()

        do_reset_pass = False
        do_sign_in = False
        do_get_balance = False
        new_wallet_pass = ""

        if choice == '1':
            do_reset_pass = True
            print("您已选择: [批量修改钱包密码]")
        elif choice == '2':
            do_sign_in = True
            print("您已选择: [批量每日签到]")
        elif choice == '3':
            do_get_balance = True
            print("您已选择: [查询所有账号余额]")
        elif choice == '4':
            do_reset_pass = True
            do_sign_in = True
            do_get_balance = True
            print("您已选择: [执行全部任务]")
        else:
            print("❌ 无效的选项，程序退出。")
            return
        
        if do_reset_pass:
            new_wallet_pass = input("请输入要统一设置的新钱包密码: ").strip()
            if not new_wallet_pass:
                print("❌ 未输入新钱包密码，程序退出。")
                return
        
        print("="*50 + "\n")

        for i, account in enumerate(self.accounts):
            print(f"\n>>> 开始处理第 {i+1}/{len(self.accounts)} 个账号: {account.get('account')} <<<")
            
            token_valid = 'token' in account and 'token_expiry' in account and time.time() < account['token_expiry']
            tasks_succeeded_with_token = False

            # --- 阶段一: 尝试使用已保存的Token (无代理) ---
            if token_valid and (do_reset_pass or do_sign_in or do_get_balance):
                print("   - 检测到有效Token，尝试直接操作 (不使用代理)...")
                session_from_token = {'id': account.get('user_id'), 'token': account.get('token')}
                
                reset_status = "SUCCESS" if not do_reset_pass else "PENDING"
                signin_status = "SUCCESS" if not do_sign_in else "PENDING"
                balance_status = "SUCCESS" if not do_get_balance else "PENDING"

                # 尝试修改密码
                if do_reset_pass:
                    reset_status = self.reset_wallet_password(account, session_from_token, new_wallet_pass, proxies=None)
                
                # 如果改密时Token未失效，继续尝试签到
                if reset_status != "TOKEN_EXPIRED" and do_sign_in:
                    signin_status = self.sign_in(account, session_from_token, proxies=None)

                # 如果之前的Token都未失效，继续尝试查余额
                if reset_status != "TOKEN_EXPIRED" and signin_status != "TOKEN_EXPIRED" and do_get_balance:
                    balance_status = self.get_wallet_balance(account, session_from_token, proxies=None)

                # 如果所有任务都成功了，或者token中途失效了，更新状态
                if "TOKEN_EXPIRED" in [reset_status, signin_status, balance_status]:
                    token_valid = False
                    print("   - Token已失效，需要重新登录...")
                elif reset_status == "SUCCESS" and signin_status == "SUCCESS" and balance_status == "SUCCESS":
                    tasks_succeeded_with_token = True

            # --- 阶段二: 完整登录流程 (如果需要) ---
            if not tasks_succeeded_with_token:
                # 只有在需要执行操作，且无法通过Token完成时，才进行登录
                if not token_valid and (do_reset_pass or do_sign_in or do_get_balance):
                    print("   - Token无效或不存在，执行完整登录流程 (将使用代理)...")
                    
                    proxies = self.proxy_manager.get_proxy(force_new=True)
                    if not proxies:
                        print(f"❌ 获取代理失败，跳过账号 {account.get('account')}")
                        time.sleep(random.uniform(3, 5))
                        continue
                    
                    proxy_ip = proxies['http'].split('@')[1].split(':')[0]
                    session = self.login(account, proxy_ip, proxies)
                    
                    if session:
                        if do_reset_pass:
                            self.reset_wallet_password(account, session, new_wallet_pass, proxies)
                            time.sleep(random.uniform(1, 2))
                        
                        if do_sign_in:
                            self.sign_in(account, session, proxies)
                            time.sleep(random.uniform(1, 2))

                        if do_get_balance:
                            self.get_wallet_balance(account, session, proxies)
                else:
                    print("   - 无需执行任何操作，或操作已通过Token完成。")


            print(f"\n>>> 账号 {account.get('account')} 处理完毕，休眠3-5秒...")
            time.sleep(random.uniform(3, 5))

        print("\n" + "="*50)
        print("🎉 所有账号处理完毕！")
        
        # 如果执行了查询余额，则显示总额
        if do_get_balance:
            print(f"\n💰 所有账号可用余额总计: {self.total_balance:.2f}")

        print("="*50)


if __name__ == "__main__":
    # 初始化代理管理器
    proxy_client = ProxyManager(
        packid=PROXY_CONFIG['packid'],
        uid=PROXY_CONFIG['uid'],
        access_name=PROXY_CONFIG['access_name'],
        access_password=PROXY_CONFIG['access_password'],
        protocol=PROXY_CONFIG['protocol'],
        time_duration=PROXY_CONFIG['time_duration']
    )

    bot = LoginBot(proxy_client)
    bot.run() 