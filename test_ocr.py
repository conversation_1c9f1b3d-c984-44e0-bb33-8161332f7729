#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 身份证OCR识别测试脚本

import os
import sys
import glob
import random

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_id_card_ocr():
    """测试身份证OCR识别功能"""
    print("🔍 身份证OCR识别测试")
    print("=" * 40)
    
    try:
        from zhuccc1 import extract_id_card_info, ID_CARD_FRONT_DIR
        
        # 检查身份证图片目录
        if not os.path.exists(ID_CARD_FRONT_DIR):
            print(f"❌ 身份证正面图片目录不存在: {ID_CARD_FRONT_DIR}")
            return False
        
        # 获取所有正面图片
        front_pattern = os.path.join(ID_CARD_FRONT_DIR, "*.*")
        front_images = glob.glob(front_pattern)
        
        if not front_images:
            print(f"❌ 身份证正面图片目录为空: {ID_CARD_FRONT_DIR}")
            return False
        
        print(f"📁 找到 {len(front_images)} 张身份证正面图片")
        
        # 随机选择几张图片进行测试
        test_count = min(3, len(front_images))
        test_images = random.sample(front_images, test_count)
        
        success_count = 0
        
        for i, image_path in enumerate(test_images, 1):
            print(f"\n🧪 测试图片 {i}/{test_count}: {os.path.basename(image_path)}")
            
            # 进行OCR识别
            name, id_card = extract_id_card_info(image_path)
            
            if name and id_card:
                print(f"✅ 识别成功:")
                print(f"   姓名: {name}")
                print(f"   身份证: {id_card}")
                success_count += 1
            else:
                print(f"❌ 识别失败")
        
        print(f"\n📊 测试结果: {success_count}/{test_count} 成功")
        
        if success_count > 0:
            print("✅ OCR识别功能正常")
            return True
        else:
            print("❌ OCR识别功能异常")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装 ddddocr: pip install ddddocr")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_image_upload():
    """测试图片上传功能"""
    print("\n📤 图片上传功能测试")
    print("=" * 40)
    
    try:
        from zhuccc1 import get_random_id_card_images
        
        # 获取随机身份证图片
        front_image, back_image = get_random_id_card_images()
        
        if front_image and back_image:
            print("✅ 身份证图片获取成功:")
            print(f"   正面: {os.path.basename(front_image)}")
            print(f"   反面: {os.path.basename(back_image)}")
            
            # 检查文件是否存在
            if os.path.exists(front_image) and os.path.exists(back_image):
                print("✅ 图片文件存在")
                return True
            else:
                print("❌ 图片文件不存在")
                return False
        else:
            print("❌ 无法获取身份证图片")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_address_generation():
    """测试地址生成功能"""
    print("\n🏠 地址生成功能测试")
    print("=" * 40)
    
    try:
        from zhuccc1 import generate_real_address
        
        print("生成的地址示例:")
        for i in range(5):
            address = generate_real_address()
            print(f"   {i+1}. {address}")
        
        print("✅ 地址生成功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 环球网站实名认证功能测试")
    print("🔧 测试OCR识别、图片上传、地址生成功能")
    print("=" * 60)
    
    tests = [
        ("身份证OCR识别", test_id_card_ocr),
        ("图片上传准备", test_image_upload),
        ("地址生成", test_address_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}测试:")
        result = test_func()
        results.append(result)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(results)
    if all_passed:
        print("\n🎉 所有测试通过! 实名认证功能准备就绪!")
        print("💡 现在可以运行完整的注册脚本:")
        print("   python zhuccc1.py")
    else:
        print("\n⚠️ 部分测试失败，请检查:")
        print("   1. 确保身份证图片目录存在且有图片")
        print("   2. 安装必要的依赖: pip install ddddocr opencv-python")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
