#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯 - 模拟客户端

import socket
import struct
import time
import hashlib
import random
import string
from typing import Optional

class KunpengClient:
    def __init__(self, host: str = "**************", port: int = 5666):
        self.host = host
        self.port = port
        self.socket: Optional[socket.socket] = None
        self.user_id = "112746"  # 你的用户ID
        self.service_id = "107998/androidservice"
        
        # 从抓包中提取的认证令牌
        self.auth_tokens = [
            "8f1d54101b8d174c34947ce7ed385c406",
            "f1d54101b8d14c34947ce7edd3834947ce7ed385c406",
            "1e6fe4e7af024a958fb43e15b446530"
        ]
    
    def connect(self) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            print(f"🔗 正在连接到 {self.host}:{self.port}...")
            self.socket.connect((self.host, self.port))
            print(f"✅ 连接成功!")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def create_packet(self, msg_type: int, payload: bytes) -> bytes:
        """创建数据包"""
        # 协议头: 04 54 00 XX (XX为消息类型)
        header = struct.pack('>BBB', 0x04, 0x54, 0x00)
        msg_type_byte = struct.pack('B', msg_type)
        
        # 载荷长度 (4字节大端序)
        payload_length = struct.pack('>I', len(payload))
        
        return header + msg_type_byte + payload_length + payload
    
    def create_auth_payload_exact(self) -> bytes:
        """创建精确的认证载荷 (完全基于抓包数据)"""
        # 直接使用抓包的载荷数据
        hex_payload = """0A 3C 0A 0E
        31 30 37 39 39 38 2F 61 6E 64 72 6F
        69 64 73 65 72 76 69 63 65 12 20 38
        66 31 64 35 34 31 30 31 62 38 64 31
        37 34 63 33 34 39 34 37 63 65 37 65
        64 33 38 35 63 34 30 36 20 01 12 06
        31 30 37 39 39 38 1A 20 65 62 30 62
        99 BA E2 80 93 E5 94 90 E5 80 8F 22
        06 31 31 32 37 34 36 2A 12 E6 90 8E
        E5 B1 80 30 C2 B5 B0 E1 84 33 38 01
        E5 B3 B0 30 C2 B5 B0 E1 84 33 38 01
        42 03 E6 88 91 60 FF FF FF FF FF FF
        FF FF FF 01 B0 01 FF FF FF FF FF FF"""

        # 清理并转换为字节
        clean_hex = ''.join(hex_payload.split())
        return bytes.fromhex(clean_hex)

    def create_auth_payload(self) -> bytes:
        """创建认证载荷 (基于抓包数据)"""
        # 使用精确的抓包数据
        return self.create_auth_payload_exact()
    
    def send_auth_request(self) -> bool:
        """发送认证请求"""
        try:
            payload = self.create_auth_payload()
            packet = self.create_packet(0x0A, payload)  # 0x0A 是认证消息类型
            
            print(f"📤 发送认证请求 ({len(packet)} 字节)...")
            print(f"   数据: {packet[:50].hex()}...")
            
            self.socket.send(packet)
            return True
        except Exception as e:
            print(f"❌ 发送认证请求失败: {e}")
            return False
    
    def receive_response(self) -> Optional[bytes]:
        """接收服务器响应"""
        try:
            # 先接收协议头 (8字节)
            header = self.socket.recv(8)
            if len(header) < 8:
                print(f"❌ 协议头不完整")
                return None
            
            # 解析协议头
            magic1, magic2, msg_type, sub_type = struct.unpack('>BBBB', header[:4])
            payload_length = struct.unpack('>I', header[4:8])[0]
            
            print(f"📥 收到响应头:")
            print(f"   魔数: {magic1:02x} {magic2:02x}")
            print(f"   消息类型: {msg_type:02x}")
            print(f"   子类型: {sub_type:02x}")
            print(f"   载荷长度: {payload_length}")
            
            # 接收载荷
            if payload_length > 0:
                payload = self.socket.recv(payload_length)
                print(f"   载荷: {payload[:50].hex()}...")
                return header + payload
            
            return header
            
        except Exception as e:
            print(f"❌ 接收响应失败: {e}")
            return None
    
    def send_heartbeat(self) -> bool:
        """发送心跳包"""
        try:
            # 简单的心跳载荷
            payload = self.service_id.encode('utf-8')
            packet = self.create_packet(0x64, payload)  # 0x64 是心跳消息类型
            
            print(f"💓 发送心跳包...")
            self.socket.send(packet)
            return True
        except Exception as e:
            print(f"❌ 发送心跳失败: {e}")
            return False
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            print(f"🔌 连接已关闭")

def main():
    print("🚀 鲲鹏通讯 - 模拟客户端")
    print("=" * 50)
    
    client = KunpengClient()
    
    try:
        # 连接服务器
        if not client.connect():
            return
        
        # 发送认证请求
        if client.send_auth_request():
            # 等待认证响应
            response = client.receive_response()
            if response:
                print(f"✅ 认证响应已收到")
            else:
                print(f"❌ 未收到认证响应")
        
        # 发送心跳包
        time.sleep(1)
        if client.send_heartbeat():
            # 等待心跳响应
            response = client.receive_response()
            if response:
                print(f"💓 心跳响应已收到")
        
        # 保持连接一段时间
        print(f"⏳ 保持连接 10 秒...")
        time.sleep(10)
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
