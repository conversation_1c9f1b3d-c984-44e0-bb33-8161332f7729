#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 快速密码测试脚本

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_password_with_server(password):
    """使用指定密码测试服务器响应"""
    try:
        from zhuccc1 import (
            generate_real_phone_number,
            generate_device_fingerprint_for_request,
            generate_request_headers,
            get_captcha_image,
            recognize_captcha,
            generate_sign_parameter
        )
        import requests
        
        print(f"🧪 测试密码: {password}")
        print("=" * 40)
        
        # 生成基本信息
        phone_number = generate_real_phone_number()
        fingerprint, _ = generate_device_fingerprint_for_request()
        
        print(f"📱 手机号: {phone_number}")
        print(f"🔐 密码: {password} (长度: {len(password)}位)")
        
        # 创建会话
        session = requests.Session()
        base_headers = generate_request_headers(fingerprint)
        session.headers.update(base_headers)
        
        # 获取验证码
        print("🔍 获取验证码...")
        code_id, captcha_image = get_captcha_image(session)
        
        if not code_id or not captcha_image:
            print("❌ 获取验证码失败")
            return False
        
        # 识别验证码
        print("🤖 识别验证码...")
        captcha_code = recognize_captcha(captcha_image)
        
        if not captcha_code or len(captcha_code) != 4:
            print("❌ 验证码识别失败")
            return False
        
        print(f"✅ 验证码: {captcha_code}")
        
        # 生成签名
        print("🔐 生成签名...")
        sign = generate_sign_parameter(phone_number, password)
        
        if not sign:
            print("❌ 签名生成失败")
            return False
        
        # 构造注册数据
        register_data = {
            'username': phone_number,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': '267524',
            'paypassword': '147258',
            'sign': sign
        }
        
        # 发送注册请求
        print("🚀 发送注册请求...")
        
        register_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/json',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': session.headers.get('User-Agent', '')
        }
        
        response = session.post(
            "https://huanqiu.cgi913.com/api/login/register",
            json=register_data,
            headers=register_headers
        )
        
        print(f"📥 服务器响应:")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {result}")
                
                if result.get('code') == 1:
                    print("🎉 注册成功!")
                    return True
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 注册失败: {error_msg}")
                    
                    # 分析错误信息
                    if "密码" in error_msg:
                        print("🔍 密码相关错误，可能的原因:")
                        print("   - 密码长度不符合要求")
                        print("   - 密码字符类型不符合要求")
                        print("   - 密码格式不符合要求")
                    
                    return False
                    
            except Exception as e:
                print(f"❌ 响应解析失败: {e}")
                print(f"   原始响应: {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 快速密码测试工具")
    print("🔧 测试不同密码格式的服务器响应")
    print("=" * 50)
    
    # 预设的测试密码
    test_passwords = [
        "123456",      # 6位纯数字
        "abc123",      # 小写字母+数字
        "Abc123",      # 大写+小写+数字
        "abcd1234",    # 4字母+4数字
        "Password1",   # 常见格式
        "12345abc",    # 数字+字母
        "a1b2c3",      # 交替格式
        "test123456",  # 长密码
    ]
    
    print("📋 预设测试密码:")
    for i, pwd in enumerate(test_passwords, 1):
        print(f"   {i}. {pwd} (长度: {len(pwd)})")
    
    print("\n选择测试方式:")
    print("1. 测试预设密码")
    print("2. 输入自定义密码")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            print("\n选择要测试的预设密码:")
            for i, pwd in enumerate(test_passwords, 1):
                print(f"   {i}. {pwd}")
            
            try:
                pwd_choice = int(input("请选择密码编号: ").strip()) - 1
                if 0 <= pwd_choice < len(test_passwords):
                    password = test_passwords[pwd_choice]
                    print(f"\n🧪 开始测试密码: {password}")
                    success = test_password_with_server(password)
                    
                    if success:
                        print(f"\n🎉 密码 '{password}' 测试成功!")
                        print("💡 可以使用这种格式的密码")
                    else:
                        print(f"\n❌ 密码 '{password}' 测试失败")
                        print("💡 尝试其他格式的密码")
                else:
                    print("❌ 无效的选择")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == '2':
            password = input("请输入要测试的密码: ").strip()
            if password:
                if 6 <= len(password) <= 11:
                    print(f"\n🧪 开始测试密码: {password}")
                    success = test_password_with_server(password)
                    
                    if success:
                        print(f"\n🎉 密码 '{password}' 测试成功!")
                        print("💡 可以使用这种格式的密码")
                    else:
                        print(f"\n❌ 密码 '{password}' 测试失败")
                        print("💡 尝试其他格式的密码")
                else:
                    print("❌ 密码长度必须是6-11位")
            else:
                print("❌ 密码不能为空")
                
        elif choice == '3':
            print("👋 退出测试工具")
            break
            
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
