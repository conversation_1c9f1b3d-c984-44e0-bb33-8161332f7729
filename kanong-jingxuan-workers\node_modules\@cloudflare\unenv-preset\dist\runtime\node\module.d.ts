import type nodeModule from "node:module";
export { Module, SourceMap, _cache, _extensions, _debug, _pathCache, _findPath, _initPaths, _load, _nodeModulePaths, _preloadModules, _resolveFilename, _resolveLookupPaths, builtinModules, constants, enableCompileCache, findSourceMap, getCompileCacheDir, globalPaths, isBuiltin, register, runMain, syncBuiltinESMExports, wrap, } from "unenv/node/module";
export declare const createRequire: typeof nodeModule.createRequire;
declare const _default: {
    Module: any;
    SourceMap: any;
    _cache: any;
    _extensions: any;
    _debug: any;
    _pathCache: any;
    _findPath: any;
    _initPaths: any;
    _load: any;
    _nodeModulePaths: any;
    _preloadModules: any;
    _resolveFilename: any;
    _resolveLookupPaths: any;
    builtinModules: any;
    enableCompileCache: any;
    constants: any;
    createRequire: any;
    findSourceMap: any;
    getCompileCacheDir: any;
    globalPaths: any;
    isBuiltin: any;
    register: any;
    runMain: any;
    syncBuiltinESMExports: any;
    wrap: any;
};
export default _default;
