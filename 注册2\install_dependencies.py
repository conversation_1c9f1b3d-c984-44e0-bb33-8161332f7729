#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
依赖安装脚本
"""

import subprocess
import sys
import os

def install_requirements():
    """安装Python依赖"""
    print("正在安装Python依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"× Python依赖安装失败: {e}")
        return False

def install_chromedriver():
    """安装ChromeDriver"""
    print("正在安装ChromeDriver...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # 下载并安装ChromeDriver
        driver_path = ChromeDriverManager().install()
        print(f"✓ ChromeDriver安装完成: {driver_path}")
        
        # 测试ChromeDriver
        service = Service(driver_path)
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.baidu.com")
        driver.quit()
        
        print("✓ ChromeDriver测试通过")
        return True
        
    except Exception as e:
        print(f"× ChromeDriver安装失败: {e}")
        return False

def main():
    print("=" * 50)
    print("51代理自动签到脚本 - 依赖安装")
    print("=" * 50)
    
    # 安装Python依赖
    if not install_requirements():
        return False
    
    # 安装ChromeDriver
    if not install_chromedriver():
        return False
    
    print("=" * 50)
    print("✓ 所有依赖安装完成!")
    print("现在可以运行 python 51daili_advanced_login.py")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
