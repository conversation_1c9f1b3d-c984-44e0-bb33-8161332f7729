import requests
import hashlib
import time
import random
import string
import json
import os
import collections
from proxy_manager import ProxyManager


# --- 1. 全局及代理配置 ---
# ==============================================================================

# 获取脚本所在的真实目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# API配置
API_URL = "http://*************:30340/api/user/register"
ACCOUNTS_FILE = os.path.join(SCRIPT_DIR, "accounts.txt") # 确保文件总是在脚本同目录下
PASSWORD = "1111qqqq" # 默认密码
API_VERSION = "1.5.0"

# --- 51代理API凭证 ---
# !!! 使用前请务必替换成您自己的真实信息 !!!
PROXY_CONFIG = {
    "packid": "2",                  # 您的套餐ID
    "uid": "42477",                  # 您的UID
    "access_name": "ab263263",        # 您的 accessName
    "access_password": "8C84B9BFE1774BFB2443DD34797EE4FB", # 您的 accessPassword
    "protocol": 'http',               # 'http' 或 'socks5'
    "time_duration": 31               # 代理时长31分钟 (根据用户提供的有效URL更新)
}

# ==============================================================================


# 50个手机品牌型号，用于生成随机设备信息
PHONE_MODELS = [
    "HUAWEI Mate 40 Pro", "HUAWEI P50 Pro", "HUAWEI Nova 9", "Xiaomi 12S Ultra", "Xiaomi Mix Fold 2",
    "Redmi K50", "OPPO Find X5 Pro", "OPPO Reno8 Pro+", "OnePlus 10 Pro", "vivo X80 Pro",
    "iQOO 9 Pro", "realme GT2 Pro", "Samsung Galaxy S22 Ultra", "Samsung Galaxy Z Fold4", "iPhone 13 Pro Max",
    "iPhone 14 Pro", "Google Pixel 6 Pro", "Sony Xperia 1 IV", "ASUS ROG Phone 6", "Motorola Edge X30",
    "Lenovo Legion Y90", "Meizu 18s Pro", "ZTE Axon 40 Ultra", "Nubia Z40 Pro", "Honor Magic4 Ultimate",
    "Honor 70 Pro+", "Black Shark 5 Pro", "Nokia X30", "LG Velvet", "HTC Desire 22 Pro",
    "HUAWEI Mate Xs 2", "Xiaomi 12 Pro", "Redmi Note 11 Pro+", "OPPO K10 Pro", "OnePlus Ace",
    "vivo S15 Pro", "iQOO Neo6", "realme Q5 Pro", "Samsung Galaxy A53", "iPhone SE (3rd generation)",
    "Google Pixel 6a", "Sony Xperia 5 IV", "ASUS Zenfone 9", "Motorola Moto G82", "Honor X40",
    "HUAWEI P40 Pro", "Xiaomi Mi 11", "Redmi K40", "OPPO Find X3", "OnePlus 9"
]

# --- 2. 随机数据生成模块 ---

def generate_device_no():
    """生成一个32位的随机MD5字符串作为设备指纹"""
    random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=64))
    return hashlib.md5(random_string.encode('utf-8')).hexdigest()

def generate_random_phone():
    """生成一个随机的手机号码"""
    # 常见的手机号段
    prefixes = ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
                "150", "151", "152", "153", "155", "156", "157", "158", "159",
                "180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
                "176", "177", "178"]
    return random.choice(prefixes) + "".join(random.choices(string.digits, k=8))

def generate_random_nickname():
    """生成一个更真实的随机中文昵称"""
    # 常见姓氏 (Top 20)
    surnames = [
        "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"
    ]
    # 常用名 (30个常用字)
    given_name_chars = [
        "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
        "艳", "勇", "杰", "秀", "婷", "文", "明", "英", "华", "平",
        "凯", "丹", "博", "瑞", "雪", "梅", "新", "志", "琳", "峰"
    ]

    surname = random.choice(surnames)
    
    # 70%的概率生成双名，30%的概率生成单名
    if random.random() < 0.7:
        given_name = random.choice(given_name_chars) + random.choice(given_name_chars)
    else:
        given_name = random.choice(given_name_chars)
        
    return surname + given_name


# --- 3. IP代理模块 (占位符) ---

def get_proxy_ip():
    """
    【此函数已废弃】
    从 proxy_manager.py 获取代理IP。
    【注意】当前为占位符，需要您提供具体实现。
    """
    # 示例: return proxy_manager.get_ip()
    # 临时返回一个测试IP
    print("【警告】正在使用临时的测试IP地址。")
    return f"116.169.{random.randint(0, 255)}.{random.randint(0, 255)}"


# --- 4. 签名计算模块 (核心) ---

def calculate_sign(params: dict, secret_key: str) -> str:
    """
    计算请求签名 - 基于逆向分析的准确实现。
    """
    # 步骤1: 过滤掉 sign 本身和值为空的参数
    filtered_params = {k: v for k, v in params.items() if k != 'sign' and v is not None and v != ''}

    # 步骤2: 使用TreeMap的逻辑，按key排序
    sorted_params = collections.OrderedDict(sorted(filtered_params.items()))

    # 步骤3: 拼接所有参数的 *值*
    values_string = "".join(sorted_params.values())

    # 步骤4: 获取 account 和 timestamp，这两个参数会被再次追加
    account = params.get('account', '')
    timestamp = params.get('timestamp', '')

    # 步骤5: 拼接成最终的待签名字符串
    # 顺序：(所有参数值拼接) + account + timestamp + secret_key
    string_to_sign = f"{values_string}{account}{timestamp}{secret_key}"

    # 步骤6: MD5加密并返回小写形式
    # print(f"   - 待签名字符串: {string_to_sign}") # (调试用)
    return hashlib.md5(string_to_sign.encode('utf-8')).hexdigest().lower()


# --- 5. 数据持久化模块 ---

def save_account(account_data: dict):
    """将成功的注册信息保存到文件"""
    try:
        with open(ACCOUNTS_FILE, "a", encoding="utf-8") as f:
            f.write(json.dumps(account_data, ensure_ascii=False) + "\n")
        print(f"✅ 账号信息已保存到 {ACCOUNTS_FILE}")
    except IOError as e:
        print(f"❌ 保存账号信息失败: {e}")


# --- 6. 主注册逻辑 ---

def register_account(invite_code: str, proxy_client: ProxyManager):
    """执行单个账号的注册流程"""
    print("\n" + "="*50)
    print("🚀 开始新一轮的账号注册...")

    # 1. 获取代理IP
    print("   - 步骤1: 获取代理IP...")
    proxies = proxy_client.get_proxy(force_new=True)
    if not proxies:
        print("   - ❌ 获取代理失败，跳过本次注册。")
        return False
    
    # 从代理字典中提取出IP地址，用于请求参数
    # '*******************:port' -> 'ip'
    try:
        proxy_ip_address = proxies['http'].split('@')[1].split(':')[0]
    except (IndexError, KeyError):
        print(f"   - ❌ 无法从代理数据 {proxies} 中解析出IP地址，跳过本次注册。")
        proxy_client.release_proxy() # 释放这个无效的代理
        return False


    # 2. 生成动态数据
    print("   - 步骤2: 生成动态数据...")
    account = generate_random_phone()
    nickname = generate_random_nickname()
    device_no = generate_device_no()
    timestamp = str(int(time.time() * 1000))
    phone_model = random.choice(PHONE_MODELS) # 随机选择一个手机型号

    print(f"   - 手机号: {account}")
    print(f"   - 昵称: {nickname}")
    print(f"   - 设备号: {device_no}")
    print(f"   - 代理IP: {proxy_ip_address}")
    print(f"   - 手机型号: {phone_model}")

    # 3. 准备请求参数
    params = {
        'question': '',
        'os': 'android',
        'invitecode': invite_code,
        'ip': proxy_ip_address, # 使用从代理获取的IP
        'deviceNo': device_no,
        'token': '',
        'password': PASSWORD,
        'mobilecode': '',
        'answer': '',
        'v': API_VERSION,
        'nickname': nickname,
        'account': account,
        'timestamp': timestamp,
        'sign': '' # sign 待计算
    }
    
    # 4. 计算签名 (核心)
    print("   - 步骤3: 计算签名...")
    secret_key = "*************"
    sign = calculate_sign(params, secret_key)
    params['sign'] = sign
    
    print(f"   - 时间戳: {timestamp}")
    print(f"   - 计算出的签名: {sign}")

    # 5. 发送POST请求
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'okhttp/4.9.3',
        'Connection': 'Keep-Alive',
        'Accept-Encoding': 'gzip'
    }
    
    try:
        print("   - 步骤4: 发送注册请求 (通过代理)...")
        response = requests.post(API_URL, data=params, headers=headers, proxies=proxies, timeout=20)
        response.raise_for_status() # 如果状态码不是200-299，则抛出异常
        
        # 6. 处理响应
        result = response.json()
        print(f"   - 服务器响应: {result}")
        
        if result.get("code") == 0 and "成功" in result.get("msg", ""):
            print(f"🎉 注册成功! 账号: {account}, 密码: {PASSWORD}")
            
            # 保存账号信息
            account_info = {
                "account": account,
                "password": PASSWORD,
                "nickname": nickname,
                "deviceNo": device_no,
                "ip": proxy_ip_address,
                "phone_model": phone_model,
                "timestamp": timestamp,
                "register_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            save_account(account_info)
            return True
        else:
            print(f"❌ 注册失败: {result.get('msg', '未知错误')}")
            # 如果注册失败，释放当前代理，以便下次获取新的
            proxy_client.release_proxy()
            return False

    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理错误: {e}")
        print("   - 可能是代理IP无效或超时，将尝试更换代理。")
        proxy_client.release_proxy()
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求发生严重错误: {e}")
        proxy_client.release_proxy()
        return False
    except json.JSONDecodeError:
        print(f"❌ 解析服务器响应失败，内容: {response.text}")
        proxy_client.release_proxy()
        return False

# --- 7. 批量执行 ---

def main():
    """主函数，用于批量注册"""
    # 询问用户要注册多少个账号
    while True:
        try:
            num_accounts = int(input("请输入要注册的账号数量: "))
            if num_accounts > 0:
                break
            else:
                print("请输入一个正整数。")
        except ValueError:
            print("无效的输入，请输入一个数字。")

    # 交互式输入邀请码
    invite_code = input("请输入要使用的邀请码 (直接回车将使用默认值 '671961'): ").strip()
    if not invite_code:
        invite_code = "671961"
        print(f"检测到输入为空，已使用默认邀请码: {invite_code}")

    # 初始化代理管理器
    print("\n" + "-"*20 + " 初始化代理管理器 " + "-"*20)
    proxy_client = ProxyManager(
        packid=PROXY_CONFIG['packid'],
        uid=PROXY_CONFIG['uid'],
        access_name=PROXY_CONFIG['access_name'],
        access_password=PROXY_CONFIG['access_password'],
        protocol=PROXY_CONFIG['protocol'],
        time_duration=PROXY_CONFIG['time_duration']
    )
    print("-" * (54))


    success_count = 0
    attempt_count = 0
    max_attempts = num_accounts * 5 # 最多尝试5倍的失败次数，防止无限循环
    
    print("\n" + "="*20 + " 开始批量注册任务 " + "="*20)

    while success_count < num_accounts and attempt_count < max_attempts:
        print(f"\n--- 正在尝试注册第 {success_count + 1}/{num_accounts} 个账号 (总尝试次数: {attempt_count + 1}) ---")
        if register_account(invite_code, proxy_client):
            success_count += 1
            # 注册成功后随机休眠1-3秒
            sleep_time = random.uniform(1, 3)
            print(f"   - 成功，休眠 {sleep_time:.1f} 秒...")
            time.sleep(sleep_time)
        else:
            # 注册失败后，也建议短暂休眠
            sleep_time = random.uniform(2, 4)
            print(f"   - 失败，休眠 {sleep_time:.1f} 秒后重试...")
            time.sleep(sleep_time)
        
        attempt_count += 1


    print("\n" + "="*50)
    print("✅ 批量注册完成!")
    print(f"目标数量: {num_accounts}, 成功: {success_count}, 总尝试次数: {attempt_count}")
    if success_count < num_accounts:
        print("【警告】未能完成所有注册任务，可能是代理IP不足或API不稳定。")


if __name__ == "__main__":
    # 在运行前，先检查一下是否存在 `accounts.txt` 文件
    if not os.path.exists(ACCOUNTS_FILE):
        print(f"提示: 未找到账号文件，将自动创建 {ACCOUNTS_FILE}")
        
    main()
