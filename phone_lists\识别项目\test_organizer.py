#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
身份证整理工具测试脚本
"""

from pathlib import Path
from 整理 import IDCardOrganizer

def test_organizer():
    """测试身份证整理器"""
    print("=== 身份证整理工具测试 ===")
    
    # 测试目录
    test_source = Path("test_images")
    test_output = Path("test_output")
    
    # 检查测试目录是否存在
    if not test_source.exists():
        print(f"测试目录不存在: {test_source}")
        print("请创建test_images目录并放入一些身份证图片进行测试")
        return
    
    try:
        # 创建整理器实例
        organizer = IDCardOrganizer(test_source, test_output)
        
        # 显示关键词配置
        print("正面关键词:", organizer.front_keywords)
        print("反面关键词:", organizer.back_keywords)
        print()
        
        # 开始处理
        organizer.process_images()
        
        print("\n测试完成！")
        print(f"结果保存在: {test_output}")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")

if __name__ == "__main__":
    test_organizer()
