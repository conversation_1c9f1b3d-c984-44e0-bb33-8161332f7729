const User = require('../models/userModel');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// @desc    注册新用户
// @route   POST /api/users/register
// @access  Public
exports.register = async (req, res) => {
  try {
    const { username, email, password, phone } = req.body;

    // 检查用户是否已存在
    const existingUser = await User.findOne({ $or: [{ email }, { username }] });
    if (existingUser) {
      return res.status(400).json({ success: false, message: '用户名或邮箱已被注册' });
    }

    // 创建用户
    const user = await User.create({
      username,
      email,
      password,
      phone
    });

    sendTokenResponse(user, 201, res);
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// @desc    用户登录
// @route   POST /api/users/login
// @access  Public
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证输入
    if (!email || !password) {
      return res.status(400).json({ success: false, message: '请提供邮箱和密码' });
    }

    // 检查用户是否存在
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({ success: false, message: '无效的凭据' });
    }

    // 检查密码是否匹配
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      return res.status(401).json({ success: false, message: '无效的凭据' });
    }

    // 更新最后登录时间
    user.lastActive = Date.now();
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// @desc    获取当前登录用户
// @route   GET /api/users/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// @desc    更新用户资料
// @route   PUT /api/users/profile
// @access  Private
exports.updateProfile = async (req, res) => {
  try {
    const fieldsToUpdate = {
      username: req.body.username,
      email: req.body.email,
      phone: req.body.phone,
      bio: req.body.bio,
      avatar: req.body.avatar
    };

    // 过滤掉未定义的字段
    Object.keys(fieldsToUpdate).forEach(key => 
      fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
    );

    const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
      new: true,
      runValidators: true
    });

    if (!user) {
      return res.status(404).json({ success: false, message: '未找到用户' });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// @desc    修改密码
// @route   PUT /api/users/password
// @access  Private
exports.updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // 获取带密码的用户
    const user = await User.findById(req.user.id).select('+password');

    // 检查当前密码
    if (!(await user.matchPassword(currentPassword))) {
      return res.status(401).json({ success: false, message: '当前密码不正确' });
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// @desc    获取指定用户的公开资料
// @route   GET /api/users/:id
// @access  Public
exports.getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-email -phone');

    if (!user) {
      return res.status(404).json({ success: false, message: '未找到用户' });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: err.message
    });
  }
};

// 生成令牌并发送响应
const sendTokenResponse = (user, statusCode, res) => {
  // 生成令牌
  const token = user.getSignedJwtToken();

  // 去除敏感字段
  const userData = { ...user.toJSON() };
  delete userData.password;

  res.status(statusCode).json({
    success: true,
    token,
    data: userData
  });
}; 