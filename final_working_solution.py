#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终工作解决方案
基于搜索结果的综合绕过方案
"""

import requests
import urllib3
import time
import random
import string
from decrypt_analysis import EncryptionSystem

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FinalWorkingSolution:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.crypto = EncryptionSystem()
        self.session = requests.Session()
        
        print("🎯 最终工作解决方案")
        print("🔧 基于网络搜索的综合绕过技术")
        print("=" * 50)

    def get_advanced_headers(self):
        """获取高级反检测请求头"""
        return {
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'dnt': '1',
            'origin': 'https://ds-web1.yrpdz.com',
            'pragma': 'no-cache',
            'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?1',
            'sec-ch-ua-platform': '"Android"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }

    def simulate_browser_session(self):
        """模拟真实浏览器会话"""
        print("🌐 模拟真实浏览器会话...")
        
        # 1. 首先访问主页
        try:
            print("📱 访问主页...")
            response = self.session.get(
                self.base_url,
                headers=self.get_advanced_headers(),
                timeout=10,
                verify=False
            )
            print(f"主页访问状态: {response.status_code}")
            
            # 随机延时
            time.sleep(random.uniform(1, 3))
            
            # 2. 访问登录页面
            print("📱 访问登录页面...")
            login_url = f"{self.base_url}/pages/login/login?code=21328050"
            response = self.session.get(
                login_url,
                headers=self.get_advanced_headers(),
                timeout=10,
                verify=False
            )
            print(f"登录页面访问状态: {response.status_code}")
            
            # 获取cookie
            cookies = {}
            for cookie in self.session.cookies:
                cookies[cookie.name] = cookie.value
            
            print(f"🍪 获取到 {len(cookies)} 个cookie")
            for name, value in cookies.items():
                print(f"  {name}: {value[:20]}...")
            
            return cookies
            
        except Exception as e:
            print(f"❌ 模拟浏览器会话失败: {e}")
            return None

    def make_api_request_with_session(self, api_path, data, description=""):
        """使用会话发送API请求"""
        print(f"\n🚀 {description}")
        print("-" * 40)
        
        try:
            # 加密数据
            encrypted = self.crypto.encrypt_data(data)
            sign = self.crypto.generate_sign(data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return None
            
            # 获取高级请求头
            headers = self.get_advanced_headers()
            
            # 添加加密相关头部
            headers.update({
                'token': '',
                'sign': sign,
                'transfersecret': encrypted,
                'is_app': 'false'
            })
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            print(f"📤 请求数据: {data}")
            print(f"🔐 加密结果: {encrypted[:30]}...")
            print(f"✍️ 签名结果: {sign}")
            
            # 发送请求（使用session保持cookie）
            response = self.session.post(
                f"{self.base_url}{api_path}",
                headers=headers,
                json=request_body,
                timeout=15,
                verify=False
            )
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"📏 响应长度: {len(response.text)}")
            
            # 检查响应
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("❌ 返回HTML页面，可能被拦截")
                # 检查是否是重定向页面
                if 'baidu.com' in response.text or 'redirect' in response.text.lower():
                    print("🔄 检测到重定向，可能触发了反爬虫")
                return None
            else:
                print("✅ 返回API响应")
                print(f"响应内容: {response.text}")
                
                # 尝试解密
                try:
                    decrypted = self.crypto.decrypt_data(response.text)
                    if decrypted:
                        print(f"🔓 解密成功: {decrypted}")
                        return decrypted
                    else:
                        print("⚠️ 响应可能是明文")
                        return response.text
                except:
                    print("⚠️ 响应不是加密格式")
                    return response.text
                
        except Exception as e:
            print(f"❌ API请求失败: {e}")
            return None

    def test_multiple_approaches(self):
        """测试多种绕过方法"""
        print("\n🧪 测试多种绕过方法")
        print("=" * 50)
        
        approaches = [
            {
                'name': '方法1: 标准会话',
                'func': self.test_standard_session
            },
            {
                'name': '方法2: 延时请求',
                'func': self.test_delayed_request
            },
            {
                'name': '方法3: 多步骤模拟',
                'func': self.test_multi_step_simulation
            }
        ]
        
        for approach in approaches:
            print(f"\n🔬 {approach['name']}")
            print("-" * 30)
            
            try:
                result = approach['func']()
                if result:
                    print(f"✅ {approach['name']} 成功！")
                    return True
                else:
                    print(f"❌ {approach['name']} 失败")
            except Exception as e:
                print(f"❌ {approach['name']} 异常: {e}")
            
            # 方法间延时
            time.sleep(2)
        
        return False

    def test_standard_session(self):
        """测试标准会话方法"""
        cookies = self.simulate_browser_session()
        if not cookies:
            return False
        
        result = self.make_api_request_with_session(
            "/dev-api/api/login/authccode.html",
            {},
            "标准会话验证码请求"
        )
        return result is not None

    def test_delayed_request(self):
        """测试延时请求方法"""
        print("⏳ 执行延时策略...")
        
        # 模拟用户行为延时
        cookies = self.simulate_browser_session()
        if not cookies:
            return False
        
        # 额外延时模拟用户思考时间
        time.sleep(random.uniform(3, 6))
        
        result = self.make_api_request_with_session(
            "/dev-api/api/login/authccode.html",
            {},
            "延时验证码请求"
        )
        return result is not None

    def test_multi_step_simulation(self):
        """测试多步骤模拟方法"""
        print("🎭 执行多步骤用户行为模拟...")
        
        # 1. 访问主页
        cookies = self.simulate_browser_session()
        if not cookies:
            return False
        
        # 2. 模拟用户在页面上的操作
        print("🖱️ 模拟用户交互...")
        time.sleep(random.uniform(2, 4))
        
        # 3. 模拟鼠标移动（通过发送额外请求）
        try:
            self.session.get(
                f"{self.base_url}/favicon.ico",
                headers=self.get_advanced_headers(),
                timeout=5,
                verify=False
            )
        except:
            pass
        
        time.sleep(random.uniform(1, 2))
        
        # 4. 发送API请求
        result = self.make_api_request_with_session(
            "/dev-api/api/login/authccode.html",
            {},
            "多步骤模拟验证码请求"
        )
        return result is not None

    def run_final_solution(self):
        """运行最终解决方案"""
        print("\n🎯 运行最终解决方案")
        print("=" * 50)
        
        success = self.test_multiple_approaches()
        
        if success:
            print("\n🎉 找到有效的绕过方法！")
            print("✅ 可以继续实现完整的注册流程")
            
            # 继续实现注册流程
            return self.complete_registration_flow()
        else:
            print("\n💔 所有方法都失败了")
            print("\n💡 建议尝试:")
            print("1. 使用代理IP")
            print("2. 更换User-Agent")
            print("3. 增加更多延时")
            print("4. 使用真实浏览器自动化")
            return False

    def complete_registration_flow(self):
        """完成注册流程"""
        print("\n📝 执行完整注册流程...")
        
        # 生成测试数据
        def generate_phone():
            prefixes = ['130', '131', '132', '133', '134', '135']
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return prefix + suffix
        
        def generate_password(length=8):
            chars = string.ascii_letters + string.digits
            return ''.join(random.choice(chars) for _ in range(length))
        
        phone = generate_phone()
        password = generate_password()
        
        print(f"📱 生成手机号: {phone}")
        print(f"🔐 生成密码: {password}")
        
        # 1. 获取验证码
        captcha_result = self.make_api_request_with_session(
            "/dev-api/api/login/authccode.html",
            {},
            "获取图片验证码"
        )
        
        if not captcha_result:
            print("❌ 获取验证码失败")
            return False
        
        # 2. 发送短信验证码
        sms_result = self.make_api_request_with_session(
            "/dev-api/api/login/smscode.html",
            {"phone": phone, "code": ""},
            f"发送短信验证码到 {phone}"
        )
        
        if sms_result:
            print("🎉 短信发送成功！")
            print("📱 请查收短信验证码")
            
            # 这里可以继续实现注册逻辑
            # 需要用户输入短信验证码
            return True
        else:
            print("❌ 短信发送失败")
            return False


if __name__ == "__main__":
    solution = FinalWorkingSolution()
    success = solution.run_final_solution()
    
    if success:
        print("\n🎊 最终解决方案成功！")
    else:
        print("\n💔 需要进一步优化方案")
