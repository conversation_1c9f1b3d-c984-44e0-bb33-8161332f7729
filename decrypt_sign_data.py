#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 解密sign数据分析脚本

import json
import base64
from urllib.parse import unquote
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def decrypt_sign_data(sign_data):
    """解密sign数据"""
    try:
        print("🔍 解密sign数据分析")
        print("=" * 50)
        
        # 你提供的sign数据
        encoded_sign = "sign=%7B%22ct%22%3A%22d8%2B1K3VFqOWPoZxFeaY4GePDAkLSXRsLIxmO%2FJOwRUgNcmwpHmUCxqtSpUrppoGm%22%2C%22iv%22%3A%22fc959df3b5cbe47782e6c267450b5795%22%2C%22s%22%3A%228802485aa551eada%22%7D"
        
        # URL解码
        decoded_sign = unquote(encoded_sign)
        print(f"📝 URL解码后: {decoded_sign}")
        
        # 提取sign参数值
        sign_value = decoded_sign.replace('sign=', '')
        print(f"📝 Sign值: {sign_value}")
        
        # 解析JSON
        sign_json = json.loads(sign_value)
        print(f"📝 Sign JSON: {sign_json}")
        
        ct = sign_json['ct']
        iv = sign_json['iv']
        s = sign_json['s']
        
        print(f"🔐 加密内容 (ct): {ct}")
        print(f"🔐 初始化向量 (iv): {iv}")
        print(f"🔐 盐值 (s): {s}")
        
        # 尝试解密 (需要知道密钥)
        print("\n🔍 尝试解密...")
        
        # 从你的脚本中获取密钥生成逻辑
        # 密钥通常是基于某些固定值生成的
        
        # 常见的密钥可能性
        possible_keys = [
            "1234567890123456",  # 16字节
            "abcdef1234567890",
            "huanqiu12345678",
            "cgi91312345678ab",
        ]
        
        for key in possible_keys:
            try:
                key_bytes = key.encode('utf-8')
                iv_bytes = bytes.fromhex(iv)
                ct_bytes = base64.b64decode(ct)
                
                cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)
                decrypted = unpad(cipher.decrypt(ct_bytes), AES.block_size)
                decrypted_text = decrypted.decode('utf-8')
                
                print(f"✅ 使用密钥 '{key}' 解密成功:")
                print(f"   解密结果: {decrypted_text}")
                return decrypted_text
                
            except Exception as e:
                print(f"❌ 密钥 '{key}' 解密失败: {e}")
                continue
        
        print("❌ 所有密钥都解密失败")
        return None
        
    except Exception as e:
        print(f"❌ 解密过程发生错误: {e}")
        return None

def analyze_request_data():
    """分析请求数据格式"""
    print("\n🔍 分析请求数据格式")
    print("=" * 50)
    
    # 你提供的完整请求数据
    request_data = "sign=%7B%22ct%22%3A%22d8%2B1K3VFqOWPoZxFeaY4GePDAkLSXRsLIxmO%2FJOwRUgNcmwpHmUCxqtSpUrppoGm%22%2C%22iv%22%3A%22fc959df3b5cbe47782e6c267450b5795%22%2C%22s%22%3A%228802485aa551eada%22%7D&code_id=17538686036889e93b57e57973100&code=9753&invitation=263154&paypassword=147258"
    
    print(f"📝 原始请求数据:")
    print(f"   {request_data}")
    
    # URL解码
    decoded_data = unquote(request_data)
    print(f"\n📝 URL解码后:")
    print(f"   {decoded_data}")
    
    # 分析参数
    params = decoded_data.split('&')
    print(f"\n📋 请求参数分析:")
    for param in params:
        if '=' in param:
            key, value = param.split('=', 1)
            print(f"   {key}: {value}")
    
    # 分析成功响应
    print(f"\n📥 成功响应分析:")
    response_data = {
        "code": 1,
        "msg": "注册成功",
        "time": "1753868618",
        "data": {
            "userinfo": {
                "id": 396540,
                "username": "15656879846",
                "nickname": "15656879846",
                "token": "de53d146-5329-403b-af2c-11e8314d3c37",
                "user_id": 396540,
                "createtime": 1753868618,
                "expiretime": 1756460618,
                "expires_in": 2592000
            }
        }
    }
    
    print(f"   响应格式: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

def compare_password_formats():
    """比较密码格式"""
    print("\n🔍 密码格式对比分析")
    print("=" * 50)
    
    # 我们生成的密码
    our_password = "y7ojohhkAt"
    
    # 分析密码特征
    print(f"📝 我们的密码: {our_password}")
    print(f"   长度: {len(our_password)}")
    print(f"   包含小写字母: {any(c.islower() for c in our_password)}")
    print(f"   包含大写字母: {any(c.isupper() for c in our_password)}")
    print(f"   包含数字: {any(c.isdigit() for c in our_password)}")
    print(f"   只包含字母数字: {our_password.isalnum()}")
    
    # 建议的密码格式
    print(f"\n💡 建议测试的密码格式:")
    test_passwords = [
        "123456",      # 6位纯数字
        "abc123",      # 小写+数字
        "Abc123",      # 大小写+数字
        "password1",   # 小写+数字
        "Password1",   # 大小写+数字
        "12345abc",    # 数字+小写
        "123Abc",      # 数字+大小写
    ]
    
    for pwd in test_passwords:
        print(f"   {pwd:12s} (长度:{len(pwd)}, 大写:{any(c.isupper() for c in pwd)}, 小写:{any(c.islower() for c in pwd)}, 数字:{any(c.isdigit() for c in pwd)})")

def main():
    """主函数"""
    print("🔍 Sign数据解密和格式分析工具")
    print("🔧 分析注册请求的真实数据格式")
    print("=" * 60)
    
    # 解密sign数据
    decrypted = decrypt_sign_data()
    
    # 分析请求数据
    analyze_request_data()
    
    # 比较密码格式
    compare_password_formats()
    
    print("\n" + "=" * 60)
    print("📊 分析总结:")
    print("1. 需要找到正确的AES解密密钥")
    print("2. 确认密码的真实格式要求")
    print("3. 可能需要调整密码生成策略")
    print("4. 注册和登录需要使用代理IP")
    
    print("\n💡 下一步建议:")
    print("1. 手动测试不同格式的密码")
    print("2. 启用代理IP进行注册")
    print("3. 对比成功注册的密码格式")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 分析被用户中断")
    except Exception as e:
        print(f"\n💥 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
