import type { InternalSelector } from "./types.js";
import { type Traversal } from "css-what";
export declare function isTraversal(token: InternalSelector): token is Traversal;
/**
 * Sort the parts of the passed selector,
 * as there is potential for optimization
 * (some types of selectors are faster than others)
 *
 * @param arr Selector to sort
 */
export default function sortByProcedure(arr: InternalSelector[]): void;
//# sourceMappingURL=sort.d.ts.map