#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
project_dir = current_dir / "phone_lists" / "识别项目"
sys.path.insert(0, str(project_dir))

# 导入整理模块
from 整理 import IDCardOrganizer

def main():
    """运行身份证图片整理程序"""
    print("=" * 50)
    print("身份证图片自动整理工具")
    print("=" * 50)
    
    # 源文件夹路径
    source_dir = r"C:\Users\<USER>\Desktop\sfz"
    
    # 输出文件夹路径（在当前目录下创建）
    output_dir = current_dir / "id_card_sorted"
    
    print(f"源文件夹: {source_dir}")
    print(f"输出文件夹: {output_dir}")
    
    # 检查源文件夹是否存在
    if not Path(source_dir).exists():
        print(f"错误：源文件夹不存在: {source_dir}")
        return
    
    try:
        # 创建整理器实例
        organizer = IDCardOrganizer(source_dir, output_dir)
        
        # 开始处理图片
        print("\n开始处理图片...")
        organizer.process_images()
        
        print("\n处理完成！")
        print(f"结果已保存到: {output_dir}")
        print("文件夹结构:")
        print(f"  - 正面: {output_dir / '正面'}")
        print(f"  - 反面: {output_dir / '反面'}")
        print(f"  - 未识别: {output_dir / '未识别'}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
