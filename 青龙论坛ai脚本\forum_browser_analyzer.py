import os
import time
import json
import logging
import subprocess
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr
import requests

# 引入配置文件
import config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 浏览器工具路径
BROWSER_TOOL_PATH = "browser-tools-mcp"

def load_seen_posts():
    """加载已经处理过的帖子URL"""
    if not os.path.exists(config.SEEN_POSTS_FILE):
        return set()
    with open(config.SEEN_POSTS_FILE, 'r', encoding='utf-8') as f:
        return set(line.strip() for line in f)

def save_seen_post(url):
    """保存已处理的帖子URL到文件"""
    with open(config.SEEN_POSTS_FILE, 'a', encoding='utf-8') as f:
        f.write(url + '\n')

def is_post_seen(url):
    """检查帖子是否已经处理过"""
    return url in seen_posts_set

def run_browser_command(command, args=None):
    """运行浏览器工具命令"""
    full_command = [BROWSER_TOOL_PATH, command]
    if args:
        full_command.extend(args)
    
    logging.info(f"执行浏览器命令: {' '.join(full_command)}")
    
    try:
        result = subprocess.run(
            full_command, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        logging.error(f"浏览器命令执行失败: {e}")
        logging.error(f"错误输出: {e.stderr}")
        return None
    except json.JSONDecodeError as e:
        logging.error(f"解析浏览器输出JSON失败: {e}")
        logging.error(f"原始输出: {result.stdout if 'result' in locals() else '无输出'}")
        return None

def login_forum(username, password):
    """登录论坛"""
    logging.info("尝试登录论坛...")
    
    # 启动浏览器，根据配置决定是否使用无头模式
    headless = getattr(config, "HEADLESS_MODE", True)
    browser_id = run_browser_command("launch", ["--headless", str(headless).lower()])
    if not browser_id:
        logging.error("启动浏览器失败")
        return None
    
    browser_id = browser_id.get("browserId")
    logging.info(f"浏览器启动成功，ID: {browser_id}")
    
    # 访问登录页面
    login_url = "https://www.51kanong.com/member.php?mod=logging&action=login"
    navigate_result = run_browser_command("navigate", [
        "--browser-id", browser_id,
        "--url", login_url
    ])
    
    if not navigate_result:
        logging.error("访问登录页面失败")
        run_browser_command("close", ["--browser-id", browser_id])
        return None
    
    # 等待页面加载
    wait_time = getattr(config, "PAGE_LOAD_WAIT", 3)
    time.sleep(wait_time)
    
    # 检查是否有登录表单
    form_exists = run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", "document.querySelector('#lsform') !== null"
    ])
    
    if not form_exists or not form_exists.get("result"):
        logging.error("找不到登录表单")
        run_browser_command("close", ["--browser-id", browser_id])
        return None
    
    # 填写用户名和密码
    run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", f"document.querySelector('#ls_username').value = '{username}'"
    ])
    
    run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", f"document.querySelector('#ls_password').value = '{password}'"
    ])
    
    # 点击登录按钮
    run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", "document.querySelector('#lsform').submit()"
    ])
    
    # 等待登录完成
    time.sleep(wait_time)
    
    # 检查登录是否成功
    is_logged_in = run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", "document.querySelector('.vwmy') !== null || document.querySelector('.avatar') !== null"
    ])
    
    if not is_logged_in or not is_logged_in.get("result"):
        logging.warning("登录可能失败，但将继续尝试")
    else:
        logging.info("登录成功！")
    
    return browser_id

def scrape_forum_with_browser(browser_id, forum_name, forum_url):
    """使用浏览器爬取论坛内容"""
    logging.info(f"正在爬取论坛板块: {forum_name}")
    
    # 导航到论坛页面
    navigate_result = run_browser_command("navigate", [
        "--browser-id", browser_id,
        "--url", forum_url
    ])
    
    if not navigate_result:
        logging.error(f"访问论坛页面失败: {forum_url}")
        return []
    
    # 等待页面加载
    wait_time = getattr(config, "PAGE_LOAD_WAIT", 5)
    time.sleep(wait_time)
    
    # 提取帖子列表
    threads_data = run_browser_command("evaluate", [
        "--browser-id", browser_id,
        "--expression", """
        (function() {
            const threads = [];
            const threadElements = document.querySelectorAll('tbody[id^="normalthread_"]');
            
            threadElements.forEach(thread => {
                const titleElement = thread.querySelector('a.s.xst');
                if (titleElement) {
                    const title = titleElement.textContent.trim();
                    const url = titleElement.href;
                    threads.push({title, url});
                }
            });
            
            return threads;
        })()
        """
    ])
    
    if not threads_data or not threads_data.get("result"):
        logging.error("提取帖子列表失败")
        return []
    
    threads = threads_data.get("result", [])
    logging.info(f"找到 {len(threads)} 个帖子")
    
    new_posts = []
    for thread in threads:
        post_title = thread.get("title")
        post_url = thread.get("url")
        
        if not post_url or is_post_seen(post_url):
            continue
        
        logging.info(f"发现新帖子: {post_title}")
        
        # 访问帖子详情页
        navigate_result = run_browser_command("navigate", [
            "--browser-id", browser_id,
            "--url", post_url
        ])
        
        if not navigate_result:
            logging.error(f"访问帖子详情页失败: {post_url}")
            continue
        
        # 等待页面加载
        time.sleep(wait_time)
        
        # 提取帖子内容
        post_data = run_browser_command("evaluate", [
            "--browser-id", browser_id,
            "--expression", """
            (function() {
                // 获取主楼内容
                const contentDiv = document.querySelector('td[id^="postmessage_"]');
                if (!contentDiv) return {content: "无法提取内容"};
                
                // 提取文本内容
                const content = contentDiv.innerText;
                
                // 提取图片
                const images = Array.from(contentDiv.querySelectorAll('img')).map(img => {
                    return {
                        src: img.src,
                        alt: img.alt || ""
                    };
                });
                
                // 提取附件
                const attachments = Array.from(document.querySelectorAll('.attachlist')).map(attach => {
                    const links = Array.from(attach.querySelectorAll('a')).map(a => a.href);
                    return links;
                }).flat();
                
                // 提取帖子的其他元数据
                const author = document.querySelector('.authi .xw1')?.innerText || "未知作者";
                const postTime = document.querySelector('.authi em')?.innerText || "";
                
                return {
                    content,
                    images,
                    attachments,
                    author,
                    postTime
                };
            })()
            """
        ])
        
        if not post_data or not post_data.get("result"):
            logging.error(f"提取帖子内容失败: {post_url}")
            continue
        
        post_content = post_data.get("result", {}).get("content", "无法提取内容")
        post_images = post_data.get("result", {}).get("images", [])
        post_attachments = post_data.get("result", {}).get("attachments", [])
        post_author = post_data.get("result", {}).get("author", "未知作者")
        post_time = post_data.get("result", {}).get("postTime", "")
        
        # 将图片和附件信息添加到内容中
        if post_images:
            img_urls = [img.get("src") for img in post_images if img.get("src")]
            if img_urls:
                post_content += "\n\n--- 图片附件 ---\n" + "\n".join(img_urls)
        
        if post_attachments:
            post_content += "\n\n--- 其他附件 ---\n" + "\n".join(post_attachments)
        
        new_posts.append({
            'title': post_title,
            'url': post_url,
            'content': post_content,
            'forum_name': forum_name,
            'author': post_author,
            'post_time': post_time
        })
        
        # 友好等待
        time.sleep(2)
    
    return new_posts

def analyze_with_ai(title, content):
    """使用AI分析帖子内容"""
    logging.info(f"正在发送内容到AI进行分析: {title}")
    if len(content) > 10000:
        content = content[:10000] + "\n... (内容过长，已被截断)"

    api_url = f"{config.GEMINI_API_HOST}/v1beta/models/gemini-1.5-flash-latest:generateContent?key={config.GEMINI_API_KEY}"
    headers = {'Content-Type': 'application/json'}
    data = {"contents": [{"parts": [{"text": config.AI_PROMPT_TEMPLATE.format(title=title, content=content)}]}]}
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=120)
        response.raise_for_status()
        result = response.json()
        analysis = result['candidates'][0]['content']['parts'][0]['text']
        return analysis
    except Exception as e:
        logging.error(f"AI分析失败: {e}")
        if 'response' in locals() and hasattr(response, 'text'):
            logging.error(f"AI API 返回内容: {response.text}")
        return "AI分析时出现错误，请检查API Key和网络连接。"

def send_email(subject, mail_body):
    """发送邮件通知"""
    logging.info("准备发送邮件...")
    msg = MIMEText(mail_body, 'html', 'utf-8')
    msg['From'] = formataddr((Header("AI论坛分析师", 'utf-8').encode(), config.SENDER_EMAIL))
    msg['To'] = formataddr((Header("尊敬的管理员", 'utf-8').encode(), config.RECEIVER_EMAIL))
    msg['Subject'] = Header(subject, 'utf-8')

    try:
        server = smtplib.SMTP_SSL(config.SMTP_SERVER, config.SMTP_PORT)
        server.login(config.SENDER_EMAIL, config.SENDER_PASSWORD)
        server.sendmail(config.SENDER_EMAIL, [config.RECEIVER_EMAIL], msg.as_string())
        server.quit()
        logging.info("✅ 邮件发送成功！")
    except Exception as e:
        logging.error(f"❌ 邮件发送失败: {e}")

def main():
    """主函数"""
    logging.info("="*30)
    logging.info("开始执行浏览器自动化论坛AI爬虫任务...")
    
    global seen_posts_set
    seen_posts_set = load_seen_posts()
    
    # 如果配置了登录信息，则尝试登录
    browser_id = None
    if hasattr(config, 'FORUM_USERNAME') and hasattr(config, 'FORUM_PASSWORD') and config.FORUM_USERNAME and config.FORUM_PASSWORD:
        browser_id = login_forum(config.FORUM_USERNAME, config.FORUM_PASSWORD)
    else:
        # 未配置登录信息，直接启动浏览器
        headless = getattr(config, "HEADLESS_MODE", True)
        browser_launch_result = run_browser_command("launch", ["--headless", str(headless).lower()])
        if browser_launch_result:
            browser_id = browser_launch_result.get("browserId")
            logging.info(f"浏览器启动成功，ID: {browser_id}")
    
    if not browser_id:
        logging.error("浏览器启动失败，退出程序")
        return
    
    try:
        all_new_posts = []
        
        # 遍历所有要监控的板块
        for forum in config.FORUM_URLS:
            # 检查URL是否还是占位符
            if "请在这里填入" in forum['url']:
                logging.warning(f"跳过板块 '{forum['name']}'，因为URL尚未配置。")
                continue
                
            logging.info(f"--- 正在检查板块: {forum['name']} ---")
            new_posts_from_forum = scrape_forum_with_browser(browser_id, forum['name'], forum['url'])
            if new_posts_from_forum:
                all_new_posts.extend(new_posts_from_forum)
            time.sleep(5)  # 友好访问，在检查不同板块间稍作停留
        
        if not all_new_posts:
            logging.info("所有已配置的板块均无新主题，任务结束。")
        else:
            total_posts = len(all_new_posts)
            logging.info(f"所有板块共发现 {total_posts} 个新主题，开始汇总处理...")
            
            report_parts = []
            processed_posts_urls = []

            for i, post in enumerate(all_new_posts):
                logging.info(f"正在处理第 {i+1}/{total_posts} 个: {post['title']}")
                ai_analysis_result = analyze_with_ai(post['title'], post['content'])
                
                # 为每个帖子创建一个独立的HTML报告部分
                part = f"""
                <div style="border-bottom: 2px solid #ccc; padding-bottom: 20px; margin-bottom: 20px;">
                    <h2 style="color: #333;">{i+1}. {post['title']}</h2>
                    <p>
                        <strong>所属板块:</strong> {post['forum_name']}<br>
                        <strong>作者:</strong> {post['author']}<br>
                        <strong>发布时间:</strong> {post['post_time']}<br>
                        <strong>源帖链接:</strong> <a href="{post['url']}">{post['url']}</a>
                    </p>
                    <h3 style="color: #444;">AI分析结果:</h3>
                    <div style="background-color: #f0f8ff; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                        <pre style="white-space: pre-wrap; font-family: 'Courier New', Courier, monospace; font-size: 14px;">{ai_analysis_result}</pre>
                    </div>
                </div>
                """
                report_parts.append(part)
                processed_posts_urls.append(post['url'])
                time.sleep(10)  # 避免过于频繁地请求AI API

            # 将所有报告部分组合成一个完整的HTML邮件
            full_report_html = "".join(report_parts)
            
            # 创建邮件主题和完整的HTML内容
            today_str = datetime.now().strftime('%Y年%m月%d日')
            email_subject = f"每日AI分析简报 ({today_str}) - {total_posts}个新主题"
            email_content_html = f"""
            <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .container {{ max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background-color: #f9f9f9; }}
                        h1 {{ color: #0056b3; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>每日AI分析简报 - {today_str}</h1>
                        <p>今日共为您发现并分析了 <strong>{total_posts}</strong> 个来自不同板块的新主题，详情如下：</p>
                        <hr>
                        {full_report_html}
                        <p style="text-align: center; color: #888; font-size: 12px;">--- 报告结束 ---</p>
                    </div>
                </body>
            </html>
            """

            # 发送汇总邮件
            send_email(email_subject, email_content_html)

            # 邮件发送成功后，将所有处理过的URL保存
            for url in processed_posts_urls:
                save_seen_post(url)
                seen_posts_set.add(url)
            
            logging.info("每日简报已发送，并已更新所有帖子的处理记录。")

    finally:
        # 确保浏览器被关闭
        if browser_id:
            run_browser_command("close", ["--browser-id", browser_id])
            logging.info(f"浏览器已关闭，ID: {browser_id}")

    logging.info("所有新主题处理完毕。")
    logging.info("="*30 + "\n")

if __name__ == '__main__':
    main() 