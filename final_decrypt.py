#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 最终解密脚本

import json
import base64
from urllib.parse import unquote
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib

def decrypt_sign():
    """解密sign数据"""
    print("🔍 解密sign数据")
    print("=" * 50)
    
    # 原始数据
    raw_data = "sign=%7B%22ct%22%3A%22d8%2B1K3VFqOWPoZxFeaY4GePDAkLSXRsLIxmO%2FJOwRUgNcmwpHmUCxqtSpUrppoGm%22%2C%22iv%22%3A%22fc959df3b5cbe47782e6c267450b5795%22%2C%22s%22%3A%228802485aa551eada%22%7D&code_id=17538686036889e93b57e57973100&code=9753&invitation=263154&paypassword=147258"
    
    # URL解码
    decoded = unquote(raw_data)
    sign_part = decoded.split('&')[0].replace('sign=', '')
    
    print(f"📝 Sign JSON: {sign_part}")
    
    # 解析JSON
    sign_json = json.loads(sign_part)
    ct = sign_json['ct']
    iv = sign_json['iv']
    s = sign_json['s']
    
    print(f"📝 加密参数:")
    print(f"   ct: {ct}")
    print(f"   iv: {iv}")
    print(f"   s: {s}")
    
    # 尝试不同的密钥处理方式
    base_key = "v4NTEx37"
    
    # 方法1: 直接使用密钥，填充到16字节
    key1 = base_key.ljust(16, '0').encode('utf-8')
    
    # 方法2: 重复密钥到16字节
    key2 = (base_key * 3)[:16].encode('utf-8')
    
    # 方法3: MD5哈希密钥
    key3 = hashlib.md5(base_key.encode()).digest()
    
    # 方法4: 密钥 + 盐值的组合
    key4 = hashlib.md5((base_key + s).encode()).digest()
    
    # 方法5: 只使用前8字节，然后重复
    key5 = (base_key[:8] * 2).encode('utf-8')
    
    keys_to_try = [
        ("填充到16字节", key1),
        ("重复到16字节", key2), 
        ("MD5哈希", key3),
        ("密钥+盐值MD5", key4),
        ("前8字节重复", key5)
    ]
    
    for method, key_bytes in keys_to_try:
        try:
            print(f"\n🔐 尝试方法: {method}")
            print(f"   密钥: {key_bytes.hex()}")
            
            iv_bytes = bytes.fromhex(iv)
            ct_bytes = base64.b64decode(ct)
            
            cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)
            decrypted = unpad(cipher.decrypt(ct_bytes), AES.block_size)
            decrypted_text = decrypted.decode('utf-8')
            
            print(f"✅ 解密成功!")
            print(f"📝 解密结果: {decrypted_text}")
            
            # 尝试解析JSON
            try:
                decrypted_json = json.loads(decrypted_text)
                print(f"📋 解密的数据:")
                for key, value in decrypted_json.items():
                    print(f"   {key}: {value}")
                
                # 分析密码
                if 'password' in decrypted_json:
                    password = decrypted_json['password']
                    print(f"\n🔐 密码分析:")
                    print(f"   密码: '{password}'")
                    print(f"   长度: {len(password)}")
                    print(f"   类型: ", end="")
                    
                    if password.isdigit():
                        print("纯数字")
                    elif password.isalpha():
                        if password.islower():
                            print("纯小写字母")
                        elif password.isupper():
                            print("纯大写字母")
                        else:
                            print("大小写字母混合")
                    elif password.isalnum():
                        parts = []
                        if any(c.islower() for c in password):
                            parts.append("小写字母")
                        if any(c.isupper() for c in password):
                            parts.append("大写字母")
                        if any(c.isdigit() for c in password):
                            parts.append("数字")
                        print(" + ".join(parts))
                    else:
                        print("包含特殊字符")
                
                return decrypted_json
                
            except json.JSONDecodeError:
                print(f"⚠️ 不是JSON格式: {decrypted_text}")
                return decrypted_text
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            continue
    
    print("\n❌ 所有方法都失败了")
    return None

def analyze_other_params():
    """分析其他参数"""
    print(f"\n📋 其他参数:")
    print("=" * 50)
    
    params = {
        'code_id': '17538686036889e93b57e57973100',
        'code': '9753', 
        'invitation': '263154',
        'paypassword': '147258'
    }
    
    for key, value in params.items():
        print(f"   {key}: {value}")

def main():
    """主函数"""
    print("🔍 Sign数据最终解密")
    print("🔧 使用JavaScript中的密钥: v4NTEx37")
    print("=" * 60)
    
    # 解密
    result = decrypt_sign()
    
    # 分析其他参数
    analyze_other_params()
    
    print("\n" + "=" * 60)
    if result:
        print("✅ 解密成功!")
        if isinstance(result, dict) and 'password' in result:
            password = result['password']
            print(f"🎯 关键发现: 真实密码是 '{password}'")
            print(f"💡 现在可以根据这个格式调整密码生成策略")
        else:
            print(f"📝 解密内容: {result}")
    else:
        print("❌ 解密失败")
        print("💡 可能需要进一步分析加密算法")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 解密被用户中断")
    except Exception as e:
        print(f"\n💥 解密过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
