import requests
import json
import base64
import time
import hmac
import hashlib
from Crypto.Cipher import AES, PKCS1_v1_5
from Crypto.PublicKey import RSA
from Crypto.Util.Padding import pad, unpad

# --- 1. 全局配置 ---
BASE_URL = "http://**************:8095"
API_KEY_STRING = "212i919292901"
GLOBAL_HMAC_KEY = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
FIXED_IV = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")
DEVICE_MODEL = "22041216UC"
OS_VERSION = "10"
DEVICE_SERIAL = "f57d62bf7a7d4631beae4b23e389d95e"
USER_AGENT = f"chat_im/2.1.8 (Linux; U; Android {OS_VERSION}; {DEVICE_MODEL} Build/UP1A.231005.007)"

# --- 2. 核心加解密与签名函数 ---

def encrypt_login_password(password: str) -> str:
    """对明文密码进行三层哈希加密"""
    m1 = hashlib.md5(password.encode('utf-8')).digest()
    cipher_aes = AES.new(m1, AES.MODE_CBC, FIXED_IV)
    a1 = cipher_aes.encrypt(pad(m1, AES.block_size))
    final_hash = hashlib.md5(a1).hexdigest()
    return final_hash

def generate_hmac_b64(key: bytes, content: str) -> str:
    """生成HmacMD5签名并返回Base64编码"""
    signature = hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()
    return base64.b64encode(signature).decode('utf-8')

def decrypt_with_login_key(login_key: bytes, encrypted_data_b64: str) -> dict:
    """使用会话密钥(loginKey)解密服务器最终响应"""
    encrypted_data = base64.b64decode(encrypted_data_b64)
    cipher = AES.new(login_key, AES.MODE_CBC, FIXED_IV)
    decrypted_padded = cipher.decrypt(encrypted_data)
    decrypted = unpad(decrypted_padded, AES.block_size)
    return json.loads(decrypted.decode('utf-8'))

def perform_login(account: str, password: str):
    """执行完整的四步登录流程，获取最终的用户密钥和令牌"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Connection': 'keep-alive'
    })

    try:
        # --- 步骤1: 客户端加密密码 ---
        print("🔐 步骤1: 对明文密码进行三层哈希加密...")
        encrypted_password_hash = encrypt_login_password(password)
        print(f"✅ 密码哈希结果: {encrypted_password_hash}\n")

        # --- 可选步骤: 验证手机号 (基于你的分析) ---
        print("📞 步骤1.5: 验证手机号是否可用...")
        verify_salt = str(int(time.time() * 1000))

        # 基于你的分析：ApiKey + areaCode + telephone + verifyType + salt
        verify_secret_content = f"{API_KEY_STRING}92{account}1{verify_salt}"
        verify_secret = generate_hmac_b64(GLOBAL_HMAC_KEY, verify_secret_content)

        verify_params = {
            "areaCode": "92",
            "telephone": account,
            "verifyType": "1",
            "language": "zh",
            "salt": verify_salt,
            "secret": verify_secret
        }

        print(f"   验证请求参数: {verify_params}")
        verify_resp = session.get(f"{BASE_URL}/verify/telephone", params=verify_params, timeout=15)
        print(f"   验证响应: {verify_resp.text}")

        # 不管验证结果如何，继续登录流程
        time.sleep(0.1)  # 短暂延迟

        # --- 请求1: /auth/getLoginCode ---
        print("📡 步骤2: 请求 /auth/getLoginCode 获取一次性的登录Code...")
        salt1 = str(int(time.time() * 1000))

        # 基于你的Hook分析的精确顺序
        mac1_content = f"{API_KEY_STRING}92{account}{salt1}"
        mac1 = generate_hmac_b64(encrypted_password_hash.encode(), mac1_content)
        print(f"   MAC内容: {mac1_content}")
        print(f"   MAC签名: {mac1}")

        # Secret签名：基于你的Hook分析，参数按字母序排列 (account, areaCode, deviceId, language, mac, salt)
        # 从你的分析: 212i919292901***********androiduqfrhjos7XBBZRG8G/EMUA==921753679228082zh
        secret1_content = f"{API_KEY_STRING}{account}92android{mac1}zh{salt1}"
        secret1 = generate_hmac_b64(GLOBAL_HMAC_KEY, secret1_content)
        print(f"   Secret内容: {secret1_content}")
        print(f"   Secret签名: {secret1}")

        params_get_code = {
            "areaCode": "92",
            "deviceId": "android",
            "account": account,
            "mac": mac1,
            "language": "zh",
            "salt": salt1,
            "secret": secret1
        }

        print(f"   请求参数: {params_get_code}")
        resp_code = session.post(f"{BASE_URL}/auth/getLoginCode", data=params_get_code, timeout=15)
        print(f"   响应状态: {resp_code.status_code}")
        print(f"   响应内容: {resp_code.text}")
        resp_code_json = resp_code.json()
        
        if resp_code_json.get('resultCode') != 1:
            print(f"❌ 获取登录码失败: {resp_code_json}")
            return None

        user_id = resp_code_json['data']['userId']
        encrypted_code_b64 = resp_code_json['data']['code']
        print(f"✅ 成功! UserID: {user_id}")
        print(f"   加密Code: {encrypted_code_b64[:50]}...\n")

        # --- 请求2: /authkeys/getLoginPrivateKey ---
        print("步骤3: 请求 /authkeys/getLoginPrivateKey 获取加密私钥...")
        salt2 = str(int(time.time() * 1000))
        
        mac_content = f"{API_KEY_STRING}{user_id}{salt2}"
        mac_signature = generate_hmac_b64(encrypted_password_hash.encode(), mac_content)
        
        secret_content = f"{API_KEY_STRING}zh{mac_signature}{salt2}{user_id}"
        secret_signature = generate_hmac_b64(GLOBAL_HMAC_KEY, secret_content)

        params_get_key = {
            "userId": user_id, "mac": mac_signature, "language": "zh", "salt": salt2, "secret": secret_signature
        }
        resp_key = session.post(f"{BASE_URL}/authkeys/getLoginPrivateKey", data=params_get_key, timeout=15).json()

        if resp_key.get('resultCode') != 1:
            print(f"获取私钥失败: {resp_key}")
            return
            
        encrypted_private_key_b64 = resp_key['data']['privateKey']
        print("获取加密私钥成功!\n")

        # --- 步骤3: 本地解密操作 ---
        print("步骤4: 在本地进行解密，获取会话密钥 (LoginKey)...")
        password_md5_key = hashlib.md5(password.encode('utf-8')).digest()
        aes_cipher_rsa = AES.new(password_md5_key, AES.MODE_CBC, FIXED_IV)
        rsa_private_key_der = unpad(aes_cipher_rsa.decrypt(base64.b64decode(encrypted_private_key_b64)), AES.block_size)
        
        rsa_key = RSA.import_key(rsa_private_key_der)
        pkcs1_cipher = PKCS1_v1_5.new(rsa_key)
        login_key = pkcs1_cipher.decrypt(base64.b64decode(encrypted_code_b64), sentinel=None)
        print(f"成功解密得到 LoginKey: {base64.b64encode(login_key).decode()}\n")

        # --- 步骤4: 发起最终登录请求 ---
        print("步骤5: 使用 LoginKey 加密数据并发起最终登录请求 /user/login/v1...")
        salt3 = str(int(time.time() * 1000))

        # 5.1 构造内部mac
        mac3_items = sorted([("model", DEVICE_MODEL), ("osVersion", OS_VERSION), ("password", encrypted_password_hash), 
                             ("serial", DEVICE_SERIAL), ("salt", salt3), ("xmppVersion", "1")])
        mac3_content = API_KEY_STRING + "".join(v for _, v in mac3_items)
        mac3_signature = generate_hmac_b64(login_key, mac3_content)
        
        # 5.2 构造并加密data
        data_to_encrypt = {"osVersion": OS_VERSION, "serial": DEVICE_SERIAL, "xmppVersion": "1", "model": DEVICE_MODEL, "mac": mac3_signature}
        data_json = json.dumps(data_to_encrypt, separators=(',', ':'))
        aes_cipher_final = AES.new(login_key, AES.MODE_CBC, FIXED_IV)
        encrypted_data = base64.b64encode(aes_cipher_final.encrypt(pad(data_json.encode('utf-8'), AES.block_size))).decode('utf-8')

        # 5.3 构造最终的secret
        secret_final_content = f"{API_KEY_STRING}{encrypted_data}androidzh{salt3}{user_id}"
        secret_final = generate_hmac_b64(GLOBAL_HMAC_KEY, secret_final_content)
        
        params_final = {"data": encrypted_data, "userId": user_id, "deviceId": "android", "language": "zh", "salt": salt3, "secret": secret_final}

        resp_final = session.get(f"{BASE_URL}/user/login/v1", params=params_final, timeout=15).json()
        
        if resp_final.get('resultCode') != 1:
            print(f"最终登录失败: {resp_final}")
            return
        
        print("最终登录请求成功！\n")
        encrypted_final_data = resp_final['data']['data']
        
        # --- 步骤6: 解密最终响应 ---
        print("步骤6: 解密服务器返回的最终用户信息...")
        final_user_data = decrypt_with_login_key(login_key, encrypted_final_data)
        
        print("\n" + "="*20 + " 解密成功 " + "="*20)
        print("以下是包含您所有密钥和令牌的完整JSON数据：\n")
        print(json.dumps(final_user_data, indent=2, ensure_ascii=False))
        print("\n" + "="*57)
        
    except requests.exceptions.RequestException as e:
        print(f"网络请求出错: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    # --- 请在这里输入账号和密码 ---
    user_account = "***********"
    user_password = "111qqq"
    # ---------------------------
    
    perform_login(user_account, user_password)