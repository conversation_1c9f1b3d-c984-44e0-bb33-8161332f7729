#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鲲鹏通讯头像上传工具
基于HCY抓包数据分析结果
"""

import requests
import json
import os
import sys
import random
import glob
from pathlib import Path

# 头像上传API配置
AVATAR_UPLOAD_URL = "http://sc.kes337f.shop:8088/upload/uploadAvatarServlet"
AVATAR_FOLDER = r"C:\Users\<USER>\Desktop\avatar"

def get_avatar_files(avatar_folder=AVATAR_FOLDER):
    """获取头像文件列表"""
    if not os.path.exists(avatar_folder):
        print(f"❌ 头像文件夹不存在: {avatar_folder}")
        return []

    # 支持的图片格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
    avatar_files = []

    for ext in image_extensions:
        pattern = os.path.join(avatar_folder, ext)
        avatar_files.extend(glob.glob(pattern))

    print(f"📁 找到 {len(avatar_files)} 个头像文件")
    return avatar_files

def get_random_avatar(avatar_files):
    """随机选择一个头像文件"""
    if not avatar_files:
        return None
    return random.choice(avatar_files)

def upload_avatar(user_id, image_path, language="zh"):
    """
    上传头像
    
    Args:
        user_id (str): 用户ID
        image_path (str): 图片文件路径
        language (str): 语言设置，默认zh
    
    Returns:
        dict: 上传结果
    """
    print(f"🖼️ 开始上传头像...")
    print(f"👤 用户ID: {user_id}")
    print(f"📁 图片路径: {image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return {"success": False, "error": "文件不存在"}
    
    # 获取文件信息
    file_size = os.path.getsize(image_path)
    file_name = os.path.basename(image_path)
    print(f"📊 文件大小: {file_size} 字节")
    print(f"📋 文件名: {file_name}")
    
    try:
        # 准备multipart表单数据
        files = {
            'userId': (None, str(user_id)),
            'language': (None, language),
            'files': (file_name, open(image_path, 'rb'), 'image/jpeg')
        }
        
        # 设置请求头
        headers = {
            'User-Agent': 'chat_im/2.1.8 (Linux; U; Android 10; 22041216UC Build/UP1A.231005.007)',
            'Accept-Encoding': 'gzip',
            'Connection': 'Keep-Alive'
        }
        
        print(f"📡 发送上传请求到: {AVATAR_UPLOAD_URL}")
        
        # 发送请求
        response = requests.post(
            AVATAR_UPLOAD_URL,
            files=files,
            headers=headers,
            timeout=30
        )
        
        # 关闭文件
        files['files'][1].close()
        
        print(f"📋 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 上传成功!")
                return {"success": True, "data": result}
            except:
                print(f"✅ 上传成功! (非JSON响应)")
                return {"success": True, "data": response.text}
        else:
            print(f"❌ 上传失败: HTTP {response.status_code}")
            return {"success": False, "error": f"HTTP {response.status_code}", "response": response.text}
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return {"success": False, "error": str(e)}

def load_account_data(account_file="zhanghao.txt"):
    """加载账号数据"""
    accounts = []
    if os.path.exists(account_file):
        with open(account_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and ':' in line:
                    parts = line.split(':', 2)
                    if len(parts) >= 3:
                        phone, password, json_data = parts
                        try:
                            account_info = json.loads(json_data)
                            accounts.append({
                                'phone': phone,
                                'password': password,
                                'user_id': account_info.get('userId'),
                                'nickname': account_info.get('nickname'),
                                'data': account_info
                            })
                        except:
                            continue
    return accounts

def batch_upload_avatar_random(account_file="zhanghao.txt", avatar_folder=AVATAR_FOLDER):
    """批量上传头像 - 每个账号使用不同的随机头像"""
    print(f"🔄 批量头像上传 (随机分配)")
    print("=" * 50)

    # 获取头像文件列表
    avatar_files = get_avatar_files(avatar_folder)
    if not avatar_files:
        print(f"❌ 头像文件夹中没有找到图片文件")
        return

    # 加载账号数据
    accounts = load_account_data(account_file)
    if not accounts:
        print(f"❌ 没有找到可用账号数据")
        return

    print(f"📋 找到 {len(accounts)} 个账号")
    print(f"🖼️ 可用头像: {len(avatar_files)} 个")

    success_count = 0
    fail_count = 0
    used_avatars = []  # 记录已使用的头像，避免重复

    for i, account in enumerate(accounts, 1):
        user_id = account['user_id']
        nickname = account['nickname']

        print(f"\n[{i}/{len(accounts)}] 处理账号: {nickname} (ID: {user_id})")

        # 选择头像策略：优先使用未使用过的头像
        available_avatars = [f for f in avatar_files if f not in used_avatars]
        if not available_avatars:
            # 如果所有头像都用过了，重新开始
            available_avatars = avatar_files
            used_avatars = []
            print("🔄 所有头像已使用，重新开始分配")

        # 随机选择一个头像
        selected_avatar = random.choice(available_avatars)
        used_avatars.append(selected_avatar)

        avatar_name = os.path.basename(selected_avatar)
        print(f"🖼️ 选择头像: {avatar_name}")

        result = upload_avatar(user_id, selected_avatar)

        if result['success']:
            success_count += 1
            print(f"✅ 账号 {nickname} 头像上传成功")
        else:
            fail_count += 1
            print(f"❌ 账号 {nickname} 头像上传失败: {result.get('error', '未知错误')}")

    print(f"\n📊 批量上传完成:")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {fail_count}")

def batch_upload_avatar(image_path, account_file="zhanghao.txt"):
    """批量上传头像 - 所有账号使用同一个头像 (保持向后兼容)"""
    print(f"🔄 批量头像上传 (统一头像)")
    print("=" * 50)

    # 加载账号数据
    accounts = load_account_data(account_file)
    if not accounts:
        print(f"❌ 没有找到可用账号数据")
        return

    print(f"📋 找到 {len(accounts)} 个账号")
    print(f"🖼️ 使用头像: {os.path.basename(image_path)}")

    success_count = 0
    fail_count = 0

    for i, account in enumerate(accounts, 1):
        user_id = account['user_id']
        nickname = account['nickname']

        print(f"\n[{i}/{len(accounts)}] 处理账号: {nickname} (ID: {user_id})")

        result = upload_avatar(user_id, image_path)

        if result['success']:
            success_count += 1
            print(f"✅ 账号 {nickname} 头像上传成功")
        else:
            fail_count += 1
            print(f"❌ 账号 {nickname} 头像上传失败: {result.get('error', '未知错误')}")

    print(f"\n📊 批量上传完成:")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {fail_count}")

def interactive_upload():
    """交互式上传"""
    print("🖼️ 鲲鹏通讯头像上传工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 单个账号上传头像")
        print("2. 批量账号上传头像 (统一头像)")
        print("3. 批量账号上传头像 (随机分配不同头像)")
        print("4. 查看账号列表")
        print("5. 查看头像文件夹")
        print("0. 退出")

        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            user_id = input("请输入用户ID: ").strip()
            image_path = input("请输入图片路径: ").strip()

            if user_id and image_path:
                result = upload_avatar(user_id, image_path)
                if result['success']:
                    print("✅ 上传成功!")
                else:
                    print(f"❌ 上传失败: {result.get('error', '未知错误')}")
            else:
                print("❌ 请输入有效的用户ID和图片路径")

        elif choice == "2":
            image_path = input("请输入图片路径: ").strip()
            if image_path:
                batch_upload_avatar(image_path)
            else:
                print("❌ 请输入有效的图片路径")

        elif choice == "3":
            print("🎲 开始批量随机分配头像...")
            batch_upload_avatar_random()

        elif choice == "4":
            accounts = load_account_data()
            if accounts:
                print(f"\n📋 账号列表 (共{len(accounts)}个):")
                for i, account in enumerate(accounts[:10], 1):  # 只显示前10个
                    print(f"{i}. {account['nickname']} (ID: {account['user_id']})")
                if len(accounts) > 10:
                    print(f"... 还有 {len(accounts) - 10} 个账号")
            else:
                print("❌ 没有找到账号数据")

        elif choice == "5":
            avatar_files = get_avatar_files()
            if avatar_files:
                print(f"\n🖼️ 头像文件夹: {AVATAR_FOLDER}")
                print(f"📁 找到 {len(avatar_files)} 个头像文件")
                print("前10个文件:")
                for i, file_path in enumerate(avatar_files[:10], 1):
                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)
                    print(f"{i}. {file_name} ({file_size} 字节)")
                if len(avatar_files) > 10:
                    print(f"... 还有 {len(avatar_files) - 10} 个文件")
            else:
                print("❌ 头像文件夹中没有找到图片文件")
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 命令行模式
        if len(sys.argv) == 3:
            user_id = sys.argv[1]
            image_path = sys.argv[2]
            upload_avatar(user_id, image_path)
        elif len(sys.argv) == 2:
            arg = sys.argv[1].lower()
            if arg == "random" or arg == "-r":
                # 随机批量上传
                batch_upload_avatar_random()
            else:
                # 统一头像批量上传
                image_path = sys.argv[1]
                batch_upload_avatar(image_path)
        else:
            print("用法:")
            print("  python avatar_upload_tool.py <图片路径>                    # 批量上传(统一头像)")
            print("  python avatar_upload_tool.py random                       # 批量上传(随机头像)")
            print("  python avatar_upload_tool.py -r                           # 批量上传(随机头像)")
            print("  python avatar_upload_tool.py <用户ID> <图片路径>           # 单个上传")
    else:
        # 交互式模式
        interactive_upload()
