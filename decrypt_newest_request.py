#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 解密最新的“手机号已注册”的请求

import base64
import json
import urllib.parse
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# 用户提供的最新请求URL
REQUEST_URL = "http://ggregesd.kes337f.shop/user/register/v1?data=7n%2BvhCrZycbsXKq%2FIGcg3BplDJmsAEJGEtiFgklM288uXHtR1rn3Yp55Z38clPc02bZV3weQyNbd2cTRvtJfsh4GqaqWcQlYpbVlJGeHnsIsT3Zv6zAlX0Q5yQ7raQbCtPx10GY3CLYRubDKkn4qqRyoe5%2Btr0GxKfKQC0nQ1Egy%2FfdS2YsZp2b2LjddvdYaQqmVVubTsDF1UZvbCS0HNuxCouIZsRsTPc0BIgI%2Bw0NyyNGAMZX%2FOmify92QdiQJTelwUGdxQmR2pjTeo%2FoUIa1yXW34k3yIRsJgVLBADpwE8IcKX31PKyRMW1YepAxUYCpdqe1zmXjkZX3MmcGV7x9W9SEmG%2F3VqFae1DMCIlfaHDUKykTutpgDwcizIbO8EqIzFL0vVuLnF%2Blono247LQ%2B%2FL2XHynrd2DQzG4Uq2iTC5wpG4D%2FmvcMpl6r832rcuORNDXJ0wqGsVjkh78iMOsgxa4wvXW4XBxpG%2Fw%2BKYlPjXC1jvjCQ0GRK6LDPSmFLZNclBETR9xJTwtWSru4IjfN6jsYMcz7%2Bfij409lxz8Q0fZTt%2FyYjLMugJ%2ByMOgX&deviceId=android&language=zh&salt=1753514910351&secret=8H0M42gSK9fXVjRvwee3Kw%3D%3D"

def main():
    print("--- 解密“手机号已注册”的请求 ---")
    try:
        # 1. 解析请求URL
        parsed_url = urllib.parse.urlparse(REQUEST_URL)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        
        encrypted_data_b64 = query_params.get('data', [''])[0]
        
        # 2. 准备密钥和IV (使用全局固定密钥)
        key = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
        iv = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")

        print(f"密钥 (全局固定): {key.hex()}")
        print(f"IV (全局固定): {iv.hex()}")

        # 3. 解密
        ciphertext = base64.b64decode(encrypted_data_b64)
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_padded_bytes = cipher.decrypt(ciphertext)
        decrypted_bytes = unpad(decrypted_padded_bytes, AES.block_size)
        decrypted_request = json.loads(decrypted_bytes.decode('utf-8'))
        
        print("\n>>> 请求解密成功! <<<\n")
        print("请求明文内容:")
        print(json.dumps(decrypted_request, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"\n请求解密失败: {e}")

if __name__ == '__main__':
    main() 