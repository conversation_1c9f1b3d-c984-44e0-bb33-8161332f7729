# 全自动注册机 (zyguobu.net)

本项目是一个针对 `www.zyguobu.net` 网站的全功能、全自动注册机器人。

## 功能特性

- **全自动流程**: 一键完成 **注册 -> 实名认证 -> 绑定银行卡 -> 每日签到** 的所有操作。
- **智能验证码识别**: 使用 `ddddocr` 库，高效识别数字验证码。
- **动态数据生成**: 为每一个新账号自动生成随机且符合规范的：
  - 中国大陆手机号
  - 中文姓名
  - 身份证号码
  - 银行卡号（包括银行名称）
- **模拟设备指纹**: 内置50多种真实的Android手机 `User-Agent`，每次注册随机选用，提高真实性。
- **图形用户界面 (GUI)**: 基于 `PyQt5` 构建了简洁易用的操作界面。
- **多线程处理**: 注册流程在后台线程执行，避免UI卡顿，可随时停止。
- **详细日志与结果**: 实时显示每个步骤的日志，并将成功账号的完整信息（包括Token、姓名、身份证等）保存到文件。

## 文件结构

```
注册机框架/
├── 注册机.py           # 核心业务逻辑（API请求、自动化流程）
├── data_generator.py  # 随机数据生成器（姓名、身份证、银行卡等）
├── 注册机_UI.py        # PyQt5图形用户界面
└── README.md          # 本说明文件
```

## 安装指南

在运行程序之前，你需要安装所有必需的Python库。

1. **确保你安装了 Python 3.8 或更高版本。**

2. **创建一个虚拟环境 (推荐)**:
   ```bash
   python -m venv .venv
   # Windows
   .\.venv\Scripts\activate
   # macOS / Linux
   source .venv/bin/activate
   ```

3. **安装依赖库**:
   打开终端或命令行，运行以下命令：
   ```bash
   pip install requests ddddocr PyQt5
   ```

## 使用说明

1. **直接运行UI程序**:
   ```bash
   python 注册机框架/注册机_UI.py
   ```

2. **在GUI界面中进行如下操作**:
   - **邀请码**: 输入你的邀请码 (程序已填写默认值)。
   - **密码**: 为所有新注册的账号设置一个统一的密码。
   - **注册数量**: 设置你希望一次性注册多少个账号。

3. **点击 "开始执行"**:
   - 程序将启动后台线程，开始全自动注册流程。
   - 你可以在 "实时日志" 窗口看到每一步的操作详情。

4. **查看结果**:
   - 每个成功完成流程的账号，其完整的JSON数据会显示在下方的 "成功账号信息" 区域。
   - 所有任务完成后，你可以点击 "保存结果" 将所有成功账号的信息以JSON格式保存到一个 `.txt` 文件中。

5. **停止任务**:
   - 在任何时候，你都可以点击 "停止" 按钮来安全地终止当前的注册任务。线程会在完成当前账号的流程后停止。

## 注意事项

- **Tesseract/OpenCV 不再需要**: 本项目已从 `pytesseract` 迁移到 `ddddocr`，无需再安装Tesseract-OCR或OpenCV。
- **网络问题**: 如果程序在运行过程中出现大量网络错误，请检查你的网络连接和代理设置。
- **API变更**: 如果目标网站的API接口发生变化，本程序可能需要更新。请关注日志中的失败信息。
- **法律与合规**: 本项目仅用于技术学习和研究目的，请勿用于任何非法活动。滥用可能导致你的IP被封禁或承担法律责任。 