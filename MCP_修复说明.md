# MCP 服务器连接问题修复说明

## 问题描述
您遇到的 MCP (Model Context Protocol) 服务器连接失败问题，错误信息显示：
- `Failed to start the MCP server`
- `MCP error -32000: Connection closed`

## 问题原因
1. **虚拟环境路径错误**：配置文件中指向了不存在的路径
2. **MCP 包版本不兼容**：旧的虚拟环境中 MCP 包安装不完整
3. **服务器脚本路径错误**：指向了错误的服务器脚本位置

## 修复步骤

### 1. 创建新的虚拟环境
```bash
python -m venv .venv-mcp
```

### 2. 安装正确的 MCP 包
```bash
.venv-mcp/Scripts/activate
pip install --upgrade pip
pip install mcp
```

### 3. 更新配置文件
已更新以下配置文件：
- `cline_mcp_settings.json`
- `cursor_mcp_config.json` 
- `complete_41_tools_config.json`

所有配置文件现在都指向：
- Python 路径：`C:\Users\<USER>\Desktop\hook\zhuce\.venv-mcp\Scripts\python.exe`
- 服务器脚本：`C:\Users\<USER>\Desktop\hook\zhuce\ida-pro-mcp-new\src\ida_pro_mcp\server.py`

### 4. 安装 IDA 插件
```bash
.venv-mcp/Scripts/python.exe ida-pro-mcp-new/src/ida_pro_mcp/server.py --install
```

## 验证修复

### 运行测试脚本
```bash
.venv-mcp/Scripts/python.exe test_mcp_server.py
```

测试结果应该显示：
```
📊 Test Results:
  MCP Import: ✅ PASS
  Server Test: ✅ PASS

🎉 All tests passed! MCP server should work properly.
```

### 手动启动服务器
使用提供的批处理文件：
```bash
start_mcp_server.bat
```

或者手动运行：
```bash
.venv-mcp/Scripts/python.exe ida-pro-mcp-new/src/ida_pro_mcp/server.py --unsafe --transport stdio
```

## 下一步操作

1. **重启 IDE**：重启您的 VS Code、Cursor 或 Claude Desktop
2. **重启 IDA Pro**：如果您在使用 IDA Pro，请重启它
3. **测试连接**：MCP 服务器现在应该能够正常连接

## 配置文件位置

MCP 服务器配置已自动安装到以下位置：
- **Cline**: `C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json`
- **Claude Desktop**: `C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json`
- **Cursor**: `C:\Users\<USER>\.cursor\mcp.json`
- **IDA Pro 插件**: `C:\Users\<USER>\AppData\Roaming\Hex-Rays\IDA Pro\plugins\mcp-plugin.py`

## 故障排除

如果仍然遇到问题：

1. **检查防火墙**：确保防火墙没有阻止 Python 进程
2. **检查端口**：确保端口 13337 没有被其他程序占用
3. **检查权限**：确保有足够的权限访问配置文件和脚本
4. **查看日志**：检查 IDE 的输出面板或控制台中的详细错误信息

## 文件清单

修复过程中创建/修改的文件：
- ✅ `.venv-mcp/` - 新的虚拟环境
- ✅ `test_mcp_server.py` - 测试脚本
- ✅ `start_mcp_server.bat` - 启动脚本
- ✅ `cline_mcp_settings.json` - 更新的配置
- ✅ `cursor_mcp_config.json` - 更新的配置
- ✅ `complete_41_tools_config.json` - 更新的配置
- ✅ `MCP_修复说明.md` - 本说明文档

## 技术细节

- **MCP 版本**: 1.12.2
- **Python 版本**: 3.13
- **传输协议**: stdio
- **安全模式**: --unsafe (启用所有功能)

修复完成！您的 MCP 服务器现在应该可以正常工作了。
