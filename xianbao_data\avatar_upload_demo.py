#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鲲鹏通讯头像上传演示脚本
基于HCY抓包数据完全重现请求
"""

import requests
import json
import os
import uuid
from PIL import Image
import io

# 从抓包数据中提取的API信息
AVATAR_UPLOAD_URL = "http://sc.kes337f.shop:8088/upload/uploadAvatarServlet"
USER_AGENT = "chat_im/2.1.8 (Linux; U; Android 10; 22041216UC Build/UP1A.231005.007)"

def create_test_image(width=200, height=200, color=(255, 0, 0)):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    
    # 保存为JPEG格式的字节流
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG', quality=85)
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def upload_avatar_exact_format(user_id, image_data=None, image_path=None, language="zh"):
    """
    完全按照抓包数据格式上传头像
    
    Args:
        user_id (str): 用户ID (从抓包数据看是107998)
        image_data (bytes): 图片二进制数据
        image_path (str): 图片文件路径
        language (str): 语言设置
    
    Returns:
        dict: 上传结果
    """
    print(f"🖼️ 头像上传 - 完全重现抓包请求")
    print("=" * 50)
    print(f"👤 用户ID: {user_id}")
    print(f"🌐 语言: {language}")
    print(f"🎯 API地址: {AVATAR_UPLOAD_URL}")
    
    # 获取图片数据
    if image_path and os.path.exists(image_path):
        with open(image_path, 'rb') as f:
            image_data = f.read()
        filename = os.path.basename(image_path)
        print(f"📁 使用文件: {filename}")
    elif image_data:
        # 生成类似抓包数据的文件名格式
        filename = f"{uuid.uuid4().hex}.jpg"
        print(f"📁 生成文件名: {filename}")
    else:
        # 创建测试图片
        image_data = create_test_image()
        filename = f"{uuid.uuid4().hex}.jpg"
        print(f"📁 创建测试图片: {filename}")
    
    print(f"📊 图片大小: {len(image_data)} 字节")
    
    try:
        # 生成boundary (模拟抓包数据中的格式)
        boundary = str(uuid.uuid4())
        
        # 构建multipart/form-data请求体
        body_parts = []
        
        # 添加userId字段
        body_parts.append(f'--{boundary}')
        body_parts.append('Content-Disposition: form-data; name="userId"')
        body_parts.append('Content-Length: ' + str(len(str(user_id))))
        body_parts.append('')
        body_parts.append(str(user_id))
        
        # 添加language字段
        body_parts.append(f'--{boundary}')
        body_parts.append('Content-Disposition: form-data; name="language"')
        body_parts.append('Content-Length: ' + str(len(language)))
        body_parts.append('')
        body_parts.append(language)
        
        # 添加files字段
        body_parts.append(f'--{boundary}')
        body_parts.append(f'Content-Disposition: form-data; name="files"; filename="{filename}"')
        body_parts.append('Content-Length: ' + str(len(image_data)))
        body_parts.append('')
        
        # 构建请求体
        body_text = '\r\n'.join(body_parts) + '\r\n'
        body_bytes = body_text.encode('utf-8') + image_data + f'\r\n--{boundary}--\r\n'.encode('utf-8')
        
        # 设置请求头 (完全按照抓包数据)
        headers = {
            'User-Agent': USER_AGENT,
            'Content-Type': f'multipart/mixed; boundary={boundary}',
            'Content-Length': str(len(body_bytes)),
            'Host': 'sc.kes337f.shop:8088',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip'
        }
        
        print(f"📡 发送请求...")
        print(f"📋 Content-Type: {headers['Content-Type']}")
        print(f"📏 Content-Length: {headers['Content-Length']}")
        
        # 发送请求
        response = requests.post(
            AVATAR_UPLOAD_URL,
            data=body_bytes,
            headers=headers,
            timeout=30
        )
        
        print(f"📋 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print(f"✅ 头像上传成功!")
            return {"success": True, "response": response.text}
        else:
            print(f"❌ 头像上传失败: HTTP {response.status_code}")
            return {"success": False, "error": f"HTTP {response.status_code}", "response": response.text}
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return {"success": False, "error": str(e)}

def test_with_captured_data():
    """使用抓包数据中的参数进行测试"""
    print("🧪 使用抓包数据参数测试")
    print("=" * 50)
    
    # 抓包数据中的参数
    user_id = "107998"  # 从抓包数据中提取
    language = "zh"     # 从抓包数据中提取
    
    # 测试上传
    result = upload_avatar_exact_format(user_id, language=language)
    
    if result['success']:
        print("🎉 测试成功! 头像上传请求重现成功")
    else:
        print(f"❌ 测试失败: {result.get('error', '未知错误')}")
    
    return result

def test_with_account_data():
    """使用本地账号数据测试"""
    print("📋 使用本地账号数据测试")
    print("=" * 50)
    
    # 加载账号数据
    account_file = "zhanghao.txt"
    if not os.path.exists(account_file):
        print(f"❌ 账号文件不存在: {account_file}")
        return
    
    with open(account_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line and ':' in line:
                parts = line.split(':', 2)
                if len(parts) >= 3:
                    phone, password, json_data = parts
                    try:
                        account_info = json.loads(json_data)
                        user_id = account_info.get('userId')
                        nickname = account_info.get('nickname')
                        
                        print(f"\n[测试账号] {nickname} (ID: {user_id})")
                        
                        result = upload_avatar_exact_format(user_id)
                        
                        if result['success']:
                            print(f"✅ 账号 {nickname} 头像上传成功")
                        else:
                            print(f"❌ 账号 {nickname} 头像上传失败")
                        
                        # 只测试第一个账号
                        break
                        
                    except Exception as e:
                        print(f"❌ 解析账号数据失败: {e}")
                        continue

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "captured":
            # 使用抓包数据参数测试
            test_with_captured_data()
        elif sys.argv[1] == "account":
            # 使用本地账号数据测试
            test_with_account_data()
        else:
            # 自定义用户ID测试
            user_id = sys.argv[1]
            image_path = sys.argv[2] if len(sys.argv) > 2 else None
            upload_avatar_exact_format(user_id, image_path=image_path)
    else:
        print("🖼️ 鲲鹏通讯头像上传演示")
        print("=" * 50)
        print("用法:")
        print("  python avatar_upload_demo.py captured           # 使用抓包数据参数测试")
        print("  python avatar_upload_demo.py account            # 使用本地账号数据测试")
        print("  python avatar_upload_demo.py <用户ID>           # 自定义用户ID测试")
        print("  python avatar_upload_demo.py <用户ID> <图片路径> # 自定义用户ID和图片测试")
        print()
        
        # 默认运行抓包数据测试
        test_with_captured_data()
