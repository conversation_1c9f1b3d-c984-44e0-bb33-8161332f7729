/**
 * 卡农精选网站 - Cloudflare Workers 版本
 * 专注卡农论坛优质内容分享 (v3.0 - 深度抓取版)
 */

import { load } from 'cheerio';

// 仿照Python脚本，增加更多监控板块
const FORUM_URLS = [
    { name: "推荐热帖", url: "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=225&filter=lastpost&orderby=lastpost" },
    { name: "羊毛专区", url: "https://www.51kanong.com/yh-282-1.htm" },
    { name: "下款线报", url: "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=23" },
    { name: "老哥生活", url: "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=2603" },
    { name: "数藏专区", url: "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=15" },
    { name: "羊毛交流", url: "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=169&filter=lastpost&orderby=lastpost" },
];

const BATCH_SIZE = 5; // 每次并发处理5个帖子
const POSTS_PER_BOARD = 3; // 每个板块抓取的帖子数量
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';

export default {
    // 处理HTTP请求
    async fetch(request, env, ctx) {
        try {
            return await handleRequest(request, env);
        } catch (error) {
            console.error('Request handling error:', error);
            return new Response(JSON.stringify({
                error: 'Internal Server Error',
                message: error.message
            }), {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
    },

    // 处理定时任务
    async scheduled(event, env, ctx) {
        console.log('🔄 Cron trigger fired. Starting scheduled deep scrape.');
        await scrapeAndStore(env);
        console.log('✅ Cron job completed successfully.');
    }
};

// 路由和请求处理器
async function handleRequest(request, env) {
    const url = new URL(request.url);
    const path = url.pathname;

    // 预检请求（CORS）
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
            },
        });
    }
    
    // API 路由
    if (path.startsWith('/api/')) {
        return handleApiRequest(path, request, env);
    }

    // 页面路由
    if (path === '/' || path === '/index.html') {
        return new Response(getHomePage(env), {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
    }

    return new Response('Page Not Found', {
        status: 404,
        headers: { 'Content-Type': 'text/plain' }
    });
}

// API请求处理器
async function handleApiRequest(path, request, env) {
    const commonHeaders = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
    };

    if (path === '/api/posts') {
        const posts = await getAllPosts(env);
        return new Response(JSON.stringify(posts || []), { headers: commonHeaders });
    }

    if (path === '/api/run-scrape') {
        try {
            const result = await scrapeAndStore(env);
            return new Response(JSON.stringify(result), { headers: commonHeaders });
        } catch (error) {
            console.error('Manual scrape failed:', error);
            const responseBody = { success: false, message: `Scrape failed: ${error.message}`, logs: [`❌ 严重错误: ${error.message}`] };
            return new Response(JSON.stringify(responseBody), { status: 500, headers: commonHeaders });
        }
    }

    return new Response(JSON.stringify({ error: 'API endpoint not found' }), {
        status: 404,
        headers: commonHeaders
    });
}

/**
 * 解析帖子详情页，提取正文内容
 * @param {string} html - 帖子详情页的HTML
 * @returns {string} - 提取的帖子正文
 */
function parsePostContent(html) {
    const $ = load(html);
    // 借鉴Python脚本中的选择器策略
    const contentSelectors = [
        'td[id*="postmessage"]',
        '.t_fsz', // 51kanong的新版内容选择器
        '.pcb',
        '.t_msgfont',
    ];

    for (const selector of contentSelectors) {
        const contentElem = $(selector).first();
        if (contentElem.length) {
            // 移除脚本和样式等不需要的元素
            contentElem.find('script, style, .jammer').remove();
            let content = contentElem.text().trim();
            if (content.length > 20) {
                // 替换多个换行符为空格
                return content.replace(/\\s{2,}/g, ' ').substring(0, 800); // 限制内容长度
            }
        }
    }
    return "内容提取失败";
}

// 调用Gemini API (v3.0 - 全新专业级Prompt)
async function callGeminiAPI(apiKey, title, content) {
    if (!apiKey) {
        throw new Error("Gemini API key is not configured.");
    }

    // 使用您提供的、更专业的分析模板
    const prompt = `
        作为一名专业的论坛"羊毛"价值分析师，请你严格、客观地分析以下论坛帖子的内容，并以清晰、简洁、有条理的方式输出。

        **分析要求:**
        1.  **核心价值判断**: 一句话精准总结这个帖子的核心价值是什么？（例如：XX银行的XX活动、某个APP的漏洞、一个薅羊毛的机会、或是无价值的水帖）
        2.  **关键信息提取**:
            *   **活动/福利**: 如果是活动或福利，说明具体是什么？（例如：5元微信立减金、话费券、实物等）
            *   **参与方式**: 如何参与这个活动？（例如：需要下载什么APP、通过什么链接、完成什么任务）
            *   **限制条件**: 有什么限制？（例如：新用户专享、地域限制、需要有实体卡等）
        3.  **价值评分**: 从0到10分，给这个帖子的价值打分，并简单说明理由。（10分表示价值极高，不容错过；0分表示毫无价值）
        4.  **总结建议**: 根据以上分析，给用户一个明确的、可执行的建议。（例如：建议立即参与、建议XX地区用户尝试、观望一下、价值不高忽略即可）

        **输出格式:**
        请严格按照以下Markdown格式返回，不要有任何多余的解释或开头语。

        \`\`\`markdown
        **AI分析报告**

        *   **核心价值**: 
        *   **关键信息**:
            *   **福利详情**: 
            *   **参与路径**: 
            *   **限制条件**: 
        *   **价值评分**: 
        *   **总结建议**: 
        \`\`\`

        **原始帖子内容:**
        ---
        **标题**: ${title}

        **正文**: 
        ${content}
        ---
    `;

    try {
        const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                contents: [{ parts: [{ text: prompt }] }],
            }),
        });

        if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`Gemini API request failed with status ${response.status}: ${errorBody}`);
        }

        const data = await response.json();
        if (data.candidates && data.candidates.length > 0) {
            return data.candidates[0].content.parts[0].text;
        } else {
            return "AI分析无返回结果。";
        }

    } catch (error) {
        console.error("Error calling Gemini API:", error);
        return `AI分析时发生错误: ${error.message}`;
    }
}

/**
 * 抓取并存储数据 (v4.0 - AI深度分析版)
 */
async function scrapeAndStore(env) {
    const logs = [`[${new Date().toLocaleString()}] 开始执行AI深度分析抓取任务...`];
    let totalScrapedCount = 0;
    const allPosts = [];

    for (const { name: boardName, url } of FORUM_URLS) {
        logs.push(`🔍 正在扫描板块: "${boardName}"...`);
        try {
            const response = await fetch(url, { headers: { 'User-Agent': 'Mozilla/5.0' } });
            if (!response.ok) {
                logs.push(`❌ 无法访问板块: ${boardName} (状态: ${response.status})`);
                continue;
            }
            const text = await response.text();
            const $ = load(text);

            const postLinks = [];
            $('a.s.xst').each((i, el) => {
                if (postLinks.length < POSTS_PER_BOARD) {
                    const postUrl = new URL($(el).attr('href'), 'https://www.51kanong.com/').href;
                    if (postUrl.includes('thread-')) {
                       const row = $(el).closest('tbody');
                       const author = row.find('td.by:nth-child(1) cite a').text().trim() || '未知';
                       const time = row.find('td.by:nth-child(1) em span').text().trim() || '刚刚';
                       const title = $(el).text().trim();

                        postLinks.push({ url: postUrl, title, author, time, boardName });
                    }
                }
            });
            logs.push(`板块 "${boardName}" 找到 ${postLinks.length} 个帖子链接。`);
            allPosts.push(...postLinks);

        } catch (error) {
            logs.push(`❌ 处理板块 "${boardName}" 时出错: ${error.message}`);
        }
    }
    logs.push(`✅ 所有板块扫描完成，总共找到 ${allPosts.length} 个帖子待处理。`);

    if (allPosts.length === 0) {
        logs.push("本次未抓取到任何新帖子。");
        await env.POST_CACHE.put("scrape_log", JSON.stringify(logs));
        return { success: true, logs, count: 0 };
    }
    
    logs.push(`🤖 开始对 ${allPosts.length} 个帖子进行内容提取与AI分析...`);

    const processedPosts = [];
    for (let i = 0; i < allPosts.length; i += BATCH_SIZE) {
        const batch = allPosts.slice(i, i + BATCH_SIZE);
        const batchPromises = batch.map(async (post) => {
            try {
                const detailResponse = await fetch(post.url, { headers: { 'User-Agent': 'Mozilla/5.0' } });
                if (!detailResponse.ok) {
                    logs.push(`  - 跳过帖子 (无法访问): ${post.title}`);
                    return null;
                }
                const detailText = await detailResponse.text();
                const $d = load(detailText);
                const detailContent = $d('td.t_f').first().text().trim();

                if (!detailContent) {
                    logs.push(`  - 跳过帖子 (无内容): ${post.title}`);
                    return null;
                }
                
                logs.push(`  - 正在分析: ${post.title}`);
                const aiAnalysis = await callGeminiAPI(env.GEMINI_API_KEY, post.title, detailContent);

                return {
                    id: post.url,
                    url: post.url,
                    title: post.title,
                    author: post.author,
                    time: post.time,
                    board: post.boardName,
                    content: detailContent,
                    aiAnalysis: aiAnalysis, // 存储AI分析的Markdown报告
                };
            } catch (error) {
                logs.push(`  - ‼️ 处理帖子时发生严重错误 "${post.title}": ${error.message}`);
                return null;
            }
        });

        const results = await Promise.all(batchPromises);
        processedPosts.push(...results.filter(p => p !== null));
        logs.push(`  ...完成第 ${Math.floor(i / BATCH_SIZE) + 1} 批处理，已处理 ${processedPosts.length} 个。`);
    }

    totalScrapedCount = processedPosts.length;
    logs.push(`💾 分析完成，正在将 ${totalScrapedCount} 个帖子存入数据库...`);

    if (totalScrapedCount > 0) {
        // 存储前先清空旧数据，确保只展示最新抓取的内容
        const oldKeysResult = await env.POST_CACHE.list();
        const keysToDelete = oldKeysResult.keys.map(key => key.name).filter(name => name.startsWith('post:'));
        
        if(keysToDelete.length > 0) {
            // Cloudflare KV bulk delete is not supported in Workers standard API
            // We delete them one by one. In a real-world high-volume scenario, consider a different strategy.
            for(const key of keysToDelete) {
                await env.POST_CACHE.delete(key);
            }
             logs.push(`🧹 已清空 ${keysToDelete.length} 条旧帖子缓存。`);
        }
        
        for (const post of processedPosts) {
            await env.POST_CACHE.put(`post:${post.id}`, JSON.stringify(post), { expirationTtl: 86400 }); // 缓存24小时
        }
    }
    
    logs.push(`✅ 任务完成！成功处理并存储了 ${totalScrapedCount} 篇帖子。`);
    await env.POST_CACHE.put("scrape_log", JSON.stringify(logs));

    return { success: true, logs, count: totalScrapedCount };
}

/**
 * 获取所有帖子数据
 */
async function getAllPosts(env) {
    const listResult = await env.POST_CACHE.list({ prefix: "post:" });
    const postPromises = listResult.keys.map(key => env.POST_CACHE.get(key.name, { type: 'json' }));
    const posts = await Promise.all(postPromises);
    // 按时间简单排序（这里假设时间字符串格式基本有序）
    return posts.filter(Boolean).sort((a, b) => (b.time || "").localeCompare(a.time || ""));
}

// 生成主页HTML (v4.1 - 终极安全修复版)
function getHomePage(env) {
    const siteName = env.SITE_NAME || '卡农精选';
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${siteName} - AI价值分析版</title>
    <link rel="icon" href="https://www.51kanong.com/favicon.ico" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; background-color: #f0f2f5; }
        .card { border: none; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 1.5rem; }
        .post-item { padding: 1.25rem; background-color: #fff; border-radius: 8px; margin-bottom: 1rem; border-left: 5px solid #0d6efd; transition: all 0.2s ease-in-out; }
        .post-item:hover { transform: translateY(-3px); box-shadow: 0 8px 15px rgba(0,0,0,0.1); }
        .post-item a { text-decoration: none; color: #212529; font-weight: 600; font-size: 1.1rem; }
        #log-output { height: 250px; background-color: #212529; color: #f8f9fa; font-size: 0.8em; border-radius: 8px; white-space: pre-wrap; word-wrap: break-word;}
        .ai-report { background-color: #f8f9fa; border-left: 3px solid #17a2b8; white-space: pre-wrap; word-wrap: break-word; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; padding: 0.75rem; border-radius: 4px; }
    </style>
</head>
<body>
<div class="container py-4">
    <header class="pb-3 mb-4 border-bottom"><a href="/" class="d-flex align-items-center text-dark text-decoration-none"><i class="fas fa-brain fs-2 me-2 text-primary"></i><span class="fs-4 fw-bold">${siteName} - AI价值分析</span></a></header>
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-list-ul me-2"></i>AI分析报告</span>
                    <span id="post-count-badge" class="badge bg-light text-dark">共 0 篇</span>
                </div>
                <div class="card-body" id="posts-container" style="max-height: 75vh; overflow-y: auto;"><p class="text-center text-muted">正在加载AI分析报告...</p></div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header"><i class="fas fa-cogs me-2"></i>控制面板</div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="runScrape(this)"><i class="fas fa-sync-alt me-2"></i>手动更新并分析</button>
                        <a href="https://dash.cloudflare.com/" target="_blank" class="btn btn-outline-secondary"><i class="fab fa-cloudflare me-2"></i>Cloudflare 后台</a>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header"><i class="fas fa-terminal me-2"></i>运行日志</div>
                <div class="card-body p-2"><pre id="log-output" class="mb-0">等待操作...</pre></div>
            </div>
        </div>
    </div>
</div>
<script>
    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return '';
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
    }

    function formatAiReport(report) {
        if (!report) return '<p class="small text-muted fst-italic">AI分析结果为空。</p>';
        
        // 终极修复：使用转义字符的split/join，这绝对是最后一次尝试
        const markdownTag = String.fromCharCode(96, 96, 96) + 'markdown';
        const backticks = String.fromCharCode(96, 96, 96);

        let cleanReport = report.split(markdownTag).join('').split(backticks).join('');

        // 然后再进行其他替换
        let html = escapeHtml(cleanReport)
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\* (.*?):/g, '<br><strong>$1:</strong>')
            .replace(/\n/g, '<br>');
            
        return html.startsWith('<br>') ? html.substring(4) : html;
    }

    async function runScrape(button) {
        const originalText = button.innerHTML;
        const logOutput = document.getElementById('log-output');
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在启动...';
        button.disabled = true;
        logOutput.textContent = '🔄 后台更新任务已启动，请稍后...';
        try {
            const response = await fetch('/api/run-scrape');
            if (!response.ok) throw new Error('Failed to start scrape task.');
            const data = await response.json();
            logOutput.textContent = (data.logs || ['没有日志返回']).join('\n');
            if (data.success) {
                logOutput.textContent += '\n\n✅ 后台任务已成功启动，正在为您抓取和分析...3秒后将自动刷新列表。';
                setTimeout(loadPosts, 3000);
            } else {
                throw new Error(data.message || '启动后台任务失败。');
            }
        } catch (error) {
            logOutput.textContent += '\n\n❌ 启动失败: ' + error.message;
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    async function loadPosts() {
        const container = document.getElementById('posts-container');
        const countBadge = document.getElementById('post-count-badge');
        container.innerHTML = '<p class="text-center text-muted">正在加载最新的AI分析报告...</p>';
        try {
            const response = await fetch('/api/posts');
            if (!response.ok) throw new Error('Failed to fetch posts.');
            const posts = await response.json();
            countBadge.textContent = '共 ' + posts.length + ' 篇';
            if (posts && posts.length > 0) {
                container.innerHTML = posts.map(function(post) {
                    return (
                        '<div class="post-item">' +
                            '<a href="' + escapeHtml(post.url) + '" target="_blank" title="' + escapeHtml(post.title) + '"><strong>' + escapeHtml(post.title) + '</strong></a>' +
                            '<div class="ai-report mt-3 p-3 rounded-2">' +
                                formatAiReport(post.aiAnalysis) +
                            '</div>' +
                            '<div class="d-flex justify-content-between text-muted small mt-3 pt-2 border-top">' +
                               '<span><i class="fas fa-user me-1"></i>' + escapeHtml(post.author) + '</span>' +
                               '<span class="badge bg-light text-dark">' + escapeHtml(post.board) + '</span>' +
                               '<span><i class="fas fa-clock me-1"></i>' + escapeHtml(post.time) + '</span>' +
                            '</div>' +
                        '</div>'
                    );
                }).join('');
            } else {
                container.innerHTML = '<p class="text-center text-muted">暂无帖子，请尝试手动更新。</p>';
            }
        } catch (error) {
            container.innerHTML = '<p class="text-center text-danger">数据加载失败: ' + error.message + '</p>';
        }
    }
    document.addEventListener('DOMContentLoaded', function() { loadPosts(); });
</script>
</body>
</html>`;
} 