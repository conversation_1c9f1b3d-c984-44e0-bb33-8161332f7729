import requests
from bs4 import BeautifulSoup
import smtplib
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr
import os
import time
import logging

# 引入我们独立的配置文件
import config

# ======================= (以下代码为核心逻辑，无需修改) =======================

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def fetch_url(url):
    """带重试功能的网络请求函数"""
    logging.info(f"正在抓取URL: {url}")
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml',
    }
    try:
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        return response.text
    except requests.exceptions.RequestException as e:
        logging.error(f"抓取 {url} 失败: {e}")
        return None

def scrape_forum(forum_name, forum_url):
    """
    爬取指定URL的论坛，获取新主题的标题、链接和内容。
    """
    html_content = fetch_url(forum_url)
    if not html_content:
        return []
        
    soup = BeautifulSoup(html_content, 'html.parser')
    new_posts = []

    for thread in soup.find_all('tbody', id=lambda x: x and x.startswith('normalthread_')):
        title_tag = thread.find('a', class_='s xst')
        if title_tag and title_tag.has_attr('href'):
            post_title = title_tag.text.strip()
            post_url = requests.compat.urljoin(forum_url, title_tag['href'])
            
            if not is_post_seen(post_url):
                logging.info(f"发现新主题: {post_title}")
                post_content_html = fetch_url(post_url)
                if post_content_html:
                    post_soup = BeautifulSoup(post_content_html, 'html.parser')
                    # 使用更精确的ID选择器定位第一个帖子的内容，以抓取主楼信息
                    content_div = post_soup.find('td', id=lambda x: x and x.startswith('postmessage_'))
                    
                    if content_div:
                        # 提取所有文本内容
                        post_content = content_div.get_text(separator='\\n', strip=True)
                        
                        # 查找帖子中的所有图片并提取其绝对URL
                        images = content_div.find_all('img')
                        img_urls = []
                        for img in images:
                            img_src = img.get('src')
                            if img_src:
                                # 将相对URL转换为绝对URL
                                img_full_url = requests.compat.urljoin(post_url, img_src)
                                img_urls.append(img_full_url)
                        
                        # 如果有图片，将其URL附加到内容末尾，以供AI分析
                        if img_urls:
                            post_content += "\\n\\n--- 图片附件 ---\\n" + "\\n".join(img_urls)
                    else:
                        post_content = "无法提取内容"
                    
                    new_posts.append({'title': post_title, 'url': post_url, 'content': post_content, 'forum_name': forum_name})
                    time.sleep(2)
    return new_posts

def load_seen_posts():
    if not os.path.exists(config.SEEN_POSTS_FILE):
        return set()
    with open(config.SEEN_POSTS_FILE, 'r', encoding='utf-8') as f:
        return set(line.strip() for line in f)

def save_seen_post(url):
    with open(config.SEEN_POSTS_FILE, 'a', encoding='utf-8') as f:
        f.write(url + '\n')

def is_post_seen(url):
    return url in seen_posts_set

def analyze_with_ai(title, content):
    logging.info(f"正在发送内容到AI进行分析: {title}")
    if len(content) > 10000:
        content = content[:10000] + "\\n... (内容过长，已被截断)"

    api_url = f"{config.GEMINI_API_HOST}/v1beta/models/gemini-1.5-flash-latest:generateContent?key={config.GEMINI_API_KEY}"
    headers = {'Content-Type': 'application/json'}
    data = {"contents": [{"parts": [{"text": config.AI_PROMPT_TEMPLATE.format(title=title, content=content)}]}]}
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=120)
        response.raise_for_status()
        result = response.json()
        analysis = result['candidates'][0]['content']['parts'][0]['text']
        return analysis
    except Exception as e:
        logging.error(f"AI分析失败: {e}")
        if 'response' in locals() and hasattr(response, 'text'):
            logging.error(f"AI API 返回内容: {response.text}")
        return "AI分析时出现错误，请检查API Key和网络连接。"

def send_email(subject, mail_body):
    logging.info("准备发送邮件...")
    msg = MIMEText(mail_body, 'html', 'utf-8')
    msg['From'] = formataddr((Header("AI论坛分析师", 'utf-8').encode(), config.SENDER_EMAIL))
    msg['To'] = formataddr((Header("尊敬的管理员", 'utf-8').encode(), config.RECEIVER_EMAIL))
    msg['Subject'] = Header(subject, 'utf-8')

    try:
        server = smtplib.SMTP_SSL(config.SMTP_SERVER, config.SMTP_PORT)
        server.login(config.SENDER_EMAIL, config.SENDER_PASSWORD)
        server.sendmail(config.SENDER_EMAIL, [config.RECEIVER_EMAIL], msg.as_string())
        server.quit()
        logging.info("✅ 邮件发送成功！")
    except Exception as e:
        logging.error(f"❌ 邮件发送失败: {e}")

if __name__ == '__main__':
    logging.info("="*30)
    logging.info("开始执行多板块论坛AI爬虫任务...")
    
    seen_posts_set = load_seen_posts()
    all_new_posts = []

    # 遍历所有要监控的板块
    for forum in config.FORUM_URLS:
        # 检查URL是否还是占位符
        if "请在这里填入" in forum['url']:
            logging.warning(f"跳过板块 '{forum['name']}'，因为URL尚未配置。")
            continue
            
        logging.info(f"--- 正在检查板块: {forum['name']} ---")
        new_posts_from_forum = scrape_forum(forum['name'], forum['url'])
        if new_posts_from_forum:
            all_new_posts.extend(new_posts_from_forum)
        time.sleep(5) # 友好访问，在检查不同板块间稍作停留
    
    if not all_new_posts:
        logging.info("所有已配置的板块均无新主题，任务结束。")
    else:
        total_posts = len(all_new_posts)
        logging.info(f"所有板块共发现 {total_posts} 个新主题，开始汇总处理...")
        
        report_parts = []
        processed_posts_urls = []

        for i, post in enumerate(all_new_posts):
            logging.info(f"正在处理第 {i+1}/{total_posts} 个: {post['title']}")
            ai_analysis_result = analyze_with_ai(post['title'], post['content'])
            
            # 为每个帖子创建一个独立的HTML报告部分
            part = f"""
            <div style="border-bottom: 2px solid #ccc; padding-bottom: 20px; margin-bottom: 20px;">
                <h2 style="color: #333;">{i+1}. {post['title']}</h2>
                <p><strong>所属板块:</strong> {post['forum_name']}<br>
                   <strong>源帖链接:</strong> <a href="{post['url']}">{post['url']}</a></p>
                <h3 style="color: #444;">AI分析结果:</h3>
                <div style="background-color: #f0f8ff; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                    <pre style="white-space: pre-wrap; font-family: 'Courier New', Courier, monospace; font-size: 14px;">{ai_analysis_result}</pre>
                </div>
            </div>
            """
            report_parts.append(part)
            processed_posts_urls.append(post['url'])
            time.sleep(10) # 避免过于频繁地请求AI API

        # 将所有报告部分组合成一个完整的HTML邮件
        full_report_html = "".join(report_parts)
        
        # 创建邮件主题和完整的HTML内容
        from datetime import datetime
        today_str = datetime.now().strftime('%Y年%m月%d日')
        email_subject = f"每日AI分析简报 ({today_str}) - {total_posts}个新主题"
        email_content_html = f"""
        <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background-color: #f9f9f9; }}
                    h1 {{ color: #0056b3; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>每日AI分析简报 - {today_str}</h1>
                    <p>今日共为您发现并分析了 <strong>{total_posts}</strong> 个来自不同板块的新主题，详情如下：</p>
                    <hr>
                    {full_report_html}
                    <p style="text-align: center; color: #888; font-size: 12px;">--- 报告结束 ---</p>
                </div>
            </body>
        </html>
        """

        # 发送汇总邮件
        send_email(email_subject, email_content_html)

        # 邮件发送成功后，将所有处理过的URL保存
        for url in processed_posts_urls:
            save_seen_post(url)
            seen_posts_set.add(url)
        
        logging.info("每日简报已发送，并已更新所有帖子的处理记录。")

    logging.info("所有新主题处理完毕。")
    logging.info("="*30 + "\n") 