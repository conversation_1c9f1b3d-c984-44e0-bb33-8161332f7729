#!/usr/bin/env python3
"""
IDA Pro MCP Server
Compatible with Model Context Protocol
"""
import asyncio
import json
import sys
import http.client
from typing import Any, Dict, List, Optional

# Try to import MCP modules
try:
    from mcp.server.fastmcp import FastMCP
    from mcp.types import Tool, TextContent
    MCP_AVAILABLE = True
except ImportError:
    print("Warning: MCP modules not available. Install with: pip install mcp")
    MCP_AVAILABLE = False

# IDA connection settings
IDA_HOST = "127.0.0.1"
IDA_PORT = 13337

def make_ida_request(method: str, params: List[Any] = None) -> Any:
    """Make a JSON-RPC request to IDA plugin"""
    if params is None:
        params = []
    
    try:
        conn = http.client.HTTPConnection(IDA_HOST, IDA_PORT)
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params,
            "id": 1
        }
        
        conn.request("POST", "/mcp", json.dumps(request), {
            "Content-Type": "application/json"
        })
        
        response = conn.getresponse()
        data = json.loads(response.read().decode())
        conn.close()
        
        if "error" in data:
            raise Exception(f"IDA Error: {data['error']['message']}")
        
        return data.get("result", "success")
        
    except Exception as e:
        return f"Connection failed: {str(e)}"

if MCP_AVAILABLE:
    # Create FastMCP server
    mcp = FastMCP("IDA Pro MCP")

    @mcp.tool()
    def check_connection() -> str:
        """Check if the IDA plugin is running"""
        try:
            result = make_ida_request("check_connection")
            return str(result)
        except Exception as e:
            return f"Failed to connect to IDA Pro: {str(e)}"

    @mcp.tool()
    def get_metadata() -> str:
        """Get metadata about the current IDB"""
        try:
            result = make_ida_request("get_metadata")
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def get_function_by_name(name: str) -> str:
        """Get a function by its name"""
        try:
            result = make_ida_request("get_function_by_name", [name])
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def get_function_by_address(address: str) -> str:
        """Get a function by its address"""
        try:
            result = make_ida_request("get_function_by_address", [address])
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def get_current_address() -> str:
        """Get the address currently selected by the user"""
        try:
            result = make_ida_request("get_current_address")
            return str(result)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def get_current_function() -> str:
        """Get the function currently selected by the user"""
        try:
            result = make_ida_request("get_current_function")
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def list_functions(offset: int = 0, count: int = 10) -> str:
        """List all functions in the database (paginated)"""
        try:
            result = make_ida_request("list_functions", [offset, count])
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    @mcp.tool()
    def convert_number(text: str, size: int = 4) -> str:
        """Convert a number (decimal, hexadecimal) to different representations"""
        try:
            result = make_ida_request("convert_number", [text, size])
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error: {str(e)}"

    def main():
        """Run the MCP server"""
        try:
            mcp.run()
        except KeyboardInterrupt:
            print("Server stopped")

else:
    # Fallback implementation without MCP
    def main():
        print("MCP modules not available. Please install with: pip install mcp")
        print("Running basic test server instead...")
        
        # Test IDA connection
        result = make_ida_request("check_connection")
        print(f"IDA Connection Test: {result}")

if __name__ == "__main__":
    main()
