#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机号扫号器 - 检测已注册的手机号
集成椰子云API获取手机号功能
"""

import requests
import json
import time
import random
import os
import sys
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 导入椰子云模块
try:
    from models.haozu import YeyeYunConfig, YeyeYunClient
except ImportError:
    print("警告: 无法导入椰子云模块，椰子云功能将不可用")
    YeyeYunConfig = None
    YeyeYunClient = None

class DynamicYeyeYunConfig:
    """动态椰子云配置类"""
    def __init__(self, username, password, project_id):
        self.USERNAME = username
        self.PASSWORD = password
        self.PROJECT_ID = project_id
        self.API_BASE_URL = "http://api.sqhyw.net:90"

class PhoneScanner:
    def __init__(self, use_yeyeyun=False, yeyeyun_config=None):
        self.base_url = "http://ac14798a72d534ae691b6c31ea2b0ad5-bce0fad476e5dcea.elb.ap-east-1.amazonaws.com:8090"
        self.endpoint = "/v1/user/sms/registercode"
        self.headers = {
            'package': 'im.wxoawr.hromno',
            'os': 'Android',
            'appid': 'wukongchat',
            'model': '1D6E1783DAFE8DFE5E2E',
            'version': '1.1.7',
            'Content-Type': 'application/json; charset=UTF-8',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip',
            'User-Agent': 'okhttp/5.0.0-alpha.2'
        }

        # 椰子云配置
        self.use_yeyeyun = use_yeyeyun
        self.yeyeyun_client = None
        if use_yeyeyun and yeyeyun_config and YeyeYunClient:
            self.yeyeyun_client = YeyeYunClient(yeyeyun_config)
            print("椰子云客户端已初始化")

        # 创建结果目录
        self.results_dir = "scan_results"
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)

        # 线程锁
        self.lock = threading.Lock()

        # 统计信息
        self.total_checked = 0
        self.registered_count = 0
        self.unregistered_count = 0
        self.error_count = 0
        self.yeyeyun_phones = []  # 存储从椰子云获取的手机号
        
    def check_phone_registered(self, phone, zone="0086"):
        """
        检查单个手机号是否已注册
        
        Args:
            phone (str): 手机号
            zone (str): 区号，默认0086
            
        Returns:
            dict: 检查结果
        """
        url = f"{self.base_url}{self.endpoint}"
        
        payload = {
            "zone": zone,
            "phone": phone
        }
        
        try:
            response = requests.post(
                url, 
                headers=self.headers, 
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                exist = result.get('exist', 0)
                
                with self.lock:
                    self.total_checked += 1
                    if exist == 1:
                        self.registered_count += 1
                        status = "已注册"
                    else:
                        self.unregistered_count += 1
                        status = "未注册"
                
                return {
                    'phone': phone,
                    'zone': zone,
                    'registered': exist == 1,
                    'status': status,
                    'response': result,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                with self.lock:
                    self.error_count += 1
                return {
                    'phone': phone,
                    'zone': zone,
                    'registered': None,
                    'status': f"请求失败: {response.status_code}",
                    'response': None,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
        except Exception as e:
            with self.lock:
                self.error_count += 1
            return {
                'phone': phone,
                'zone': zone,
                'registered': None,
                'status': f"错误: {str(e)}",
                'response': None,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def get_phones_from_yeyeyun(self, count=100, delay_range=(0.2, 0.8), auto_release=True, max_workers=5):
        """
        多线程从椰子云获取手机号列表

        Args:
            count (int): 获取数量
            delay_range (tuple): 延迟范围(秒)
            auto_release (bool): 是否自动释放手机号
            max_workers (int): 并发线程数 (建议3-10)

        Returns:
            list: 手机号列表
        """
        if not self.yeyeyun_client:
            print("错误: 椰子云客户端未初始化")
            return []

        # 登录椰子云
        if not self.yeyeyun_client.login():
            print("错误: 椰子云登录失败")
            return []

        phones = []
        print(f"🚀 开始多线程从椰子云获取 {count} 个手机号...")
        print(f"🔥 并发线程数: {max_workers}")
        print(f"⏱️  延迟设置: {delay_range[0]}-{delay_range[1]}秒")
        if auto_release:
            print("✅ 已启用自动释放模式，获取后立即释放手机号避免占用资源")

        start_time = time.time()

        # 使用线程池并发获取
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = []
            for _ in range(count):
                future = executor.submit(self._get_single_phone_with_delay, delay_range, auto_release)
                futures.append(future)

            # 收集结果
            completed = 0
            for future in as_completed(futures):
                try:
                    result = future.result()
                    completed += 1

                    if result:
                        phone_number, location = result
                        phones.append(phone_number)
                        self.yeyeyun_phones.append({
                            'phone': phone_number,
                            'location': location,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })

                        # 计算速度
                        elapsed = time.time() - start_time
                        speed = completed / elapsed * 60 if elapsed > 0 else 0
                        eta = (count - completed) / (speed / 60) if speed > 0 else 0

                        status = "已释放" if auto_release else "已获取"
                        print(f"✅ [{completed}/{count}] {status}: {phone_number} ({location}) | 速度: {speed:.1f}/分钟 | 剩余: {eta/60:.1f}分钟")
                    else:
                        # 获取失败
                        elapsed = time.time() - start_time
                        speed = completed / elapsed * 60 if elapsed > 0 else 0
                        print(f"❌ [{completed}/{count}] 获取失败 | 速度: {speed:.1f}/分钟")

                except Exception as e:
                    completed += 1
                    print(f"❌ [{completed}/{count}] 线程异常: {e}")

        total_time = time.time() - start_time
        avg_speed = len(phones) / total_time * 60 if total_time > 0 else 0
        print(f"\n🎉 从椰子云成功获取 {len(phones)} 个手机号")
        print(f"⏱️  总耗时: {total_time/60:.1f} 分钟，平均速度: {avg_speed:.1f} 个/分钟")
        return phones

    def _get_single_phone_with_delay(self, delay_range, auto_release):
        """
        单线程获取一个手机号（带延迟和释放）

        Args:
            delay_range (tuple): 延迟范围
            auto_release (bool): 是否自动释放

        Returns:
            tuple: (phone_number, location) 或 None
        """
        try:
            # 添加随机延迟
            time.sleep(random.uniform(delay_range[0], delay_range[1]))

            # 获取手机号
            success, result = self.yeyeyun_client.get_phone_number()
            if success:
                phone_number, location = result

                # 立即释放手机号
                if auto_release:
                    self.yeyeyun_client.release_phone_number(phone_number)

                return (phone_number, location)
            else:
                return None
        except Exception as e:
            print(f"获取手机号异常: {e}")
            return None

    def generate_phone_numbers(self, prefix="166", count=100):
        """
        生成手机号列表

        Args:
            prefix (str): 手机号前缀
            count (int): 生成数量

        Returns:
            list: 手机号列表
        """
        phones = []
        for _ in range(count):
            # 生成8位随机数字
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            phone = prefix + suffix
            phones.append(phone)
        return phones
    
    def load_phone_numbers_from_file(self, file_path):
        """
        从文件加载手机号列表
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            list: 手机号列表
        """
        phones = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    phone = line.strip()
                    if phone and phone.isdigit() and len(phone) == 11:
                        phones.append(phone)
        except Exception as e:
            print(f"读取文件失败: {e}")
        return phones
    
    def save_results(self, results, filename_prefix="scan_result"):
        """
        保存扫描结果

        Args:
            results (list): 扫描结果列表
            filename_prefix (str): 文件名前缀
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        try:
            # 保存所有结果
            all_results_file = os.path.join(self.results_dir, f"{filename_prefix}_all_{timestamp}.json")
            with open(all_results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            # 保存已注册的手机号
            registered_phones = [r for r in results if r.get('registered') == True]
            if registered_phones:
                registered_file = os.path.join(self.results_dir, f"{filename_prefix}_registered_{timestamp}.txt")
                with open(registered_file, 'w', encoding='utf-8') as f:
                    for result in registered_phones:
                        f.write(f"{result['phone']}\n")

                registered_json_file = os.path.join(self.results_dir, f"{filename_prefix}_registered_{timestamp}.json")
                with open(registered_json_file, 'w', encoding='utf-8') as f:
                    json.dump(registered_phones, f, ensure_ascii=False, indent=2)

            print(f"\n✅ 结果已保存:")
            print(f"- 所有结果: {all_results_file}")
            if registered_phones:
                print(f"- 已注册手机号: {registered_file}")
                print(f"- 已注册详细信息: {registered_json_file}")

        except Exception as e:
            print(f"❌ 保存结果时出错: {e}")

    def save_progress(self, results, current_index, total_count):
        """
        保存进度（用于断点续传）

        Args:
            results (list): 当前结果列表
            current_index (int): 当前索引
            total_count (int): 总数量
        """
        try:
            progress_file = os.path.join(self.results_dir, "scan_progress.json")
            progress_data = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'current_index': current_index,
                'total_count': total_count,
                'results': results,
                'statistics': {
                    'total_checked': self.total_checked,
                    'registered_count': self.registered_count,
                    'unregistered_count': self.unregistered_count,
                    'error_count': self.error_count
                }
            }

            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)

            print(f"💾 进度已保存: {current_index}/{total_count}")

        except Exception as e:
            print(f"⚠️ 保存进度失败: {e}")

    def load_progress(self):
        """
        加载之前的进度

        Returns:
            dict: 进度数据，如果没有则返回None
        """
        try:
            progress_file = os.path.join(self.results_dir, "scan_progress.json")
            if os.path.exists(progress_file):
                with open(progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载进度失败: {e}")
        return None
    
    def scan_phones(self, phone_list, max_workers=20, delay_range=(0.1, 0.5), batch_size=100):
        """
        批量扫描手机号 - 多线程版本

        Args:
            phone_list (list): 手机号列表
            max_workers (int): 最大并发数 (建议10-50)
            delay_range (tuple): 延迟范围(秒)
            batch_size (int): 批次大小，用于显示进度

        Returns:
            list: 扫描结果列表
        """
        results = []

        print(f"开始多线程扫描 {len(phone_list)} 个手机号...")
        print(f"并发线程数: {max_workers}")
        print(f"延迟范围: {delay_range[0]}-{delay_range[1]}秒")
        print("=" * 60)

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_phone = {}
            for phone in phone_list:
                future = executor.submit(self.check_phone_registered_with_delay, phone, delay_range)
                future_to_phone[future] = phone

            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_phone):
                phone = future_to_phone[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1

                    # 计算进度和速度
                    elapsed = time.time() - start_time
                    speed = completed_count / elapsed * 60 if elapsed > 0 else 0
                    eta = (len(phone_list) - completed_count) / (speed / 60) if speed > 0 else 0

                    # 实时显示进度
                    status = result['status']
                    if result['registered']:
                        print(f"🎯 [{completed_count}/{len(phone_list)}] {phone}: {status} | 速度: {speed:.1f}/分钟 | 剩余: {eta/60:.1f}分钟")
                    elif completed_count % 10 == 0:  # 每10个显示一次未注册的
                        print(f"📱 [{completed_count}/{len(phone_list)}] 已检查 | 速度: {speed:.1f}/分钟 | 剩余: {eta/60:.1f}分钟")

                    # 批次进度显示
                    if completed_count % batch_size == 0:
                        registered_in_batch = sum(1 for r in results[-batch_size:] if r.get('registered'))
                        print(f"📊 批次进度: {completed_count}/{len(phone_list)} | 本批已注册: {registered_in_batch}/{batch_size}")

                except Exception as e:
                    print(f"❌ 处理手机号 {phone} 时出错: {e}")

        total_time = time.time() - start_time
        avg_speed = len(phone_list) / total_time * 60 if total_time > 0 else 0
        print(f"\n✅ 扫描完成！总耗时: {total_time/60:.1f} 分钟，平均速度: {avg_speed:.1f} 个/分钟")

        return results

    def check_phone_registered_with_delay(self, phone, delay_range, zone="0086"):
        """
        带延迟的手机号检查（用于多线程）

        Args:
            phone (str): 手机号
            delay_range (tuple): 延迟范围
            zone (str): 区号

        Returns:
            dict: 检查结果
        """
        # 添加随机延迟
        time.sleep(random.uniform(delay_range[0], delay_range[1]))
        return self.check_phone_registered(phone, zone)
    
    def print_summary(self):
        """打印扫描统计信息"""
        print(f"\n=== 扫描统计 ===")
        print(f"总计检查: {self.total_checked}")
        print(f"已注册: {self.registered_count}")
        print(f"未注册: {self.unregistered_count}")
        print(f"错误: {self.error_count}")
        if self.total_checked > 0:
            print(f"注册率: {self.registered_count/self.total_checked*100:.2f}%")

def main():
    print("=== 手机号扫号器 ===")
    print("1. 随机生成手机号扫描")
    print("2. 从文件读取手机号扫描")
    print("3. 手动输入手机号扫描")
    print("4. 从椰子云获取手机号扫描")

    choice = input("请选择模式 (1-4): ").strip()

    # 初始化扫描器
    scanner = None
    phone_list = []

    if choice == "4":
        # 椰子云模式
        if not YeyeYunClient:
            print("错误: 椰子云模块未导入，无法使用此功能")
            return

        print("\n=== 椰子云配置 ===")
        username = input("请输入椰子云用户名: ").strip()
        password = input("请输入椰子云密码: ").strip()
        project_id = input("请输入项目ID: ").strip()

        if not all([username, password, project_id]):
            print("错误: 椰子云配置信息不完整")
            return

        # 创建动态椰子云配置
        yeyeyun_config = DynamicYeyeYunConfig(username, password, project_id)

        # 初始化带椰子云的扫描器
        scanner = PhoneScanner(use_yeyeyun=True, yeyeyun_config=yeyeyun_config)

        count = int(input("请输入获取手机号数量 (默认50): ").strip() or "50")

        # 询问是否自动释放
        auto_release_input = input("是否自动释放手机号? (y/n, 默认y): ").strip().lower()
        auto_release = auto_release_input != 'n'

        # 设置延迟
        delay_input = input("请输入获取延迟范围 (格式: 最小,最大 默认0.5,1.5): ").strip()
        if delay_input:
            try:
                min_delay, max_delay = map(float, delay_input.split(','))
                delay_range = (min_delay, max_delay)
            except:
                print("延迟格式错误，使用默认值")
                delay_range = (0.5, 1.5)
        else:
            delay_range = (0.5, 1.5)

        phone_list = scanner.get_phones_from_yeyeyun(count, delay_range=delay_range, auto_release=auto_release)

    else:
        # 普通模式
        scanner = PhoneScanner()

        if choice == "1":
            prefix = input("请输入手机号前缀 (默认166): ").strip() or "166"
            count = int(input("请输入生成数量 (默认100): ").strip() or "100")
            phone_list = scanner.generate_phone_numbers(prefix, count)

        elif choice == "2":
            file_path = input("请输入手机号文件路径: ").strip()
            phone_list = scanner.load_phone_numbers_from_file(file_path)
            if not phone_list:
                print("未能从文件中读取到有效手机号")
                return

        elif choice == "3":
            phones_input = input("请输入手机号 (多个用逗号分隔): ").strip()
            phone_list = [p.strip() for p in phones_input.split(',') if p.strip()]

        else:
            print("无效选择")
            return
    
    if not phone_list:
        print("没有手机号需要扫描")
        return
    
    # 设置扫描参数
    print(f"\n=== 扫描参数设置 ===")
    max_workers = int(input("请输入并发线程数 (建议10-50, 默认20): ").strip() or "20")

    # 设置延迟
    delay_input = input("请输入扫描延迟范围 (格式: 最小,最大 默认0.1,0.5): ").strip()
    if delay_input:
        try:
            min_delay, max_delay = map(float, delay_input.split(','))
            delay_range = (min_delay, max_delay)
        except:
            print("延迟格式错误，使用默认值")
            delay_range = (0.1, 0.5)
    else:
        delay_range = (0.1, 0.5)

    print(f"\n配置确认:")
    print(f"- 手机号数量: {len(phone_list)}")
    print(f"- 并发线程数: {max_workers}")
    print(f"- 延迟范围: {delay_range[0]}-{delay_range[1]}秒")
    print(f"- 预计耗时: {len(phone_list) / (max_workers * 2):.1f} 分钟")

    confirm = input("\n确认开始扫描? (y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消扫描")
        return

    # 开始扫描
    results = scanner.scan_phones(phone_list, max_workers=max_workers, delay_range=delay_range)
    
    # 保存结果
    scanner.save_results(results)
    
    # 显示统计
    scanner.print_summary()

if __name__ == "__main__":
    main()
