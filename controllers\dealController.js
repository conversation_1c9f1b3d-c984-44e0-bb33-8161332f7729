const Deal = require('../models/dealModel');
const User = require('../models/userModel');
const Interaction = require('../models/interactionModel');

// @desc    创建新羊毛活动
// @route   POST /api/deals
// @access  Private
exports.createDeal = async (req, res) => {
  try {
    // 添加作者ID
    req.body.author = req.user.id;
    
    const deal = await Deal.create(req.body);

    // 增加用户贡献值
    await User.findByIdAndUpdate(
      req.user.id, 
      { $inc: { contributionPoints: 5 } }
    );

    res.status(201).json({
      success: true,
      data: deal
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    获取所有羊毛活动
// @route   GET /api/deals
// @access  Public
exports.getDeals = async (req, res) => {
  try {
    // 构建查询条件
    const query = { status: 'active' };
    
    // 分类过滤
    if (req.query.category) {
      query.category = req.query.category;
    }
    
    // 标签过滤
    if (req.query.tag) {
      query.tags = { $in: [req.query.tag] };
    }
    
    // 搜索
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // 分页
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    
    // 排序
    let sortBy = {};
    if (req.query.sort) {
      switch (req.query.sort) {
        case 'newest':
          sortBy = { createdAt: -1 };
          break;
        case 'oldest':
          sortBy = { createdAt: 1 };
          break;
        case 'popular':
          sortBy = { views: -1 };
          break;
        case 'likes':
          sortBy = { likes: -1 };
          break;
        default:
          sortBy = { createdAt: -1 };
      }
    } else {
      sortBy = { createdAt: -1 }; // 默认按创建时间降序
    }

    // 执行查询
    const deals = await Deal.find(query)
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name')
      .sort(sortBy)
      .skip(startIndex)
      .limit(limit);

    // 获取总文档数
    const total = await Deal.countDocuments(query);

    // 分页结果
    const pagination = {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: deals.length,
      pagination,
      data: deals
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    获取单个羊毛活动
// @route   GET /api/deals/:id
// @access  Public
exports.getDeal = async (req, res) => {
  try {
    const deal = await Deal.findById(req.params.id)
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name');

    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到该活动'
      });
    }

    // 增加浏览次数
    deal.views += 1;
    await deal.save();

    res.status(200).json({
      success: true,
      data: deal
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    更新羊毛活动
// @route   PUT /api/deals/:id
// @access  Private
exports.updateDeal = async (req, res) => {
  try {
    let deal = await Deal.findById(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到该活动'
      });
    }

    // 确保用户是活动作者或管理员
    if (deal.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        message: '未授权修改此活动'
      });
    }

    // 更新活动
    deal = await Deal.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: deal
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    删除羊毛活动
// @route   DELETE /api/deals/:id
// @access  Private
exports.deleteDeal = async (req, res) => {
  try {
    const deal = await Deal.findById(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到该活动'
      });
    }

    // 确保用户是活动作者或管理员
    if (deal.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        message: '未授权删除此活动'
      });
    }

    await deal.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    点赞/取消点赞活动
// @route   POST /api/deals/:id/like
// @access  Private
exports.likeDeal = async (req, res) => {
  try {
    const deal = await Deal.findById(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到该活动'
      });
    }

    // 检查用户是否已经点赞
    const existingInteraction = await Interaction.findOne({
      user: req.user.id,
      deal: req.params.id,
      type: 'like'
    });

    if (existingInteraction) {
      // 取消点赞
      await Interaction.findByIdAndDelete(existingInteraction._id);
      deal.likes -= 1;
      await deal.save();

      return res.status(200).json({
        success: true,
        message: '已取消点赞',
        liked: false,
        likes: deal.likes
      });
    } else {
      // 添加点赞
      await Interaction.create({
        user: req.user.id,
        deal: req.params.id,
        type: 'like'
      });
      
      deal.likes += 1;
      await deal.save();

      return res.status(200).json({
        success: true,
        message: '点赞成功',
        liked: true,
        likes: deal.likes
      });
    }
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    收藏/取消收藏活动
// @route   POST /api/deals/:id/save
// @access  Private
exports.saveDeal = async (req, res) => {
  try {
    const deal = await Deal.findById(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到该活动'
      });
    }

    // 检查用户是否已经收藏
    const existingInteraction = await Interaction.findOne({
      user: req.user.id,
      deal: req.params.id,
      type: 'save'
    });

    if (existingInteraction) {
      // 取消收藏
      await Interaction.findByIdAndDelete(existingInteraction._id);
      deal.saves -= 1;
      await deal.save();

      return res.status(200).json({
        success: true,
        message: '已取消收藏',
        saved: false,
        saves: deal.saves
      });
    } else {
      // 添加收藏
      await Interaction.create({
        user: req.user.id,
        deal: req.params.id,
        type: 'save'
      });
      
      deal.saves += 1;
      await deal.save();

      return res.status(200).json({
        success: true,
        message: '收藏成功',
        saved: true,
        saves: deal.saves
      });
    }
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    获取用户已收藏的活动
// @route   GET /api/deals/saved
// @access  Private
exports.getSavedDeals = async (req, res) => {
  try {
    // 获取用户收藏的活动ID
    const interactions = await Interaction.find({
      user: req.user.id,
      type: 'save'
    });

    const dealIds = interactions.map(interaction => interaction.deal);

    // 获取活动详情
    const deals = await Deal.find({
      _id: { $in: dealIds },
      status: 'active'
    })
    .populate({
      path: 'author',
      select: 'username avatar creditScore level'
    })
    .populate('category', 'name')
    .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: deals.length,
      data: deals
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    获取用户发布的活动
// @route   GET /api/deals/user/:userId
// @access  Public
exports.getUserDeals = async (req, res) => {
  try {
    const deals = await Deal.find({
      author: req.params.userId,
      status: 'active'
    })
    .populate({
      path: 'author',
      select: 'username avatar creditScore level'
    })
    .populate('category', 'name')
    .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: deals.length,
      data: deals
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
}; 