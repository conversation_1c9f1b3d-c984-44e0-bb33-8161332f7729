#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MLKit OCR 配置文件
用于设置Google Cloud Vision API的凭证和参数
"""

import os

# Google Cloud Vision API 配置
GOOGLE_CLOUD_CONFIG = {
    # 凭证文件路径 - 请下载你的服务账号密钥文件
    'credentials_path': 'google-cloud-credentials.json',
    
    # 项目ID (可选)
    'project_id': 'your-project-id',
    
    # 语言提示 - 优化中文识别
    'language_hints': ['zh-CN', 'en'],
    
    # OCR参数优化
    'ocr_config': {
        'enable_text_detection_confidence_score': True,
        'include_bounding_boxes': True,
    }
}

# 图像预处理参数
IMAGE_PROCESSING_CONFIG = {
    # 目标图像高度 (MLKit推荐1200px以上)
    'target_height': 1200,
    
    # CLAHE参数 (对比度限制自适应直方图均衡)
    'clahe_clip_limit': 3.0,
    'clahe_tile_grid_size': (8, 8),
    
    # 双边滤波参数 (去噪但保持边缘)
    'bilateral_d': 9,
    'bilateral_sigma_color': 75,
    'bilateral_sigma_space': 75,
    
    # 自适应阈值参数
    'adaptive_threshold_max_value': 255,
    'adaptive_threshold_block_size': 11,
    'adaptive_threshold_c': 2,
    
    # 形态学操作参数
    'morph_kernel_size': (2, 1),
    
    # 输出格式
    'output_format': '.png',
    'png_compression': 0,  # 0=无压缩，保持最高质量
}

# 身份证识别参数
ID_CARD_CONFIG = {
    # 身份证号码正则表达式
    'id_patterns': [
        r'\b\d{17}[\dXx]\b',  # 18位身份证
        r'\b\d{15}\b',        # 15位身份证
        r'\b\d{6}[\d]{8}[\dXx]{4}\b',  # 带分隔符格式
    ],
    
    # 姓名长度限制
    'name_min_length': 2,
    'name_max_length': 4,
    
    # 常见姓氏 (前100个)
    'common_surnames': [
        '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
        '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
        '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
        '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
        '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎',
        '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜',
        '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆',
        '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史',
        '顾', '侯', '邵', '孟', '龙', '万', '段', '雷', '钱', '汤',
        '尹', '黎', '易', '常', '武', '乔', '贺', '赖', '龚', '文'
    ],
    
    # 需要过滤的无关词汇
    'exclude_words': [
        '中华人民共和国', '居民身份证', '公民身份', '姓名', '性别', '民族',
        '出生', '住址', '身份证', '有效期', '签发机关', '年月日', '汉族', 
        '满族', '回族', '藏族', '维吾尔', '苗族', '彝族', '壮族', '布依', 
        '朝鲜', '公安局', '派出所', '长期', '临时', '男', '女', '省', '市',
        '县', '区', '镇', '乡', '村', '街道', '号', '室', '楼', '层'
    ]
}

# 调试配置
DEBUG_CONFIG = {
    # 是否保存预处理后的图像
    'save_processed_image': True,
    
    # 是否显示详细的识别结果
    'show_detailed_results': True,
    
    # 是否记录识别时间
    'log_timing': True,
    
    # 日志级别
    'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
}

def setup_google_cloud_credentials(credentials_path: str = None) -> bool:
    """
    设置Google Cloud凭证
    
    Args:
        credentials_path: 凭证文件路径
        
    Returns:
        是否设置成功
    """
    if not credentials_path:
        credentials_path = GOOGLE_CLOUD_CONFIG['credentials_path']
    
    if os.path.exists(credentials_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
        print(f"✅ Google Cloud凭证已设置: {credentials_path}")
        return True
    else:
        print(f"❌ 凭证文件不存在: {credentials_path}")
        print("💡 请从Google Cloud Console下载服务账号密钥文件")
        print("💡 下载地址: https://console.cloud.google.com/iam-admin/serviceaccounts")
        return False

def get_credentials_setup_guide() -> str:
    """
    获取凭证设置指南
    
    Returns:
        设置指南文本
    """
    guide = """
🔧 Google Cloud Vision API 凭证设置指南:

1. 访问 Google Cloud Console:
   https://console.cloud.google.com/

2. 创建或选择项目

3. 启用 Vision API:
   https://console.cloud.google.com/apis/library/vision.googleapis.com

4. 创建服务账号:
   - 进入 IAM & Admin > Service Accounts
   - 点击 "Create Service Account"
   - 输入名称和描述
   - 分配角色: "Cloud Vision API User"

5. 下载密钥文件:
   - 点击创建的服务账号
   - 进入 "Keys" 标签
   - 点击 "Add Key" > "Create new key"
   - 选择 JSON 格式
   - 下载并保存为 'google-cloud-credentials.json'

6. 设置环境变量:
   set GOOGLE_APPLICATION_CREDENTIALS=path\\to\\google-cloud-credentials.json

7. 安装依赖:
   pip install google-cloud-vision

8. 测试连接:
   python mlkit_ocr.py
"""
    return guide

if __name__ == "__main__":
    print("MLKit OCR 配置文件")
    print("=" * 50)
    
    # 检查凭证文件
    credentials_path = GOOGLE_CLOUD_CONFIG['credentials_path']
    if setup_google_cloud_credentials(credentials_path):
        print("✅ 配置检查通过")
    else:
        print("\n" + get_credentials_setup_guide())
