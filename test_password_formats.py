#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 密码格式测试脚本 - 测试不同密码格式

import sys
import os
import string
import random

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_test_passwords():
    """生成不同格式的测试密码"""
    test_passwords = []
    
    # 格式1: 纯数字 (6-11位)
    for length in [6, 8, 11]:
        password = ''.join(random.choices(string.digits, k=length))
        test_passwords.append(('纯数字', password))
    
    # 格式2: 纯小写字母 (6-11位)
    for length in [6, 8, 11]:
        password = ''.join(random.choices(string.ascii_lowercase, k=length))
        test_passwords.append(('纯小写字母', password))
    
    # 格式3: 小写字母+数字 (6-11位)
    for length in [6, 8, 11]:
        chars = string.ascii_lowercase + string.digits
        password = ''.join(random.choices(chars, k=length))
        # 确保包含字母和数字
        if not any(c.isalpha() for c in password):
            password = password[:-1] + random.choice(string.ascii_lowercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + random.choice(string.digits)
        test_passwords.append(('小写字母+数字', password))
    
    # 格式4: 大小写字母+数字 (6-11位)
    for length in [6, 8, 11]:
        chars = string.ascii_letters + string.digits
        password = ''.join(random.choices(chars, k=length))
        # 确保包含字母和数字
        if not any(c.isalpha() for c in password):
            password = password[:-1] + random.choice(string.ascii_letters)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + random.choice(string.digits)
        test_passwords.append(('大小写字母+数字', password))
    
    # 格式5: 常见密码格式
    common_formats = [
        '123456',      # 6位纯数字
        'abc123',      # 字母+数字
        'Abc123',      # 大写+小写+数字
        'password',    # 纯字母
        'Password1',   # 大写+小写+数字
        '12345678',    # 8位纯数字
        'abcd1234',    # 4字母+4数字
    ]
    
    for password in common_formats:
        if 6 <= len(password) <= 11:
            test_passwords.append(('常见格式', password))
    
    return test_passwords

def analyze_password(password):
    """分析密码特征"""
    analysis = {
        'length': len(password),
        'has_lower': any(c.islower() for c in password),
        'has_upper': any(c.isupper() for c in password),
        'has_digit': any(c.isdigit() for c in password),
        'has_special': any(not c.isalnum() for c in password),
        'is_alnum': password.isalnum(),
        'char_types': []
    }
    
    if analysis['has_lower']:
        analysis['char_types'].append('小写字母')
    if analysis['has_upper']:
        analysis['char_types'].append('大写字母')
    if analysis['has_digit']:
        analysis['char_types'].append('数字')
    if analysis['has_special']:
        analysis['char_types'].append('特殊字符')
    
    return analysis

def test_current_generator():
    """测试当前的密码生成器"""
    print("🧪 当前密码生成器测试")
    print("=" * 50)
    
    try:
        from zhuccc1 import generate_random_password
        
        print("📋 生成20个密码进行分析:")
        
        password_stats = {
            'lengths': [],
            'formats': [],
            'all_valid': True
        }
        
        for i in range(20):
            password = generate_random_password()
            analysis = analyze_password(password)
            
            # 检查是否符合基本要求
            length_ok = 6 <= analysis['length'] <= 11
            has_letter = analysis['has_lower'] or analysis['has_upper']
            has_digit = analysis['has_digit']
            is_valid = length_ok and has_letter and has_digit
            
            if not is_valid:
                password_stats['all_valid'] = False
            
            password_stats['lengths'].append(analysis['length'])
            password_stats['formats'].append(' + '.join(analysis['char_types']))
            
            status = "✅" if is_valid else "❌"
            print(f"   {i+1:2d}. {password:12s} (长度:{analysis['length']:2d}, {' + '.join(analysis['char_types'])}) {status}")
        
        # 统计分析
        print(f"\n📊 统计分析:")
        print(f"   长度范围: {min(password_stats['lengths'])}-{max(password_stats['lengths'])}")
        print(f"   格式分布: {set(password_stats['formats'])}")
        print(f"   全部有效: {'✅ 是' if password_stats['all_valid'] else '❌ 否'}")
        
        return password_stats['all_valid']
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def suggest_password_formats():
    """建议可能的密码格式"""
    print("\n💡 密码格式建议")
    print("=" * 50)
    
    print("根据服务器错误 '登录密码限制6-11位字符'，可能的要求:")
    print("1. 长度: 6-11位 ✅ (已满足)")
    print("2. 字符类型可能的要求:")
    print("   - 必须包含字母和数字")
    print("   - 可能需要大写字母")
    print("   - 可能不允许特殊字符")
    print("   - 可能有特定的字符组合要求")
    
    print("\n🔍 建议测试的密码格式:")
    
    test_formats = [
        ("格式A", "大写+小写+数字", "Abc123"),
        ("格式B", "小写+数字", "abc123"),
        ("格式C", "数字+小写", "123abc"),
        ("格式D", "更多数字", "12ab34"),
        ("格式E", "更多字母", "ab12cd"),
    ]
    
    for format_name, description, example in test_formats:
        print(f"   {format_name}: {description:15s} 例如: {example}")
    
    print("\n💡 下一步建议:")
    print("1. 尝试手动输入不同格式的密码测试")
    print("2. 查看网站前端的密码验证规则")
    print("3. 分析其他成功注册的密码格式")

def main():
    """主函数"""
    print("🧪 密码格式分析工具")
    print("🔧 分析服务器密码要求并测试不同格式")
    print("=" * 60)
    
    # 测试当前生成器
    current_ok = test_current_generator()
    
    # 分析测试密码
    print("\n🧪 不同格式密码分析")
    print("=" * 50)
    
    test_passwords = generate_test_passwords()
    
    print("📋 各种格式的密码示例:")
    for format_type, password in test_passwords[:15]:  # 只显示前15个
        analysis = analyze_password(password)
        length_ok = 6 <= analysis['length'] <= 11
        status = "✅" if length_ok else "❌"
        print(f"   {format_type:15s}: {password:12s} (长度:{analysis['length']:2d}, {' + '.join(analysis['char_types'])}) {status}")
    
    # 建议
    suggest_password_formats()
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    if current_ok:
        print("✅ 当前密码生成器格式正确")
        print("⚠️ 但服务器仍然拒绝，可能有其他隐藏要求")
    else:
        print("❌ 当前密码生成器需要修复")
    
    print("\n💡 建议:")
    print("1. 手动测试几种不同格式的密码")
    print("2. 检查网站的前端验证规则")
    print("3. 尝试更常见的密码格式")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 分析被用户中断")
    except Exception as e:
        print(f"\n💥 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
