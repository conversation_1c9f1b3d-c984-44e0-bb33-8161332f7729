#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果检查工具 - 查看和分析扫描结果
"""

import json
import os
from datetime import datetime

def list_result_files():
    """列出所有结果文件"""
    results_dir = "scan_results"
    if not os.path.exists(results_dir):
        print("❌ scan_results 目录不存在")
        return []
    
    files = []
    for filename in os.listdir(results_dir):
        if filename.endswith('.json'):
            filepath = os.path.join(results_dir, filename)
            stat = os.stat(filepath)
            files.append({
                'name': filename,
                'path': filepath,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime)
            })
    
    # 按修改时间排序
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def analyze_results(filepath):
    """分析结果文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 判断文件类型
        if isinstance(data, list):
            # 扫描结果列表
            results = data
        elif isinstance(data, dict):
            if 'results' in data:
                # 进度文件
                results = data['results']
                print(f"📊 进度文件信息:")
                if 'current' in data and 'total' in data:
                    print(f"   进度: {data['current']}/{data['total']}")
                if 'statistics' in data:
                    stats = data['statistics']
                    print(f"   统计: 检查{stats.get('total_checked', 0)}, 注册{stats.get('registered_count', 0)}, 错误{stats.get('error_count', 0)}")
                print()
            else:
                print("❌ 无法识别的文件格式")
                return
        else:
            print("❌ 无法识别的文件格式")
            return
        
        if not results:
            print("📝 文件中没有扫描结果")
            return
        
        # 分析结果
        total = len(results)
        registered = [r for r in results if r.get('registered') == True]
        unregistered = [r for r in results if r.get('registered') == False]
        errors = [r for r in results if r.get('registered') is None]
        
        print(f"📊 结果分析:")
        print(f"   总计: {total}")
        print(f"   已注册: {len(registered)} ({len(registered)/total*100:.1f}%)")
        print(f"   未注册: {len(unregistered)} ({len(unregistered)/total*100:.1f}%)")
        print(f"   错误: {len(errors)} ({len(errors)/total*100:.1f}%)")
        
        if registered:
            print(f"\n🎯 已注册手机号:")
            for i, r in enumerate(registered[:10], 1):  # 只显示前10个
                print(f"   {i}. {r['phone']} ({r.get('timestamp', 'N/A')})")
            if len(registered) > 10:
                print(f"   ... 还有 {len(registered) - 10} 个")
        
        if errors:
            # 分析错误类型
            error_types = {}
            for r in errors:
                status = r.get('status', '未知错误')
                error_types[status] = error_types.get(status, 0) + 1
            
            print(f"\n❌ 错误类型分析:")
            for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                print(f"   {error_type}: {count} 次")
        
    except Exception as e:
        print(f"❌ 分析文件失败: {e}")

def extract_registered_phones(filepath):
    """提取已注册手机号"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取结果列表
        if isinstance(data, list):
            results = data
        elif isinstance(data, dict) and 'results' in data:
            results = data['results']
        else:
            print("❌ 无法识别的文件格式")
            return
        
        # 提取已注册手机号
        registered = [r for r in results if r.get('registered') == True]
        
        if not registered:
            print("📝 没有找到已注册的手机号")
            return
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"scan_results/extracted_phones_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for r in registered:
                f.write(f"{r['phone']}\n")
        
        print(f"✅ 已提取 {len(registered)} 个已注册手机号")
        print(f"📄 保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")

def main():
    print("🔍 扫描结果检查工具")
    print("=" * 40)
    
    # 列出结果文件
    files = list_result_files()
    
    if not files:
        print("📝 没有找到结果文件")
        return
    
    print(f"📁 找到 {len(files)} 个结果文件:")
    for i, file_info in enumerate(files, 1):
        size_kb = file_info['size'] / 1024
        print(f"{i:2d}. {file_info['name']}")
        print(f"     大小: {size_kb:.1f}KB, 修改时间: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n选择操作:")
    print("1. 分析结果文件")
    print("2. 提取已注册手机号")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice in ['1', '2']:
        try:
            file_index = int(input(f"请选择文件 (1-{len(files)}): ")) - 1
            if 0 <= file_index < len(files):
                selected_file = files[file_index]
                print(f"\n📄 处理文件: {selected_file['name']}")
                
                if choice == '1':
                    analyze_results(selected_file['path'])
                elif choice == '2':
                    extract_registered_phones(selected_file['path'])
            else:
                print("❌ 无效的文件选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
