import requests
import random
import base64
from io import BytesIO
from PIL import Image
import ddddocr
import time
from faker import Faker
from id_validator import validator
import urllib3

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def generate_apple_user_agents(count=50):
    user_agents = []
    # iPhone 11 (iOS 13) to iPhone 16 (iOS 18, hypothetical)
    for _ in range(count):
        ios_major = random.randint(13, 18)
        ios_minor = random.randint(0, 7)
        
        # Version string for Safari
        safari_version_str = f"{ios_major}.{ios_minor}"
        
        # Decide if there's a patch version for the OS string
        if random.random() > 0.4:
            ios_patch = random.randint(1, 3)
            os_version_str = f"{ios_major}_{ios_minor}_{ios_patch}"
        else:
            os_version_str = f"{ios_major}_{ios_minor}"

        # Build number
        build_prefix = ios_major + 4
        build_letter = random.choice("ABCDEFGHJKLMNPQRST")
        build_suffix = random.randint(10, 99)
        if random.random() > 0.7:
            build_number = f"{build_prefix}{build_letter}{build_suffix}{random.choice('abcdef')}"
        else:
            build_number = f"{build_prefix}{build_letter}{build_suffix}"
        
        apple_webkit_version = "605.1.15"
        safari_final_version = "604.1"

        ua = (
            f"Mozilla/5.0 (iPhone; CPU iPhone OS {os_version_str} like Mac OS X) "
            f"AppleWebKit/{apple_webkit_version} (KHTML, like Gecko) "
            f"Version/{safari_version_str} Mobile/{build_number} Safari/{safari_final_version}"
        )
        user_agents.append(ua)
    return user_agents

USER_AGENTS_POOL = generate_apple_user_agents(50)


class Zhonggjingji:
    def __init__(self, invite_code):
        self.base_url = "https://www.zhonggjingji.com/api"
        self.invite_code = invite_code
        self.fake = Faker('zh_CN')
        self.session = requests.Session()
        # 禁用 SSL 验证
        self.session.verify = False
        self.ocr = ddddocr.DdddOcr()
        self.session.headers.update({
            "User-Agent": random.choice(USER_AGENTS_POOL),
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://www.zhonggjingji.com",
            "Referer": f"https://www.zhonggjingji.com/register?code={self.invite_code}",
        })

    def get_captcha_from_image(self, img_base64):
        try:
            img_data = base64.b64decode(img_base64.split(',')[1])
            # 保存图片用于调试
            with open(f"captcha_{int(time.time())}.png", "wb") as f:
                f.write(img_data)
            
            # 尝试多种识别方法
            result = self.ocr.classification(img_data)
            print(f"原始识别结果: {result}")
            
            # 清理结果，只保留数字和字母
            cleaned_result = ''.join(c for c in result if c.isalnum())
            print(f"清理后结果: {cleaned_result}")
            
            # 如果结果长度不是4位，尝试其他方法
            if len(cleaned_result) != 4:
                # 尝试只保留数字
                digits_only = ''.join(c for c in result if c.isdigit())
                if len(digits_only) == 4:
                    print(f"使用纯数字结果: {digits_only}")
                    return digits_only
                else:
                    print(f"识别结果长度异常: {len(cleaned_result)}")
                    return None
            
            return cleaned_result
        except Exception as e:
            print(f"验证码识别失败: {e}")
            return None

    def get_captcha(self):
        url = f"{self.base_url}/auth/captchaImg"
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 200:
                print("验证码图片获取成功")
                key = data["data"]["key"]
                img_base64 = data["data"]["img"]
                captcha = self.get_captcha_from_image(img_base64)
                if captcha:
                    print(f"验证码识别成功: {captcha}")
                    return key, captcha
                else:
                    return None, None
            else:
                print(f"获取验证码失败: {data.get('msg')}")
                return None, None
        except requests.exceptions.RequestException as e:
            print(f"获取验证码请求失败: {e}")
            return None, None

    def register(self, account, password, key, captcha):
        url = f"{self.base_url}/auth/register"
        payload = {
            "account": account,
            "password": password,
            "re_password": password,
            "code": self.invite_code,
            "key": key,
            "captcha": captcha,
        }
        try:
            response = self.session.post(url, json=payload, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 200:
                print("注册成功")
                return data["data"]["token"], "注册成功"
            else:
                msg = data.get('msg', '未知错误')
                print(f"注册失败: {msg}")
                return None, msg
        except requests.exceptions.RequestException as e:
            msg = f"注册请求失败: {e}"
            print(msg)
            return None, msg

    def identify(self, token, name, id_card_no):
        url = f"{self.base_url}/user/identify"
        headers = dict(self.session.headers)
        headers["Authorization"] = token
        payload = {
            "name": name,
            "id_card_no": id_card_no,
            "id_card_front": "",
            "id_card_end": "",
        }
        try:
            response = self.session.post(url, headers=headers, json=payload, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 200:
                print("实名认证成功")
                return True
            else:
                print(f"实名认证失败: {data.get('msg')}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"实名认证请求失败: {e}")
            return False

    def sign_in(self, token):
        url = f"{self.base_url}/user/sign"
        headers = dict(self.session.headers)
        headers["Authorization"] = token
        try:
            response = self.session.post(url, headers=headers, json={}, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("code") == 200:
                print(f"签到成功: {data.get('msg')}")
                return True
            else:
                print(f"签到失败: {data.get('msg')}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"签到请求失败: {e}")
            return False

    def get_random_phone(self):
        return f"1{random.randint(3, 9)}{''.join(random.choices('**********', k=9))}"

    def get_random_password(self):
        return ''.join(random.choices('abcdefghijklmnopqrstuvwxyz**********', k=8))

    def get_random_name_and_id(self):
        while True:
            id_card = validator.fake_id()
            if validator.is_valid(id_card):
                name = self.fake.name()
                return name, id_card

if __name__ == '__main__':
    invite_code = input("请输入邀请码: ")
    num_accounts = int(input("请输入要注册的账号数量: "))
    MAX_RETRIES = 5  # Max retries for captcha/registration

    for i in range(num_accounts):
        print(f"--- 开始注册第 {i+1} 个账号 ---")
        app = Zhonggjingji(invite_code)
        
        phone = app.get_random_phone()
        password = app.get_random_password()
        name, id_card = app.get_random_name_and_id()

        token = None
        for attempt in range(MAX_RETRIES):
            print(f"尝试第 {attempt + 1}/{MAX_RETRIES} 次...")
            key, captcha = app.get_captcha()
            if not key:
                time.sleep(1)
                continue

            token, msg = app.register(phone, password, key, captcha)
            if token:
                break  # Success

            if "验证不通过" in msg:
                print("验证码错误，正在重试...")
                time.sleep(1)
            elif "邀请" in msg or "邀请码" in msg:
                print(f"邀请码相关错误 ({msg})，停止重试此账号。")
                break  # 邀请码问题，停止重试
            else:
                print(f"注册时遇到其他错误 ({msg})，不再重试此账号。")
                break  # break retry loop

        if not token:
            print(f"--- 第 {i+1} 个账号注册失败，已达到最大重试次数 ---")
            continue

        if app.identify(token, name, id_card):
            app.sign_in(token)
        
        print(f"--- 第 {i+1} 个账号注册流程结束 ---\n")



