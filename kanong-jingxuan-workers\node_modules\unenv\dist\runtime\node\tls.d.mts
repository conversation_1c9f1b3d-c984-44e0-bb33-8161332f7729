import type nodeTls from "node:tls";
export * from "./internal/tls/constants.mjs";
export { TLSSocket } from "./internal/tls/tls-socket.mjs";
export { Server } from "./internal/tls/server.mjs";
export { SecureContext } from "./internal/tls/secure-context.mjs";
export declare const connect: typeof nodeTls.connect;
export declare const createServer: typeof nodeTls.createServer;
export declare const checkServerIdentity: typeof nodeTls.checkServerIdentity;
export declare const convertALPNProtocols: unknown;
export declare const createSecureContext: typeof nodeTls.createSecureContext;
export declare const createSecurePair: typeof nodeTls.createSecurePair;
export declare const getCiphers: typeof nodeTls.getCiphers;
export declare const rootCertificates: typeof nodeTls.rootCertificates;
declare const _default: typeof nodeTls;
export default _default;
