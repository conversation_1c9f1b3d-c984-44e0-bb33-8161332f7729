import requests
import time
import json

class Proxy91Config:
    """
    91代理的配置类。
    请在此处填写您从91代理官方获取的API参数。
    """
    # 您的订单号 (trade_no)
    TRADE_NO = "A485329204029"
    # 您的密钥 (secret)
    SECRET = "NkiEECQ9QrRnvuWZ"
    
    # --- 其他API参数 (可根据需求修改) ---
    # 每次获取的IP数量
    NUM = 1
    # 协议: 1=HTTP/HTTPS, 2=SOCKS5
    PROTOCOL = 1
    # 返回格式: 'json' 或 'text'
    FORMAT = "json"
    # IP和端口的分隔符: 1=冒号(:), 2=下划线(_)
    SEP = 1
    # 代理时长 (分钟), 根据API文档，对于某些套餐此值固定为 1
    TIME = 1
    # 是否需要密码, 1=是, 0=否
    PW = 1
    # 是否过滤已使用过的: 1=是, 0=否
    FILTER = 1


class Proxy91Manager:
    """
    用于对接91代理的管理器。
    负责获取、管理和测试从91代理API获取的IP。
    """
    def __init__(self, config: Proxy91Config):
        self.config = config
        self.api_url = "http://api.91http.com/v1/get-ip"
        self.current_proxy = None
        self.proxy_info = {}
        print("✅ 91代理管理器已初始化。")

    def get_proxy(self, force_new=False):
        """
        获取一个代理IP。
        如果已有未过期的IP且不强制刷新，则返回当前IP。
        否则，调用API获取新IP。
        """
        if self.current_proxy and not force_new:
            # 简单的实现，没有检查过期时间，如果需要可以添加
            print("ℹ️ [91代理] 返回已缓存的代理。")
            return self.current_proxy

        print("ℹ️ [91代理] 正在从API获取新代理IP...")
        params = {
            "trade_no": self.config.TRADE_NO,
            "secret": self.config.SECRET,
            "num": self.config.NUM,
            "protocol": self.config.PROTOCOL,
            "format": self.config.FORMAT,
            "sep": self.config.SEP,
            "time": self.config.TIME,
            "filter": self.config.FILTER
        }
        # 如果设置了pw，则加入该参数
        if hasattr(self.config, 'PW'):
            params["pw"] = self.config.PW
        
        # 最多尝试3次
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                response = requests.get(self.api_url, params=params, timeout=15)
                response.raise_for_status()
                data = response.json()

                # code=0 是成功的标志
                if data.get("code") == 0:
                    # 增强解析的健壮性，根据用户提供的成功响应格式进行解析
                    response_data = data.get("data")
                    if not response_data or not isinstance(response_data, dict):
                        print(f"❌ [91代理] 获取代理失败: API返回的 'data' 字段为空或格式不正确。")
                        if attempt < max_attempts - 1:
                            print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                            continue
                        return None

                    proxy_list = response_data.get("proxy_list")
                    if not proxy_list or not isinstance(proxy_list, list) or len(proxy_list) == 0:
                        print(f"❌ [91代理] 获取代理失败: API返回的 'proxy_list' 字段为空或格式不正确。")
                        if attempt < max_attempts - 1:
                            print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                            continue
                        return None
                    
                    # 解析返回的代理信息
                    first_proxy_item = proxy_list[0]
                    ip = first_proxy_item.get("ip")
                    port = first_proxy_item.get("port")
                    user = first_proxy_item.get("http_user")
                    password = first_proxy_item.get("http_pass")

                    if ip and port:
                        ip_port = f"{ip}:{port}"
                        # 根据是否有用户名密码，构建不同的代理URL
                        if user and password:
                            proxy_url = f"http://{user}:{password}@{ip_port}"
                        else:
                            proxy_url = f"http://{ip_port}"
                        
                        self.current_proxy = {'http': proxy_url, 'https': proxy_url}
                        self.proxy_info = first_proxy_item
                        
                        expire_time = self.proxy_info.get('expire_time', '未知')
                        print(f"✅ [91代理] 成功获取新代理: {ip_port} (过期时间: {expire_time})")
                        return self.current_proxy
                    else:
                        print(f"❌ [91代理] 获取代理失败: 未能从API响应中解析出IP和端口。")
                        if attempt < max_attempts - 1:
                            print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                            continue
                        return None
                else:
                    # 处理API返回的错误
                    error_msg = data.get('msg', '未知API错误')
                    print(f"❌ [91代理] 获取代理失败: {error_msg}")
                    if data.get("code") == 2009:
                        print("    -> 解决方案: 请将您当前机器的公网IP地址添加到91代理后台的API使用白名单中。")
                    if attempt < max_attempts - 1:
                        print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                        continue
                    return None

            except requests.exceptions.RequestException as e:
                print(f"❌ [91代理] 网络错误: 请求API时失败: {e}")
                if attempt < max_attempts - 1:
                    print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                    continue
                return None
            except (json.JSONDecodeError, Exception) as e:
                print(f"❌ [91代理] 解析代理时发生未知错误: {e}")
                if attempt < max_attempts - 1:
                    print(f"尝试重新获取代理 (第 {attempt+2}/{max_attempts} 次)...")
                    continue
                return None
                
        print(f"❌ [91代理] 在 {max_attempts} 次尝试后仍未能获取代理")
        return None

    def release_proxy(self):
        """
        释放(清除)当前持有的代理。
        对于91代理的IP白名单模式，这只是一个本地操作。
        """
        self.current_proxy = None
        self.proxy_info = {}
        print("ℹ️ [91代理] 已清除当前代理。下次将获取新代理。")

# ==============================================================================
# ----------------------------- 模块独立测试区 --------------------------------
# ==============================================================================

def test_proxy_connection(proxy_dict):
    """使用获取到的代理测试网络连接"""
    if not proxy_dict:
        return
    print(f"\n--- 正在使用代理 {proxy_dict.get('http')} 测试连接... ---")
    try:
        # 使用百度作为测试目标
        test_url = "https://www.baidu.com"
        start_time = time.time()
        response = requests.get(test_url, proxies=proxy_dict, timeout=10)
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ 代理连接测试成功！响应时间: {elapsed:.2f}秒")
        else:
            print(f"❌ 代理连接测试失败，HTTP状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 代理连接测试失败: {e}")

if __name__ == '__main__':
    """
    当此文件被直接运行时，将执行以下测试代码。
    """
    print("="*60)
    print("            ▶▶▶ 91代理 (91HTTP) 模块测试工具 ◀◀◀")
    print("="*60 + "\n")
    
    # 1. 初始化配置和管理器
    config = Proxy91Config()
    manager = Proxy91Manager(config)
    
    # 2. 获取代理
    proxy = manager.get_proxy(force_new=True)
    
    # 3. 测试代理
    if proxy:
        test_proxy_connection(proxy)
    else:
        print("\n未能成功获取代理IP，请检查您的API配置或网络。")
        print("""
可能的原因:
1. API凭证错误 (trade_no, secret)
2. 套餐余额不足或已过期
3. 您当前的公网IP未在91代理后台的白名单中
4. 网络连接问题
""")

    print("\n--- 测试完毕 ---") 