#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 测试新的密码生成策略

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_password_generation():
    """测试新的密码生成策略"""
    print("🧪 新密码生成策略测试")
    print("=" * 50)
    
    try:
        from zhuccc1 import generate_random_password
        
        print("📋 生成20个测试密码:")
        
        password_stats = {
            'pure_digits': 0,
            'mixed': 0,
            'common': 0,
            'lengths': []
        }
        
        for i in range(20):
            password = generate_random_password()
            length = len(password)
            password_stats['lengths'].append(length)
            
            # 分类密码类型
            if password.isdigit():
                password_type = "纯数字"
                password_stats['pure_digits'] += 1
            elif password in ['123456', 'abc123', 'password1', '12345abc']:
                password_type = "常见格式"
                password_stats['common'] += 1
            else:
                password_type = "混合格式"
                password_stats['mixed'] += 1
            
            # 检查是否符合6-11位要求
            length_ok = 6 <= length <= 11
            status = "✅" if length_ok else "❌"
            
            print(f"   {i+1:2d}. {password:12s} (长度:{length:2d}, {password_type}) {status}")
        
        # 统计分析
        print(f"\n📊 统计分析:")
        print(f"   纯数字: {password_stats['pure_digits']}/20 ({password_stats['pure_digits']*5}%)")
        print(f"   混合格式: {password_stats['mixed']}/20 ({password_stats['mixed']*5}%)")
        print(f"   常见格式: {password_stats['common']}/20 ({password_stats['common']*5}%)")
        print(f"   长度范围: {min(password_stats['lengths'])}-{max(password_stats['lengths'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_testing_order():
    """建议测试顺序"""
    print(f"\n💡 建议测试顺序:")
    print("=" * 50)
    
    test_passwords = [
        ("123456", "6位纯数字 - 最简单"),
        ("1234567", "7位纯数字"),
        ("12345678", "8位纯数字"),
        ("abc123", "小写字母+数字"),
        ("123abc", "数字+小写字母"),
        ("password1", "常见密码格式"),
        ("abcd1234", "4字母+4数字"),
    ]
    
    print("🎯 手动测试建议:")
    for i, (pwd, desc) in enumerate(test_passwords, 1):
        print(f"   {i}. {pwd:12s} - {desc}")
    
    print(f"\n🔧 测试步骤:")
    print("1. 运行主脚本: python zhuccc1.py")
    print("2. 启用代理: Y")
    print("3. 使用自定义密码: Y")
    print("4. 按顺序测试上面的密码")
    print("5. 观察哪种格式能成功注册")

def analyze_success_pattern():
    """分析成功模式"""
    print(f"\n🔍 成功注册分析:")
    print("=" * 50)
    
    print("📝 从你的成功注册数据:")
    print("   用户名: 15656879846 (手机号)")
    print("   响应: 注册成功")
    print("   用户ID: 396540")
    
    print(f"\n💡 关键观察:")
    print("1. 请求格式是正确的 (form-data)")
    print("2. 加密算法基本正确 (使用v4NTEx37密钥)")
    print("3. 问题可能在密码的具体内容")
    print("4. 需要找到服务器接受的密码格式")
    
    print(f"\n🎯 测试策略:")
    print("1. 先测试最简单的纯数字密码")
    print("2. 如果纯数字不行，尝试简单混合")
    print("3. 启用代理IP进行测试")
    print("4. 观察错误信息的变化")

def main():
    """主函数"""
    print("🧪 新密码策略测试工具")
    print("🔧 测试更简单的密码格式")
    print("=" * 60)
    
    # 测试密码生成
    test_success = test_new_password_generation()
    
    # 建议测试顺序
    suggest_testing_order()
    
    # 分析成功模式
    analyze_success_pattern()
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    if test_success:
        print("✅ 新密码生成策略测试通过")
        print("💡 现在偏向生成更简单的密码格式")
        print("🎯 建议按顺序手动测试不同格式")
    else:
        print("❌ 密码生成测试失败")
    
    print(f"\n🚀 下一步:")
    print("1. 运行主脚本并启用代理")
    print("2. 使用自定义密码测试")
    print("3. 从最简单的格式开始")
    print("4. 找到成功的密码格式")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
