#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 账号保存功能测试脚本

import os
import sys
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_account_save():
    """测试账号保存功能"""
    print("🧪 账号保存功能测试")
    print("=" * 40)
    
    # 测试数据
    test_accounts = [
        {
            'phone': '***********',
            'password': 'Test123456',
            'device': 'iPhone 14 Pro',
            'user_id': 'test_user_001',
            'token': 'abcdef1234567890abcd',
            'proxy_ip': '*************:8080',
            'status': '注册+登录+实名认证成功'
        },
        {
            'phone': '***********',
            'password': 'Test789012',
            'device': 'iPhone 13 Pro Max',
            'user_id': 'test_user_002',
            'token': 'xyz9876543210fedcba98',
            'proxy_ip': '直连',
            'status': '注册+登录成功,跳过实名认证'
        },
        {
            'phone': '***********',
            'password': 'Test345678',
            'device': 'iPhone 12',
            'user_id': '未知',
            'token': 'test_token_prefix',
            'proxy_ip': '********:3128',
            'status': '登录验证失败'
        }
    ]
    
    # 测试文件路径
    test_file = "test_accounts.txt"
    
    try:
        print("📝 正在写入测试账号数据...")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            for account in test_accounts:
                account_info = (
                    f"{account['phone']}:{account['password']}:{account['device']}:"
                    f"{account['user_id']}:{account['token']}:{account['proxy_ip']}:{account['status']}\n"
                )
                f.write(account_info)
        
        print(f"✅ 测试数据已写入: {test_file}")
        
        # 读取并验证
        print("\n📖 正在读取并验证数据...")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 读取到 {len(lines)} 行数据")
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            parts = line.split(':')
            
            if len(parts) >= 7:
                print(f"\n📱 账号 #{i}:")
                print(f"   手机号: {parts[0]}")
                print(f"   密码: {parts[1]}")
                print(f"   设备: {parts[2]}")
                print(f"   用户ID: {parts[3]}")
                print(f"   Token: {parts[4]}")
                print(f"   代理IP: {parts[5]}")
                print(f"   状态: {parts[6]}")
            else:
                print(f"❌ 第{i}行数据格式错误: {line}")
        
        # 测试统计功能
        print("\n📊 统计信息:")
        success_count = sum(1 for line in lines if '成功' in line)
        proxy_count = sum(1 for line in lines if '直连' not in line.split(':')[5])
        
        print(f"   总账号数: {len(lines)}")
        print(f"   成功账号: {success_count}")
        print(f"   使用代理: {proxy_count}")
        
        # 清理测试文件
        os.remove(test_file)
        print(f"\n🗑️ 测试文件已清理: {test_file}")
        
        print("\n✅ 账号保存功能测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 账号保存功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return False

def test_directory_creation():
    """测试目录创建功能"""
    print("\n🧪 目录创建功能测试")
    print("=" * 40)
    
    test_dir = "test_output_dir"
    test_file = os.path.join(test_dir, "test_account.txt")
    
    try:
        print("📁 测试目录自动创建...")
        
        # 确保测试目录不存在
        if os.path.exists(test_dir):
            import shutil
            shutil.rmtree(test_dir)
        
        # 模拟主脚本的目录创建逻辑
        os.makedirs(os.path.dirname(test_file), exist_ok=True)
        
        if os.path.exists(test_dir):
            print(f"✅ 目录创建成功: {test_dir}")
        else:
            print(f"❌ 目录创建失败: {test_dir}")
            return False
        
        # 测试文件写入
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("test_phone:test_password:test_device:test_user:test_token:test_proxy:test_status\n")
        
        if os.path.exists(test_file):
            print(f"✅ 文件写入成功: {test_file}")
        else:
            print(f"❌ 文件写入失败: {test_file}")
            return False
        
        # 清理测试目录
        import shutil
        shutil.rmtree(test_dir)
        print(f"🗑️ 测试目录已清理: {test_dir}")
        
        print("✅ 目录创建功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 目录创建功能测试失败: {e}")
        
        # 清理测试目录
        if os.path.exists(test_dir):
            import shutil
            shutil.rmtree(test_dir)
        
        return False

def test_encoding():
    """测试中文编码"""
    print("\n🧪 中文编码测试")
    print("=" * 40)
    
    test_file = "test_encoding.txt"
    
    try:
        # 测试中文内容
        chinese_content = "测试手机号:测试密码:iPhone设备:用户123:令牌abc:代理IP:注册成功\n"
        
        print("📝 写入中文测试数据...")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(chinese_content)
        
        print("📖 读取中文测试数据...")
        with open(test_file, 'r', encoding='utf-8') as f:
            read_content = f.read()
        
        if read_content == chinese_content:
            print("✅ 中文编码测试通过!")
            result = True
        else:
            print("❌ 中文编码测试失败!")
            print(f"   写入: {chinese_content.strip()}")
            print(f"   读取: {read_content.strip()}")
            result = False
        
        # 清理测试文件
        os.remove(test_file)
        print(f"🗑️ 测试文件已清理: {test_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ 中文编码测试失败: {e}")
        
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return False

def main():
    """主测试函数"""
    print("🧪 账号保存功能完整测试套件")
    print("🔧 测试账号数据的保存、读取和管理功能")
    print("=" * 60)
    
    tests = [
        ("账号保存功能", test_account_save),
        ("目录创建功能", test_directory_creation),
        ("中文编码测试", test_encoding)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        result = test_func()
        results.append(result)
        
        if result:
            print(f"✅ {test_name}通过")
        else:
            print(f"❌ {test_name}失败")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过! 账号保存功能正常!")
        print("💡 现在可以放心使用注册脚本，账号信息会正确保存")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
    
    return passed_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
