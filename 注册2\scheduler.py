#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
定时任务调度器
"""

import schedule
import time
import logging
import threading
from datetime import datetime
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("scheduler.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskScheduler:
    def __init__(self):
        self.running = False
        self.thread = None
        
    def run_signin_task(self):
        """执行签到任务"""
        logger.info("=" * 50)
        logger.info("开始执行定时签到任务")
        logger.info("=" * 50)
        
        try:
            # 导入并执行签到脚本
            from 51daili_advanced_login import AdvancedDailiLogin
            
            client = AdvancedDailiLogin(ACCOUNT, PASSWORD, headless=HEADLESS)
            success = client.run()
            
            if success:
                logger.info("✓ 签到任务执行成功")
            else:
                logger.error("× 签到任务执行失败")
                
        except Exception as e:
            logger.error(f"签到任务执行出错: {e}")
        
        logger.info("=" * 50)
        logger.info("签到任务执行完毕")
        logger.info("=" * 50)
    
    def setup_schedule(self):
        """设置定时任务"""
        # 每天上午9点执行
        schedule.every().day.at("09:00").do(self.run_signin_task)
        
        # 每天下午6点执行（备用）
        schedule.every().day.at("18:00").do(self.run_signin_task)
        
        logger.info("定时任务已设置:")
        logger.info("- 每天 09:00 执行签到")
        logger.info("- 每天 18:00 执行签到（备用）")
    
    def run_scheduler(self):
        """运行调度器"""
        self.running = True
        logger.info("定时调度器启动")
        
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def start(self):
        """启动调度器"""
        if self.thread and self.thread.is_alive():
            logger.warning("调度器已经在运行")
            return
        
        self.setup_schedule()
        self.thread = threading.Thread(target=self.run_scheduler)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("调度器已停止")
    
    def run_now(self):
        """立即执行一次签到任务"""
        logger.info("手动触发签到任务")
        self.run_signin_task()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='51代理签到定时调度器')
    parser.add_argument('--now', action='store_true', help='立即执行一次签到')
    parser.add_argument('--daemon', action='store_true', help='以守护进程模式运行')
    
    args = parser.parse_args()
    
    scheduler = TaskScheduler()
    
    if args.now:
        # 立即执行
        scheduler.run_now()
    elif args.daemon:
        # 守护进程模式
        try:
            scheduler.start()
            logger.info("调度器正在后台运行，按 Ctrl+C 停止")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到停止信号")
            scheduler.stop()
    else:
        # 交互模式
        scheduler.start()
        
        print("\n" + "=" * 50)
        print("51代理签到调度器")
        print("=" * 50)
        print("命令:")
        print("  now  - 立即执行签到")
        print("  stop - 停止调度器")
        print("  quit - 退出程序")
        print("=" * 50)
        
        try:
            while True:
                cmd = input("\n请输入命令: ").strip().lower()
                
                if cmd == 'now':
                    scheduler.run_now()
                elif cmd == 'stop':
                    scheduler.stop()
                    print("调度器已停止")
                elif cmd == 'quit':
                    break
                else:
                    print("未知命令，请输入 now/stop/quit")
                    
        except KeyboardInterrupt:
            pass
        finally:
            scheduler.stop()
            print("\n程序已退出")

if __name__ == "__main__":
    main()
