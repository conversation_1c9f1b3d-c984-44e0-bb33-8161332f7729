#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium直接在浏览器中执行API请求
绕过反爬虫检测的终极方案
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json
import base64
from decrypt_analysis import EncryptionSystem

class SeleniumDirectAPI:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.crypto = EncryptionSystem()
        self.driver = None
        
        print("🎯 Selenium直接API调用方案")
        print("🌐 在浏览器环境中执行API请求")
        print("=" * 50)

    def setup_driver(self):
        """配置Chrome浏览器"""
        print("🔧 配置Chrome浏览器...")
        
        chrome_options = Options()
        
        # 基础配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        
        # 反检测配置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome浏览器配置成功")
            return True
            
        except Exception as e:
            print(f"❌ Chrome浏览器配置失败: {e}")
            return False

    def inject_crypto_functions(self):
        """注入加密函数到浏览器"""
        print("🔐 注入加密函数...")
        
        # 注入CryptoJS库
        crypto_js = """
        // 简化的AES加密函数
        window.customEncrypt = function(data) {
            const key = 'wtBfKcqAMug1wbH8';
            const jsonStr = JSON.stringify(data);
            
            // 这里使用浏览器原生的crypto API或者注入完整的CryptoJS
            // 为了简化，我们先返回Python计算的结果
            return null; // 将由Python计算
        };
        
        // MD5签名函数
        window.customSign = function(data) {
            const signKey = 'xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY';
            // 同样由Python计算
            return null;
        };
        
        // API请求函数
        window.makeAPIRequest = function(url, encryptedData, sign) {
            return fetch(url, {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                    'content-type': 'application/json',
                    'is_app': 'false',
                    'origin': 'https://ds-web1.yrpdz.com',
                    'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': navigator.userAgent,
                    'token': '',
                    'sign': sign,
                    'transfersecret': encryptedData
                },
                body: JSON.stringify({[encryptedData]: encryptedData})
            }).then(response => {
                return {
                    status: response.status,
                    text: response.text()
                };
            });
        };
        """
        
        self.driver.execute_script(crypto_js)
        print("✅ 加密函数注入完成")

    def call_api_in_browser(self, api_path, data):
        """在浏览器中调用API"""
        print(f"\n🚀 在浏览器中调用API: {api_path}")
        print("-" * 40)
        
        try:
            # 使用Python计算加密和签名
            encrypted = self.crypto.encrypt_data(data)
            sign = self.crypto.generate_sign(data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return None
            
            print(f"📤 请求数据: {data}")
            print(f"🔐 加密结果: {encrypted[:30]}...")
            print(f"✍️ 签名结果: {sign}")
            
            # 构造JavaScript代码来执行API请求
            js_code = f"""
            const apiUrl = '{self.base_url}{api_path}';
            const encryptedData = '{encrypted}';
            const signData = '{sign}';
            
            return fetch(apiUrl, {{
                method: 'POST',
                headers: {{
                    'accept': '*/*',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                    'content-type': 'application/json',
                    'is_app': 'false',
                    'origin': 'https://ds-web1.yrpdz.com',
                    'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': navigator.userAgent,
                    'token': '',
                    'sign': signData,
                    'transfersecret': encryptedData
                }},
                body: JSON.stringify({{[encryptedData]: encryptedData}})
            }}).then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        text: text,
                        headers: Object.fromEntries(response.headers.entries())
                    }};
                }});
            }}).catch(error => {{
                return {{
                    error: error.message
                }};
            }});
            """
            
            # 在浏览器中执行API请求
            print("📡 在浏览器中执行API请求...")
            result = self.driver.execute_async_script(f"""
                const callback = arguments[arguments.length - 1];
                ({js_code}).then(callback).catch(callback);
            """)
            
            print(f"📥 API响应: {result}")
            
            if 'error' in result:
                print(f"❌ 请求出错: {result['error']}")
                return None
            
            if result.get('status') == 200:
                response_text = result.get('text', '')
                
                if response_text.strip().startswith('<!DOCTYPE html>'):
                    print("❌ 返回HTML页面")
                    print(f"HTML前100字符: {response_text[:100]}...")
                    return None
                else:
                    print("✅ 返回API响应")
                    print(f"响应内容: {response_text}")
                    
                    # 尝试解密
                    try:
                        decrypted = self.crypto.decrypt_data(response_text)
                        if decrypted:
                            print(f"🔓 解密成功: {decrypted}")
                            return decrypted
                        else:
                            print("⚠️ 响应可能是明文")
                            return response_text
                    except:
                        print("⚠️ 响应不是加密格式")
                        return response_text
            else:
                print(f"❌ HTTP状态码: {result.get('status')}")
                return None
                
        except Exception as e:
            print(f"❌ 浏览器API调用失败: {e}")
            return None

    def selenium_direct_process(self):
        """Selenium直接API调用流程"""
        print("\n🎯 Selenium直接API调用流程")
        print("=" * 50)
        
        # 1. 设置浏览器
        if not self.setup_driver():
            return False
        
        try:
            # 2. 访问登录页面建立会话
            login_url = f"{self.base_url}/pages/login/login?code=21328050"
            print(f"📱 访问登录页面: {login_url}")
            
            self.driver.get(login_url)
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 3. 注入加密函数
            self.inject_crypto_functions()
            
            # 4. 测试验证码API
            captcha_result = self.call_api_in_browser("/dev-api/api/login/authccode.html", {})
            
            if captcha_result:
                print("\n🎉 验证码API调用成功！")
                
                # 5. 可以继续测试其他API
                # 比如短信验证码API
                phone = "13800138000"  # 测试手机号
                sms_result = self.call_api_in_browser("/dev-api/api/login/smscode.html", {
                    "phone": phone,
                    "code": ""
                })
                
                if sms_result:
                    print("🎉 短信API也调用成功！")
                    return True
                else:
                    print("⚠️ 短信API调用失败，但验证码API成功")
                    return True
            else:
                print("❌ 验证码API调用失败")
                return False
                
        finally:
            # 清理资源
            if self.driver:
                print("🧹 保持浏览器打开以便查看...")
                input("按回车键关闭浏览器...")
                self.driver.quit()


if __name__ == "__main__":
    api_caller = SeleniumDirectAPI()
    success = api_caller.selenium_direct_process()
    
    if success:
        print("\n🎊 Selenium直接API调用成功！")
        print("✅ 已成功绕过反爬虫检测")
    else:
        print("\n💔 Selenium直接API调用失败")
        print("\n💡 可能的原因:")
        print("1. 网站更新了反爬虫策略")
        print("2. 需要更长的等待时间")
        print("3. 需要处理额外的验证步骤")
