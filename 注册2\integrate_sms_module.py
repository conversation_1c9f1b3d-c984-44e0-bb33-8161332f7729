#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成短信验证码模块到现有注册机脚本的示例
这个文件展示如何将phone_sms_module.py模块集成到现有的注册机脚本中
"""

import os
import sys
import time
import random
import json
import requests
import base64
import threading
import queue
from typing import Dict, Any, Optional
import logging
import urllib3

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("sms_register.log", encoding="utf-8")
    ]
)
logger = logging.getLogger("SMSRegister")

# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入短信验证码模块
from phone_sms_module import get_phone_and_sms, SmsApiClient

# 导入注册机的加密功能
try:
    from 注册122 import rsa_encrypt_with_public_key, PUBLIC_KEY_D_BASE64
except ImportError:
    # 如果无法直接导入，提供一个简化的加密函数
    def rsa_encrypt_with_public_key(data):
        """
        示例加密函数，实际使用时应当使用注册122.py中的真实加密函数
        """
        logger.warning("使用了模拟的加密函数，实际使用时应导入真实的加密函数")
        return base64.b64encode(data).decode('utf-8')

# 全局配置
# 可根据实际情况修改
class Config:
    # 注册站点配置
    REGISTER_API = "https://zcbe1esh.quantumblazerhub.com/home/<USER>"
    
    # 文件路径
    SUCCESS_FILE = os.path.join(current_dir, "sms_success.txt")
    FAILURE_FILE = os.path.join(current_dir, "sms_failure.txt")
    
    # 短信平台配置
    SMS_USERNAME = ""  # 在运行时获取
    SMS_PASSWORD = ""  # 在运行时获取
    SMS_PROJECT_ID = ""  # 在运行时获取
    
    # 线程配置
    DEFAULT_THREADS = 3
    
    # 锁，用于线程安全操作
    file_lock = threading.Lock()

# 辅助函数
def log_to_file(filename: str, data: str) -> None:
    """线程安全地将数据写入文件"""
    with Config.file_lock:
        try:
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(data + '\n')
                f.flush()
                os.fsync(f.fileno())
        except Exception as e:
            logger.error(f"文件写入错误 '{filename}': {e}")
            logger.error(f"数据: {data}")

def generate_random_password(length: int = 10) -> str:
    """生成随机密码"""
    chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return ''.join(random.choice(chars) for _ in range(length))

# 核心注册函数
def register_with_sms_verification(sms_client: Optional[SmsApiClient] = None) -> bool:
    """
    使用短信验证码进行注册
    
    :param sms_client: 可选的SMS客户端实例，如果不提供则创建新的
    :return: 是否成功
    """
    # 1. 获取手机号和验证码
    if sms_client is None:
        sms_result = get_phone_and_sms(
            Config.SMS_USERNAME,
            Config.SMS_PASSWORD,
            Config.SMS_PROJECT_ID
        )
        phone_number = sms_result.get("phone")
        sms_code = sms_result.get("code")
        client = sms_result.get("client")
        success = sms_result.get("success", False)
        
        if not success:
            logger.error(f"获取手机号或验证码失败: {sms_result.get('message')}")
            return False
    else:
        # 使用提供的客户端获取手机号
        success, result = sms_client.get_phone_number(Config.SMS_PROJECT_ID)
        if not success:
            logger.error(f"获取手机号失败: {result}")
            return False
        
        phone_number, _ = result
        client = sms_client
        
        # 获取验证码
        sms_code = client.get_sms_code(phone_number, Config.SMS_PROJECT_ID)
        if not sms_code:
            logger.error(f"获取手机号 {phone_number} 的验证码失败")
            client.release_phone_number(phone_number, Config.SMS_PROJECT_ID)
            return False
    
    logger.info(f"成功获取手机号 {phone_number} 和验证码 {sms_code}")
    
    try:
        # 2. 准备注册数据
        generated_password = generate_random_password()
        
        registration_data = {
            "phone": phone_number,
            "password": generated_password,
            "password2": generated_password,  # 确认密码
            "code": sms_code,  # 短信验证码
            "imgCode": "",  # 部分网站可能需要图片验证码
            "inviteCode": ""  # 邀请码，如有需要请设置
        }
        
        # 3. 加密数据（如果需要）
        json_data = json.dumps(registration_data, ensure_ascii=False).encode('utf-8')
        encrypted_key = rsa_encrypt_with_public_key(json_data)
        payload = {"key": encrypted_key}
        
        # 4. 发送注册请求
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://zcbe1esh.quantumblazerhub.com",
            "Referer": "https://zcbe1esh.quantumblazerhub.com/signup"
        }
        
        response = requests.post(
            Config.REGISTER_API,
            headers=headers,
            json=payload,
            timeout=30,
            verify=False  # 注意：生产环境应启用SSL验证
        )
        
        # 5. 处理响应
        if response.status_code == 200:
            result = response.json()
            
            # 根据实际网站响应调整判断逻辑
            if result.get("code") == 200:  # 假设200是成功代码
                logger.info(f"账号 {phone_number} 注册成功!")
                
                # 记录成功信息
                account_info = f"手机号:{phone_number}----密码:{generated_password}----注册时间:{time.strftime('%Y-%m-%d %H:%M:%S')}"
                log_to_file(Config.SUCCESS_FILE, account_info)
                
                return True
            else:
                error_msg = result.get("msg", "未知错误")
                logger.error(f"注册失败: {error_msg}")
                
                # 记录失败信息
                failure_info = f"手机号:{phone_number}, 失败原因:{error_msg}"
                log_to_file(Config.FAILURE_FILE, failure_info)
                
                return False
        else:
            logger.error(f"注册请求失败，HTTP状态码: {response.status_code}")
            
            # 记录失败信息
            failure_info = f"手机号:{phone_number}, 失败原因:HTTP {response.status_code}"
            log_to_file(Config.FAILURE_FILE, failure_info)
            
            return False
            
    except Exception as e:
        logger.error(f"注册过程发生异常: {e}")
        
        # 记录失败信息
        failure_info = f"手机号:{phone_number}, 失败原因:{str(e)}"
        log_to_file(Config.FAILURE_FILE, failure_info)
        
        return False
    finally:
        # 释放手机号
        try:
            if client and phone_number:
                client.release_phone_number(phone_number, Config.SMS_PROJECT_ID)
                logger.info(f"手机号 {phone_number} 已释放")
        except Exception as e:
            logger.warning(f"释放手机号 {phone_number} 失败: {e}")

# 线程工作函数
def worker_thread(task_queue: queue.Queue, sms_client: SmsApiClient) -> None:
    """
    工作线程函数
    :param task_queue: 任务队列
    :param sms_client: 短信客户端实例
    """
    thread_name = threading.current_thread().name
    logger.info(f"线程 {thread_name} 已启动")
    
    while not task_queue.empty():
        try:
            # 从队列获取任务（本示例中任务为空占位符）
            task_queue.get_nowait()
        except queue.Empty:
            break
        
        try:
            logger.info(f"线程 {thread_name} 开始处理一个注册任务")
            success = register_with_sms_verification(sms_client)
            
            if success:
                logger.info(f"线程 {thread_name} 成功完成注册")
            else:
                logger.warning(f"线程 {thread_name} 注册失败")
                
        except Exception as e:
            logger.error(f"线程 {thread_name} 发生未处理的异常: {e}")
        finally:
            task_queue.task_done()
            
            # 任务间随机延迟，避免请求过于集中
            delay = random.uniform(2, 5)
            logger.debug(f"线程 {thread_name} 将等待 {delay:.1f} 秒后处理下一个任务")
            time.sleep(delay)
    
    logger.info(f"线程 {thread_name} 已完成所有任务")

def main():
    """主函数"""
    print("\n" + "="*60)
    print("  短信验证码自动注册系统  ")
    print("="*60)
    
    # 获取短信平台配置
    Config.SMS_USERNAME = input("请输入短信平台用户名: ")
    Config.SMS_PASSWORD = input("请输入短信平台密码: ")
    Config.SMS_PROJECT_ID = input("请输入短信平台项目ID: ")
    
    # 获取要注册的账号数量
    try:
        num_accounts = int(input("请输入要注册的账号数量: "))
        if num_accounts <= 0:
            print("账号数量必须为正整数，设置为默认值1")
            num_accounts = 1
    except ValueError:
        print("输入无效，设置为默认值1")
        num_accounts = 1
    
    # 获取线程数量
    try:
        num_threads = int(input(f"请输入线程数量 (建议 1-{Config.DEFAULT_THREADS}): ") or Config.DEFAULT_THREADS)
        if num_threads <= 0:
            print(f"线程数量必须为正整数，设置为默认值 {Config.DEFAULT_THREADS}")
            num_threads = Config.DEFAULT_THREADS
    except ValueError:
        print(f"输入无效，设置为默认值 {Config.DEFAULT_THREADS}")
        num_threads = Config.DEFAULT_THREADS
    
    # 确保线程数不超过账号数
    num_threads = min(num_threads, num_accounts)
    
    print("\n" + "="*60)
    print(f"配置信息：")
    print(f"- 注册账号数量: {num_accounts}")
    print(f"- 线程数量: {num_threads}")
    print(f"- 短信平台用户名: {Config.SMS_USERNAME}")
    print(f"- 项目ID: {Config.SMS_PROJECT_ID}")
    print(f"- 成功记录文件: {Config.SUCCESS_FILE}")
    print(f"- 失败记录文件: {Config.FAILURE_FILE}")
    print("="*60)
    
    # 确认继续
    confirm = input("\n以上信息确认无误? (y/n): ").lower()
    if confirm != 'y':
        print("已取消操作")
        return
    
    # 创建任务队列
    task_queue = queue.Queue()
    for _ in range(num_accounts):
        task_queue.put(None)  # 向队列中放入占位符任务
    
    # 创建短信验证码客户端
    sms_clients = []
    for i in range(num_threads):
        client = SmsApiClient(
            username=Config.SMS_USERNAME,
            password=Config.SMS_PASSWORD,
            project_id=Config.SMS_PROJECT_ID
        )
        # 登录
        if not client.login():
            print(f"短信客户端 {i+1} 登录失败，程序退出")
            return
        sms_clients.append(client)
        print(f"短信客户端 {i+1} 登录成功")
    
    # 创建并启动线程
    print(f"\n开始创建 {num_threads} 个线程处理 {num_accounts} 个注册任务...")
    threads = []
    
    for i in range(num_threads):
        thread = threading.Thread(
            target=worker_thread,
            args=(task_queue, sms_clients[i]),
            name=f"注册线程-{i+1}"
        )
        thread.daemon = True
        thread.start()
        threads.append(thread)
        # 给每个线程一点启动间隔时间
        time.sleep(0.5)
    
    # 等待所有任务完成
    try:
        # 显示进度
        total = num_accounts
        while not task_queue.empty():
            remaining = task_queue.unfinished_tasks
            done = total - remaining
            percent = (done / total) * 100 if total > 0 else 0
            print(f"\r进度: {done}/{total} ({percent:.1f}%)", end="")
            time.sleep(1)
        
        # 等待任务队列处理完毕
        task_queue.join()
        print("\n所有任务已处理完毕!")
        
        # 给线程一点时间来完成
        for thread in threads:
            thread.join(timeout=1.0)
            
    except KeyboardInterrupt:
        print("\n接收到中断信号，正在停止...")
    finally:
        # 尝试统计结果
        try:
            success_count = 0
            failure_count = 0
            
            if os.path.exists(Config.SUCCESS_FILE):
                with open(Config.SUCCESS_FILE, "r", encoding="utf-8") as f:
                    success_count = len(f.readlines())
                    
            if os.path.exists(Config.FAILURE_FILE):
                with open(Config.FAILURE_FILE, "r", encoding="utf-8") as f:
                    failure_count = len(f.readlines())
            
            print("\n" + "="*60)
            print(f"注册统计:")
            print(f"- 成功: {success_count}")
            print(f"- 失败: {failure_count}")
            print(f"- 总计: {success_count + failure_count}")
            print("="*60)
        except Exception as e:
            print(f"统计结果时出错: {e}")
    
    print("\n程序执行完毕，感谢使用!")

if __name__ == "__main__":
    main() 