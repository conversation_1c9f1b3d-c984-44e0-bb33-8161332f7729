#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# HCY抓包数据分析工具
# 用于分析和解析HCY抓包工具捕获的网络数据

import json
import base64
import re
import urllib.parse
from datetime import datetime
import os

def analyze_hcy_log_file(file_path):
    """分析HCY日志文件"""
    print(f"🔍 分析HCY日志文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 分析HTTP请求
        analyze_http_requests(content)
        
        # 分析JSON数据
        analyze_json_data(content)
        
        # 分析加密数据
        analyze_encrypted_data(content)
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def analyze_http_requests(content):
    """分析HTTP请求"""
    print("\n" + "="*50)
    print("🌐 HTTP请求分析")
    print("="*50)
    
    # 查找URL
    url_pattern = r'https?://[^\s\'"<>]+'
    urls = re.findall(url_pattern, content)
    
    if urls:
        print(f"📍 发现 {len(urls)} 个URL:")
        for i, url in enumerate(set(urls), 1):
            print(f"  {i}. {url}")
    
    # 查找HTTP方法
    method_pattern = r'(GET|POST|PUT|DELETE|PATCH)\s+'
    methods = re.findall(method_pattern, content)
    
    if methods:
        print(f"\n🔧 HTTP方法统计:")
        method_count = {}
        for method in methods:
            method_count[method] = method_count.get(method, 0) + 1
        for method, count in method_count.items():
            print(f"  {method}: {count} 次")

def analyze_json_data(content):
    """分析JSON数据"""
    print("\n" + "="*50)
    print("📋 JSON数据分析")
    print("="*50)
    
    # 查找JSON对象
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    potential_jsons = re.findall(json_pattern, content)
    
    valid_jsons = []
    for json_str in potential_jsons:
        try:
            parsed = json.loads(json_str)
            valid_jsons.append(parsed)
        except:
            continue
    
    if valid_jsons:
        print(f"📊 发现 {len(valid_jsons)} 个有效JSON对象:")
        for i, json_obj in enumerate(valid_jsons[:5], 1):  # 只显示前5个
            print(f"\n  JSON {i}:")
            print(f"    键: {list(json_obj.keys())}")
            if isinstance(json_obj, dict):
                for key, value in json_obj.items():
                    if isinstance(value, str) and len(value) > 50:
                        print(f"    {key}: {value[:50]}...")
                    else:
                        print(f"    {key}: {value}")

def analyze_encrypted_data(content):
    """分析加密数据"""
    print("\n" + "="*50)
    print("🔐 加密数据分析")
    print("="*50)
    
    # 查找Base64编码的数据
    base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
    base64_matches = re.findall(base64_pattern, content)
    
    if base64_matches:
        print(f"🔑 发现 {len(base64_matches)} 个可能的Base64编码数据:")
        for i, b64_data in enumerate(base64_matches[:10], 1):  # 只显示前10个
            print(f"  {i}. {b64_data[:50]}{'...' if len(b64_data) > 50 else ''}")
            
            # 尝试解码
            try:
                decoded = base64.b64decode(b64_data)
                if all(32 <= b <= 126 for b in decoded[:20]):  # 检查是否为可打印字符
                    print(f"     解码: {decoded[:50].decode('utf-8', errors='ignore')}...")
            except:
                pass
    
    # 查找十六进制数据
    hex_pattern = r'[0-9a-fA-F]{32,}'
    hex_matches = re.findall(hex_pattern, content)
    
    if hex_matches:
        print(f"\n🔢 发现 {len(hex_matches)} 个可能的十六进制数据:")
        for i, hex_data in enumerate(hex_matches[:5], 1):
            print(f"  {i}. {hex_data[:50]}{'...' if len(hex_data) > 50 else ''}")

def analyze_url_parameters(url):
    """分析URL参数"""
    print(f"\n🔍 分析URL参数: {url}")
    
    parsed = urllib.parse.urlparse(url)
    params = urllib.parse.parse_qs(parsed.query)
    
    if params:
        print("📋 URL参数:")
        for key, values in params.items():
            for value in values:
                print(f"  {key}: {value}")
                
                # 检查是否为加密数据
                if len(value) > 20 and re.match(r'^[A-Za-z0-9+/=]+$', value):
                    try:
                        decoded = base64.b64decode(value)
                        print(f"    (Base64解码尝试: {decoded[:30]}...)")
                    except:
                        pass

def interactive_analyzer():
    """交互式分析器"""
    print("🎯 HCY抓包数据分析工具")
    print("=" * 50)
    
    while True:
        print("\n请选择分析方式:")
        print("  1. 分析日志文件")
        print("  2. 分析单个URL")
        print("  3. 分析JSON字符串")
        print("  4. 分析Base64数据")
        print("  5. 分析十六进制数据")
        print("  Q. 退出")
        
        choice = input("\n您的选择: ").strip().upper()
        
        if choice == '1':
            file_path = input("请输入HCY日志文件路径: ").strip()
            analyze_hcy_log_file(file_path)
            
        elif choice == '2':
            url = input("请输入URL: ").strip()
            analyze_url_parameters(url)
            
        elif choice == '3':
            json_str = input("请输入JSON字符串: ").strip()
            try:
                parsed = json.loads(json_str)
                print("✅ JSON解析成功:")
                print(json.dumps(parsed, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                
        elif choice == '4':
            b64_data = input("请输入Base64数据: ").strip()
            try:
                decoded = base64.b64decode(b64_data)
                print(f"✅ Base64解码成功:")
                print(f"原始字节: {decoded}")
                try:
                    text = decoded.decode('utf-8')
                    print(f"UTF-8文本: {text}")
                except:
                    print("无法解码为UTF-8文本")
            except Exception as e:
                print(f"❌ Base64解码失败: {e}")
                
        elif choice == '5':
            hex_data = input("请输入十六进制数据: ").strip()
            try:
                decoded = bytes.fromhex(hex_data)
                print(f"✅ 十六进制解码成功:")
                print(f"原始字节: {decoded}")
                try:
                    text = decoded.decode('utf-8')
                    print(f"UTF-8文本: {text}")
                except:
                    print("无法解码为UTF-8文本")
            except Exception as e:
                print(f"❌ 十六进制解码失败: {e}")
                
        elif choice == 'Q':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

def analyze_avatar_upload_request(file_path):
    """专门分析头像上传请求"""
    print("🖼️ 头像上传请求分析")
    print("=" * 50)

    try:
        # 先尝试读取前面的HTTP头部分（通常是文本）
        with open(file_path, 'rb') as f:
            raw_data = f.read()

        # 查找HTTP头结束位置（双换行符）
        header_end = raw_data.find(b'\r\n\r\n')
        if header_end == -1:
            header_end = raw_data.find(b'\n\n')

        if header_end != -1:
            # 提取HTTP头部分
            header_data = raw_data[:header_end].decode('utf-8', errors='ignore')
            content = header_data
        else:
            # 如果找不到分隔符，尝试解码前1000字节
            content = raw_data[:1000].decode('utf-8', errors='ignore')

        # 提取HTTP请求信息
        lines = content.split('\n')

        # 解析请求行
        if lines[0].startswith('POST'):
            method, path, protocol = lines[0].split(' ')
            print(f"📡 请求方法: {method}")
            print(f"🎯 请求路径: {path}")
            print(f"📋 协议版本: {protocol}")

        # 解析请求头
        print(f"\n📋 请求头信息:")
        for line in lines[1:]:
            if line.strip() == "":
                break
            if ':' in line:
                key, value = line.split(':', 1)
                print(f"  {key.strip()}: {value.strip()}")

        # 分析multipart数据
        print(f"\n📦 Multipart表单数据分析:")

        # 查找boundary
        boundary_match = re.search(r'boundary=([^\r\n;]+)', content)
        if boundary_match:
            boundary = boundary_match.group(1)
            print(f"🔗 Boundary: {boundary}")

            # 在原始数据中查找multipart字段
            boundary_bytes = boundary.encode()
            parts = raw_data.split(b'--' + boundary_bytes)

            for i, part in enumerate(parts[1:-1], 1):  # 跳过第一个和最后一个空部分
                try:
                    part_text = part.decode('utf-8', errors='ignore')

                    # 提取字段名
                    name_match = re.search(r'name="([^"]+)"', part_text)
                    if name_match:
                        field_name = name_match.group(1)

                        if field_name == "userId":
                            value_match = re.search(r'\r?\n\r?\n([^\r\n]+)', part_text)
                            if value_match:
                                user_id = value_match.group(1).strip()
                                print(f"  👤 用户ID: {user_id}")

                        elif field_name == "language":
                            value_match = re.search(r'\r?\n\r?\n([^\r\n]+)', part_text)
                            if value_match:
                                language = value_match.group(1).strip()
                                print(f"  🌐 语言: {language}")

                        elif field_name == "files":
                            filename_match = re.search(r'filename="([^"]+)"', part_text)
                            if filename_match:
                                filename = filename_match.group(1)
                                print(f"  📁 文件名: {filename}")

                            # 计算文件大小
                            header_end = part.find(b'\r\n\r\n')
                            if header_end != -1:
                                file_data = part[header_end + 4:]
                                file_size = len(file_data)
                                print(f"  📊 文件大小: {file_size} 字节")

                                # 检查文件类型
                                if file_data.startswith(b'\xff\xd8\xff'):
                                    print(f"  🖼️ 文件类型: JPEG图片")
                except:
                    continue

        # 提取服务器地址
        host_match = re.search(r'Host: ([^\r\n]+)', content)
        if host_match:
            host = host_match.group(1)
            print(f"\n🌍 服务器: {host}")
            print(f"✅ 完整API地址: http://{host}{path}")

    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    # 检查是否有命令行参数指定文件
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if file_path.endswith('.hcy'):
            analyze_avatar_upload_request(file_path)
        else:
            analyze_hcy_log_file(file_path)
    else:
        interactive_analyzer()
