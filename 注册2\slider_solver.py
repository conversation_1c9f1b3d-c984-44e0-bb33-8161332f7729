#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
滑块验证码解决器
"""

import cv2
import numpy as np
from PIL import Image
import io
import base64
import time
import random
import logging
from selenium.webdriver.common.action_chains import ActionChains

logger = logging.getLogger(__name__)

class SliderSolver:
    def __init__(self, driver):
        self.driver = driver
        
    def get_gap_position(self, bg_img, slider_img):
        """
        通过图像识别获取滑块缺口位置
        """
        try:
            # 转换为灰度图
            bg_gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)
            slider_gray = cv2.cvtColor(slider_img, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            bg_edges = cv2.Canny(bg_gray, 50, 150)
            slider_edges = cv2.Canny(slider_gray, 50, 150)
            
            # 模板匹配
            result = cv2.matchTemplate(bg_edges, slider_edges, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 返回匹配位置
            return max_loc[0]
            
        except Exception as e:
            logger.error(f"图像识别失败: {e}")
            return None
    
    def get_track(self, distance):
        """
        生成人类化的滑动轨迹
        """
        track = []
        current = 0
        mid = distance * 4 / 5  # 减速点
        t = 0.2
        v = 0
        
        while current < distance:
            if current < mid:
                a = 2  # 加速度
            else:
                a = -3  # 减速度
            
            v0 = v
            v = v0 + a * t
            move = v0 * t + 1 / 2 * a * t * t
            current += move
            track.append(round(move))
        
        return track
    
    def add_noise_to_track(self, track):
        """
        为轨迹添加噪声，使其更像人类操作
        """
        noisy_track = []
        for move in track:
            # 添加随机噪声
            noise = random.uniform(-0.5, 0.5)
            noisy_move = move + noise
            noisy_track.append(noisy_move)
        
        return noisy_track
    
    def solve_slider_by_image(self, bg_element, slider_element, gap_element):
        """
        通过图像识别解决滑块验证
        """
        try:
            # 获取背景图和滑块图
            bg_img_base64 = bg_element.screenshot_as_base64
            slider_img_base64 = slider_element.screenshot_as_base64
            
            # 解码图像
            bg_img_data = base64.b64decode(bg_img_base64)
            slider_img_data = base64.b64decode(slider_img_base64)
            
            bg_img = cv2.imdecode(np.frombuffer(bg_img_data, np.uint8), cv2.IMREAD_COLOR)
            slider_img = cv2.imdecode(np.frombuffer(slider_img_data, np.uint8), cv2.IMREAD_COLOR)
            
            # 获取缺口位置
            gap_x = self.get_gap_position(bg_img, slider_img)
            
            if gap_x is None:
                logger.error("无法识别缺口位置")
                return False
            
            # 计算需要移动的距离
            slider_btn = self.driver.find_element_by_class_name("slider-btn")
            distance = gap_x - slider_btn.location['x']
            
            logger.info(f"识别到缺口位置: {gap_x}, 需要移动距离: {distance}")
            
            # 执行滑动
            return self.perform_slide(slider_btn, distance)
            
        except Exception as e:
            logger.error(f"图像识别滑块验证失败: {e}")
            return False
    
    def solve_slider_by_calculation(self, slider_btn, track_element):
        """
        通过计算解决滑块验证
        """
        try:
            # 获取轨道宽度
            track_width = track_element.size['width']
            slider_width = slider_btn.size['width']
            
            # 计算移动距离（轨道宽度 - 滑块宽度 - 边距）
            distance = track_width - slider_width - 10
            
            logger.info(f"计算移动距离: {distance}")
            
            return self.perform_slide(slider_btn, distance)
            
        except Exception as e:
            logger.error(f"计算滑块验证失败: {e}")
            return False
    
    def perform_slide(self, slider_btn, distance):
        """
        执行滑动操作
        """
        try:
            # 生成滑动轨迹
            track = self.get_track(distance)
            track = self.add_noise_to_track(track)
            
            # 点击并按住滑块
            ActionChains(self.driver).click_and_hold(slider_btn).perform()
            time.sleep(random.uniform(0.1, 0.2))
            
            # 按轨迹移动
            for move in track:
                ActionChains(self.driver).move_by_offset(move, random.randint(-2, 2)).perform()
                time.sleep(random.uniform(0.01, 0.02))
            
            # 添加随机抖动
            for _ in range(3):
                ActionChains(self.driver).move_by_offset(
                    random.randint(-2, 2), 
                    random.randint(-2, 2)
                ).perform()
                time.sleep(random.uniform(0.01, 0.02))
            
            # 释放鼠标
            ActionChains(self.driver).release().perform()
            
            # 等待验证结果
            time.sleep(2)
            
            logger.info("滑块操作完成")
            return True
            
        except Exception as e:
            logger.error(f"滑块操作失败: {e}")
            return False
    
    def solve_common_slider(self):
        """
        解决常见的滑块验证
        """
        try:
            # 尝试多种选择器
            slider_selectors = [
                ".slider-btn",
                ".slide-btn", 
                ".slider-button",
                "#slider-btn",
                "[class*='slider']",
                "[class*='slide']"
            ]
            
            track_selectors = [
                ".slider-track",
                ".slide-track",
                ".slider-bg",
                "#slider-track",
                "[class*='track']"
            ]
            
            slider_btn = None
            track_element = None
            
            # 查找滑块按钮
            for selector in slider_selectors:
                try:
                    slider_btn = self.driver.find_element_by_css_selector(selector)
                    if slider_btn.is_displayed():
                        logger.info(f"找到滑块按钮: {selector}")
                        break
                except:
                    continue
            
            # 查找滑块轨道
            for selector in track_selectors:
                try:
                    track_element = self.driver.find_element_by_css_selector(selector)
                    if track_element.is_displayed():
                        logger.info(f"找到滑块轨道: {selector}")
                        break
                except:
                    continue
            
            if not slider_btn or not track_element:
                logger.error("未找到滑块元素")
                return False
            
            # 尝试计算方法
            return self.solve_slider_by_calculation(slider_btn, track_element)
            
        except Exception as e:
            logger.error(f"通用滑块验证失败: {e}")
            return False
    
    def solve_geetest_slider(self):
        """
        解决极验滑块验证
        """
        try:
            # 极验特定的选择器
            slider_btn = self.driver.find_element_by_css_selector(".geetest_slider_button")
            canvas = self.driver.find_element_by_css_selector(".geetest_canvas_img")
            
            # 获取canvas图像并分析
            canvas_img = canvas.screenshot_as_png
            img = Image.open(io.BytesIO(canvas_img))
            
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # 查找缺口（这里需要根据具体的极验样式调整）
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 找到最大的轮廓（可能是缺口）
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)
                
                # 执行滑动
                return self.perform_slide(slider_btn, x)
            
            return False
            
        except Exception as e:
            logger.error(f"极验滑块验证失败: {e}")
            return False
