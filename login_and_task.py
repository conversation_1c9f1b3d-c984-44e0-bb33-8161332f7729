import requests
import json
import time
import random
import os
import sys
import threading
import queue
from proxy_manager import ProxyManager
import ssl
from requests.adapters import HTTPAdapter
from urllib3.poolmanager import PoolManager
from device_emulator import generate_complete_android_profile
from retrying import retry
from utils import logger
from haozu import YeyeYunClient, YeyeYunConfig
from phone import HaozhumaClient, HaozhumaConfig
import re
from decimal import Decimal

# ==============================================================================
# ---------------------- 1.5. 高级网络配置 (应对SSL错误) ------------------------
# ==============================================================================

class Tls12Adapter(HTTPAdapter):
    """一个强制使用 TLS 1.2 的请求适配器"""
    def init_poolmanager(self, connections, maxsize, block=False, **pool_kwargs):
        # 在Python 3.10+中，ssl_version 已被弃用，需要使用 context
        context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.maximum_version = ssl.TLSVersion.TLSv1_2
        
        self.poolmanager = PoolManager(
            num_pools=connections,
            maxsize=maxsize,
            block=block,
            ssl_context=context,
            **pool_kwargs
        )


# ==============================================================================
# ----------------------------- 1. 全局配置区 ----------------------------------
# ==============================================================================

# --- 线程安全配置 ---
class ThreadSafeCounter:
    """线程安全的计数器"""
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    def increment(self):
        with self._lock:
            self._value += 1
    
    @property
    def value(self):
        return self._value

# --- 线程安全的余额累加器 ---
class ThreadSafeBalanceAccumulator:
    """线程安全的余额累加器"""
    def __init__(self):
        self._total_balance = Decimal('0')
        self._lock = threading.Lock()
        self._balance_file_lock = threading.Lock()

    def add_balance(self, balance_value):
        with self._lock:
            self._total_balance += Decimal(str(balance_value))
    
    @property
    def total_balance(self):
        return self._total_balance
    
    def write_balance_to_file(self, file_path, phone, balance_data):
        with self._balance_file_lock:
            try:
                with open(file_path, 'a', encoding='utf-8') as f:
                    cash_balance = balance_data.get("cash_balance", "0")
                    rmb_balance = balance_data.get("rmb_balance", "0")
                    total_balance = balance_data.get("total_balance", "0")
                    f.write(f"账号: {phone} | 现金余额: {cash_balance} | 人民币余额: {rmb_balance} | 总余额: {total_balance}\n")
            except Exception as e:
                print(f"❌ 写入余额文件失败: {e}")


# --- 目标网站配置 ---
class WebsiteConfig:
    """网站相关配置"""
    BASE_URL = "https://www.xlcfxndv.vip"

# --- 本地文件配置 ---
DESKTOP_PATH = os.path.join(os.path.expanduser('~'), 'Desktop')
# 优先读取整理好的文件
FORMATTED_ACCOUNTS_PATH = os.path.join(DESKTOP_PATH, '账号_已整理.txt')
# 如果整理好的文件不存在，则读取原始文件
RAW_ACCOUNTS_PATH = os.path.join(DESKTOP_PATH, '账号.txt')
# 登录成功后的账号信息（token等）会保存在这里，但本脚本主要用作批量操作，所以可选1
ACCOUNT_INFO_PATH = os.path.join(DESKTOP_PATH, 'account_info.json')
# 保存余额信息的文件
BALANCE_LOG_PATH = os.path.join(os.path.join(DESKTOP_PATH, 'hook', '注册机'), '余额.txt')


# --- 51代理配置 (与注册脚本保持一致) ---
class ProxyConfig:
    """51代理相关配置"""
    # 您的套餐ID
    PACK_ID = "2"
    # 您的用户ID
    UID = "42477"
    # 权限校验名称
    ACCESS_NAME = "ab263263"
    # 权限校验密码
    ACCESS_PASSWORD = "8C84B9BFE1774BFB2443DD34797EE4FB"
    # 代理协议 ('http' 或 'socks5')
    PROTOCOL = "http"
    # 代理使用时长(分钟)
    TIME_DURATION = 3


# ==============================================================================
# ----------------------------- 2. 核心功能模块 -------------------------------
# ==============================================================================

class WebsiteClient:
    """封装所有与目标网站交互的逻辑，包括登录、签到、抽奖、代理管理等"""
    
    def __init__(self, base_url, proxy_client=None):
        self.base_url = base_url
        self.session = requests.Session()
        
        # 挂载自定义的TLS 1.2适配器，增强连接稳定性
        self.session.mount('https://', Tls12Adapter())
        
        # --- 核心改造: 为每个客户端实例生成一个唯一且持久的设备档案 ---
        self.device_profile = generate_complete_android_profile()
        
        # 1. 应用该设备的浏览器指纹到请求头
        self.session.headers.update(self.device_profile['headers'])
        
        # 2. 更新通用请求头
        self.session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': base_url,
            'Referer': f"{base_url}/"
        })
        
        self.api_token = None
        self.proxy_client = proxy_client
        
        # 使用更简洁的方式打印伪装信息
        print(f"客户端已初始化，伪装 User-Agent: {self.session.headers.get('User-Agent', '未知')}")
        
        if self.proxy_client:
            self.apply_new_proxy()
    
    def test_proxy(self, proxies, test_url="https://www.baidu.com", timeout=10):
        """测试代理是否可用"""
        print(f"正在测试代理: {proxies.get('http', '未知')}...")
        test_session = requests.Session()
        test_session.proxies.update(proxies)
        # 修正: 测试时也应使用和主会话一致的User-Agent
        test_session.headers.update({'User-Agent': self.session.headers['User-Agent']})
        try:
            start_time = time.time()
            response = test_session.get(test_url, timeout=timeout)
            elapsed = time.time() - start_time
            if response.status_code == 200:
                print(f"✅ 代理测试成功! 响应时间: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ 代理测试失败: HTTP状态码 {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 代理测试失败: {e}")
            return False
    
    def apply_new_proxy(self):
        """应用一个新的代理到会话中"""
        if not self.proxy_client: return False
        try:
            max_attempts = 3
            for attempt in range(1, max_attempts + 1):
                proxies = self.proxy_client.get_proxy(force_new=True)
                if not proxies:
                    print("❌ 无法从代理池获取IP")
                    return False
                if self.test_proxy(proxies):
                    self.session.proxies.update(proxies)
                    print(f"✅ 已应用新代理: {proxies.get('http', '未知')}")
                    return True
                else:
                    print(f"⚠️ 代理不可用，尝试第 {attempt}/{max_attempts} 次重新获取...")
                    self.proxy_client.release_proxy()
            
            print("❌ 多次尝试后仍未获取到可用代理，将使用直接连接。")
            self.session.proxies.clear()
            return False
        except Exception as e:
            print(f"❌ 应用代理时出错: {e}")
            return False
    
    def login(self, phone, password):
        """登录目标网站并获取ApiToken"""
        print(f"\n--- 正在登录账号: {phone} ---")
        try:
            login_url = f"{self.base_url}/api/member.Login/login"
            login_data = {"phone": phone, "password": password}
            response = self.session.post(login_url, json=login_data, timeout=20)
            data = response.json()
            
            # 终极精准修复：根据您提供的JSON，将 "token" 改为 "ApiToken"
            if data.get("code") == 200 and "data" in data and "ApiToken" in data["data"]:
                self.api_token = data["data"]["ApiToken"]
                self.session.headers.update({'apitoken': self.api_token})
                print(f"✅ 登录成功！用户名: {data['data'].get('nickname', '未知')}")
                return True
            else:
                print(f"❌ 登录失败: {data.get('msg', '未知错误')}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 登录时发生网络错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 登录时发生未知错误: {e}")
            return False
    
    def sign_in(self):
        """执行每日签到（包含网络错误重试逻辑）"""
        if not self.api_token:
            print("❌ 未登录，无法签到")
            return
        
        print("--- 正在执行每日签到 ---")
        max_retries = 3
        for attempt in range(max_retries):
            try:
                sign_url = f"{self.base_url}/api/sign.Sign/sign"
                response = self.session.post(sign_url, json={}, timeout=20)
                data = response.json()
                
                if data.get("code") == 200:
                    print(f"✅ 签到成功: {data.get('msg', '')}, 奖励: {json.dumps(data.get('data', ''), ensure_ascii=False)}")
                    return
                elif "已签到" in data.get("msg", "") or "已用完" in data.get("msg", ""):
                    print(f"⚠️ 今日已签到或无机会: {data.get('msg', '')}")
                    return
                else:
                    print(f"❌ 签到失败: {data.get('msg', '未知错误')}")
                    return # 逻辑错误，不重试
            except requests.exceptions.RequestException as e:
                print(f"❌ 签到时发生网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    print("❌ 签到因网络问题多次重试失败。")
            except Exception as e:
                print(f"❌ 签到时发生未知错误: {e}")
                return

    def draw_lottery(self):
        """执行抽奖（包含网络错误重试逻辑）"""
        if not self.api_token:
            print("❌ 未登录，无法抽奖")
            return
        
        print("--- 正在执行抽奖 ---")
        max_retries = 3
        for attempt in range(max_retries):
            try:
                lottery_url = f"{self.base_url}/api/drawing.Drawing/start"
                response = self.session.post(lottery_url, json={}, timeout=20)
                data = response.json()
                
                if data.get("code") == 200:
                    print(f"✅ 抽奖成功! 结果: {json.dumps(data.get('data', ''), ensure_ascii=False)}")
                    return
                elif "次数不足" in data.get("msg", "") or "已用完" in data.get("msg", ""):
                    print(f"⚠️ 抽奖次数已用完: {data.get('msg', '')}")
                    return
                else:
                    print(f"❌ 抽奖失败: {data.get('msg', '未知错误')}")
                    return # 逻辑错误，不重试
            except requests.exceptions.RequestException as e:
                print(f"❌ 抽奖时发生网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    print("❌ 抽奖因网络问题多次重试失败。")
            except Exception as e:
                print(f"❌ 抽奖时发生未知错误: {e}")
                return

    def query_balance(self):
        """查询会员余额"""
        if not self.api_token:
            print("❌ 未登录，无法查询余额")
            return None
        
        print("--- 正在查询余额 ---")
        max_retries = 3
        for attempt in range(max_retries):
            try:
                url = f"{self.base_url}/api/member.Member/getBalance"
                params = {"_t": str(int(time.time() * 1000))}
                
                response = self.session.get(url, params=params, timeout=20)
                response.raise_for_status()
                
                json_data = response.json()
                
                if json_data.get("code") == 200:
                    data = json_data.get("data", {})
                    cash_balance = data.get("cash_balance")
                    rmb_balance = data.get("rmb_balance")
                    total_balance = data.get("total_balance")
                    
                    print(f"✅ 余额查询成功！现金余额: {cash_balance}, 人民币余额: {rmb_balance}, 总余额: {total_balance}")
                    return data
                else:
                    print(f"❌ 余额查询失败: {json_data.get('msg', '未知错误')}")
                    return None
                
            except requests.exceptions.RequestException as e:
                print(f"❌ 查询余额时发生网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    print("❌ 余额查询因网络问题多次重试失败。")
            except Exception as e:
                print(f"❌ 查询余额时发生未知错误: {e}")
                return None

# ==============================================================================
# ----------------------------- 3. 多线程工作与主程序 --------------------------
# ==============================================================================

def task_worker(account_queue, proxy_manager, successful_logins, failed_logins, balance_accumulator):
    """
    线程工作函数：从队列中获取账号并执行登录、签到、抽奖任务。
    包含登录失败后的自动重试逻辑。
    """
    thread_name = threading.current_thread().name
    MAX_LOGIN_ATTEMPTS = 3 # 每个账号最多尝试登录3次

    while not account_queue.empty():
        try:
            account = account_queue.get_nowait()
        except queue.Empty:
            break

        phone = account['phone']
        password = account['password']
        
        print(f"\n[{thread_name}] 开始处理账号: {phone}")

        # 每个线程处理自己的账号时，创建独立的客户端实例
        website = WebsiteClient(WebsiteConfig.BASE_URL, proxy_manager)
        
        login_successful = False
        for attempt in range(MAX_LOGIN_ATTEMPTS):
            if website.login(phone, password):
                successful_logins.increment()
                print(f"[{thread_name}] 账号 {phone} 登录成功，开始执行任务...")
                website.sign_in()
                time.sleep(random.randint(2, 5))
                website.draw_lottery()
                time.sleep(random.randint(2, 5))
                
                # 查询余额并保存
                balance_data = website.query_balance()
                if balance_data and 'cash_balance' in balance_data:
                    # 写入完整的余额详情到日志文件，供详细查阅
                    balance_accumulator.write_balance_to_file(BALANCE_LOG_PATH, phone, balance_data)
                    # 但只累加现金余额用于最终统计
                    try:
                        cash_balance_value = balance_data.get('cash_balance', '0')
                        balance_accumulator.add_balance(cash_balance_value)
                    except Exception as e:
                        print(f"❌ 累加现金余额时出错: {e}")
                
                login_successful = True
                break # 登录成功，跳出重试循环
            else:
                print(f"⚠️ [{thread_name}] 账号 {phone} 登录失败 (尝试 {attempt + 1}/{MAX_LOGIN_ATTEMPTS})。")
                if attempt < MAX_LOGIN_ATTEMPTS - 1:
                    print(f"[{thread_name}] 正在更换代理并等待3秒后重试...")
                    if not website.apply_new_proxy():
                        print(f"[{thread_name}] 更换代理失败，将直接重试。")
                    time.sleep(3)
        
        if not login_successful:
            failed_logins.increment()
            print(f"❌ [{thread_name}] 账号 {phone} 多次尝试后登录失败，已放弃。")

        account_queue.task_done()
        time.sleep(random.randint(1, 3))


def read_accounts_from_log(file_path):
    """从账号文件中读取账号和密码列表 (兼容多种格式)"""
    print(f"正在从 '{os.path.basename(file_path)}' 读取账号信息...")
    accounts = []
    if not os.path.exists(file_path):
        # 这个分支的打印信息现在由主逻辑处理，这里只返回结果
        return [], False 
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 使用集合来自动去重
            unique_accounts = set()
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or "===" in line: continue
                
                parts = [p.strip() for p in line.split('|')]
                phone = ""
                password = ""

                # 格式: 手机号 | 密码
                if len(parts) == 2:
                    phone = parts[0]
                    password = parts[1]
                # 格式: ... | 手机号 | 密码 | ... (兼容带时间和邀请码的格式)
                elif len(parts) >= 3:
                    phone = parts[1]
                    password = parts[2]
                
                # 统一校验和添加
                if phone and password and phone.isdigit():
                    # 以元组形式存入集合，因为字典不可哈希
                    unique_accounts.add((phone, password))
                else:
                    print(f"⚠️ 警告: 第 {line_num} 行格式无法识别，已跳过。 -> 内容: '{line}'")
        
        # 将去重后的元组转换回字典列表
        accounts = [{"phone": phone, "password": password} for phone, password in unique_accounts]
        
        if len(accounts) > 0:
            print(f"✅ 成功读取并去重后得到 {len(accounts)} 个有效账号。")
        else:
            print(f"❌ 未能从文件中读取到任何有效账号。请检查文件内容和格式。")
            
        return accounts, True
    except Exception as e:
        print(f"❌ 读取账号文件时出错: {e}")
        return [], False

if __name__ == '__main__':
    print("\n" + "="*70)
    print("            ▶▶▶ 网站自动登录/签到/抽奖工具 ◀◀◀")
    print("="*70 + "\n")

    use_proxy_input = input("是否使用代理IP? (推荐使用)\n  [1] 是 (默认)\n  [2] 否，直接连接\n请选择 (1/2): ").strip() or "1"
    use_proxy = use_proxy_input == '1'

    proxy_manager = None
    if use_proxy:
        print("\n--- 正在初始化代理IP管理器 ---")
        try:
            proxy_manager = ProxyManager(
                packid=ProxyConfig.PACK_ID,
                uid=ProxyConfig.UID,
                access_name=ProxyConfig.ACCESS_NAME,
                access_password=ProxyConfig.ACCESS_PASSWORD,
                protocol=ProxyConfig.PROTOCOL,
                time_duration=ProxyConfig.TIME_DURATION
            )
            print("✅ 代理管理器已初始化。")
            
            # 新增：代理服务预检查
            print("--- 正在进行代理服务预检查 ---")
            test_proxy = proxy_manager.get_proxy(force_new=True)
            if test_proxy:
                print(f"✅ 代理服务预检查通过！获取到测试代理: {test_proxy.get('http')}")
                proxy_manager.release_proxy() # 释放测试代理，不占用
            else:
                sys.exit("❌ 代理服务预检查失败！无法获取到代理IP，请检查代理客户端是否运行或配置是否正确。")

        except Exception as e:
            print(f"❌ 代理管理器初始化失败: {e}")
            proxy_manager = None

    # --- 智能选择账号文件 ---
    print("\n--- 正在选择账号文件 ---")
    accounts = []
    # 优先尝试读取整理好的文件
    if os.path.exists(FORMATTED_ACCOUNTS_PATH):
        accounts, success = read_accounts_from_log(FORMATTED_ACCOUNTS_PATH)
    
    # 如果整理好的文件不存在或读取失败，则尝试原始文件
    if not accounts:
        if not os.path.exists(FORMATTED_ACCOUNTS_PATH):
             print(f"ℹ️ 未找到 '{os.path.basename(FORMATTED_ACCOUNTS_PATH)}'，将尝试读取原始文件。")
        else:
             print(f"⚠️ 从 '{os.path.basename(FORMATTED_ACCOUNTS_PATH)}' 中未读取到有效账号，将尝试原始文件。")

        accounts, success = read_accounts_from_log(RAW_ACCOUNTS_PATH)

    if not accounts:
        sys.exit("\n❌ 在所有可用的账号文件中均未读取到有效账号，程序退出。")
        
    # --- 多线程配置 ---
    num_threads_input = input(f"请输入并发线程数 (默认为5): ").strip()
    num_threads = int(num_threads_input) if num_threads_input and num_threads_input.isdigit() else 5

    print(f"\n--- 准备为 {len(accounts)} 个账号执行任务，使用 {num_threads} 个线程 ---")
    
    # 创建并填充任务队列
    account_queue = queue.Queue()
    for acc in accounts:
        account_queue.put(acc)
    
    successful_logins = ThreadSafeCounter()
    failed_logins = ThreadSafeCounter()
    balance_accumulator = ThreadSafeBalanceAccumulator()

    # 确保余额文件的目录存在
    os.makedirs(os.path.dirname(BALANCE_LOG_PATH), exist_ok=True)
    
    # 清空余额文件内容
    with open(BALANCE_LOG_PATH, 'w', encoding='utf-8') as f:
        f.write(f"--- 余额查询结果 ({time.strftime('%Y-%m-%d %H:%M:%S')}) ---\n\n")

    # 创建并启动线程
    threads = []
    print("\n--- 开始执行多线程任务 ---")
    for i in range(num_threads):
        thread = threading.Thread(
            target=task_worker,
            args=(account_queue, proxy_manager, successful_logins, failed_logins, balance_accumulator),
            name=f"任务线程-{i+1}"
        )
        threads.append(thread)
        thread.start()
        time.sleep(0.1) # 稍微错开线程启动时间

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    # 写入总额到文件
    with open(BALANCE_LOG_PATH, 'a', encoding='utf-8') as f:
        f.write("\n" + "="*50 + "\n")
        f.write(f"总计现金余额: {balance_accumulator.total_balance}\n")

    print("\n" + "="*70)
    print("🎉 所有账号的任务均已执行完毕！")
    print(f"   - 成功登录: {successful_logins.value}个")
    print(f"   - 登录失败: {failed_logins.value}个")
    print(f"   - 总计现金余额: {balance_accumulator.total_balance}")
    print(f"   - 余额详情已保存至: {BALANCE_LOG_PATH}")
    print("="*70) 