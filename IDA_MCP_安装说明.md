# IDA Pro MCP 插件安装说明

## 1. 安装 IDA 插件

### 手动安装步骤：

1. **创建 IDA 插件目录**（如果不存在）：
   ```
   %APPDATA%\Hex-Rays\IDA Pro\plugins
   ```

2. **复制插件文件**：
   将 `mcp-plugin.py` 复制到上述目录中

3. **重启 IDA Pro**

### 自动安装（推荐）：
运行 `install_ida_plugin.bat` 批处理文件

## 2. 配置 Cursor MCP

### 手动配置：

1. **创建配置目录**（如果不存在）：
   ```
   %APPDATA%\Cursor\User\globalStorage\saoudrizwan.claude-dev\settings
   ```

2. **复制配置文件**：
   将 `cline_mcp_settings.json` 复制到上述目录中

3. **重启 Cursor**

## 3. 使用步骤

1. **启动 IDA Pro** 并加载一个二进制文件
2. **启动 MCP 服务器**：
   - 方法1：菜单 `Edit -> Plugins -> MCP`
   - 方法2：快捷键 `Ctrl+Alt+M`
3. **验证连接**：在 IDA 输出窗口应该看到：
   ```
   [MCP] Server started at http://localhost:13337
   ```
4. **在 Cursor 中测试**：现在可以使用 MCP 工具了

## 4. 测试 MCP 功能

运行测试脚本：
```bash
python test_mcp_server.py
```

## 5. 可用的 MCP 工具

### 基础功能：
- `check_connection()` - 检查 IDA 插件是否运行
- `get_metadata()` - 获取当前 IDB 的元数据
- `get_function_by_name(name)` - 根据名称获取函数
- `get_function_by_address(address)` - 根据地址获取函数
- `get_current_address()` - 获取用户当前选择的地址
- `get_current_function()` - 获取用户当前选择的函数

### 列表功能：
- `list_functions(offset, count)` - 列出数据库中的所有函数（分页）
- `list_globals_filter(offset, count, filter)` - 列出匹配的全局变量（分页，过滤）
- `list_globals(offset, count)` - 列出所有全局变量（分页）
- `list_strings_filter(offset, count, filter)` - 列出匹配的字符串（分页，过滤）
- `list_strings(offset, count)` - 列出所有字符串（分页）
- `list_local_types()` - 列出数据库中的所有本地类型

### 分析功能：
- `decompile_function(address)` - 反编译指定地址的函数
- `disassemble_function(start_address)` - 获取函数的汇编代码
- `get_xrefs_to(address)` - 获取指向给定地址的所有交叉引用
- `get_xrefs_to_field(struct_name, field_name)` - 获取指向命名结构字段的所有交叉引用
- `get_entry_points()` - 获取数据库中的所有入口点

### 修改功能：
- `set_comment(address, comment)` - 为给定地址设置注释
- `rename_local_variable(function_address, old_name, new_name)` - 重命名函数中的局部变量
- `rename_global_variable(old_name, new_name)` - 重命名全局变量
- `set_global_variable_type(variable_name, new_type)` - 设置全局变量的类型
- `rename_function(function_address, new_name)` - 重命名函数
- `set_function_prototype(function_address, prototype)` - 设置函数的原型
- `declare_c_type(c_declaration)` - 从 C 声明创建或更新本地类型
- `set_local_variable_type(function_address, variable_name, new_type)` - 设置局部变量的类型

### 工具功能：
- `convert_number(text, size)` - 将数字（十进制，十六进制）转换为不同的表示形式

### 调试功能（需要 --unsafe 标志）：
- `dbg_get_registers()` - 获取所有寄存器及其值
- `dbg_get_call_stack()` - 获取当前调用堆栈
- `dbg_list_breakpoints()` - 列出程序中的所有断点
- `dbg_start_process()` - 启动调试器
- `dbg_exit_process()` - 退出调试器
- `dbg_continue_process()` - 继续调试器
- `dbg_run_to(address)` - 运行调试器到指定地址
- `dbg_set_breakpoint(address)` - 在指定地址设置断点
- `dbg_delete_breakpoint(address)` - 删除指定地址的断点
- `dbg_enable_breakpoint(address, enable)` - 启用或禁用指定地址的断点

## 6. 故障排除

### 常见问题：

1. **MCP 服务器无法启动**：
   - 检查端口 13337 是否被占用
   - 确保 Python 版本 >= 3.11（推荐）

2. **Cursor 无法连接到 MCP**：
   - 确保 Cursor 已完全重启
   - 检查 MCP 配置文件路径是否正确

3. **IDA 插件无法加载**：
   - 检查插件文件是否在正确的目录中
   - 查看 IDA 输出窗口的错误信息

### 调试步骤：

1. 运行 `test_mcp_server.py` 检查服务器状态
2. 检查 IDA 输出窗口的日志信息
3. 确保所有文件路径正确
4. 重启 IDA Pro 和 Cursor

## 7. 文件结构

```
zhuce/
├── mcp-plugin.py                    # IDA 插件文件
├── cline_mcp_settings.json         # Cursor MCP 配置
├── test_mcp_server.py              # 测试脚本
├── install_ida_plugin.bat          # 自动安装脚本
└── IDA_MCP_安装说明.md             # 本说明文件
```
