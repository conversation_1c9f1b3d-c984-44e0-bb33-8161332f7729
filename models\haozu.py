import requests
import time
import json

# ==============================================================================
# ----------------------------- 椰子云API配置 ----------------------------------
# ==============================================================================

class YeyeYunConfig:
    """椰子云平台配置"""
    # 您的椰子云登录账号
    USERNAME = "zzzxxx"  
    # 您的椰子云登录密码
    PASSWORD = "xxxxx"
    # 您要注册的专属项目ID或专属对接码 (请在椰子云后台查找)
    PROJECT_ID = "866795" 
    # API域名 (主备选一个，如果主的不通，请换备用)
    API_BASE_URL = "http://api.sqhyw.net:90"
    # 卡类型现在于 core_logic.py 中动态配置，此行可删除或注释
    # CARD_TYPE = 'real'

# ==============================================================================
# ------------------------ 椰子云API客户端模块 -----------------------------
# ==============================================================================

class YeyeYunClient:
    """封装所有与椰子云API交互的逻辑"""
    def __init__(self, config: YeyeYunConfig):
        self.config = config
        self.base_url = config.API_BASE_URL
        self.token = None
        print("椰子云客户端已初始化。")

    def login(self):
        """登录并获取token"""
        print("正在登录椰子云平台...")
        url = f"{self.base_url}/api/logins"
        params = {
            "username": self.config.USERNAME,
            "password": self.config.PASSWORD
        }
        try:
            response = requests.get(url, params=params, timeout=30)  # 增加超时时间
            response.raise_for_status()
            data = response.json()
            
            # 根据返回示例，token直接在顶层
            if "token" in data and data["token"]:
                self.token = data["token"]
                money = "未知"
                # 安全地提取余额信息
                if "data" in data and isinstance(data["data"], list) and len(data["data"]) > 0:
                    money = data["data"][0].get("money", "未知")
                print(f"✅ 登录成功！账户余额: {money}")
                return True
            else:
                # 增加详细日志
                error_msg = data.get('message', '未知错误')
                print(f"❌ 登录失败: {error_msg}")
                print(f"   完整服务器响应: {json.dumps(data, ensure_ascii=False)}")
                return False
        except Exception as e:
            print(f"❌ 登录时发生网络错误: {e}")
            return False

    def get_exclusive_projects(self):
        """获取用户已对接的所有专属项目"""
        if not self.token:
            print("错误：未登录，无法获取专属项目列表。")
            return None

        print("\n正在获取您的专属项目列表...")
        url = f"{self.base_url}/api/get_join"
        params = {"token": self.token}
        try:
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            if data.get("message") == "ok" and "data" in data and data["data"]:
                projects = data["data"]
                print(f"✅ 成功获取到 {len(projects)} 个专属项目。")
                return projects
            elif data.get("message") == "ok":
                print("⚠️ 您尚未对接任何专属项目。")
                return []
            else:
                print(f"❌ 获取专属项目失败: {data.get('message', '未知错误')}")
                return None
        except Exception as e:
            print(f"❌ 获取专属项目时发生网络错误: {e}")
            return None

    def get_phone_number(self):
        """
        获取一个手机号码
        返回: (bool, result) -> (是否成功, (手机号, 归属地) 或 错误信息)
        """
        if not self.token:
            return False, "错误：未登录，请先调用login()"

        print(f"正在为项目ID「{self.config.PROJECT_ID}」获取手机号码...")
        url = f"{self.base_url}/api/get_mobile"
        params = {
            "token": self.token,
            "project_id": self.config.PROJECT_ID,
        }
        
        # 检查 CARD_TYPE 是否已在 config 中定义
        if hasattr(self.config, 'CARD_TYPE'):
            card_type = self.config.CARD_TYPE

            # 使用官方文档中正确的参数值和名称
            type_map = {0: "全部", 1: "实卡", 2: "虚卡"}
            api_operator_map = {1: 4, 2: 5} # 映射我们的选择到API的参数值 (1->4, 2->5)

            card_type_msg = f"【{type_map.get(card_type, '未知')}】"

            # 仅当选择的不是 "全部" (0) 时，才添加 'operator' 参数
            if card_type in api_operator_map:
                params['operator'] = api_operator_map[card_type]

            print(f"...已指定获取{card_type_msg}号码。")

        try:
            response = requests.get(url, params=params, timeout=30)  # 增加超时时间
            response.raise_for_status()
            data = response.json()

            # 关键：检查速率限制
            remaining = int(data.get("1分钟内剩余取卡数", 999))
            if remaining < 10:
                error_msg = f"警告：剩余取卡数过低 ({remaining})，为避免封号，暂停获取。"
                print(f"❌ {error_msg}")
                return False, error_msg
            
            if data.get("message") == "ok" and "mobile" in data:
                phone_number = data["mobile"]
                # 尝试从返回数据中提取归属地信息，如果不存在则为"未知归属地"
                location_data = data.get("data", [{}])
                location = location_data[0].get("isp", "未知归属地") if location_data else "未知归属地"
                print(f"✅ 成功获取手机号: {phone_number} ({location})")
                return True, (phone_number, location)
            else:
                error_msg = data.get('message', '返回信息错误')
                print(f"❌ 获取手机号失败: {error_msg}")
                # 新增：打印完整的服务器响应以供调试
                print(f"   完整服务器响应: {json.dumps(data, ensure_ascii=False)}")
                return False, error_msg
        except Exception as e:
            error_msg = f"获取手机号时发生网络错误: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg

    def get_sms_code(self, phone_number, max_wait_sec=90, poll_interval_sec=5):
        """循环查询短信验证码"""
        if not self.token:
            print("错误：未登录")
            return None

        print(f"⏳ 开始等待手机号 {phone_number} 的短信，最长等待 {max_wait_sec} 秒...")
        start_time = time.time()
        url = f"{self.base_url}/api/get_message"
        params = {
            "token": self.token,
            "project_id": self.config.PROJECT_ID,
            "phone_num": phone_number,
        }

        while time.time() - start_time < max_wait_sec:
            try:
                response = requests.get(url, params=params, timeout=15)  # 增加超时时间
                data = response.json()
                if data.get("message") == "ok" and "code" in data:
                    sms_code = data["code"]
                    full_sms = data.get("data", [{}])[0].get("modle", "")
                    print(f"\n✅ 成功获取到验证码: {sms_code}")
                    print(f"   完整短信内容: {full_sms}")
                    return sms_code
                else:
                    # 短信还没到，继续等待
                    print(".", end="", flush=True)
                    time.sleep(poll_interval_sec)
            except Exception as e:
                print(f"\n查询短信时出错: {e}")
                time.sleep(poll_interval_sec)

        print("\n❌ 获取验证码超时。")
        return None
        
    def release_phone_number(self, phone_number):
        """释放一个或所有手机号"""
        if not self.token:
            return
            
        print(f"正在释放手机号: {phone_number}...")
        url = f"{self.base_url}/api/free_mobile"
        params = {"token": self.token, "phone_num": phone_number}
        try:
            response = requests.get(url, params=params, timeout=15)  # 增加超时时间
            if response.json().get("message") == "ok":
                print(f"✅ 号码 {phone_number} 已成功释放。")
        except Exception as e:
            print(f"释放号码时出错: {e}")

    def add_to_blacklist(self, phone_number):
        """拉黑一个手机号"""
        if not self.token:
            print("错误：未登录，无法拉黑号码。")
            return

        print(f"正在将手机号 {phone_number} 加入平台黑名单...")
        url = f"{self.base_url}/api/add_blacklist"
        params = {
            "token": self.token,
            "project_id": self.config.PROJECT_ID,
            "phone_num": phone_number,
        }
        try:
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()
            if data.get("message") == "ok":
                print(f"✅ 号码 {phone_number} 已成功拉黑。")
            else:
                print(f"❌ 拉黑号码 {phone_number} 失败: {data.get('message', '未知错误')}")
        except Exception as e:
            print(f"❌ 拉黑号码时发生网络错误: {e}")
