#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整自动注册脚本 - 基于uni-app框架
支持自动填写表单、验证码识别、短信验证
"""

import time
import random
import json
import cv2
import numpy as np
from PIL import Image
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
import ddddocr
import io
import base64

class UniAppAutoRegister:
    def __init__(self, url="https://ds-web1.yrpdz.com/pages/login/login?code=21511001"):
        self.url = url
        self.driver = None
        self.wait = None
        self.ocr = ddddocr.DdddOcr()
        
        print("🚀 UniApp自动注册系统初始化完成")

    def setup_driver(self):
        """设置浏览器驱动"""
        print("🔧 设置浏览器驱动...")
        
        chrome_options = Options()
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 移动端模拟
        mobile_emulation = {
            "deviceMetrics": {"width": 375, "height": 812, "pixelRatio": 3.0},
            "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
        }
        chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        self.wait = WebDriverWait(self.driver, 15)
        print("✅ 浏览器驱动设置完成")

    def generate_person_info(self):
        """生成个人信息"""
        print("👤 生成个人信息...")
        
        # 姓氏和名字
        surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林']
        given_names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明']
        
        name = random.choice(surnames) + random.choice(given_names)
        
        # 手机号
        prefixes = ['138', '139', '137', '136', '135', '134', '159', '158', '150', '151']
        phone = random.choice(prefixes) + ''.join([str(random.randint(0, 9)) for _ in range(8)])
        
        # 密码
        password = self.generate_password()
        
        # 身份证号
        id_card = self.generate_id_card()
        
        # 邀请码（随机生成）
        invite_code = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        
        person_info = {
            'name': name,
            'phone': phone,
            'password': password,
            'id_card': id_card,
            'invite_code': invite_code
        }
        
        print(f"姓名: {name}")
        print(f"手机号: {phone}")
        print(f"密码: {password}")
        print(f"身份证: {id_card}")
        print(f"邀请码: {invite_code}")
        
        return person_info

    def generate_password(self):
        """生成随机密码"""
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        password = ''.join(random.choice(chars) for _ in range(8))
        # 确保包含大小写字母和数字
        password = 'A' + 'a' + '1' + password[3:]
        return password

    def generate_id_card(self):
        """生成身份证号"""
        area_codes = ['110101', '310101', '440101', '440301', '320101', '330101']
        area_code = random.choice(area_codes)
        
        year = random.randint(1970, 2005)
        month = random.randint(1, 12)
        day = random.randint(1, 28)
        birth_date = f"{year}{month:02d}{day:02d}"
        
        sequence = f"{random.randint(0, 999):03d}"
        first_17 = area_code + birth_date + sequence
        
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_val = sum(int(first_17[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_val % 11]
        
        return first_17 + check_code

    def open_page(self):
        """打开注册页面"""
        print("🌐 打开注册页面...")
        self.driver.get(self.url)
        time.sleep(3)
        print("✅ 页面加载完成")

    def find_input_by_placeholder(self, placeholder_text):
        """根据placeholder查找输入框"""
        try:
            # 尝试多种选择器
            selectors = [
                f"input[placeholder*='{placeholder_text}']",
                f".uni-input-input",
                f"input.uni-input-input"
            ]
            
            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    # 检查父元素的placeholder
                    parent = element.find_element(By.XPATH, "..")
                    placeholder_elements = parent.find_elements(By.CSS_SELECTOR, ".uni-input-placeholder")
                    for placeholder_elem in placeholder_elements:
                        if placeholder_text in placeholder_elem.text:
                            return element
            
            return None
        except Exception as e:
            print(f"❌ 查找输入框失败: {e}")
            return None

    def input_text_slowly(self, element, text, description=""):
        """慢速输入文本"""
        try:
            # 点击元素激活
            self.driver.execute_script("arguments[0].click();", element)
            time.sleep(0.5)
            
            # 清空现有内容
            element.clear()
            time.sleep(0.3)
            
            # 逐字符输入
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.1, 0.3))
            
            print(f"✅ 输入{description}: {text}")
            return True
        except Exception as e:
            print(f"❌ 输入{description}失败: {e}")
            return False

    def capture_captcha(self):
        """截取验证码图片"""
        try:
            print("📸 查找验证码图片...")

            # 查找验证码图片元素 - 根据您提供的HTML结构
            captcha_selectors = [
                "uni-image[style*='width: 90px'] img",  # 根据您的HTML结构
                "uni-image[style*='width: 90px']",
                ".captcha-img",
                "img[src*='captcha']",
                "uni-image img"
            ]

            captcha_element = None
            for selector in captcha_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        captcha_element = elements[0]
                        print(f"✅ 找到验证码元素: {selector}")
                        break
                except:
                    continue

            if not captcha_element:
                print("❌ 未找到验证码图片，尝试手动输入")
                manual_captcha = input("请手动输入图片验证码: ")
                return manual_captcha if manual_captcha else None

            # 尝试获取图片src属性
            try:
                img_src = captcha_element.get_attribute('src')
                if img_src and img_src.startswith('data:image'):
                    # 处理base64图片
                    print("🔍 处理base64验证码图片...")
                    base64_data = img_src.split(',')[1]
                    image_data = base64.b64decode(base64_data)

                    # 保存调试图片
                    with open('captcha_debug.png', 'wb') as f:
                        f.write(image_data)

                    # OCR识别
                    captcha_text = self.ocr.classification(image_data)
                    print(f"🔍 验证码识别结果: {captcha_text}")
                    return captcha_text
            except Exception as e:
                print(f"⚠️ base64处理失败: {e}")

            # 如果base64方式失败，尝试截图方式
            try:
                print("🔍 尝试截图方式识别验证码...")

                # 滚动到验证码元素位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", captcha_element)
                time.sleep(1)

                # 获取图片位置和大小
                location = captcha_element.location
                size = captcha_element.size

                print(f"🔍 验证码位置: x={location['x']}, y={location['y']}")
                print(f"🔍 验证码大小: width={size['width']}, height={size['height']}")

                # 截取整个页面
                screenshot = self.driver.get_screenshot_as_png()
                image = Image.open(io.BytesIO(screenshot))

                # 计算裁剪区域（添加边距确保完整截取）
                margin = 2
                left = max(0, int(location['x']) - margin)
                top = max(0, int(location['y']) - margin)
                right = min(image.width, int(location['x'] + size['width']) + margin)
                bottom = min(image.height, int(location['y'] + size['height']) + margin)

                print(f"🔍 裁剪区域: left={left}, top={top}, right={right}, bottom={bottom}")

                # 裁剪验证码区域
                captcha_image = image.crop((left, top, right, bottom))
                captcha_image.save('captcha_debug.png')
                print("💾 验证码图片已保存为 captcha_debug.png")

                # 图像预处理提高识别率
                captcha_image = self.preprocess_captcha_image(captcha_image)

                # 转换为字节数据供OCR使用
                img_byte_arr = io.BytesIO()
                captcha_image.save(img_byte_arr, format='PNG')
                img_byte_data = img_byte_arr.getvalue()

                # OCR识别
                captcha_text = self.ocr.classification(img_byte_data)

                # 清理识别结果
                captcha_text = ''.join(filter(str.isalnum, captcha_text))
                print(f"🔍 验证码识别结果: '{captcha_text}'")

                # 如果识别结果长度不合理，提示确认
                if not captcha_text or len(captcha_text) < 3:
                    print("⚠️ 自动识别结果可能不准确，请查看 captcha_debug.png")
                    manual_input = input("请手动输入验证码（直接回车使用自动识别结果）: ").strip()
                    if manual_input:
                        captcha_text = manual_input

                return captcha_text

            except Exception as e:
                print(f"⚠️ 截图方式失败: {e}")

            # 如果自动识别都失败，提示手动输入
            print("🔍 自动识别失败，请查看 captcha_debug.png 并手动输入")
            manual_captcha = input("请手动输入图片验证码: ")
            return manual_captcha if manual_captcha else None

        except Exception as e:
            print(f"❌ 验证码处理失败: {e}")
            print("🔍 请手动输入验证码")
            manual_captcha = input("请手动输入图片验证码: ")
            return manual_captcha if manual_captcha else None

    def preprocess_captcha_image(self, image):
        """验证码图像预处理，提高识别率"""
        try:
            # 转换为numpy数组
            img_array = np.array(image)

            # 如果是彩色图像，转换为灰度图
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # 二值化处理
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # 去噪处理
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # 转换回PIL图像
            processed_image = Image.fromarray(cleaned)

            # 保存预处理后的图片用于调试
            processed_image.save('captcha_processed.png')
            print("💾 预处理后的验证码图片已保存为 captcha_processed.png")

            return processed_image

        except Exception as e:
            print(f"⚠️ 图像预处理失败: {e}")
            return image  # 返回原图

    def click_button_by_text(self, button_text):
        """根据文本点击按钮"""
        try:
            # 尝试多种选择器
            selectors = [
                f"uni-view:contains('{button_text}')",
                f".loginBtn",
                f"button:contains('{button_text}')",
                f"[class*='btn']:contains('{button_text}')"
            ]
            
            for selector in selectors:
                try:
                    if ":contains" in selector:
                        # 使用XPath查找包含文本的元素
                        xpath = f"//*[contains(text(), '{button_text}')]"
                        element = self.driver.find_element(By.XPATH, xpath)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    self.driver.execute_script("arguments[0].click();", element)
                    print(f"✅ 点击{button_text}按钮成功")
                    return True
                except:
                    continue
            
            print(f"❌ 未找到{button_text}按钮")
            return False
            
        except Exception as e:
            print(f"❌ 点击{button_text}按钮失败: {e}")
            return False

    def auto_register(self):
        """自动注册流程"""
        print("🎯 开始自动注册流程...")
        
        try:
            # 1. 设置浏览器
            self.setup_driver()
            
            # 2. 生成个人信息
            person_info = self.generate_person_info()
            
            # 3. 打开页面
            self.open_page()
            
            # 等待页面加载
            time.sleep(3)
            
            # 4. 填写手机号
            print("📱 填写手机号...")
            phone_input = self.find_input_by_placeholder("请输入手机号")
            if phone_input:
                self.input_text_slowly(phone_input, person_info['phone'], "手机号")
            else:
                print("❌ 未找到手机号输入框")
                return False
            
            # 5. 填写密码
            print("🔐 填写密码...")
            password_input = self.find_input_by_placeholder("请输入密码")
            if password_input:
                self.input_text_slowly(password_input, person_info['password'], "密码")
            else:
                print("❌ 未找到密码输入框")
                return False
            
            # 6. 填写确认密码
            print("🔐 填写确认密码...")
            confirm_password_input = self.find_input_by_placeholder("请再次确认密码")
            if confirm_password_input:
                self.input_text_slowly(confirm_password_input, person_info['password'], "确认密码")
            else:
                print("❌ 未找到确认密码输入框")
                return False
            
            # 7. 处理图片验证码
            print("🔍 处理图片验证码...")
            captcha_code = self.capture_captcha()
            if captcha_code:
                captcha_input = self.find_input_by_placeholder("请输入验证码")
                if captcha_input:
                    self.input_text_slowly(captcha_input, captcha_code, "图片验证码")
                else:
                    print("❌ 未找到验证码输入框")
            
            # 8. 填写邀请码（如果需要）
            print("🎫 填写邀请码...")
            invite_input = self.find_input_by_placeholder("邀请码")
            if invite_input:
                self.input_text_slowly(invite_input, person_info['invite_code'], "邀请码")
            
            # 9. 点击注册按钮
            print("📝 提交注册...")
            if self.click_button_by_text("注册"):
                time.sleep(3)
                
                # 检查是否需要短信验证码
                if "验证码" in self.driver.page_source:
                    print("📱 需要短信验证码，请手动输入...")
                    sms_code = input("请输入收到的短信验证码: ")
                    
                    # 查找短信验证码输入框
                    sms_input = self.driver.find_elements(By.CSS_SELECTOR, "input[type='number']")
                    if sms_input:
                        self.input_text_slowly(sms_input[-1], sms_code, "短信验证码")
                        
                        # 再次点击注册
                        self.click_button_by_text("注册")
                        time.sleep(3)
                
                # 检查注册结果 - 更准确的成功检测
                page_source = self.driver.page_source
                success_indicators = [
                    "成功",
                    "注册成功",
                    "实名认证",  # 出现实名认证弹窗说明注册成功
                    "立即认证",  # 立即认证按钮出现说明注册成功
                    "DeepSeek卡余额",  # 奖励信息出现说明注册成功
                    "原始股",
                    "领取超多福利"
                ]

                registration_success = False
                for indicator in success_indicators:
                    if indicator in page_source:
                        print(f"🎉 注册成功！(检测到: {indicator})")
                        registration_success = True
                        break

                if registration_success:
                    self.save_account_info(person_info)

                    # 检查是否出现实名认证弹窗
                    if "立即认证" in page_source or "实名认证" in page_source:
                        print("\n🆔 检测到实名认证弹窗，自动开始实名认证...")
                        print("🔄 开始实名认证流程...")
                        if self.auto_real_name_auth(person_info):
                            print("🎉 实名认证成功！")
                        else:
                            print("❌ 实名认证失败")

                    return True
                else:
                    print("❌ 注册状态未确定，但继续尝试实名认证...")
                    print("💡 如果看到实名认证弹窗，说明注册已成功")

                    # 尝试查找实名认证相关元素
                    try:
                        auth_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '立即认证')]")
                        if auth_elements:
                            print("🎉 发现实名认证按钮，注册应该成功了！")
                            self.save_account_info(person_info)

                            print("🔄 自动开始实名认证流程...")
                            if self.auto_real_name_auth(person_info):
                                print("🎉 实名认证成功！")
                            else:
                                print("❌ 实名认证失败")
                            return True
                    except:
                        pass

                    print("⚠️ 无法确定注册状态，请手动检查")
                    return False
            else:
                print("❌ 无法点击注册按钮")
                return False
                
        except Exception as e:
            print(f"❌ 自动注册过程中出错: {e}")
            return False
        
        finally:
            # 保持浏览器打开以便查看结果
            input("按回车键关闭浏览器...")
            if self.driver:
                self.driver.quit()

    def auto_real_name_auth(self, person_info):
        """自动实名认证"""
        try:
            print("🆔 开始自动实名认证...")

            # 等待页面稳定
            time.sleep(2)

            # 根据您提供的HTML结构和截图，查找"立即认证"按钮
            auth_selectors = [
                ".noticIden-btn",  # 主要选择器
                "uni-view.noticIden-btn",  # 完整选择器
                "//*[contains(text(), '立即认证')]",  # XPath方式
                "//*[contains(text(), '立即认证') and contains(@style, 'background')]",  # 蓝色按钮
                "//div[contains(text(), '立即认证')]",
                "//button[contains(text(), '立即认证')]",
                "//*[contains(text(), '实名认证')]",
                "//*[contains(text(), '认证')]"
            ]

            auth_element = None
            for selector in auth_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS选择器
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        auth_element = elements[0]
                        print(f"✅ 找到实名认证按钮: {selector}")
                        break
                except Exception as e:
                    print(f"⚠️ 选择器 {selector} 失败: {e}")
                    continue

            if not auth_element:
                print("❌ 未找到'立即认证'按钮")
                print("💡 请手动点击实名认证按钮")
                input("点击实名认证按钮后，按回车继续...")
            else:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", auth_element)
                time.sleep(1)

                # 点击实名认证按钮
                self.driver.execute_script("arguments[0].click();", auth_element)
                print("✅ 成功点击'立即认证'按钮")
                time.sleep(3)

            # 填写实名信息
            return self.fill_real_name_form(person_info)

        except Exception as e:
            print(f"❌ 实名认证过程失败: {e}")
            return False

    def fill_real_name_form(self, person_info):
        """填写实名认证表单"""
        try:
            print("📝 填写实名认证信息...")

            # 等待页面加载
            time.sleep(2)

            # 1. 填写姓名 - 根据HTML结构查找
            print("👤 填写姓名...")
            name_selectors = [
                "input[placeholder*='请输入姓名']",
                ".u-input__input[maxlength='20']",
                "//div[contains(text(), '姓名')]/../..//input",
                ".u-form-item .u-input__input"
            ]

            name_filled = False
            for selector in name_selectors:
                try:
                    if selector.startswith("//"):
                        name_inputs = self.driver.find_elements(By.XPATH, selector)
                    else:
                        name_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if name_inputs:
                        name_input = name_inputs[0]
                        if self.input_text_slowly(name_input, person_info['name'], "真实姓名"):
                            name_filled = True
                            break
                except Exception as e:
                    print(f"⚠️ 姓名选择器 {selector} 失败: {e}")
                    continue

            if not name_filled:
                print("⚠️ 自动填写姓名失败，请手动输入")
                input(f"请手动输入姓名: {person_info['name']}，完成后按回车继续...")

            # 2. 填写证件类型 - 默认为"身份证"
            print("🆔 填写证件类型...")
            cert_type_selectors = [
                "input[placeholder*='请输入证件类型']",
                "//div[contains(text(), '证件类型')]/../..//input"
            ]

            cert_type_filled = False
            for selector in cert_type_selectors:
                try:
                    if selector.startswith("//"):
                        cert_inputs = self.driver.find_elements(By.XPATH, selector)
                    else:
                        cert_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if cert_inputs:
                        cert_input = cert_inputs[0]
                        if self.input_text_slowly(cert_input, "身份证", "证件类型"):
                            cert_type_filled = True
                            break
                except Exception as e:
                    print(f"⚠️ 证件类型选择器 {selector} 失败: {e}")
                    continue

            if not cert_type_filled:
                print("⚠️ 自动填写证件类型失败，请手动输入")
                input("请手动输入证件类型: 身份证，完成后按回车继续...")

            # 3. 填写证件号码（身份证号）
            print("🔢 填写证件号码...")
            id_selectors = [
                "input[placeholder*='请输入证件号码']",
                ".u-input__input[maxlength='18']",
                "//div[contains(text(), '证件号码')]/../..//input"
            ]

            id_filled = False
            for selector in id_selectors:
                try:
                    if selector.startswith("//"):
                        id_inputs = self.driver.find_elements(By.XPATH, selector)
                    else:
                        id_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if id_inputs:
                        id_input = id_inputs[0]
                        if self.input_text_slowly(id_input, person_info['id_card'], "身份证号"):
                            id_filled = True
                            break
                except Exception as e:
                    print(f"⚠️ 身份证选择器 {selector} 失败: {e}")
                    continue

            if not id_filled:
                print("⚠️ 自动填写身份证号失败，请手动输入")
                input(f"请手动输入身份证号: {person_info['id_card']}，完成后按回车继续...")

            # 4. 点击"立即实名"按钮
            print("✅ 点击立即实名按钮...")
            submit_selectors = [
                "uni-button:contains('立即实名')",
                ".u-btn:contains('立即实名')",
                "//uni-button[contains(text(), '立即实名')]",
                "//*[contains(text(), '立即实名')]"
            ]

            submit_clicked = False
            for selector in submit_selectors:
                try:
                    if selector.startswith("//") or selector.startswith("//*"):
                        submit_buttons = self.driver.find_elements(By.XPATH, selector)
                    elif ":contains" in selector:
                        # 转换为XPath
                        text = selector.split(":contains('")[1].split("')")[0]
                        xpath = f"//*[contains(text(), '{text}')]"
                        submit_buttons = self.driver.find_elements(By.XPATH, xpath)
                    else:
                        submit_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if submit_buttons:
                        submit_button = submit_buttons[0]
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                        time.sleep(1)
                        # 点击按钮
                        self.driver.execute_script("arguments[0].click();", submit_button)
                        print("✅ 成功点击'立即实名'按钮")
                        submit_clicked = True
                        time.sleep(3)
                        break
                except Exception as e:
                    print(f"⚠️ 提交按钮选择器 {selector} 失败: {e}")
                    continue

            if not submit_clicked:
                print("⚠️ 自动点击提交按钮失败，请手动点击")
                input("请手动点击'立即实名'按钮，完成后按回车继续...")

            # 5. 检查认证结果
            time.sleep(3)
            success_keywords = ["成功", "通过", "完成", "已认证", "认证成功"]
            page_source = self.driver.page_source

            for keyword in success_keywords:
                if keyword in page_source:
                    print("🎉 实名认证提交成功！")
                    # 更新账号信息
                    person_info['real_name_status'] = '已认证'
                    person_info['auth_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
                    return True

            print("✅ 实名认证信息已提交，请等待审核")
            person_info['real_name_status'] = '审核中'
            person_info['auth_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            return True

        except Exception as e:
            print(f"❌ 填写实名认证表单失败: {e}")
            return False

    def save_account_info(self, person_info):
        """保存账号信息"""
        try:
            try:
                with open('registered_accounts.json', 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
            except:
                accounts = []

            person_info['register_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            accounts.append(person_info)

            with open('registered_accounts.json', 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)

            print("💾 账号信息已保存到 registered_accounts.json")

        except Exception as e:
            print(f"❌ 保存账号信息失败: {e}")

    def batch_register(self, count=1):
        """批量注册"""
        print(f"🚀 开始批量注册 {count} 个账号...")
        
        success_count = 0
        for i in range(count):
            print(f"\n🔄 注册第 {i+1} 个账号...")
            
            if self.auto_register():
                success_count += 1
                print(f"✅ 第 {i+1} 个账号注册成功")
            else:
                print(f"❌ 第 {i+1} 个账号注册失败")
            
            if i < count - 1:
                print("⏳ 等待 30 秒后继续...")
                time.sleep(30)
        
        print(f"\n📊 批量注册完成: 成功 {success_count}/{count}")

if __name__ == "__main__":
    print("🎯 UniApp自动注册系统启动")
    print("=" * 50)
    
    # 可以修改URL
    url = input("请输入注册页面URL (直接回车使用默认): ").strip()
    if not url:
        url = "https://ds-web1.yrpdz.com/pages/login/login?code=21511001"
    
    auto_register = UniAppAutoRegister(url)
    
    while True:
        print("\n🎮 请选择操作:")
        print("1. 单个账号注册")
        print("2. 批量注册")
        print("3. 查看已注册账号")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            auto_register.auto_register()
        elif choice == '2':
            count = int(input("请输入注册数量: "))
            auto_register.batch_register(count)
        elif choice == '3':
            try:
                with open('registered_accounts.json', 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
                print(f"\n📋 已注册账号 ({len(accounts)} 个):")
                for i, account in enumerate(accounts, 1):
                    print(f"{i}. 手机号: {account['phone']}, 密码: {account['password']}, 时间: {account.get('register_time', '未知')}")
            except:
                print("📋 暂无已注册账号")
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
