#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# "鲲鹏通讯" - 批量注册和加群工具 (整理版)
# 新服务器: http://**************:8095
# 支持真实身份证数据和交互式团队前缀输入

import requests
import json
import time
import random
import hashlib
import hmac
import base64
import uuid
import os

try:
    from Cryptodome.Cipher import AES
    from Cryptodome.Util.Padding import pad, unpad
except ImportError:
    try:
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import pad, unpad
    except ImportError:
        print("❌ 缺少加密库，请安装: pip install pycryptodome")
        exit(1)

# --- 1. 全局配置 ---
BASE_URL = "http://**************:8095"  # 注册服务器地址
JOIN_GROUP_BASE_URL = "http://ggregesd.kes337f.shop"  # 加群服务器地址
API_KEY_STRING = "kunpeng_tongxin_2024"
API_KEY_STRING_OLD = "212i919292901"  # 加群时使用的API密钥
OUTPUT_FILE = "registered_accounts.txt"

# 旧版本成功的加密配置
GLOBAL_AES_KEY = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
GLOBAL_AES_IV = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")
API_KEY_STRING_OLD = "212i919292901"

# 真实身份证数据库 (从40.txt文件获取)
def load_id_data_from_file():
    """从40.txt文件加载身份证数据"""
    id_data_file = r"C:\Users\<USER>\Desktop\40.txt"
    id_data = []
    try:
        with open(id_data_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if '----' in line:
                    parts = line.split('----')
                    if len(parts) == 2:
                        name, id_card = parts[0].strip(), parts[1].strip()
                        if name and id_card and len(id_card) == 18:
                            id_data.append({"name": name, "id_card": id_card})
        print(f"✅ 成功加载 {len(id_data)} 条身份证数据")
        return id_data
    except Exception as e:
        print(f"⚠️ 无法加载身份证数据文件: {e}")
        # 备用数据
        return [
            {"name": "张军", "id_card": "622201198403260618"},
            {"name": "袁莎", "id_card": "510411198111248429"},
            {"name": "郑杰", "id_card": "331081198407063719"},
        ]

# 全局身份证数据
REAL_ID_DATA = load_id_data_from_file()
CURRENT_ID_INDEX = 0  # 当前使用的身份证索引

# --- 设备指纹生成函数 ---
def generate_password():
    """生成8-10位小写英文和数字混合密码，确保至少包含一个字母和一个数字"""
    password_length = random.randint(8, 10)
    letters = 'abcdefghijklmnopqrstuvwxyz'
    digits = '0123456789'

    # 确保至少有一个字母和一个数字
    password_chars = [random.choice(letters), random.choice(digits)]

    # 填充剩余位数
    all_chars = letters + digits
    for _ in range(password_length - 2):
        password_chars.append(random.choice(all_chars))

    # 随机打乱顺序
    random.shuffle(password_chars)
    return ''.join(password_chars)

def generate_device_fingerprint():
    """生成动态设备指纹"""

    # Android设备型号列表
    device_models = [
        "SM-G973F", "SM-G975F", "SM-G988B", "SM-N975F", "SM-N986B",
        "Pixel 4", "Pixel 4 XL", "Pixel 5", "Pixel 6", "Pixel 6 Pro",
        "Mi 9", "Mi 10", "Mi 11", "Redmi Note 8", "Redmi Note 9",
        "HUAWEI P30", "HUAWEI P40", "HUAWEI Mate 30", "HUAWEI Nova 7",
        "OPPO Find X2", "OPPO Reno4", "vivo X50", "vivo X60",
        "OnePlus 7T", "OnePlus 8", "OnePlus 9", "realme X2",
        "22041216UC", "M2012K11AC", "M2102J2SC", "2201116SG"
    ]

    # Android版本列表
    android_versions = [
        "10", "11", "12", "13", "14"
    ]

    # Build版本列表
    build_versions = [
        "UP1A.231005.007", "MRA58K", "QP1A.190711.020",
        "RP1A.200720.011", "SP2A.220405.004", "TQ3A.230805.001",
        "UQ1A.240205.004", "AP1A.240405.002"
    ]

    # 随机选择设备信息
    device_model = random.choice(device_models)
    android_version = random.choice(android_versions)
    build_version = random.choice(build_versions)

    # 生成设备序列号 (12位数字)
    device_serial = ''.join([str(random.randint(0, 9)) for _ in range(12)])

    # 生成设备ID (基于UUID)
    device_id = str(uuid.uuid4()).replace('-', '')[:16]

    return {
        'model': device_model,
        'android_version': android_version,
        'build_version': build_version,
        'serial': device_serial,
        'device_id': device_id,
        'user_agent': f"chat_im/2.1.8 (Linux; U; Android {android_version}; {device_model} Build/{build_version})"
    }

def load_existing_nicknames(account_file="zhanghao.txt"):
    """加载已存在的昵称列表"""
    existing_nicknames = set()
    if os.path.exists(account_file):
        try:
            with open(account_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and ':' in line:
                        parts = line.split(':', 2)
                        if len(parts) >= 3:
                            try:
                                json_data = json.loads(parts[2])
                                nickname = json_data.get('nickname')
                                if nickname:
                                    existing_nicknames.add(nickname)
                            except:
                                continue
        except Exception as e:
            print(f"⚠️ 读取账号文件失败: {e}")

    print(f"📋 已加载 {len(existing_nicknames)} 个已存在的昵称")
    return existing_nicknames

def check_nickname_duplicate(nickname, existing_nicknames):
    """检查昵称是否重复"""
    return nickname in existing_nicknames

def get_next_id_info():
    """获取下一个身份证信息"""
    global CURRENT_ID_INDEX
    if CURRENT_ID_INDEX >= len(REAL_ID_DATA):
        CURRENT_ID_INDEX = 0  # 循环使用

    id_info = REAL_ID_DATA[CURRENT_ID_INDEX]
    CURRENT_ID_INDEX += 1
    return id_info["name"], id_info["id_card"]

# --- 2. 加密工具函数 ---
def generate_mac_signature(key: bytes, content: str) -> str:
    """生成HmacMD5签名"""
    return hmac.new(key, content.encode('utf-8'), hashlib.md5).hexdigest()

def encrypt_request_data(payload: dict) -> str:
    """加密请求数据 (使用成功的格式)"""
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    json_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
    encrypted = cipher.encrypt(pad(json_str.encode('utf-8'), AES.block_size))
    return base64.b64encode(encrypted).decode('utf-8')

def join_param_values(params: dict) -> str:
    """连接参数值 (按字母顺序)"""
    sorted_keys = sorted(params.keys())
    return "".join([str(params[key]) for key in sorted_keys])

def generate_mac_signature_old(key: bytes, content: str) -> str:
    """生成MAC签名"""
    return base64.b64encode(hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()).decode('utf-8')

def encrypt_login_password_old(password: str) -> str:
    """旧版本的密码加密方式"""
    md5_hash = hashlib.md5(password.encode('utf-8')).digest()
    cipher = AES.new(md5_hash, AES.MODE_CBC, GLOBAL_AES_IV)
    return hashlib.md5(cipher.encrypt(pad(md5_hash, AES.block_size))).hexdigest()

def decrypt_response_data(encrypted_data_b64: str) -> dict:
    """解密响应数据"""
    try:
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted_data_b64)

        # AES解密
        cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
        decrypted_bytes = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
        decrypted_text = decrypted_bytes.decode('utf-8')

        # 解析JSON
        decrypted_json = json.loads(decrypted_text)
        return decrypted_json

    except Exception as e:
        print(f"❌ 解密响应数据失败: {e}")
        return None

# --- 3. 注册功能 ---
def auto_register(invite_code: str, team_prefix: str, fixed_password: str = None) -> bool:
    """自动注册一个账号 (使用成功的旧版本格式，支持身份证验证失败自动重试)

    Args:
        invite_code: 邀请码
        team_prefix: 团队前缀
        fixed_password: 固定密码，如果为None则使用随机密码
    """
    max_retries = 10  # 增加重试次数，因为要考虑昵称重复

    # 加载已存在的昵称
    existing_nicknames = load_existing_nicknames()

    for retry_count in range(max_retries):
        try:
            # 获取下一个身份证数据
            real_name, id_card = get_next_id_info()

            if retry_count > 0:
                print(f"🔄 第 {retry_count + 1} 次尝试，使用身份: {real_name}")
            else:
                print(f"👤 使用身份: {real_name}")

            # 生成手机号 (13开头的11位数字)
            phone = "13" + "".join([str(random.randint(0, 9)) for _ in range(9)])

            # 生成密码 (根据用户选择使用固定密码或随机密码)
            if fixed_password:
                password = fixed_password
                print(f"🔐 使用固定密码: {password}")
            else:
                password = generate_password()
                print(f"🔐 生成随机密码: {password}")

            # 生成昵称: 团队前缀–身份证姓名 (使用特殊破折号，与成功案例一致)
            nickname = f"{team_prefix}–{real_name}"

            # 检查昵称是否重复
            if check_nickname_duplicate(nickname, existing_nicknames):
                print(f"⚠️ 昵称重复: {nickname}")
                print(f"🔄 跳过此身份，尝试下一个...")
                continue  # 跳过这个身份，尝试下一个

            print(f"📱 手机号: {phone}")
            print(f"🏷️ 昵称: {nickname} ✅")
            print(f"🆔 身份证: {real_name} / {id_card}")
            print(f"🎫 邀请码: {invite_code}")

            # 生成动态设备指纹
            device_info = generate_device_fingerprint()
            print(f"📱 设备型号: {device_info['model']}")
            print(f"🤖 Android版本: {device_info['android_version']}")
            print(f"🔧 Build版本: {device_info['build_version']}")
            print(f"🆔 设备序列号: {device_info['serial']}")

            # 使用成功案例的参数格式
            area_code, sex, birthday = "92", "1", str(int(time.time()))
            device_model, os_version, serial = device_info['model'], device_info['android_version'], device_info['serial']
            language, salt = "zh", str(int(time.time() * 1000))

            # 加密密码
            encrypted_password = encrypt_login_password_old(password)

            # 构建参数 (与成功案例完全一致)
            params_for_mac = {
                "apiVersion": "76",
                "areaCode": area_code,
                "areaId": "0",
                "birthday": birthday,
                "cityId": "0",
                "countryId": "0",
                "idcard": id_card,
                "inviteCode": invite_code,
                "isSmsRegister": "0",
                "model": device_model,
                "name": real_name,
                "nickname": nickname,
                "osVersion": os_version,
                "password": encrypted_password,
                "provinceId": "0",
                "serial": serial,
                "sex": sex,
                "smsCode": "",
                "telephone": phone,
                "userType": "0",
                "xmppVersion": "1"
            }

            # 生成MAC签名
            joined_values = join_param_values(params_for_mac)
            mac_content_to_sign = API_KEY_STRING_OLD + joined_values + salt
            mac_signature = generate_mac_signature_old(GLOBAL_AES_KEY, mac_content_to_sign)

            # 添加MAC到参数
            payload = params_for_mac.copy()
            payload['mac'] = mac_signature

            # 加密请求数据
            encrypted_data = encrypt_request_data(payload)

            # 构建请求参数
            random_secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
            params = {
                "data": encrypted_data,
                "deviceId": device_info['device_id'],  # 使用动态生成的设备ID
                "language": language,
                "salt": salt,
                "secret": random_secret
            }

            # 请求头 (使用动态生成的User-Agent)
            headers = {
                "User-Agent": device_info['user_agent'],
                "Connection": "Keep-Alive"
            }

            # 调试：打印请求数据
            print(f"📋 请求参数: {json.dumps(params_for_mac, ensure_ascii=False, indent=2)}")

            print("🚀 发送注册请求...")

            # 发送注册请求 (使用GET方法，与成功案例一致)
            response = requests.get(
                f"{BASE_URL}/user/register/v1",
                params=params,
                headers=headers,
                timeout=15
            )

            if response.status_code == 200:
                result = response.json()
                print(f"📋 服务器响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get("resultCode") == 1:
                    print(f"\033[92m✅ 注册成功！\033[0m")

                    # 解密响应数据
                    response_data = result.get("data", {})
                    if "data" in response_data and isinstance(response_data["data"], str):
                        # 响应包含加密数据，需要解密
                        print("🔓 正在解密响应数据...")
                        decrypted_data = decrypt_response_data(response_data["data"])
                        if decrypted_data:
                            user_data = decrypted_data
                            print("✅ 响应数据解密成功")
                        else:
                            print("❌ 响应数据解密失败，使用原始数据")
                            user_data = response_data
                    else:
                        # 响应数据已经是明文
                        user_data = response_data

                    # 将设备信息添加到用户数据中
                    user_data['device_info'] = device_info

                    # 写入文件 - 使用与现有账号相同的格式：手机号:密码:完整JSON数据
                    with open("zhanghao.txt", "a", encoding="utf-8") as f:
                        f.write(f"{phone}:{password}:{json.dumps(user_data, separators=(',', ':'), ensure_ascii=False)}\n")

                    # 同时也保存到registered_accounts.txt作为备份
                    account_info = {
                        "phone": phone,
                        "password": password,
                        "nickname": nickname,
                        "real_name": real_name,
                        "id_card": id_card,
                        "device_info": device_info,
                        "user_data": user_data
                    }
                    with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                        f.write(f"{phone}:{password}:{json.dumps(account_info, ensure_ascii=False)}\n")

                    # 将新昵称添加到已存在列表中，避免同批次内重复
                    existing_nicknames.add(nickname)
                    print(f"✅ 昵称 {nickname} 已添加到重复检查列表")

                    return True
                else:
                    error_msg = result.get("resultMsg", "未知错误")
                    print(f"\033[91m❌ 注册失败: {error_msg}\033[0m")

                    # 检查是否是身份证验证失败
                    if "姓名与身份证号校验未通过" in error_msg or "身份证" in error_msg:
                        print(f"🔄 身份证验证失败，准备使用下一个身份重试...")
                        if retry_count < max_retries - 1:
                            time.sleep(1)  # 短暂等待
                            continue  # 继续下一次重试
                        else:
                            print(f"❌ 已达到最大重试次数 ({max_retries})，注册失败")
                            return False
                    else:
                        # 其他错误，不重试
                        print(f"📋 完整错误信息: {result}")
                        return False
            else:
                print(f"\033[91m❌ 请求失败: HTTP {response.status_code}\033[0m")
                print(f"📋 响应内容: {response.text}")
                return False

        except Exception as e:
            print(f"\033[91m❌ 发生错误: {e}\033[0m")
            if retry_count < max_retries - 1:
                print(f"🔄 出现异常，准备重试...")
                time.sleep(1)
                continue
            else:
                return False

    # 如果所有重试都失败了
    print(f"❌ 所有重试都失败了，注册失败")
    return False

# --- 4. 加群功能 ---
def generate_api_signature(sign_key_bytes: bytes, user_id: str, access_token: str, **kwargs) -> tuple:
    """为登录后的API请求生成正确的签名(secret)和salt"""
    salt = str(int(time.time() * 1000))
    # 签名原文: ApiKey + userId + access_token + [其他参数的值] + salt
    content_parts = [API_KEY_STRING_OLD, str(user_id), access_token]
    if kwargs:
        for key in sorted(kwargs.keys()):
            content_parts.append(str(kwargs[key]))
    content_parts.append(salt)
    content_to_sign = "".join(content_parts)
    secret = base64.b64encode(hmac.new(sign_key_bytes, content_to_sign.encode('utf-8'), hashlib.md5).digest()).decode('utf-8')
    return salt, secret

def auto_join_group(phone: str, password: str, account_data: dict, target_room_id: str):
    """自动加群功能 (使用成功的两步加群流程)"""
    try:
        print(f"📱 账号: {phone}")

        # 从账号数据中提取必要信息
        user_id = str(account_data.get("userId"))
        access_token = account_data.get("access_token")
        http_key_b64 = account_data.get("httpKey")  # Base64字符串

        if not all([user_id, access_token, http_key_b64]):
            print(f"❌ 账号 {phone} 数据不完整，跳过")
            return False

        # 解码httpKey为字节 (关键修复点)
        try:
            sign_key = base64.b64decode(http_key_b64)
        except Exception as e:
            print(f"❌ httpKey解码失败: {e}")
            return False

        # 获取设备信息 (优先使用保存的设备信息，否则生成新的)
        device_info = account_data.get('device_info')
        if not device_info:
            device_info = generate_device_fingerprint()
            print(f"⚠️ 账号无设备信息，生成新的设备指纹")

        print(f"📱 使用设备: {device_info['model']} (Android {device_info['android_version']})")

        session = requests.Session()
        headers = {
            "User-Agent": device_info['user_agent']
        }
        language = "zh"
        
        # --- 步骤 A: /room/get "敲门" ---
        print(f"🚪 正在敲门...")
        salt_get, secret_get = generate_api_signature(
            sign_key, user_id, access_token,
            roomId=target_room_id, language=language
        )
        params_get = {
            "roomId": target_room_id,
            "language": language,
            "access_token": access_token,
            "salt": salt_get,
            "secret": secret_get
        }

        response_get = session.get(
            f"{JOIN_GROUP_BASE_URL}/room/get",
            params=params_get,
            headers=headers,
            timeout=15
        )

        if response_get.status_code != 200:
            print(f"❌ 敲门请求失败: HTTP {response_get.status_code}")
            return False

        resp_get_json = response_get.json()

        if resp_get_json.get("resultCode") != 1:
            if resp_get_json.get("resultCode") == 1030102:
                print(f"⚠️ 账号 {phone} 令牌已失效")
            else:
                print(f"❌ 敲门失败: {resp_get_json.get('resultMsg', '未知错误')}")
            return False

        print("✅ 敲门成功")
        time.sleep(random.uniform(0.5, 1.2))

        # --- 步骤 B: /room/join 正式加群 ---
        print(f"🎯 正在发送正式加群请求...")
        op_type, join_type = "1", "2"

        params_for_join_sign = {
            'operationType': op_type,
            'type': join_type,
            'roomId': target_room_id,
            'language': language
        }
        salt_join, secret_join = generate_api_signature(
            sign_key, user_id, access_token, **params_for_join_sign
        )

        params_join = {
            "operationType": op_type,
            "type": join_type,
            "roomId": target_room_id,
            "language": language,
            "access_token": access_token,
            "salt": salt_join,
            "secret": secret_join
        }

        response = session.get(
            f"{JOIN_GROUP_BASE_URL}/room/join",
            params=params_join,
            headers=headers,
            timeout=15
        )

        if response.status_code == 200:
            result = response.json()

            if result.get("resultCode") == 1:
                print(f"\033[92m✅ 成功加入群聊\033[0m")
                return True
            else:
                error_msg = result.get("resultMsg", "未知错误")
                print(f"\033[91m❌ 加群失败: {error_msg}\033[0m")
                return False
        else:
            print(f"\033[91m❌ 加群失败: HTTP {response.status_code}\033[0m")
            return False
        
    except Exception as e:
        print(f"\033[91m❌ 加群失败: {e}\033[0m")

# --- 5. 主程序 ---
if __name__ == "__main__":
    print("🎯 鲲鹏通讯 - 批量注册和加群工具")
    print("📍 新服务器: http://**************:8095")
    print("=" * 50)
    
    try:
        while True:
            print("\n请选择要执行的功能:")
            print("  1. 批量注册账号")
            print("  2. 批量加群")
            print("  Q. 退出")
            
            choice = input("\n您的选择: ").strip().upper()
            
            if choice == '1':
                print("\n🎯 鲲鹏通讯 - 批量注册功能")
                print("📍 新服务器: http://**************:8095")
                print("=" * 50)
                
                team_prefix = input("请输入团队前缀 (例如: 黄朝凤团队): ").strip()
                if not team_prefix:
                    print("❌ 团队前缀不能为空")
                    continue
                
                invite = input("请输入【邀请码】: ").strip()
                if not invite:
                    print("❌ 邀请码不能为空")
                    continue

                # 密码选择
                print("\n🔐 密码设置:")
                print("  1. 随机密码 (8-10位字母数字混合)")
                print("  2. 固定密码 (手动输入)")
                password_choice = input("请选择密码类型 (1/2): ").strip()

                fixed_password = None
                if password_choice == "2":
                    fixed_password = input("请输入固定密码: ").strip()
                    if not fixed_password:
                        print("❌ 固定密码不能为空")
                        continue
                    print(f"✅ 将使用固定密码: {fixed_password}")
                elif password_choice == "1":
                    print("✅ 将使用随机密码")
                else:
                    print("❌ 无效选择，默认使用随机密码")
                    password_choice = "1"

                try:
                    num = int(input("请输入要注册的【账号数量】: ").strip())
                    if num <= 0:
                        print("❌ 数量必须大于0")
                        continue
                    
                    print(f"\n🎯 开始批量注册 {num} 个账号...")
                    print(f"🏷️ 昵称格式: {team_prefix}-{{身份证姓名}}")
                    print("=" * 50)

                    success_count = 0
                    for i in range(num):
                        print("\n" + "="*50 + f"\n--- [注册任务 {i + 1} / {num}] ---")

                        if auto_register(invite, team_prefix, fixed_password):
                            success_count += 1
                        if i < num - 1:
                            wait_time = random.uniform(2, 5)
                            print(f"⏳ 等待 {wait_time:.1f} 秒后继续...")
                            time.sleep(wait_time)

                    print("\n" + "=" * 50)
                    print(f"🎉 批量注册完成!")
                    print(f"✅ 成功: {success_count} 个")
                    print(f"❌ 失败: {num - success_count} 个")
                    print("=" * 50)
                except ValueError: 
                    print("无效的数量输入。")
                    
            elif choice == '2':
                room_id = input("请输入要加入的【群聊ID】: ").strip()
                if room_id:
                    try:
                        # 使用zhanghao.txt文件
                        account_file = "zhanghao.txt"
                        if not os.path.exists(account_file):
                            print(f"\n❌ 错误：找不到账号文件 '{account_file}'")
                            print("请确保zhanghao.txt文件存在于当前目录")
                            continue

                        with open(account_file, "r", encoding="utf-8") as f:
                            lines = f.readlines()

                        valid_accounts = []
                        for i, line in enumerate(lines):
                            line = line.strip()
                            if not line:
                                continue
                            try:
                                phone, pwd, data_str = line.split(':', 2)
                                data = json.loads(data_str)

                                # 检查是否包含加密数据需要解密
                                if "data" in data and isinstance(data["data"], str):
                                    print(f"🔓 第{i+1}行包含加密数据，正在解密...")
                                    decrypted_data = decrypt_response_data(data["data"])
                                    if decrypted_data:
                                        data = decrypted_data
                                        print(f"✅ 第{i+1}行解密成功")
                                    else:
                                        print(f"❌ 第{i+1}行解密失败，跳过")
                                        continue

                                # 验证必要字段
                                if all(key in data for key in ["userId", "access_token", "httpKey"]):
                                    valid_accounts.append((phone, pwd, data))
                                else:
                                    print(f"⚠️ 第{i+1}行缺少必要字段，跳过")

                            except Exception as e:
                                print(f"❌ 第{i+1}行数据格式错误: {e}")

                        if not valid_accounts:
                            print("\n❌ 没有找到有效的账号数据")
                            continue

                        print(f"\n📋 找到 {len(valid_accounts)} 个有效账号，即将开始批量加群...")
                        print(f"🎯 目标群聊ID: {room_id}")
                        print("=" * 60)

                        success_count = 0
                        for i, (phone, pwd, data) in enumerate(valid_accounts):
                            print(f"\n--- [加群任务 {i + 1} / {len(valid_accounts)}] ---")
                            if auto_join_group(phone, pwd, data, room_id):
                                success_count += 1
                            if i < len(valid_accounts) - 1:
                                wait_time = random.uniform(2, 5)
                                print(f"⏳ 等待 {wait_time:.1f} 秒后继续...")
                                time.sleep(wait_time)

                        print("\n" + "=" * 60)
                        print(f"🎉 批量加群完成!")
                        print(f"✅ 成功: {success_count} 个")
                        print(f"❌ 失败: {len(valid_accounts) - success_count} 个")
                        print("=" * 60)

                    except Exception as e:
                        print(f"\n❌ 处理账号文件失败: {e}")
                        
            elif choice == 'Q':
                break
            else:
                print("无效的选择，请输入 1, 2, 或 Q。")
                
    except KeyboardInterrupt: 
        pass
    print("\n程序已退出。")
