#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import random
import string
import json
import requests

# 添加当前目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入短信验证码模块
from phone_sms_module import get_phone_and_sms

# 注册网站的API地址
REGISTER_API = "https://zcbe1esh.quantumblazerhub.com/home/<USER>"

def generate_random_password(length=10):
    """生成随机密码"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

def register_with_sms_verification(username, password, project_id, invite_code=None):
    """
    使用短信验证码进行注册的完整流程
    
    :param username: 短信平台用户名
    :param password: 短信平台密码
    :param project_id: 短信平台项目ID
    :param invite_code: 邀请码(可选)
    :return: (成功状态, 详细信息字典)
    """
    print("\n" + "=" * 60)
    print("  开始注册流程 - 使用短信验证码  ")
    print("=" * 60)
    
    # 1. 获取手机号和短信验证码
    print("\n[步骤1] 获取手机号和短信验证码...")
    sms_result = get_phone_and_sms(username, password, project_id)
    
    if not sms_result["success"]:
        print(f"❌ 获取手机号或验证码失败: {sms_result['message']}")
        return False, {"error": sms_result['message']}
    
    phone_number = sms_result["phone"]
    sms_code = sms_result["code"]
    print(f"✅ 成功获取手机号: {phone_number}")
    print(f"✅ 成功获取验证码: {sms_code}")
    
    # 2. 准备注册数据
    print("\n[步骤2] 准备注册数据...")
    generated_password = generate_random_password()
    
    registration_data = {
        "phone": phone_number,
        "password": generated_password,
        "password2": generated_password,  # 确认密码
        "code": sms_code,  # 短信验证码
        "imgCode": "",  # 如果需要图片验证码，在这里填写
    }
    
    # 如果有邀请码，添加到注册数据中
    if invite_code:
        registration_data["inviteCode"] = invite_code
        
    print(f"用户名: {phone_number}")
    print(f"密码: {generated_password}")
    print(f"短信验证码: {sms_code}")
    if invite_code:
        print(f"邀请码: {invite_code}")
    
    # 3. 发送注册请求
    print("\n[步骤3] 发送注册请求...")
    try:
        # 这里需要根据实际网站的注册逻辑进行调整
        # 如果需要加密数据，请参考注册122.py中的rsa_encrypt_with_public_key函数
        
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://zcbe1esh.quantumblazerhub.com",
            "Referer": "https://zcbe1esh.quantumblazerhub.com/signup"
        }
        
        # 假设需要加密，使用以下伪代码（实际情况请使用真实的加密逻辑）
        # json_data = json.dumps(registration_data, ensure_ascii=False).encode('utf-8')
        # encrypted_key = rsa_encrypt_with_public_key(json_data)
        # payload = {"key": encrypted_key}
        
        # 非加密版本（简化示例）
        payload = registration_data
        
        response = requests.post(REGISTER_API, headers=headers, json=payload, timeout=30)
        
        # 处理响应
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:  # 假设200是成功代码
                print(f"✅ 注册成功! 消息: {result.get('msg', '成功')}")
                
                # 保存账号信息到文件
                account_info = f"手机号: {phone_number}, 密码: {generated_password}, 注册时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                    f.write(account_info + "\n")
                    
                return True, {
                    "phone": phone_number,
                    "password": generated_password,
                    "sms_code": sms_code,
                    "response": result
                }
            else:
                error_msg = result.get("msg", "未知错误")
                print(f"❌ 注册失败: {error_msg}")
                return False, {"error": error_msg, "response": result}
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False, {"error": f"HTTP错误: {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 注册过程发生异常: {e}")
        return False, {"error": str(e)}
    finally:
        # 不管成功失败，都尝试释放手机号
        print("\n[清理] 尝试释放手机号...")
        if sms_result["client"].release_phone_number(phone_number, project_id):
            print(f"✅ 手机号 {phone_number} 已成功释放")
        else:
            print(f"⚠️ 手机号 {phone_number} 释放失败，但不影响注册流程")

# 主函数
if __name__ == "__main__":
    print("=" * 60)
    print("  短信验证码注册模块使用示例  ")
    print("=" * 60 + "\n")
    
    # 获取短信平台配置
    username = input("请输入短信平台用户名: ")
    password = input("请输入短信平台密码: ")
    project_id = input("请输入短信平台项目ID: ")
    
    # 邀请码
    invite_code = input("请输入邀请码（可选，直接回车跳过）: ").strip() or None
    
    # 循环注册多个账号
    num_accounts = input("请输入要注册的账号数量: ")
    try:
        num_accounts = int(num_accounts)
    except ValueError:
        print("输入无效，默认注册1个账号")
        num_accounts = 1
    
    success_count = 0
    for i in range(num_accounts):
        print(f"\n开始注册第 {i+1}/{num_accounts} 个账号...")
        success, result = register_with_sms_verification(username, password, project_id, invite_code)
        if success:
            success_count += 1
            
        if i < num_accounts - 1:
            wait_time = random.uniform(3, 6)
            print(f"\n等待 {wait_time:.1f} 秒后继续下一个账号注册...")
            time.sleep(wait_time)
    
    print("\n" + "=" * 60)
    print(f"注册完成! 成功: {success_count}/{num_accounts}")
    print("=" * 60) 