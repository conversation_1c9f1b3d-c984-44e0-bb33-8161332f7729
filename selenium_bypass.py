#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium绕过阿里云WAF反爬虫
基于无头浏览器的解决方案
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import requests
import time
import json
from decrypt_analysis import EncryptionSystem

class SeleniumBypass:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.crypto = EncryptionSystem()
        self.driver = None
        self.session = requests.Session()
        
        print("🤖 Selenium绕过方案启动")
        print("🌐 使用无头浏览器获取有效cookie")
        print("=" * 50)

    def setup_driver(self):
        """配置Chrome浏览器"""
        print("🔧 配置Chrome浏览器...")
        
        chrome_options = Options()
        
        # 基础配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        
        # 反检测配置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1')
        
        # 可选：无头模式（如果需要看到浏览器操作，注释掉这行）
        # chrome_options.add_argument('--headless')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome浏览器配置成功")
            return True
            
        except Exception as e:
            print(f"❌ Chrome浏览器配置失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return False

    def get_cookies_with_selenium(self):
        """使用Selenium获取有效cookie"""
        print("🍪 使用Selenium获取cookie...")
        
        try:
            # 访问登录页面
            login_url = f"{self.base_url}/pages/login/login?code=21328050"
            print(f"📱 访问页面: {login_url}")
            
            self.driver.get(login_url)
            
            # 等待页面加载完成
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 等待关键元素加载（可以根据实际页面调整）
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                print("⚠️ 页面加载超时，继续执行...")
            
            # 获取所有cookie
            cookies = self.driver.get_cookies()
            cookie_dict = {}
            
            print("🍪 获取到的cookie:")
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
                print(f"  {cookie['name']}: {cookie['value'][:20]}...")
            
            # 检查是否获取到关键cookie
            if 'acw_tc' in cookie_dict and 'cdn_sec_tc' in cookie_dict:
                print("✅ 成功获取关键cookie")
                return cookie_dict
            else:
                print("⚠️ 未获取到关键cookie，可能需要更多等待时间")
                return cookie_dict
                
        except Exception as e:
            print(f"❌ 获取cookie失败: {e}")
            return None

    def test_api_with_selenium_cookies(self, cookies):
        """使用Selenium获取的cookie测试API"""
        print("\n🚀 使用Selenium cookie测试API")
        print("-" * 40)
        
        if not cookies:
            print("❌ 没有可用的cookie")
            return False
        
        try:
            # 准备测试数据
            test_data = {}
            encrypted = self.crypto.encrypt_data(test_data)
            sign = self.crypto.generate_sign(test_data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return False
            
            # 格式化cookie
            cookie_header = "; ".join([f"{k}={v}" for k, v in cookies.items()])
            
            # 构造请求头
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': cookie_header,
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'token': '',  # 使用空token
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            print(f"🔐 加密数据: {encrypted[:30]}...")
            print(f"✍️ 签名: {sign}")
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/dev-api/api/login/authccode.html",
                headers=headers,
                json=request_body,
                timeout=15,
                verify=False
            )
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"📏 响应长度: {len(response.text)}")
            
            # 检查响应
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("❌ 仍然返回HTML页面")
                print(f"HTML前100字符: {response.text[:100]}...")
                return False
            else:
                print("✅ 返回API响应")
                print(f"响应内容: {response.text}")
                
                # 尝试解密
                try:
                    decrypted = self.crypto.decrypt_data(response.text)
                    if decrypted:
                        print(f"🔓 解密成功: {decrypted}")
                    else:
                        print("⚠️ 响应可能是明文")
                except:
                    print("⚠️ 响应不是加密格式")
                
                return True
                
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            return False

    def selenium_register_process(self):
        """使用Selenium的完整注册流程"""
        print("\n🎯 Selenium注册流程")
        print("=" * 50)
        
        # 1. 设置浏览器
        if not self.setup_driver():
            return False
        
        try:
            # 2. 获取cookie
            cookies = self.get_cookies_with_selenium()
            if not cookies:
                print("❌ 无法获取cookie")
                return False
            
            # 3. 测试API
            api_success = self.test_api_with_selenium_cookies(cookies)
            
            if api_success:
                print("\n🎉 Selenium方案成功！")
                print("可以继续实现完整的注册流程")
                
                # 这里可以继续实现注册逻辑
                # self.complete_registration_with_selenium(cookies)
                
                return True
            else:
                print("\n❌ API测试失败")
                return False
                
        finally:
            # 清理资源
            if self.driver:
                self.driver.quit()
                print("🧹 浏览器已关闭")

    def complete_registration_with_selenium(self, cookies):
        """使用Selenium完成完整注册"""
        print("\n📝 执行完整注册流程...")
        
        # 生成账号信息
        import random
        import string
        
        def generate_phone():
            prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139']
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return prefix + suffix
        
        def generate_password(length=8):
            chars = string.ascii_letters + string.digits
            return ''.join(random.choice(chars) for _ in range(length))
        
        phone = generate_phone()
        password = generate_password()
        
        print(f"📱 生成手机号: {phone}")
        print(f"🔐 生成密码: {password}")
        
        # 这里可以继续实现具体的注册API调用
        # 使用获取到的有效cookies
        
        return True


if __name__ == "__main__":
    bypass = SeleniumBypass()
    success = bypass.selenium_register_process()
    
    if success:
        print("\n🎊 Selenium绕过成功！")
    else:
        print("\n💔 Selenium绕过失败")
        print("\n💡 可能的解决方案:")
        print("1. 确保安装了Chrome浏览器和ChromeDriver")
        print("2. 尝试增加等待时间")
        print("3. 检查网络连接")
        print("4. 考虑使用代理IP")
