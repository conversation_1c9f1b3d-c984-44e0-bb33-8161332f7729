export { type _PerformanceEntryType, _PerformanceEntry, _PerformanceMark, _PerformanceMeasure, _PerformanceResourceTiming, _Performance, _PerformanceObserver, _PerformanceObserverEntryList } from "./_polyfills.mjs";
export declare const PerformanceEntry: typeof globalThis.PerformanceEntry;
export declare const PerformanceMark: typeof globalThis.PerformanceMark;
export declare const PerformanceMeasure: typeof globalThis.PerformanceMeasure;
export declare const PerformanceResourceTiming: typeof globalThis.PerformanceResourceTiming;
export declare const PerformanceObserver: typeof globalThis.PerformanceObserver;
export declare const Performance: typeof globalThis.Performance;
export declare const PerformanceObserverEntryList: typeof globalThis.PerformanceObserverEntryList;
export declare const performance: unknown;
