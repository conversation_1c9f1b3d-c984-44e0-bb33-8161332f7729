@echo off
echo 🚀 安装Google Cloud Vision API
echo ================================

echo 📦 正在安装google-cloud-vision...
pip install google-cloud-vision

echo.
echo ✅ 安装完成！
echo.
echo 🧪 测试MLKit OCR...
python -c "try: from google.cloud import vision; print('✅ Google Cloud Vision API 可用'); except ImportError: print('❌ 安装失败')"

echo.
echo 🔑 检查凭证文件...
if exist "google-cloud-credentials.json" (
    echo ✅ 找到凭证文件: google-cloud-credentials.json
    set GOOGLE_APPLICATION_CREDENTIALS=%CD%\google-cloud-credentials.json
    echo 🔑 环境变量已设置
) else (
    echo ❌ 未找到凭证文件
    echo 💡 请确保 google-cloud-credentials.json 文件在当前目录
)

echo.
echo 🎯 现在可以运行主脚本测试MLKit OCR了
pause
