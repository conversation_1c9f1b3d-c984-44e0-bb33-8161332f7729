# 51代理自动登录签到脚本

这是一个用于51代理网站的自动登录和签到脚本，支持处理滑块验证和反爬虫JS验证。

## 功能特点

- ✅ 自动登录51代理网站
- ✅ 自动执行每日签到
- ✅ 处理滑块验证码
- ✅ 绕过反爬虫JS检测
- ✅ 模拟人类操作行为
- ✅ 支持无头模式运行
- ✅ 详细的日志记录
- ✅ 错误重试机制

## 环境要求

- Python 3.7+
- Chrome浏览器
- 稳定的网络连接

## 安装步骤

### 1. 克隆或下载代码

```bash
git clone <repository_url>
cd 51daili-auto-signin
```

### 2. 安装依赖

```bash
# 方法1: 使用安装脚本（推荐）
python install_dependencies.py

# 方法2: 手动安装
pip install -r requirements.txt
```

### 3. 配置账号信息

编辑 `config.py` 文件或设置环境变量：

```python
# 方法1: 直接修改config.py
ACCOUNT = "your_username"
PASSWORD = "your_password"

# 方法2: 设置环境变量（推荐）
export DAILI_ACCOUNT="your_username"
export DAILI_PASSWORD="your_password"
```

## 使用方法

### 基础使用

```bash
# 运行高级版本（推荐）
python 51daili_advanced_login.py

# 运行简化版本
python 51daili_simple_login.py
```

### 定时任务

#### Linux/Mac (crontab)

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天上午9点执行）
0 9 * * * cd /path/to/script && python 51daili_advanced_login.py
```

#### Windows (任务计划程序)

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为每天
4. 设置操作为启动程序：`python.exe`
5. 添加参数：`51daili_advanced_login.py`
6. 设置起始位置为脚本所在目录

#### 青龙面板

```bash
# 添加定时任务
0 9 * * * python /path/to/51daili_advanced_login.py
```

## 配置说明

### config.py 主要配置项

```python
# 基础配置
ACCOUNT = "your_username"          # 账号
PASSWORD = "your_password"         # 密码
HEADLESS = True                    # 无头模式（True=后台运行）

# 延迟配置
LOGIN_DELAY = (2, 4)              # 登录延迟范围
TYPE_DELAY = (0.05, 0.15)         # 打字延迟范围
SLIDER_DELAY = (0.01, 0.02)       # 滑块移动延迟

# 重试配置
MAX_RETRY = 3                     # 最大重试次数
RETRY_DELAY = 5                   # 重试间隔
```

## 脚本版本说明

### 1. 51daili_simple_login.py
- 简化版本，使用预设Cookie
- 适合网站反爬虫较弱的情况
- 运行速度快，资源占用少

### 2. 51daili_advanced_login.py
- 高级版本，使用Selenium模拟浏览器
- 支持滑块验证和JS验证
- 更强的反检测能力
- 适合网站反爬虫较强的情况

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   ```bash
   # 重新安装ChromeDriver
   python install_dependencies.py
   ```

2. **滑块验证失败**
   - 检查网络连接
   - 尝试调整滑块配置参数
   - 设置 `HEADLESS = False` 观察验证过程

3. **登录失败**
   - 确认账号密码正确
   - 检查网站是否更新了验证机制
   - 查看日志文件获取详细错误信息

4. **依赖安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

### 日志分析

脚本会生成详细的日志文件 `51daili_advanced.log`，包含：
- 登录过程详情
- 验证码处理结果
- 签到执行状态
- 错误信息和堆栈跟踪

## 注意事项

1. **合规使用**: 请确保使用脚本符合网站服务条款
2. **频率控制**: 避免过于频繁的请求，建议每天执行1-2次
3. **账号安全**: 妥善保管账号密码，建议使用环境变量
4. **网络环境**: 确保网络稳定，避免在网络不稳定时运行
5. **更新维护**: 网站可能会更新反爬虫机制，需要及时更新脚本

## 技术原理

### 反爬虫对抗
- 使用真实浏览器内核（Chrome）
- 模拟人类操作行为（随机延迟、鼠标轨迹）
- 反检测JS注入
- 随机User-Agent轮换

### 滑块验证破解
- 图像识别定位缺口
- 生成人类化滑动轨迹
- 模拟真实鼠标操作
- 随机抖动增加真实性

## 更新日志

- v1.0: 基础登录签到功能
- v1.1: 添加滑块验证支持
- v1.2: 增强反爬虫对抗能力
- v1.3: 优化配置管理和错误处理

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。
