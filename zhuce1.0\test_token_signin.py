import requests
import json
import os
import time
import collections
import hashlib

# --- 配置 ---
# 从主脚本复制过来的核心配置
SIGN_IN_URL = "http://206.119.6.100:30340/api/sign/globalsign"
ACCOUNTS_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\accounts.txt"
API_VERSION = "1.5.0"
SECRET_KEY = "*************"

def calculate_sign(params: dict, secret_key: str) -> str:
    """
    计算签到请求的签名。
    """
    filtered_params = {k: v for k, v in params.items() if k != 'sign' and v is not None and v != ''}
    sorted_params = collections.OrderedDict(sorted(filtered_params.items()))
    values_string = "".join(sorted_params.values())
    string_to_sign = f"{values_string}{secret_key}"
    return hashlib.md5(string_to_sign.encode('utf-8')).hexdigest().lower()

def find_account_with_token() -> dict:
    """
    加载并查找第一个拥有有效token的账号。
    """
    if not os.path.exists(ACCOUNTS_FILE):
        return None
    
    with open(ACCOUNTS_FILE, "r", encoding="utf-8") as f:
        for line in f:
            if not line.strip():
                continue
            account = json.loads(line)
            if 'token' in account and 'token_expiry' in account and time.time() < account['token_expiry']:
                print(f"✅ 找到一个带有效Token的测试账号: {account['account']}")
                return account
    return None

def sign_in_without_proxy(account_info: dict):
    """
    使用token进行签到，但不通过代理。
    """
    print("\n--- 开始测试: 使用Token直接签到 (不通过代理) ---")
    
    session_data = {'id': account_info.get('user_id'), 'token': account_info.get('token')}

    params = {
        'os': 'android',
        'user_id': session_data['id'],
        'v': API_VERSION,
        'token': session_data['token'],
        'timestamp': str(int(time.time() * 1000)),
        'sign': ''
    }
    params['sign'] = calculate_sign(params, SECRET_KEY)

    print(f"   - 使用账号: {account_info['account']}")
    print(f"   - 使用Token: {session_data['token'][:10]}... ")
    print("   - 正在发送请求 (本地网络)...")

    try:
        response = requests.post(SIGN_IN_URL, data=params, timeout=20) # 注意：这里没有 proxies 参数
        result = response.json()
        
        print("\n--- 测试结果 ---")
        print(f"服务器原始响应: {result}")
        
        if result.get('code') == 0 or "已签到" in result.get('msg', ''):
            print("\n🎉 测试成功！服务器接受了来自不同IP的Token。")
        else:
            print("\n❌ 测试失败。服务器很可能验证了IP一致性。")

    except Exception as e:
        print(f"\n❌ 请求异常: {e}")

if __name__ == "__main__":
    print("="*50)
    print("      无代理Token签到测试脚本")
    print("="*50)
    
    target_account = find_account_with_token()
    
    if target_account:
        sign_in_without_proxy(target_account)
    else:
        print("\n❌ 未在 aacounts.txt 中找到任何带有有效Token的账号。")
        print("   - 请先运行 login_and_actions.py 登录一次以生成Token。")
    
    print("\n测试结束。") 