#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯注册流程加解密工具 (v2 - 修正版)

import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# --- 根据日志确定的加密参数 ---
SECRET_KEY_B64 = "cY6leN3U1xIJHJ6jLuh0Rg=="
SECRET_KEY_BYTES = base64.b64decode(SECRET_KEY_B64)
STATIC_ZERO_IV = b'\x00' * 16

def encrypt_registration_data(user_data: dict) -> str:
    """
    加密要发送的注册数据。
    """
    print("--- 开始加密注册数据 ---")
    try:
        # 修正: 使用 ensure_ascii=False 来防止中文字符被转义，与客户端行为保持一致
        data_to_encrypt_str = json.dumps(user_data, separators=(',', ':'), ensure_ascii=False)
        data_to_encrypt_bytes = data_to_encrypt_str.encode('utf-8')
        
        print(f"密钥: {SECRET_KEY_BYTES.hex()}")
        print(f"IV: {STATIC_ZERO_IV.hex()}")
        print(f"待加密JSON: {data_to_encrypt_str}")
        
        cipher = AES.new(SECRET_KEY_BYTES, AES.MODE_CBC, STATIC_ZERO_IV)
        padded_data = pad(data_to_encrypt_bytes, AES.block_size)
        encrypted_bytes = cipher.encrypt(padded_data)
        
        encrypted_data_b64 = base64.b64encode(encrypted_bytes).decode('utf-8')
        print(f"加密结果 (Base64): {encrypted_data_b64}")
        
        return encrypted_data_b64
        
    except Exception as e:
        print(f"加密失败: {e}")
        return ""

def decrypt_registration_response(encrypted_response_b64: str) -> dict:
    """
    解密服务器返回的注册响应数据。
    """
    print("\n--- 开始解密注册响应 ---")
    key = SECRET_KEY_BYTES
    print(f"密钥: {key.hex()}")
    encrypted_data_bytes = base64.b64decode(encrypted_response_b64)

    # 修正: 增加两种IV策略尝试
    # 策略 1: IV 来自响应数据的前16字节
    print("\n[策略 1] 尝试使用数据头作为IV...")
    try:
        iv = encrypted_data_bytes[:16]
        ciphertext = encrypted_data_bytes[16:]
        print(f"IV (来自响应数据): {iv.hex()}")
        
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_bytes = unpad(cipher.decrypt(ciphertext), AES.block_size)
        decrypted_json = json.loads(decrypted_bytes.decode('utf-8'))
        
        print(">>> 解密成功! (使用数据头作为IV)")
        return decrypted_json
    except Exception as e:
        print(f"解密失败: {e}")

    # 策略 2: IV 是固定的全零 (同加密请求)
    print("\n[策略 2] 尝试使用静态全零作为IV...")
    try:
        iv = STATIC_ZERO_IV
        ciphertext = encrypted_data_bytes # 此时整个数据块都是密文
        print(f"IV (静态全零): {iv.hex()}")

        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_bytes = unpad(cipher.decrypt(ciphertext), AES.block_size)
        decrypted_json = json.loads(decrypted_bytes.decode('utf-8'))

        print(">>> 解密成功! (使用静态全零作为IV)")
        return decrypted_json
    except Exception as e:
        print(f"解密失败: {e}")

    return {}

if __name__ == '__main__':
    # --- 1. 验证加密流程 ---
    original_user_data = {
        "nickName": "测试用户2", "platform": "android", "password": "a123456",
        "sex": 1, "verifyCode": "4444"
    }
    encrypted_result = encrypt_registration_data(original_user_data)
    
    original_request_data = "7n+vhCrZycbsXKq/IGcg3Oy5qMDnS3/gVTRMYqGi3vfy/WKPHeVuRK4PoMQEP17gvQAss6U5CDR9NMvpUlAa/1twl3DdiZFA8ZDujnk5lLh4Yg5PDLfY3Wf8TszkEB/uDCv4o1BQ2XdpePtU11HgOWgLASvXettze+aBCS+0G7yTT9MhYZna36rDweE+6c3wzLVNm3sNKVHDESFeQQpEohUr0oKl6395ncfDKqMmfFpxpU8iVNU+i+n1jbYAXhXHukpgJ9V2WiOK/uE6qHv/aNIzRyzAAIrH80S5SzS9tnNosEN/nwsA4fZAGoz6FkhUd/ge8G+w7qvA3/8PFsB18JUAuUZpftxZvnoHOYM8p3sC84nnFtnPB9LRPuR4DhTLtCZ/6NQF0I0jeLDylfJ1m1ImfQX+Eq5ZI+uea5xJs+VaXVCiSYxe1EHbTlWjttzdBxEKeMokiZdc/RY9VE6LBJn4dxKGuZophp6stdl8lGyBoFoUwQl1AkHSAVxGEYv94/+CqaaGCKD4uE5oVAq8Wq557RqhlMZUryJ/kpzFzmWwMq140GQZapOAJTHD4bgN"
    print(f"\n原始请求中的'data': {original_request_data}")
    
    if encrypted_result == original_request_data:
        print(">>> 加密验证成功！脚本实现的加密逻辑与日志完全一致！")
    else:
        print(">>> 加密验证失败！请检查代码。")
        
    print("\n" + "="*50 + "\n")
    
    # --- 2. 解密服务器响应 ---
    response_data_b64 = "ZNP7H0h33DspulnqkOTV11qpkHMt4ucvVWw7V1CCMEo3zGX3rbHkZtz6gsJFKVlsKjQKzMtbfOf72Cm+9yLVCt5U0h3VnwMlqaW6fJn4Lxi/uV3QmrnuGgh/GpSpTIPj3bntjbOiepxW4bVSnx2+0SkhlKGs/G/UYAJNbrzUzn17rwQ9+5OsRp0YO3xBWTBHsXwCg4j4nkmDv9YdR1LDRZ/fM5B8rXmlI5RKNgP1eXBtkIVfLCx2924y/3e7n6z+/+12kKLOWKnWdScK1Ph8XEAfEKfSJ7xOZYxeHTnyAw2CYKZY2zUsvfAB+r4JCkYP6yDFivJ5W08EYap1dm0RruCY6J1hOXHLdUS8WpEB3khZvjKiUKSXakBwGeqymG+0ObJc96QWNGtbKLa/m7eBLwrLwLzDa5Wr2FlCERfzKr0XF4GygUkAWjE+YiaOyHJhcF+tOixwZ3gO395Xp2cRFM5lUp8w/ar6WyHnnXKZGB+3rebvouiq78TrwChEyUyEH33Fn5PUOMvCJNJG4LhZ5upBtHDKlEK+ljjLqyL2C9pT1dBkYnzpOHqxDLV0lT0NcDaLoTgwkR3/Tu04ptJF+6PjJTum31bkZRTUk8ODXz/h+Q2zUA380tDZxLhEHx5k19LryvGS+NGS+xYDh/WfP0KiUmQ+bX8DlWQ9LED9nyLJg3eXrsui5t3xcXKYOx4PqGm2lx9n7HP1WeKUSiiGgc7XG/m6wFR9aea/zpnEf3wyZGrvy1dBICzwCbU/5SIJxKMobA3uXAS7ZvZsGPc1eFiwH5BaksFo3UE9qIl05wu/fhkHvaV24eJoLB6vjrUeV7t6cMmcRcWGpwzJ3c1eFJsZuRex1Sk8dNb/GzQsDvVnycr8g3MQdh25VPBg0D0YvnztTrY9UaXPwz7FnmE4zrI4RnWCNz7krs02R1O2rXGWfNgUejJ7PVWv5YXdmOI9VGsPVJyv9fuQaMCUyVNnJ8I/gvHkn7IGT+K/PRsKVwUCb8TpweNOqEH+2la5iqMT61XaZDuGDlgqFHH+CDP3Nm0f6zsP/9gP8q+lULyuUdl54kUZ8pRg2TNv2jYyelteVA5haIqfsWwRCbS5xVS/9s6tFwxZecDr+IjeY4WO8tns3Tl0swxs+NLrc3926KcQXuK8GEuoUSkK6JBvouXgUm63/5ANFa/5Q4pwHsswLahVEuizO/71mkfuDqmm7pOFXzSzKygHO2thtN6Q/jP76duvlUajzrStXmNRzB+idmAvLW5hxJ3uckzy/hmmfdqsO0FlOLDgH85id5sOM6STR14BUIup3FbBJEf4xfHP+WEVxuzKLAeccfRXxn7WcYX4Pt/hl1fGnClplUl2/Cx10XMtO0pRtoogvFjwv+cuJkK7oNDdRCLnAslZZo1/O7aXRyAFZB9TfRoMZiPX0gAqDcVJSXUKKbeJHohjyRQqmLV+3VbpeVEzMa45DMwFlGwufQW+vPwtltPiivm/rE6IRFLY4IeVbLKmz7p3BmitFp8="
    decrypted_response = decrypt_registration_response(response_data_b64)
    
    if decrypted_response:
        print("\n解密后的响应内容:")
        print(json.dumps(decrypted_response, indent=2, ensure_ascii=False)) 