#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MLKit OCR 身份证识别模块
专门用于身份证正面信息提取（姓名、身份证号码）
支持图像预处理和智能文本解析
"""

import os
import re
import cv2
import numpy as np
from typing import Tuple, Optional, Dict, List
import json

# Google Cloud Vision API
try:
    from google.cloud import vision
    from google.cloud.vision_v1 import types
    VISION_AVAILABLE = True
    print("✓ Google Cloud Vision API 已加载")
except ImportError:
    VISION_AVAILABLE = False
    print("⚠ Google Cloud Vision API 未安装: pip install google-cloud-vision")

# OpenCV图像处理
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
    print("✓ OpenCV 图像处理库已加载")
except ImportError:
    CV2_AVAILABLE = False
    print("⚠ OpenCV 未安装，图像预处理功能受限")

class MLKitOCR:
    """
    MLKit OCR 身份证识别器
    专门优化中文和数字识别
    """
    
    def __init__(self, credentials_path: Optional[str] = None):
        """
        初始化MLKit OCR
        
        Args:
            credentials_path: Google Cloud 凭证文件路径
        """
        self.client = None
        self.is_available = False
        
        # 设置凭证
        if credentials_path and os.path.exists(credentials_path):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
        
        # 初始化客户端
        if VISION_AVAILABLE:
            try:
                self.client = vision.ImageAnnotatorClient()
                self.is_available = True
                print("✅ MLKit OCR 初始化成功")
            except Exception as e:
                print(f"❌ MLKit OCR 初始化失败: {e}")
                print("💡 请确保设置了 GOOGLE_APPLICATION_CREDENTIALS 环境变量")
        
        # 身份证号码正则表达式
        self.id_patterns = [
            r'\b\d{17}[\dXx]\b',  # 18位身份证
            r'\b\d{15}\b',        # 15位身份证
            r'\b\d{6}[\d]{8}[\dXx]{4}\b',  # 带分隔符格式
        ]
        
        # 常见姓氏列表（扩展版）
        self.common_surnames = [
            '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
            '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
            '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
            '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
            '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎',
            '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜',
            '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆',
            '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史',
            '顾', '侯', '邵', '孟', '龙', '万', '段', '雷', '钱', '汤',
            '尹', '黎', '易', '常', '武', '乔', '贺', '赖', '龚', '文'
        ]
        
        # 需要过滤的无关词汇
        self.exclude_words = [
            '中华人民共和国', '居民身份证', '公民身份', '姓名', '性别', '民族',
            '出生', '住址', '身份证', '有效期', '签发机关', '年月日', '汉族', 
            '满族', '回族', '藏族', '维吾尔', '苗族', '彝族', '壮族', '布依', 
            '朝鲜', '公安局', '派出所', '长期', '临时', '男', '女'
        ]

    def preprocess_image(self, image_path: str) -> Optional[bytes]:
        """
        专门针对身份证的图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            处理后的图像字节数据
        """
        if not CV2_AVAILABLE:
            # 没有OpenCV，直接读取原图
            try:
                with open(image_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                print(f"❌ 图像读取失败: {e}")
                return None
        
        try:
            print("🔧 开始MLKit专用图像预处理...")
            
            # 读取图像 - 解决中文路径问题
            try:
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                nparr = np.frombuffer(image_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except:
                img = cv2.imread(image_path)
            
            if img is None:
                print("❌ 图像解码失败")
                return None
            
            print(f"📐 原始图像尺寸: {img.shape}")
            
            # 1. 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 2. 图像去噪 - 保持文字边缘清晰
            denoised = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # 3. 增强对比度 - CLAHE自适应直方图均衡
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 4. 锐化处理 - 增强文字边缘
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            # 5. 自适应二值化 - 适应不同光照条件
            binary = cv2.adaptiveThreshold(
                sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 6. 形态学操作 - 连接断开的文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 1))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 7. 调整图像大小 - MLKit在高分辨率下效果更好
            height, width = cleaned.shape
            target_height = 1200  # MLKit推荐高度
            if height < target_height:
                scale_factor = target_height / height
                new_width = int(width * scale_factor)
                new_height = target_height
                cleaned = cv2.resize(cleaned, (new_width, new_height), 
                                   interpolation=cv2.INTER_CUBIC)
                print(f"📏 图像已优化到: {new_width}x{new_height}")
            
            # 8. 转换为RGB格式（MLKit推荐）
            rgb_image = cv2.cvtColor(cleaned, cv2.COLOR_GRAY2RGB)
            
            # 9. 编码为字节数据
            success, buffer = cv2.imencode('.png', rgb_image, 
                                         [cv2.IMWRITE_PNG_COMPRESSION, 0])
            if not success:
                print("❌ 图像编码失败")
                return None
            
            # 保存预处理后的图像用于调试
            debug_path = image_path.replace('.', '_mlkit_processed.')
            cv2.imwrite(debug_path, rgb_image)
            print(f"🔍 MLKit预处理图像已保存: {debug_path}")
            
            print("✅ MLKit专用图像预处理完成")
            return buffer.tobytes()
            
        except Exception as e:
            print(f"⚠️ 图像预处理失败，使用原图: {e}")
            try:
                with open(image_path, 'rb') as f:
                    return f.read()
            except Exception as e2:
                print(f"❌ 原图读取也失败: {e2}")
                return None

    def extract_text_with_mlkit(self, image_bytes: bytes) -> Optional[str]:
        """
        使用MLKit进行OCR文本识别
        
        Args:
            image_bytes: 图像字节数据
            
        Returns:
            识别出的文本内容
        """
        if not self.is_available:
            print("❌ MLKit OCR 不可用")
            return None
        
        try:
            print("🤖 开始MLKit OCR识别...")
            
            # 创建图像对象
            image = vision.Image(content=image_bytes)
            
            # 配置OCR参数 - 优化中文识别
            image_context = vision.ImageContext(
                language_hints=['zh-CN', 'en']  # 中文简体 + 英文数字
            )
            
            # 执行文本检测
            response = self.client.text_detection(
                image=image,
                image_context=image_context
            )
            
            # 检查错误
            if response.error.message:
                print(f"❌ MLKit API错误: {response.error.message}")
                return None
            
            # 获取文本注释
            texts = response.text_annotations
            if not texts:
                print("❌ MLKit未检测到任何文本")
                return None
            
            # 第一个注释包含完整文本
            full_text = texts[0].description
            print(f"📝 MLKit识别结果: {full_text}")
            
            # 可选：获取详细的文本块信息
            self._log_detailed_results(texts)
            
            return full_text
            
        except Exception as e:
            print(f"❌ MLKit OCR识别失败: {e}")
            return None

    def _log_detailed_results(self, texts: List) -> None:
        """
        记录详细的识别结果（用于调试）
        
        Args:
            texts: MLKit返回的文本注释列表
        """
        print("🔍 详细识别结果:")
        for i, text in enumerate(texts[1:], 1):  # 跳过第一个完整文本
            print(f"  文本块 {i}: '{text.description}' "
                  f"置信度: {getattr(text, 'confidence', 'N/A')}")

    def parse_id_card_info(self, ocr_text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        从OCR文本中解析身份证信息
        
        Args:
            ocr_text: OCR识别的文本
            
        Returns:
            (姓名, 身份证号码) 元组
        """
        if not ocr_text:
            return None, None
        
        print(f"🔍 解析OCR文本: {ocr_text}")
        
        # 清理文本
        cleaned_text = self._clean_text(ocr_text)
        
        # 提取身份证号码
        id_number = self._extract_id_number(cleaned_text)
        
        # 提取姓名
        name = self._extract_name(cleaned_text)
        
        return name, id_number

    def _clean_text(self, text: str) -> str:
        """清理OCR文本"""
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符但保留中文、数字、字母
        cleaned = re.sub(r'[^\u4e00-\u9fa5\w\s]', ' ', cleaned)
        
        return cleaned

    def _extract_id_number(self, text: str) -> Optional[str]:
        """提取身份证号码"""
        for pattern in self.id_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # 验证身份证号码格式
                if self._validate_id_number(match):
                    print(f"✅ 找到身份证号码: {match}")
                    return match
        
        print("⚠️ 未找到有效的身份证号码")
        return None

    def _extract_name(self, text: str) -> Optional[str]:
        """提取姓名"""
        # 方法1: 查找2-4个连续中文字符
        chinese_pattern = r'[\u4e00-\u9fa5]{2,4}'
        chinese_matches = re.findall(chinese_pattern, text)
        
        for match in chinese_matches:
            if self._is_valid_name(match):
                print(f"✅ 找到姓名: {match}")
                return match
        
        print("⚠️ 未找到有效的姓名")
        return None

    def _is_valid_name(self, name: str) -> bool:
        """验证姓名是否有效"""
        if len(name) < 2 or len(name) > 4:
            return False
        
        # 检查是否包含无关词汇
        for word in self.exclude_words:
            if word in name:
                return False
        
        # 检查是否以常见姓氏开头
        if name[0] in self.common_surnames:
            return True
        
        return False

    def _validate_id_number(self, id_num: str) -> bool:
        """验证身份证号码格式"""
        if len(id_num) == 15:
            return id_num.isdigit()
        elif len(id_num) == 18:
            return id_num[:-1].isdigit() and id_num[-1] in '0123456789Xx'
        return False

    def recognize_id_card(self, image_path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        识别身份证信息的主要接口
        
        Args:
            image_path: 身份证图像路径
            
        Returns:
            (姓名, 身份证号码) 元组
        """
        print(f"🎯 开始MLKit身份证识别: {image_path}")
        
        if not self.is_available:
            print("❌ MLKit OCR 不可用，请检查配置")
            return None, None
        
        # 1. 图像预处理
        image_bytes = self.preprocess_image(image_path)
        if not image_bytes:
            print("❌ 图像预处理失败")
            return None, None
        
        # 2. OCR文本识别
        ocr_text = self.extract_text_with_mlkit(image_bytes)
        if not ocr_text:
            print("❌ OCR文本识别失败")
            return None, None
        
        # 3. 解析身份证信息
        name, id_number = self.parse_id_card_info(ocr_text)
        
        if name and id_number:
            print(f"✅ MLKit识别成功:")
            print(f"   姓名: {name}")
            print(f"   身份证号: {id_number}")
        else:
            print("⚠️ MLKit识别不完整")
        
        return name, id_number

# 便捷函数
def recognize_id_card_simple(image_path: str, credentials_path: Optional[str] = None) -> Tuple[Optional[str], Optional[str]]:
    """
    简单的身份证识别函数
    
    Args:
        image_path: 身份证图像路径
        credentials_path: Google Cloud 凭证文件路径
        
    Returns:
        (姓名, 身份证号码) 元组
    """
    ocr = MLKitOCR(credentials_path)
    return ocr.recognize_id_card(image_path)

if __name__ == "__main__":
    # 测试代码
    test_image = "test_id_card.jpg"
    if os.path.exists(test_image):
        name, id_num = recognize_id_card_simple(test_image)
        if name and id_num:
            print(f"识别结果: {name} - {id_num}")
        else:
            print("识别失败")
    else:
        print("请提供测试图片")
