#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import ddddocr
import io
from PIL import Image

# 初始化ddddocr
ocr = ddddocr.DdddOcr()

def get_captcha():
    """获取验证码并直接使用ddddocr识别"""
    # 设置请求头
    headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Referer": "https://fnxb.xtelh.cn/pages/register/index?code=V4TBZG",
        "Sec-<PERSON>tch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "Priority": "u=1, i"
    }
    
    # 设置代理
    proxies = None  # 默认不使用代理
    
    # 发送请求
    url = "https://fnxb.xtelh.cn/app-api/app/captcha/generate"
    try:
        response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
        
        # 检查响应状态
        if response.status_code == 200:
            # 获取验证码key
            captcha_key = response.headers.get('captcha-key')
            print(f"验证码key: {captcha_key}")
            
            # 直接使用ddddocr识别验证码
            try:
                # 使用ddddocr识别
                result = ocr.classification(response.content)
                
                # 清理结果，确保是5位英文数字混合
                result = result.strip().lower()
                print(f"原始识别结果: {result}")
                
                # 如果长度不是5，尝试修复
                if len(result) != 5:
                    print(f"警告: 识别结果长度不是5位: {result}")
                    # 如果长度大于5，截取前5位
                    if len(result) > 5:
                        result = result[:5]
                    # 如果长度小于5，补充随机字符
                    elif len(result) < 5:
                        import random
                        import string
                        chars = string.ascii_lowercase + string.digits
                        while len(result) < 5:
                            result += random.choice(chars)
                
                print(f"最终识别结果: {result}")
                return captcha_key, result
            except Exception as e:
                print(f"验证码识别失败: {e}")
                return captcha_key, None
        else:
            print(f"获取验证码失败: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"请求异常: {e}")
        return None, None

if __name__ == "__main__":
    captcha_key, captcha_text = get_captcha()
    if captcha_key and captcha_text:
        print(f"验证码key: {captcha_key}")
        print(f"识别的验证码: {captcha_text}")
    else:
        print("获取或识别验证码失败") 