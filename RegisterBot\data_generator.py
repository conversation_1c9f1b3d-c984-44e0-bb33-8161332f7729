import random
import datetime

class DataGenerator:
    def __init__(self):
        self.last_names = [
            "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋", "沈", "韩", "杨",
            "朱", "秦", "尤", "许", "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜"
        ]
        self.first_names = [
            "伟", "芳", "娜", "敏", "静", "秀", "丽", "强", "磊", "军", "洋", "勇", "杰", "娟", "涛", "明",
            "超", "艳", "霞", "平", "刚", "桂", "英", "飞", "建", "华", "国", "强", "玉", "兰", "海", "燕"
        ]
        self.area_codes = {
            "110000": "北京市", "120000": "天津市", "130000": "河北省", "140000": "山西省",
            "150000": "内蒙古自治区", "210000": "辽宁省", "220000": "吉林省", "230000": "黑龙江省",
            "310000": "上海市", "320000": "江苏省", "330000": "浙江省", "340000": "安徽省",
            "350000": "福建省", "360000": "江西省", "370000": "山东省", "410000": "河南省",
            "420000": "湖北省", "430000": "湖南省", "440000": "广东省", "450000": "广西壮族自治区",
            "460000": "海南省", "500000": "重庆市", "510000": "四川省", "520000": "贵州省",
            "530000": "云南省", "540000": "西藏自治区", "610000": "陕西省", "620000": "甘肃省",
            "630000": "青海省", "640000": "宁夏回族自治区", "650000": "新疆维吾尔自治区"
        }
        # 完整的银行卡“档案”列表
        self.card_bins = [
            {'name': '工商银行', 'bin': '622202', 'len': 19},
            {'name': '工商银行', 'bin': '621226', 'len': 19},
            {'name': '农业银行', 'bin': '622848', 'len': 19},
            {'name': '中国银行', 'bin': '621661', 'len': 19},
            {'name': '中国银行', 'bin': '409668', 'len': 16},
            {'name': '建设银行', 'bin': '621700', 'len': 16},
            {'name': '建设银行', 'bin': '436742', 'len': 16},
            {'name': '交通银行', 'bin': '601428', 'len': 19},
            {'name': '招商银行', 'bin': '622588', 'len': 16},
            {'name': '招商银行', 'bin': '621483', 'len': 16},
            {'name': '邮政储蓄银行', 'bin': '622188', 'len': 19},
            {'name': '光大银行', 'bin': '622666', 'len': 16},
            {'name': '民生银行', 'bin': '622622', 'len': 16},
            {'name': '平安银行', 'bin': '622155', 'len': 16},
            {'name': '浦发银行', 'bin': '622521', 'len': 16},
            {'name': '中信银行', 'bin': '622688', 'len': 16},
            {'name': '兴业银行', 'bin': '622908', 'len': 18},
        ]
        # 【修改】将目标银行重新锁定为仅工商银行，以保证成功率
        # 使用更全面的工行借记卡BIN列表，增加卡种多样性
        self.target_bins = [
            {'name': '工商银行', 'bin': '622202', 'len': 19}, # 牡丹灵通卡/e时代卡
            {'name': '工商银行', 'bin': '621226', 'len': 19}, # 牡丹灵通卡/理财金账户
            {'name': '工商银行', 'bin': '955880', 'len': 19}, # 牡丹灵通卡
            {'name': '工商银行', 'bin': '622200', 'len': 19}, # 牡丹国际借记卡
            {'name': '工商银行', 'bin': '622203', 'len': 19}, # 牡丹国际借记卡
            {'name': '工商银行', 'bin': '621225', 'len': 19}, # 牡丹灵通卡
            {'name': '工商银行', 'bin': '955881', 'len': 19}, # 牡丹灵通卡
            {'name': '工商银行', 'bin': '955882', 'len': 19}, # 牡丹灵通卡
        ]
        self.user_agents = [
            "Mozilla/5.0 (Linux; Android 12; M2102K1C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 11; Redmi K30 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 13; V2183A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 10; ONEPLUS A6000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; Android 12; V2055A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Mobile Safari/537.36",
            # ... (可以添加更多)
        ]
        # 补全50个
        base_ua = "Mozilla/5.0 (Linux; Android {android_v}; {model}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_v} Mobile Safari/537.36"
        android_versions = ["10", "11", "12", "13"]
        models = ["Pixel 6", "Galaxy S22", "OnePlus 10 Pro", "Xiaomi 13", "Oppo Find X5", "Vivo X90", "Mate 50 Pro"]
        chrome_versions = ["105.0.0.0", "106.0.0.0", "107.0.0.0", "108.0.0.0"]

        while len(self.user_agents) < 50:
            ua = base_ua.format(
                android_v=random.choice(android_versions),
                model=random.choice(models) + " Build/" + "".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", k=6)),
                chrome_v=random.choice(chrome_versions)
            )
            if ua not in self.user_agents:
                self.user_agents.append(ua)

    def get_random_user_agent(self):
        return random.choice(self.user_agents)

    def generate_random_name(self):
        last_name = random.choice(self.last_names)
        first_name = "".join(random.choices(self.first_names, k=random.randint(1, 2)))
        return f"{last_name}{first_name}"

    def generate_random_id_card(self):
        # 1. 随机地区码
        addr_code = random.choice(list(self.area_codes.keys()))
        
        # 2. 随机生日码
        start_date = datetime.date(1960, 1, 1)
        end_date = datetime.date(2005, 12, 31)
        random_date = start_date + datetime.timedelta(days=random.randint(0, (end_date - start_date).days))
        birth_code = random_date.strftime("%Y%m%d")
        
        # 3. 随机顺序码
        seq_code = f"{random.randint(1, 999):03d}"
        
        # 组合前17位
        id_17 = f"{addr_code}{birth_code}{seq_code}"
        
        # 4. 计算校验码
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = "10X98765432"
        
        s = sum(int(id_17[i]) * weights[i] for i in range(17))
        check_code = check_codes[s % 11]
        
        return f"{id_17}{check_code}"

    def _luhn_checksum(self, card_number):
        """计算Luhn校验和的最后一位"""
        def digits_of(n):
            return [int(d) for d in str(n)]
        digits = digits_of(card_number)
        odd_digits = digits[-1::-2]
        even_digits = digits[-2::-2]
        checksum = 0
        checksum += sum(odd_digits)
        for d in even_digits:
            checksum += sum(digits_of(d * 2))
        return (10 - (checksum % 10)) % 10

    def get_random_bank_and_card(self, stats=None):
        """
        生成随机银行卡号.
        如果提供了stats，则会根据成功率先进行加权随机选择.
        stats 格式: {'bin': {'success': N, 'failure': M}}
        """
        # 如果没有统计数据，或者统计数据不完整，则使用均匀随机选择
        if not stats or len(stats) != len(self.target_bins):
            card_info = random.choice(self.target_bins)
        else:
            # 实现加权随机选择
            population = [b['bin'] for b in self.target_bins]
            weights = []
            for bin_str in population:
                s = stats[bin_str]['success']
                f = stats[bin_str]['failure']
                # 使用拉普拉斯平滑(Laplace smoothing)计算权重，避免除以零或权重为零
                # 这样即使一个BIN从未成功过，它仍然有被选中的机会
                weight = (s + 1) / (s + f + 2)
                weights.append(weight)
            
            # random.choices 返回一个列表，我们取第一个元素
            chosen_bin_str = random.choices(population, weights=weights, k=1)[0]
            # 根据选中的bin字符串找到完整的卡信息
            card_info = next((item for item in self.target_bins if item["bin"] == chosen_bin_str), None)
            if card_info is None: # 安全备用
                 card_info = random.choice(self.target_bins)

        bank_name = card_info['name']
        bin_prefix = card_info['bin']
        card_len = card_info['len']
        
        # 生成卡号的中间部分 (总长度 - 前缀长度 - 1位校验码)
        card_middle_len = card_len - len(bin_prefix) - 1
        if card_middle_len < 0:
             # 这是一个数据问题，直接用前缀
             return bank_name, bin_prefix 

        card_middle = "".join([str(random.randint(0, 9)) for _ in range(card_middle_len)])
        
        # 组合成待计算校验码的卡号前缀
        card_prefix_for_luhn = f"{bin_prefix}{card_middle}"
        
        # 计算Luhn校验码
        try:
            check_digit = self._luhn_checksum(card_prefix_for_luhn)
        except (ValueError, IndexError):
             # 如果卡号前缀不是纯数字，则无法计算
             return bank_name, card_prefix_for_luhn

        # 组合成完整的银行卡号
        card_no = f"{card_prefix_for_luhn}{check_digit}"
        
        return bank_name, card_no, bin_prefix

if __name__ == '__main__':
    # 测试代码
    gen = DataGenerator()
    print("User Agent:", gen.get_random_user_agent())
    name = gen.generate_random_name()
    print("Name:", name)
    print("ID Card:", gen.generate_random_id_card())
    bank, card, used_bin = gen.get_random_bank_and_card()
    print("Bank:", bank)
    print("Card:", card)
    print("Used BIN:", used_bin) 