#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 测试脚本 - 验证设备指纹生成和加密功能

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zhuccc1 import (
    generate_device_fingerprint_for_request,
    generate_sign_parameter,
    generate_real_phone_number,
    generate_random_password
)

def test_device_fingerprint():
    """测试设备指纹生成"""
    print("=" * 50)
    print("🧪 测试设备指纹生成")
    print("=" * 50)
    
    try:
        fingerprint, device_params = generate_device_fingerprint_for_request()
        
        print("✅ 设备指纹生成成功!")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   型号: {fingerprint['model']}")
        print(f"   iOS版本: {fingerprint['ios_version'].replace('_', '.')}")
        print(f"   设备ID: {fingerprint['device_id'][:20]}...")
        print(f"   屏幕: {fingerprint['screen_info']['width']}x{fingerprint['screen_info']['height']}")
        
        print("\n📱 设备参数:")
        for key, value in device_params.items():
            print(f"   {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"❌ 设备指纹生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sign_generation():
    """测试签名生成"""
    print("\n" + "=" * 50)
    print("🔐 测试签名生成")
    print("=" * 50)
    
    try:
        phone_number = generate_real_phone_number()
        password = generate_random_password()

        print(f"测试手机号: {phone_number}")
        print(f"测试密码: {password} (长度: {len(password)}位)")

        sign_value = generate_sign_parameter(phone_number, password)
        
        if sign_value:
            print("✅ 签名生成成功!")
            print(f"签名长度: {len(sign_value)} 字符")
            print(f"签名预览: {sign_value[:100]}...")
            
            # 验证JSON格式
            import json
            try:
                parsed = json.loads(sign_value)
                print("✅ 签名JSON格式验证通过!")
                print(f"   包含字段: {list(parsed.keys())}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ 签名JSON格式错误: {e}")
                return False
        else:
            print("❌ 签名生成失败!")
            return False
            
    except Exception as e:
        print(f"❌ 签名生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 环球网站注册脚本 - 功能测试")
    print("🔧 测试设备指纹生成和加密功能")
    
    # 测试设备指纹生成
    fingerprint_ok = test_device_fingerprint()
    
    # 测试签名生成
    sign_ok = test_sign_generation()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"设备指纹生成: {'✅ 通过' if fingerprint_ok else '❌ 失败'}")
    print(f"签名生成: {'✅ 通过' if sign_ok else '❌ 失败'}")
    
    if fingerprint_ok and sign_ok:
        print("\n🎉 所有测试通过! 脚本准备就绪!")
        print("💡 可以运行 python zhuccc1.py 开始自动注册")
    else:
        print("\n⚠️ 部分测试失败，请检查代码")
        
    return fingerprint_ok and sign_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
