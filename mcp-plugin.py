import os
import sys

if sys.version_info < (3, 11):
    print("[MCP] Warning: Python 3.11+ recommended, but continuing with current version")

import json
import threading
import http.server
from urllib.parse import urlparse
from typing import Any, Callable, get_type_hints, TypedDict, Optional, Annotated

class JSONRPCError(Exception):
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data

class RPCRegistry:
    def __init__(self):
        self.methods: dict[str, Callable] = {}

    def register(self, func: Callable) -> Callable:
        self.methods[func.__name__] = func
        return func

    def dispatch(self, method: str, params: Any) -> Any:
        if method not in self.methods:
            raise JSONRPCError(-32601, f"Method '{method}' not found")

        func = self.methods[method]
        
        if isinstance(params, list):
            return func(*params)
        elif isinstance(params, dict):
            return func(**params)
        else:
            return func()

rpc_registry = RPCRegistry()

def jsonrpc(func: Callable) -> Callable:
    """Decorator to register a function as a JSON-RPC method"""
    global rpc_registry
    return rpc_registry.register(func)

class JSONRPCRequestHandler(http.server.BaseHTTPRequestHandler):
    def send_jsonrpc_error(self, code: int, message: str, id: Any = None):
        response = {
            "jsonrpc": "2.0",
            "error": {
                "code": code,
                "message": message
            }
        }
        if id is not None:
            response["id"] = id

        response_body = json.dumps(response).encode("utf-8")
        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        self.send_header("Content-Length", len(response_body))
        self.end_headers()
        self.wfile.write(response_body)

    def do_POST(self):
        global rpc_registry
        
        parsed_path = urlparse(self.path)
        if parsed_path.path != "/mcp":
            self.send_jsonrpc_error(-32098, "Invalid endpoint", None)
            return

        content_length = int(self.headers.get("Content-Length", 0))
        if content_length == 0:
            self.send_jsonrpc_error(-32700, "Parse error: missing request body", None)
            return

        request_body = self.rfile.read(content_length)

        try:
            request = json.loads(request_body)
        except json.JSONDecodeError:
            self.send_jsonrpc_error(-32700, "Parse error: invalid JSON", None)
            return

        # Prepare the response
        response = {
            "jsonrpc": "2.0"
        }
        if request.get("id") is not None:
            response["id"] = request.get("id")

        try:
            # Basic JSON-RPC validation
            if not isinstance(request, dict):
                raise JSONRPCError(-32600, "Invalid Request")
            if request.get("jsonrpc") != "2.0":
                raise JSONRPCError(-32600, "Invalid JSON-RPC version")
            if "method" not in request:
                raise JSONRPCError(-32600, "Method not specified")

            # Dispatch the method
            result = rpc_registry.dispatch(request["method"], request.get("params", []))
            response["result"] = result

        except JSONRPCError as e:
            response["error"] = {
                "code": e.code,
                "message": e.message
            }
            if e.data is not None:
                response["error"]["data"] = e.data
        except Exception as e:
            import traceback
            traceback.print_exc()
            response["error"] = {
                "code": -32603,
                "message": f"Internal error: {str(e)}",
                "data": traceback.format_exc(),
            }

        try:
            response_body = json.dumps(response).encode("utf-8")
        except Exception as e:
            import traceback
            response_body = json.dumps({
                "jsonrpc": "2.0",
                "error": {
                    "code": -32603,
                    "message": "Internal error (JSON encoding failed)",
                    "data": str(e),
                }
            }).encode("utf-8")

        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        self.send_header("Content-Length", len(response_body))
        self.end_headers()
        self.wfile.write(response_body)

    def log_message(self, format, *args):
        # Suppress logging
        pass

class MCPHTTPServer(http.server.HTTPServer):
    allow_reuse_address = True

class Server:
    HOST = "localhost"
    PORT = 13337

    def __init__(self):
        self.server = None
        self.server_thread = None
        self.running = False

    def start(self):
        if self.running:
            print("[MCP] Server is already running")
            return

        self.server_thread = threading.Thread(target=self._run_server, daemon=True)
        self.running = True
        self.server_thread.start()

    def stop(self):
        if not self.running:
            return

        self.running = False
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        if self.server_thread:
            self.server_thread.join()
        self.server = None
        print("[MCP] Server stopped")

    def _run_server(self):
        try:
            # Create server in the thread to handle binding
            self.server = MCPHTTPServer((Server.HOST, Server.PORT), JSONRPCRequestHandler)
            print(f"[MCP] Server started at http://{Server.HOST}:{Server.PORT}")
            self.server.serve_forever()
        except OSError as e:
            if e.errno == 98 or e.errno == 10048:  # Port already in use (Linux/Windows)
                print("[MCP] Error: Port 13337 is already in use")
            else:
                print(f"[MCP] Server error: {e}")
            self.running = False
        except Exception as e:
            print(f"[MCP] Server error: {e}")
        finally:
            self.running = False

# Import IDA modules
try:
    import idaapi
    import idc
    import idautils
    import ida_nalt
    import ida_funcs
    import ida_kernwin
    import ida_bytes
    import ida_typeinf
    import ida_xref
    import ida_entry
    
    IDA_AVAILABLE = True
    print("[MCP] IDA modules imported successfully")
    
except ImportError as e:
    IDA_AVAILABLE = False
    print(f"[MCP] Warning: Could not import IDA modules: {e}")

# Define MCP functions
@jsonrpc
def check_connection():
    """Check if the IDA plugin is running"""
    if IDA_AVAILABLE:
        try:
            metadata = get_metadata()
            return f"Successfully connected to IDA Pro (open file: {metadata.get('module', 'unknown')})"
        except:
            return "Connected to IDA Pro but no file loaded"
    else:
        return "MCP server running but IDA not available"

@jsonrpc
def get_metadata():
    """Get metadata about the current IDB"""
    if not IDA_AVAILABLE:
        return {"error": "IDA not available"}
    
    try:
        return {
            "path": idaapi.get_input_file_path(),
            "module": idaapi.get_root_filename(),
            "base": hex(idaapi.get_imagebase()),
            "size": hex(idaapi.get_imagebase() + idaapi.get_segm_end(idaapi.get_first_seg()) - idaapi.get_imagebase())
        }
    except Exception as e:
        return {"error": f"Failed to get metadata: {str(e)}"}

@jsonrpc
def get_function_by_name(name: str):
    """Get a function by its name"""
    if not IDA_AVAILABLE:
        return {"error": "IDA not available"}
    
    try:
        ea = idaapi.get_name_ea(idaapi.BADADDR, name)
        if ea == idaapi.BADADDR:
            raise Exception(f"No function found with name {name}")
        func = idaapi.get_func(ea)
        if not func:
            raise Exception(f"No function found at address {hex(ea)}")
        return {
            "address": hex(ea),
            "name": name,
            "size": hex(func.end_ea - func.start_ea)
        }
    except Exception as e:
        return {"error": str(e)}

@jsonrpc
def get_function_by_address(address: str):
    """Get a function by its address"""
    if not IDA_AVAILABLE:
        return {"error": "IDA not available"}
    
    try:
        ea = int(address, 0)
        func = idaapi.get_func(ea)
        if not func:
            raise Exception(f"No function found at address {address}")
        name = idaapi.get_func_name(ea)
        return {
            "address": hex(ea),
            "name": name,
            "size": hex(func.end_ea - func.start_ea)
        }
    except Exception as e:
        return {"error": str(e)}

@jsonrpc
def get_current_address():
    """Get the address currently selected by the user"""
    if not IDA_AVAILABLE:
        return "0x0"
    
    try:
        return hex(idaapi.get_screen_ea())
    except Exception as e:
        return f"Error: {str(e)}"

@jsonrpc
def get_current_function():
    """Get the function currently selected by the user"""
    if not IDA_AVAILABLE:
        return {"error": "IDA not available"}
    
    try:
        ea = idaapi.get_screen_ea()
        func = idaapi.get_func(ea)
        if not func:
            return {"error": "No function at current address"}
        name = idaapi.get_func_name(ea)
        return {
            "address": hex(func.start_ea),
            "name": name,
            "size": hex(func.end_ea - func.start_ea)
        }
    except Exception as e:
        return {"error": str(e)}

@jsonrpc
def list_functions(offset: int = 0, count: int = 100):
    """List all functions in the database (paginated)"""
    if not IDA_AVAILABLE:
        return {"error": "IDA not available"}
    
    try:
        functions = []
        func_list = list(idautils.Functions())
        
        if count == 0:
            count = len(func_list)
        
        for i, ea in enumerate(func_list[offset:offset + count]):
            func = idaapi.get_func(ea)
            if func:
                name = idaapi.get_func_name(ea)
                functions.append({
                    "address": hex(ea),
                    "name": name,
                    "size": hex(func.end_ea - func.start_ea)
                })
        
        next_offset = offset + count if offset + count < len(func_list) else None
        
        return {
            "data": functions,
            "next_offset": next_offset
        }
    except Exception as e:
        return {"error": str(e)}

# IDA Plugin class
if IDA_AVAILABLE:
    class MCP(idaapi.plugin_t):
        flags = idaapi.PLUGIN_KEEP
        comment = "MCP Plugin"
        help = "MCP"
        wanted_name = "MCP"
        wanted_hotkey = "Ctrl-Alt-M"

        def init(self):
            self.server = Server()
            hotkey = MCP.wanted_hotkey.replace("-", "+")
            if sys.platform == "darwin":
                hotkey = hotkey.replace("Alt", "Option")
            print(f"[MCP] Plugin loaded, use Edit -> Plugins -> MCP ({hotkey}) to start the server")
            return idaapi.PLUGIN_KEEP

        def run(self, args):
            self.server.start()

        def term(self):
            self.server.stop()

    def PLUGIN_ENTRY():
        return MCP()

# For testing outside IDA
if __name__ == "__main__":
    server = Server()
    try:
        server.start()
        print("MCP Server running. Press Ctrl+C to stop.")
        import time
        while server.running:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping server...")
        server.stop()
