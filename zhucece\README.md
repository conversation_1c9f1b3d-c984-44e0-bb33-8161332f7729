# 凤鸟星宝自动注册系统

这是一个自动化系统，用于生成身份证图片、识别验证码、自动注册账号并完成实名认证。

## 功能特点

1. 自动生成高清身份证图片
2. 自动识别网站验证码
3. 自动注册账号
4. 自动登录
5. 自动上传身份证进行实名认证
6. 自动保存账号信息

## 目录结构

```
zhuce/              # 主目录
├── README.md       # 说明文档
├── requirements.txt # 依赖包列表
├── fnxb_register.py # 凤鸟星宝网站自动注册脚本
├── extract_id_info.py # 身份证信息提取脚本
├── run_full_process.py # 完整流程运行脚本
└── logs/           # 账号信息保存目录
sfz/                # 身份证生成目录
├── generate.py     # 身份证生成脚本
└── output/         # 身份证图片输出目录
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：运行完整流程

```bash
python run_full_process.py
```

这将自动执行以下步骤：
1. 生成一套新的身份证图片
2. 使用生成的身份证进行自动注册
3. 登录并完成实名认证

### 方法二：单独运行各个步骤

1. 生成身份证图片：

```bash
cd ../sfz
python generate.py
```

2. 提取身份证信息（可选）：

```bash
python extract_id_info.py ../sfz/output/[身份证号]_front.png
```

3. 自动注册：

```bash
python fnxb_register.py
```

## 注意事项

1. 确保已安装Tesseract OCR并正确配置路径
2. 如果使用代理，请在fnxb_register.py中配置正确的代理地址
3. 生成的账号信息会保存在logs目录下
4. 默认使用的邀请码为"V4TBZG"，可以在fnxb_register.py中修改

## 依赖项

- Python 3.6+
- requests
- Pillow
- pytesseract
- ddddocr
- Tesseract OCR（需要单独安装） 