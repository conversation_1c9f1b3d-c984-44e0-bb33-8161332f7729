import requests
import time

# ==============================================================================
# ----------------------------- 好猪马API配置 ----------------------------------
# ==============================================================================

class HaozhumaConfig:
    """好猪马平台配置"""
    # 您的好猪马登录账号
    USERNAME = "your_username"
    # 您的好猪马登录密码
    PASSWORD = "your_password"
    # 服务器地址 (例如: api.haozhuma.com)
    API_BASE_URL = "api.haozhuma.com"
    # 默认的项目ID
    PROJECT_ID = "1000" 

# ==============================================================================
# ------------------------ 好猪马API客户端模块 -----------------------------
# ==============================================================================

class HaozhumaClient:
    """封装所有与好猪马API交互的逻辑"""
    def __init__(self, config: HaozhumaConfig):
        self.config = config
        self.base_url = f"https://{self.config.API_BASE_URL}/sms/"
        self.token = None
        print("好猪马客户端已初始化。")

    def login(self):
        """登录并获取token"""
        print("正在登录好猪马平台...")
        params = {
            "api": "login",
            "user": self.config.USERNAME,
            "pass": self.config.PASSWORD
        }
        try:
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == 0:
                self.token = data.get("token")
                print("✅ 好猪马登录成功！")
                self.get_account_info() # 登录后立即获取并显示余额
                return True
            else:
                print(f"❌ 好猪马登录失败: {data.get('msg', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 好猪马登录时发生网络错误: {e}")
            return False

    def get_account_info(self):
        """获取账户余额等信息"""
        if not self.token:
            print("错误：未登录，请先调用login()")
            return None
        
        params = { "api": "getSummary", "token": self.token }
        try:
            response = requests.get(self.base_url, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 0:
                money = data.get("money", "未知")
                print(f"   账户余额: {money}")
                return data
            else:
                print(f"❌ 获取账户信息失败: {data.get('msg', '未知错误')}")
                return None
        except Exception as e:
            print(f"❌ 获取账户信息时发生网络错误: {e}")
            return None

    def get_phone_number(self, ascription=2):
        """
        获取一个手机号码 (ascription=2 表示只取实卡)
        返回: (bool, str) -> (是否成功, 手机号或错误信息)
        """
        if not self.token:
            return False, "错误：未登录，请先调用login()"

        params = {
            "api": "getPhone",
            "token": self.token,
            "sid": self.config.PROJECT_ID,
            "ascription": ascription 
        }
        params = {k: v for k, v in params.items() if v is not None}
        try:
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            if str(data.get("code")) == "0":
                phone_number = data.get("phone")
                location = f"{data.get('sp', '')} - {data.get('phone_gsd', '')}"
                return True, (phone_number, location)
            else:
                return False, data.get('msg', '返回信息错误')
        except Exception as e:
            return False, f"获取手机号时发生网络错误: {e}"

    def get_sms_code(self, phone_number, max_wait_sec=90, poll_interval_sec=5):
        """
        循环查询短信验证码 (采用椰子云的"耐心"等待逻辑)。
        """
        if not self.token:
            print("错误：未登录")
            return None

        print(f"⏳ 开始等待手机号 {phone_number} 的短信，最长等待 {max_wait_sec} 秒...")
        start_time = time.time()
        
        params = { "api": "getMessage", "token": self.token, "sid": self.config.PROJECT_ID, "phone": phone_number }

        while time.time() - start_time < max_wait_sec:
            try:
                response = requests.get(self.base_url, params=params, timeout=15)
                data = response.json()
                
                # 核心逻辑：只有在code为0且获取到yzm时才算成功，其他所有情况都继续等待
                if str(data.get("code")) == "0" and data.get("yzm"):
                    sms_code = data["yzm"]
                    full_sms = data.get("sms", "")
                    print(f"\n✅ 成功获取到验证码: {sms_code}")
                    print(f"   完整短信内容: {full_sms}")
                    
                    # 成功后自动拉黑
                    self.add_to_blacklist(phone_number)
                    return sms_code
                else:
                    # 其他所有情况（包括API返回任何错误信息），都视为"短信未到"，继续等待
                    print(".", end="", flush=True)
                    time.sleep(poll_interval_sec)

            except Exception as e:
                print(f"\n查询短信时出错: {e}")
                time.sleep(poll_interval_sec)

        print("\n❌ 获取验证码超时。")
        return None
        
    def release_phone_number(self, phone_number):
        """释放一个手机号"""
        if not self.token: return
            
        print(f"正在释放手机号: {phone_number}...")
        params = { "api": "cancelRecv", "token": self.token, "sid": self.config.PROJECT_ID, "phone": phone_number }
        try:
            response = requests.get(self.base_url, params=params, timeout=15)
            data = response.json()
            if str(data.get("code")) == "0":
                print(f"✅ 号码 {phone_number} 已成功释放。")
            else:
                print(f"释放号码 {phone_number} 失败: {data.get('msg')}")
        except Exception as e:
            print(f"释放号码时出错: {e}")

    def add_to_blacklist(self, phone_number):
        """拉黑一个手机号"""
        if not self.token: return
            
        print(f"正在拉黑手机号: {phone_number}...")
        params = { "api": "addBlacklist", "token": self.token, "sid": self.config.PROJECT_ID, "phone": phone_number }
        try:
            response = requests.get(self.base_url, params=params, timeout=15)
            data = response.json()
            if str(data.get("code")) == "0":
                print(f"✅ 号码 {phone_number} 已成功拉黑。")
            else:
                print(f"拉黑号码 {phone_number} 失败: {data.get('msg')}")
        except Exception as e:
            print(f"拉黑号码时出错: {e}")
