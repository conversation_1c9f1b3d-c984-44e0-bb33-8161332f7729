#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键运行脚本 - 自动安装依赖并运行
"""

import subprocess
import sys
import os

def install_packages():
    """安装必要的包"""
    print("🔧 正在安装依赖包...")
    
    packages = [
        "selenium==4.15.2",
        "opencv-python==********", 
        "pillow==10.0.1",
        "numpy==1.24.3",
        "requests==2.31.0",
        "ddddocr==1.4.11",
        "webdriver-manager==4.0.1"
    ]
    
    for package in packages:
        try:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"])
            print(f"✅ {package} 安装成功")
        except Exception as e:
            print(f"❌ {package} 安装失败: {e}")
    
    print("✅ 依赖包安装完成！")

def setup_chromedriver():
    """设置ChromeDriver"""
    print("🚗 设置ChromeDriver...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        ChromeDriverManager().install()
        print("✅ ChromeDriver 设置完成")
        return True
    except Exception as e:
        print(f"❌ ChromeDriver 设置失败: {e}")
        print("💡 请确保已安装Chrome浏览器")
        return False

def main():
    print("🎯 UniApp自动注册系统")
    print("=" * 50)
    
    # 检查是否需要安装依赖
    try:
        import selenium
        import cv2
        import ddddocr
        print("✅ 依赖包已安装")
    except ImportError:
        print("📦 检测到缺少依赖包，开始安装...")
        install_packages()
    
    # 设置ChromeDriver
    if not setup_chromedriver():
        return
    
    # 运行主程序
    print("\n🚀 启动自动注册程序...")
    try:
        from auto_register_complete import UniAppAutoRegister
        
        # 获取注册页面URL
        url = input("请输入注册页面URL (直接回车使用默认): ").strip()
        if not url:
            url = "https://ds-web1.yrpdz.com/pages/login/login?code=21511001"
        
        auto_register = UniAppAutoRegister(url)
        
        while True:
            print("\n🎮 请选择操作:")
            print("1. 单个账号注册")
            print("2. 批量注册")
            print("3. 查看已注册账号")
            print("4. 退出")
            
            choice = input("请输入选择 (1-4): ").strip()
            
            if choice == '1':
                auto_register.auto_register()
            elif choice == '2':
                count = int(input("请输入注册数量: "))
                auto_register.batch_register(count)
            elif choice == '3':
                try:
                    import json
                    with open('registered_accounts.json', 'r', encoding='utf-8') as f:
                        accounts = json.load(f)
                    print(f"\n📋 已注册账号 ({len(accounts)} 个):")
                    for i, account in enumerate(accounts, 1):
                        print(f"{i}. 手机号: {account['phone']}, 密码: {account['password']}, 时间: {account.get('register_time', '未知')}")
                except:
                    print("📋 暂无已注册账号")
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        print("💡 请检查是否正确安装了所有依赖")

if __name__ == "__main__":
    main()
