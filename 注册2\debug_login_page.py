#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试登录页面，查看完整内容
"""

import requests
import re

session = requests.Session()
session.verify = False

# 设置请求头
session.headers.update({
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
})

# 访问登录页面
resp = session.get("https://m.51daili.com/wap/login.html", timeout=10)

print(f"状态码: {resp.status_code}")
print(f"内容长度: {len(resp.text)}")
print("=" * 60)
print("完整页面内容:")
print(resp.text)
print("=" * 60)

# 查找所有可能的token
token_patterns = [
    r'name="__login_token__"\s*value="([^"]*)"',
    r'"__login_token__"\s*value="([^"]*)"',
    r'__login_token__["\']?\s*[:=]\s*["\']([^"\']*)["\']',
    r'login_token["\']?\s*[:=]\s*["\']([^"\']*)["\']',
    r'token["\']?\s*[:=]\s*["\']([a-f0-9]{32})["\']'
]

print("查找Token:")
for i, pattern in enumerate(token_patterns):
    matches = re.findall(pattern, resp.text, re.IGNORECASE)
    if matches:
        print(f"模式 {i+1}: {pattern}")
        print(f"找到: {matches}")
        print()
