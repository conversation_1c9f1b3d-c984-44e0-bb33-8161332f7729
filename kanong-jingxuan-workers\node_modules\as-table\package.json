{"name": "as-table", "version": "1.0.55", "description": "A simple function that print objects / arrays as ASCII tables. Handles ANSI styling and weird 💩 Unicode emoji symbols – they won't break the layout.", "main": "build/as-table.js", "typings": "./as-table.d.ts", "scripts": {"lint": "eslint as-table.js", "lint-test": "eslint test.js", "babel": "babel as-table.js --source-maps inline --out-file ./build/as-table.js", "build": "npm run lint && npm run lint-test && npm run babel", "coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "npm run build && env AS_TABLE_TEST_FILE='./build/as-table' nyc --reporter=html --reporter=text mocha --reporter spec", "autotest": "env AS_TABLE_TEST_FILE='./as-table' mocha --reporter spec --watch"}, "repository": {"type": "git", "url": "https://github.com/xpl/as-table.git"}, "keywords": ["ASCII", "table", "sheet", "grid", "print", "log", "print table", "object as table", "array as table", "text table", "array table", "object table", "array format", "columns", "as table", "tablefy", "columns", "stringify", "print object", "grid", "tty", "terminal", "console", "text", "layout"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/xpl/as-table/issues"}, "homepage": "https://github.com/xpl/as-table", "devDependencies": {"ansicolor": "^1.1.81", "babel-cli": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "coveralls": "^3.0.3", "eslint": "^4.19.1", "istanbul": "^0.4.5", "mocha": "^6.0.2", "nyc": "^13.3.0"}, "dependencies": {"printable-characters": "^1.0.42"}}