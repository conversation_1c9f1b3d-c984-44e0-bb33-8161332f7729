{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio-select/ef063a6ca4c3f0d02d2fc3505e750b6fb81c448d/src/", "sources": ["index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAiB,YAAY,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAC3E,OAAO,EACH,aAAa,IAAI,YAAY,EAE7B,cAAc,GACjB,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AACrC,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AAErC,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC/D,OAAO,EAEH,QAAQ,EAER,QAAQ,GACX,MAAM,kBAAkB,CAAC;AAE1B,oCAAoC;AACpC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAEvD,MAAM,kBAAkB,GAAa;IACjC,IAAI,EAAE,YAAY,CAAC,SAAS;IAC5B,SAAS,EAAE,IAAI;CAClB,CAAC;AACF,MAAM,YAAY,GAAa;IAC3B,IAAI,EAAE,YAAY,CAAC,MAAM;IACzB,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,IAAI;CACb,CAAC;AAOF,MAAM,UAAU,EAAE,CACd,OAAgB,EAChB,QAA6C,EAC7C,UAAmB,EAAE;IAErB,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,IAAI,CAChB,QAAmB,EACnB,QAA6C,EAC7C,UAAmB,EAAE;IAErB,IAAI,OAAO,QAAQ,KAAK,UAAU;QAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEnE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE1D,OAAO,CACH,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACjE,QAAQ,CAAC,IAAI,CACT,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAC/D,CACJ,CAAC;AACN,CAAC;AAED,SAAS,gBAAgB,CACrB,MAAc,EACd,KAAgB,EAChB,IAAkC,EAClC,OAAgB;IAEhB,MAAM,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAEhE,QAAQ,MAAM,EAAE;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,IAAI;YACL,6BAA6B;YAC7B,OAAO,KAAK,CAAC;QACjB,KAAK,MAAM;YACP,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChE,KAAK,KAAK,CAAC;QACX,KAAK,IAAI;YACL,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;gBAChD,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpD,CAAC,CAAC,EAAE,CAAC;QACb,KAAK,IAAI;YACL,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,KAAK,MAAM;YACP,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/C,KAAK,KAAK;YACN,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/C,KAAK,KAAK,CAAC,CAAC;YACR,MAAM,QAAQ,GAAG,IAAI,GAAG,CACpB,YAAY,CAAC,IAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CACrD,CAAC;YAEF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAChD;KACJ;AACL,CAAC;AAED,MAAM,UAAU,MAAM,CAClB,QAAgB,EAChB,QAAmB,EACnB,UAAmB,EAAE;IAErB,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,YAAY,CACjB,QAAsB,EACtB,QAAmB,EACnB,OAAgB;IAEhB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAErC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IACrE,IAAI,KAA+B,CAAC;IAEpC,IAAI,cAAc,CAAC,MAAM,EAAE;QACvB,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAEnE,uCAAuC;QACvC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC;SACnB;QAED,0CAA0C;QAC1C,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC7B;KACJ;IAED,KACI,IAAI,CAAC,GAAG,CAAC,EACT,CAAC,GAAG,iBAAiB,CAAC,MAAM,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,QAAQ,CAAC,MAAM,EAC/D,CAAC,EAAE,EACL;QACE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,KAAK;YACjB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,QAAQ,CAAC;QAEf,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM;QAChC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEvE,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE;gBACR;;;mBAGG;gBACH,IAAI,CAAC,KAAK,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpC,OAAO,QAAQ,CAAC;iBACnB;gBAED,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;aAC7B;iBAAM;gBACH,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5C;SACJ;KACJ;IAED,OAAO,OAAO,KAAK,KAAK,WAAW;QAC/B,CAAC,CAAE,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM;YAC5B,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,oCAAoC;gBACpC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAClB,KAAsB,CAAC,GAAG,CAAC,EAAE,CAAC,CAClC,CAAe;QACxB,CAAC,CAAC,EAAE,CAAC;AACb,CAAC;AAED,SAAS,gBAAgB,CACrB,QAAoB,EACpB,QAAmB,EACnB,OAAgB;;IAEhB,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAC5B;;;WAGG;QACH,MAAM,IAAI,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAM,IAAI,GAAG,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC;QACxE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC1E;IACD,2EAA2E;IAC3E,OAAO,kBAAkB,CACrB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,QAAQ,CAAC,MAAM,CAClB,CAAC;AACN,CAAC;AAED,MAAM,UAAU,MAAM,CAClB,QAA6C,EAC7C,IAAyB,EACzB,UAAmB,EAAE,EACrB,KAAK,GAAG,QAAQ;IAEhB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC/B;IAED,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE1D,MAAM,OAAO,GAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAC9C,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CACtD,CAAC;IAEF,gDAAgD;IAChD,IAAI,KAAK,CAAC,MAAM,EAAE;QACd,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;KAC3D;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,EAAE,CAAC;KACb;IAED,8DAA8D;IAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;KACrB;IAED,yCAAyC;IACzC,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,kBAAkB,CACvB,IAAyB,EACzB,QAAoB,EACpB,OAAgB,EAChB,gBAAyB,EACzB,UAAkB;IAElB,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjD,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAC3C,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAoB,CAAC;IACxD,yFAAyF;IACzF,MAAM,SAAS,GACX,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEhE;;;OAGG;IACH,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAE5D,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAE3B;;;OAGG;IACH,MAAM,YAAY,GACd,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACpC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QACnD,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC;YAClB,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC9D,CAAC,CAAC,gBAAgB,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;gBAC3C,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC;gBAC3C,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;IAE/C,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAE3C,IAAI,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAExE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,EAAE;QAC5D,OAAO,MAAM,CAAC;KACjB;IAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAC1D,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAElE,IAAI,qBAAqB,EAAE;QACvB,IAAI,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC,MAAM,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEtC,IACI,IAAI,KAAK,YAAY,CAAC,OAAO;gBAC7B,IAAI,KAAK,YAAY,CAAC,QAAQ,EAChC;gBACE,wEAAwE;gBACxE,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAc,CAAC;aAChE;YAED,0CAA0C;YAC1C,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SACjD;QAED,OAAO,GAAG;YACN,GAAG,OAAO;YACV,kCAAkC;YAClC,gBAAgB,EAAE,KAAK;YACvB;;;eAGG;YACH,QAAQ,EAAE,CAAC,EAAW,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;SACjD,CAAC;KACL;SAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE;QACnE,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;KACzD;IAED;;;;;;OAMG;IACH,OAAO,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnC,CAAC,CAAC,kBAAkB,CACd,MAAM,EACN,iBAAiB,EACjB,OAAO,EACP,KAAK,EACL,UAAU,CACb;QACH,CAAC,CAAC,qBAAqB;YACvB,CAAC,CAAC,gDAAgD;gBAChD,YAAY,CAAC,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC;YAChE,CAAC,CAAC,gEAAgE;gBAChE,cAAc,CAAC,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/D,CAAC;AAOD,SAAS,YAAY,CACjB,IAAyB,EACzB,GAAiB,EACjB,OAAgB,EAChB,KAAa;IAEb,MAAM,KAAK,GAAkB,YAAY,CACrC,GAAG,EACH,OAAO,EACP,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,IAAI,CACT,IAAyB,EACzB,KAAoB,EACpB,KAAK,GAAG,QAAQ;IAEhB,MAAM,KAAK,GAAG,cAAc,CACxB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,sBAAsB,CAC/B,CAAC;IAEF,OAAO,QAAQ,CAAC,IAAI,CAChB,CAAC,IAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EACtD,KAAK,EACL,IAAI,EACJ,KAAK,CACK,CAAC;AACnB,CAAC;AAED,SAAS,cAAc,CACnB,QAA6B,EAC7B,GAAiB,EACjB,OAAgB;IAEhB,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAChE,QAAQ,CAAC,KAAK,CACjB,CAAC;IAEF,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,GAAG,CAAC;IAEjC,MAAM,KAAK,GAAG,YAAY,CAAmB,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3D,OAAO,KAAK,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC"}