// 🚀 浏览器控制台注入脚本 - 完整版
// 在 ds-web1.yrpdz.com 的登录页面控制台中运行此脚本

console.log('🎯 开始注入完整版自动注册脚本...');

// 清理控制台
console.clear();

// 检查并等待CryptoJS库加载
async function waitForCryptoJS() {
    console.log('🔍 检查CryptoJS库...');

    // 如果CryptoJS已经存在，直接返回
    if (typeof CryptoJS !== 'undefined') {
        console.log('✅ CryptoJS库已加载');
        return true;
    }

    // 尝试从网站加载CryptoJS
    console.log('📥 正在加载CryptoJS库...');

    return new Promise((resolve) => {
        // 方法1: 检查是否有现有的CryptoJS脚本标签
        const existingScript = document.querySelector('script[src*="crypto"]');
        if (existingScript) {
            console.log('🔗 发现现有CryptoJS脚本');
        }

        // 方法2: 动态加载CryptoJS
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js';
        script.onload = () => {
            console.log('✅ CryptoJS库加载成功');
            resolve(true);
        };
        script.onerror = () => {
            console.log('⚠️ 外部CryptoJS加载失败，尝试等待网站自带的库...');

            // 轮询等待CryptoJS
            const checkInterval = setInterval(() => {
                if (typeof CryptoJS !== 'undefined') {
                    console.log('✅ 网站CryptoJS库已加载');
                    clearInterval(checkInterval);
                    resolve(true);
                }
            }, 500);

            // 10秒后超时
            setTimeout(() => {
                clearInterval(checkInterval);
                console.error('❌ CryptoJS库加载超时');
                resolve(false);
            }, 10000);
        };

        document.head.appendChild(script);
    });
}

// 工具函数：等待元素出现
function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }

        const observer = new MutationObserver(() => {
            const element = document.querySelector(selector);
            if (element) {
                observer.disconnect();
                resolve(element);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        setTimeout(() => {
            observer.disconnect();
            resolve(null);
        }, timeout);
    });
}

// 工具函数：模拟用户输入
function simulateUserInput(element, value) {
    if (!element) return false;

    // 聚焦元素
    element.focus();

    // 清空现有值
    element.value = '';

    // 逐字符输入（模拟真实用户行为）
    let index = 0;
    const inputInterval = setInterval(() => {
        if (index < value.length) {
            element.value += value[index];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            index++;
        } else {
            clearInterval(inputInterval);
            element.blur();
        }
    }, 100);

    return true;
}

// 1. 注入加密函数
window.AutoRegister = {
    // AES加密密钥和签名密钥
    aesKey: 'wtBfKcqAMug1wbH8',
    signKey: 'xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY',
    
    // 加密函数（使用网站已有的CryptoJS）
    encryptData: function(data) {
        try {
            const jsonStr = JSON.stringify(data);
            const key = CryptoJS.enc.Utf8.parse(this.aesKey);
            const encrypted = CryptoJS.AES.encrypt(jsonStr, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            return encrypted.toString();
        } catch (e) {
            console.error('❌ 加密失败:', e);
            return null;
        }
    },
    
    // 生成MD5签名
    generateSign: function(data) {
        try {
            // 参数排序
            const sortedKeys = Object.keys(data).sort();
            const sortedParams = sortedKeys.map(key => `${key}=${data[key]}`).join('&');
            const signString = `${sortedParams}&key=${this.signKey}`;
            return CryptoJS.MD5(signString).toString();
        } catch (e) {
            console.error('❌ 签名生成失败:', e);
            return null;
        }
    },
    
    // 发送API请求
    makeAPIRequest: async function(apiPath, data, description = '') {
        console.log(`🚀 ${description}`);
        console.log('📤 请求数据:', data);
        
        try {
            // 加密和签名
            const encrypted = this.encryptData(data);
            const sign = this.generateSign(data);
            
            if (!encrypted || !sign) {
                console.error('❌ 加密或签名失败');
                return null;
            }
            
            console.log('🔐 加密结果:', encrypted.substring(0, 30) + '...');
            console.log('✍️ 签名结果:', sign);
            
            // 构造请求
            const response = await fetch(`https://ds-web1.yrpdz.com${apiPath}`, {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                    'content-type': 'application/json',
                    'is_app': 'false',
                    'origin': 'https://ds-web1.yrpdz.com',
                    'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': navigator.userAgent,
                    'token': '',
                    'sign': sign,
                    'transfersecret': encrypted
                },
                body: JSON.stringify({[encrypted]: encrypted})
            });
            
            console.log('📥 响应状态:', response.status);
            
            const responseText = await response.text();
            console.log('📄 响应内容:', responseText);
            
            // 尝试解密响应
            if (responseText && !responseText.startsWith('<!DOCTYPE html>')) {
                try {
                    const decrypted = this.decryptData(responseText);
                    if (decrypted) {
                        console.log('🔓 解密响应:', decrypted);
                        return decrypted;
                    }
                } catch (e) {
                    console.log('⚠️ 响应可能是明文');
                }
                return responseText;
            } else {
                console.error('❌ 返回HTML页面，可能被拦截');
                return null;
            }
            
        } catch (error) {
            console.error('❌ 请求失败:', error);
            return null;
        }
    },
    
    // 解密响应数据
    decryptData: function(encryptedData) {
        try {
            const key = CryptoJS.enc.Utf8.parse(this.aesKey);
            const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);
            return JSON.parse(decryptedStr);
        } catch (e) {
            console.error('❌ 解密失败:', e);
            return null;
        }
    },
    
    // 生成随机手机号
    generatePhone: function() {
        const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139'];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const suffix = Array.from({length: 8}, () => Math.floor(Math.random() * 10)).join('');
        return prefix + suffix;
    },
    
    // 生成随机密码
    generatePassword: function(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        return Array.from({length}, () => chars[Math.floor(Math.random() * chars.length)]).join('');
    },
    
    // 完整注册流程
    autoRegister: async function() {
        console.log('🎯 开始自动注册流程');
        console.log('=' * 50);
        
        // 生成账号信息
        const phone = this.generatePhone();
        const password = this.generatePassword();
        
        console.log('📱 生成账号信息:');
        console.log('手机号:', phone);
        console.log('密码:', password);
        
        // 步骤1: 获取图片验证码
        console.log('\n📸 步骤1: 获取图片验证码');
        const captchaResult = await this.makeAPIRequest(
            '/dev-api/api/login/authccode.html',
            {},
            '获取图片验证码'
        );
        
        if (captchaResult) {
            console.log('✅ 验证码获取成功');
            
            // 步骤2: 发送短信验证码
            console.log('\n📱 步骤2: 发送短信验证码');
            const smsResult = await this.makeAPIRequest(
                '/dev-api/api/login/smscode.html',
                {
                    phone: phone,
                    code: '' // 图片验证码，如果需要的话
                },
                `发送短信验证码到 ${phone}`
            );
            
            if (smsResult) {
                console.log('✅ 短信发送成功！');
                console.log('📱 请查收短信验证码');
                
                // 提示用户输入短信验证码
                const smsCode = prompt('请输入收到的短信验证码:');
                
                if (smsCode) {
                    // 步骤3: 完成注册
                    console.log('\n📝 步骤3: 完成注册');
                    const registerResult = await this.makeAPIRequest(
                        '/dev-api/api/login/register.html',
                        {
                            phone: phone,
                            password: password,
                            code: smsCode,
                            captcha: '' // 图片验证码，如果需要的话
                        },
                        `注册账号 ${phone}`
                    );
                    
                    if (registerResult) {
                        console.log('🎉 注册成功！');
                        console.log('✅ 账号:', phone);
                        console.log('✅ 密码:', password);
                        
                        // 保存账号信息到本地存储
                        const accounts = JSON.parse(localStorage.getItem('autoRegisteredAccounts') || '[]');
                        accounts.push({phone, password, registerTime: new Date().toISOString()});
                        localStorage.setItem('autoRegisteredAccounts', JSON.stringify(accounts));
                        
                        return {success: true, phone, password};
                    } else {
                        console.error('❌ 注册失败');
                        return {success: false, error: '注册失败'};
                    }
                } else {
                    console.error('❌ 未输入短信验证码');
                    return {success: false, error: '未输入短信验证码'};
                }
            } else {
                console.error('❌ 短信发送失败');
                return {success: false, error: '短信发送失败'};
            }
        } else {
            console.error('❌ 验证码获取失败');
            return {success: false, error: '验证码获取失败'};
        }
    },
    
    // 批量注册
    batchRegister: async function(count = 1) {
        console.log(`🚀 开始批量注册 ${count} 个账号`);
        const results = [];
        
        for (let i = 0; i < count; i++) {
            console.log(`\n🔄 注册第 ${i + 1} 个账号`);
            const result = await this.autoRegister();
            results.push(result);
            
            // 延时避免频率限制
            if (i < count - 1) {
                console.log('⏳ 等待 3 秒...');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
        
        console.log('📊 批量注册结果:', results);
        return results;
    },
    
    // 查看已注册的账号
    getRegisteredAccounts: function() {
        const accounts = JSON.parse(localStorage.getItem('autoRegisteredAccounts') || '[]');
        console.log('📋 已注册的账号:', accounts);
        return accounts;
    }
};

// 初始化函数
async function initAutoRegister() {
    console.log('🚀 初始化自动注册系统...');

    // 等待CryptoJS加载
    const cryptoLoaded = await waitForCryptoJS();

    if (!cryptoLoaded) {
        console.error('❌ CryptoJS库加载失败，无法继续');
        console.log('💡 请刷新页面重试，或手动加载CryptoJS库');
        return false;
    }

    console.log('✅ 自动注册脚本注入完成！');
    console.log('');
    console.log('🎯 使用方法:');
    console.log('1. 单个注册: AutoRegister.autoRegister()');
    console.log('2. 批量注册: AutoRegister.batchRegister(5)');
    console.log('3. 查看账号: AutoRegister.getRegisteredAccounts()');
    console.log('4. 测试验证码: AutoRegister.makeAPIRequest("/dev-api/api/login/authccode.html", {}, "测试验证码")');
    console.log('');
    console.log('💡 提示: 请确保在 ds-web1.yrpdz.com 的登录页面运行此脚本');

    return true;
}

// 自动初始化
initAutoRegister().then(success => {
    if (success) {
        console.log('🎊 系统初始化成功，可以开始使用！');

        // 自动测试API
        console.log('🧪 自动测试API连接...');
        AutoRegister.makeAPIRequest('/dev-api/api/login/authccode.html', {}, '自动测试验证码API');
    } else {
        console.log('💔 系统初始化失败');
    }
});
