import requests
import base64
import json
import time
import random
import string
import os
import concurrent.futures
import threading
import itertools
import urllib3
import sys
sys.path.append('..')
from proxy_manager import ProxyManager

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    import ddddocr
except Exception as e:
    print(f"!!! 导入 ddddocr 库时发生致命错误 !!!")
    print(f"错误类型: {type(e).__name__}")
    print(f"详细信息: {e}")
    print("\n这通常不是一个简单的'未安装'问题。请检查以上详细信息。")
    print("常见原因包括：")
    print("1. ddddocr或其依赖库（onnxruntime）版本冲突。")
    print("2. 缺少必要的系统组件（如 Visual C++ Redistributable）。")
    print("3. 文件损坏或权限问题。")
    exit(1)

try:
    from data_generator import DataGenerator
except ImportError:
    print("错误：找不到 data_generator.py 文件。")
    print("请确保 data_generator.py 与本文件在同一目录下。")
    exit(1)


class ZyguobuRegister:
    def __init__(self, stats=None, stats_lock=None, golden_card=None, golden_card_lock=None):
        self.session = requests.Session()
        # 禁用 SSL 验证
        self.session.verify = False

        self.base_url = "https://www.zyguobu.net"
        # 移除 show_ad=False 参数以兼容旧版 ddddocr
        self.ocr = ddddocr.DdddOcr()
        self.data_gen = DataGenerator()
        self.token = None # 用于保存登录后的token
        self.print_lock = threading.Lock() # 为日志输出添加线程锁
        # 用于动态优化的统计信息
        self.stats = stats
        self.stats_lock = stats_lock
        # 用于100%成功率的"黄金卡"
        self.golden_card = golden_card
        self.golden_card_lock = golden_card_lock
        # 黄金卡文件路径
        self.golden_card_file = "RegisterBot/黄金卡.txt"
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Content-Type": "application/json;charset=UTF-8",
            "Origin": "https://www.zyguobu.net",
        }

    def _log(self, message):
        """带线程锁和线程ID的日志记录"""
        with self.print_lock:
            thread_id = threading.get_ident()
            print(f"[线程 {thread_id}] {message}")

    def save_golden_card_to_file(self, card_number, bank_name):
        """保存黄金卡信息到文件"""
        try:
            with open(self.golden_card_file, 'a', encoding='utf-8') as f:
                f.write(f"黄金卡号: {card_number}, 银行名称: {bank_name}\n")
            self._log(f"黄金卡信息已保存到文件: {card_number} - {bank_name}")
        except Exception as e:
            self._log(f"保存黄金卡信息到文件失败: {e}")

    def load_golden_cards_from_file(self):
        """从文件读取黄金卡信息"""
        golden_cards = []
        try:
            if os.path.exists(self.golden_card_file):
                with open(self.golden_card_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and "黄金卡号:" in line:
                            # 解析格式: "黄金卡号: XXXX, 银行名称: YYYY"
                            parts = line.split(", ")
                            if len(parts) >= 2:
                                card_number = parts[0].replace("黄金卡号: ", "").strip()
                                bank_name = parts[1].replace("银行名称: ", "").strip()
                                golden_cards.append({"card_number": card_number, "bank_name": bank_name})
                self._log(f"从文件读取到 {len(golden_cards)} 张黄金卡信息")
        except Exception as e:
            self._log(f"读取黄金卡文件失败: {e}")
        return golden_cards

    def _update_headers(self, new_headers):
        """更新请求头"""
        self.headers.update(new_headers)
        if self.token:
            self.headers['Authorization'] = self.token

    def get_captcha(self, proxies=None):
        """获取验证码"""
        url = f"{self.base_url}/api/auth/captchaImg"
        try:
            response = self.session.get(url, headers=self.headers, proxies=proxies, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data["code"] == 200:
                key = data["data"]["key"]
                img_base64 = data["data"]["img"]
                img_data = img_base64.split(",")[-1]
                img_bytes = base64.b64decode(img_data)
                return key, img_bytes
        except requests.exceptions.RequestException as e:
            self._log(f"获取验证码网络请求失败: {e}")
        except Exception as e:
            self._log(f"解析验证码响应失败: {e}")
        return None, None
    
    def recognize_captcha(self, img_bytes):
        """使用 ddddocr 识别验证码"""
        try:
            res = self.ocr.classification(img_bytes)
            self._log(f"Ddddocr识别结果: {res}")
            # ddddocr 识别的准确率较高，通常不需要过多处理
            if len(res) >= 4 and res.isalnum():
                 return res
        except Exception as e:
            self._log(f"验证码识别出错: {e}")
        return ""

    def generate_phone(self):
        """生成随机手机号"""
        prefixes = ['135', '136', '137', '138', '139', '150', '151', '152', '157', '158', '159', '182', '183', '187', '188', '198']
        prefix = random.choice(prefixes)
        suffix = ''.join(random.choice(string.digits) for _ in range(8))
        return prefix + suffix

    def register(self, account, password, invite_code, key, captcha, proxies=None):
        """注册账号"""
        url = f"{self.base_url}/api/auth/register"
        data = {
            "account": account,
            "password": password,
            "re_password": password,
            "code": invite_code,
            "key": key,
            "captcha": captcha
        }
        try:
            response = self.session.post(url, headers=self.headers, data=json.dumps(data), proxies=proxies, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                self.token = result['data']['token']
                # 【修复】在这里立刻更新请求头，以包含刚获取到的token
                self._update_headers({})
                self._log(f"注册成功: {account}")
                return True, result['data']['token']
            else:
                self._log(f"注册失败: {result.get('msg', '未知错误')}")
        except requests.exceptions.RequestException as e:
            self._log(f"注册请求失败: {e}")
        except Exception as e:
            self._log(f"解析注册响应失败: {e}")
        return False, None

    def login(self, account, password):
        """使用账号密码登录以获取新的token"""
        self._log("\n----子步骤：执行登录操作----")
        url = f"{self.base_url}/api/auth/login"
        data = {
            "account": account,
            "password": password,
            "key": None,
            "captcha": ""
        }
        try:
            # 使用一个新的会话进行登录，或者重置当前会话，避免旧header干扰
            self.session = requests.Session() 
            # 禁用 SSL 验证
            self.session.verify = False

            self._update_headers({"User-Agent": self.data_gen.get_random_user_agent()})

            response = self.session.post(url, headers=self.headers, data=json.dumps(data), timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                self.token = result['data']['token']
                self._update_headers({}) # 更新包含新token的请求头
                self._log(f"登录成功，已刷新Token: {account}")
                return True, self.token
            else:
                self._log(f"登录失败: {result.get('msg', '未知错误')}")
        except requests.exceptions.RequestException as e:
            self._log(f"登录请求失败: {e}")
        except Exception as e:
            self._log(f"解析登录响应失败: {e}")
        return False, None
        
    def identify(self, name, id_card):
        """实名认证"""
        if not self.token:
            self._log("实名认证失败：缺少token")
            return False
        
        url = f"{self.base_url}/api/user/identify"
        data = {
            "name": name,
            "id_card_no": id_card,
            "id_card_front": "",
            "id_card_end": ""
        }
        try:
            response = self.session.post(url, headers=self.headers, data=json.dumps(data), timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                self._log(f"实名认证成功: {name} - {id_card}")
                return True
            else:
                self._log(f"实名认证失败: {result.get('msg', '未知错误')}")
        except requests.exceptions.RequestException as e:
            self._log(f"实名认证请求失败: {e}")
        except Exception as e:
            self._log(f"解析实名认证响应失败: {e}")
        return False

    def add_bank(self, real_name, bank_name, bank_no):
        """
        绑定银行卡 (简化版，只尝试一次)
        外层循环将处理重试逻辑
        """
        if not self.token:
            self._log("绑卡失败：缺少token")
            return False
        
        url = f"{self.base_url}/api/user/addBank"
        data = {
            "real_name": real_name,
            "bank_name": bank_name,
            "bank_no": bank_no
        }
        
        try:
            response = self.session.post(url, headers=self.headers, data=json.dumps(data), timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                self._log(f"绑卡成功: {bank_name} - {bank_no}")
                # 保存黄金卡信息到文件
                self.save_golden_card_to_file(bank_no, bank_name)
                return True
            else:
                msg = result.get('msg', '未知错误')
                self._log(f"绑卡失败: {msg}")
                return False
        except requests.exceptions.RequestException as e:
            self._log(f"绑卡网络请求失败: {e}")
        except Exception as e:
            self._log(f"解析绑卡响应失败: {e}")
        
        return False

    def sign_in(self):
        """每日签到"""
        if not self.token:
            self._log("签到失败：缺少token")
            return False
        
        url = f"{self.base_url}/api/user/sign"
        try:
            response = self.session.post(url, headers=self.headers, data=json.dumps({}), timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                self._log(f"签到成功: {result.get('msg')}")
                return True
            else:
                self._log(f"签到失败: {result.get('msg', '未知错误')}")
        except requests.exceptions.RequestException as e:
            self._log(f"签到请求失败: {e}")
        except Exception as e:
            self._log(f"解析签到响应失败: {e}")
        return False
    
    def run_full_workflow(self, invite_code, password="123456abc", proxy_manager=None):
        """执行完整的自动化流程"""
        # 0. 重置会话和token，并设置随机UA
        self.session = requests.Session()
        self.session.verify = False  # 禁用 SSL 验证
        self.token = None

        self._update_headers({"User-Agent": self.data_gen.get_random_user_agent()})

        # 获取代理IP（仅用于注册阶段）
        current_proxies = None
        if proxy_manager:
            self._log("正在获取代理IP...")
            current_proxies = proxy_manager.get_proxy(force_new=True)
            if current_proxies:
                self._log(f"使用代理IP: {current_proxies['http']}")
            else:
                self._log("获取代理IP失败，将使用本地IP进行注册")

        # 1. 注册（使用代理IP）
        self._log("----步骤1：开始注册----")
        key, img_bytes = self.get_captcha(proxies=current_proxies)
        if not key or not img_bytes:
            self._log("获取验证码失败，工作流终止。")
            return None

        captcha = self.recognize_captcha(img_bytes)
        if not captcha:
            self._log("验证码识别失败，工作流终止。")
            return None

        account = self.generate_phone()
        reg_success, token = self.register(account, password, invite_code, key, captcha, proxies=current_proxies)
        if not reg_success:
            self._log("注册失败，工作流终止。")
            return None

        # 注册成功后，释放代理IP，后续操作使用本地IP
        if proxy_manager and current_proxies:
            proxy_manager.release_proxy()
            self._log("注册成功！后续操作将使用本地IP")
        
        time.sleep(random.uniform(1, 2))

        # 1.5. 登录
        login_success, new_token = self.login(account, password)
        if not login_success:
            self._log("登录失败，工作流终止。")
            return None # 如果登录是必须的，就终止

        # 准备个人数据
        name = self.data_gen.generate_random_name()
        id_card = self.data_gen.generate_random_id_card()
        
        time.sleep(random.uniform(1, 2))

        # 2. 实名
        self._log("\n----步骤2：开始实名认证----")
        if not self.identify(name, id_card):
            self._log("实名失败，工作流终止。")
            return None
        
        self._log("\n----步骤3：开始绑定银行卡----")
        
        final_bank_name = None
        final_bank_no = None

        # 核心逻辑：先检查是否已发现"黄金卡"
        with self.golden_card_lock:
            use_golden = bool(self.golden_card) # 检查字典是否为空

        if use_golden:
            # --- 收割模式 ---
            bank_name = self.golden_card['bank_name']
            bank_no = self.golden_card['bank_no']
            self._log(f"检测到\"黄金卡\"，直接使用: {bank_name} - {bank_no}")
            if self.add_bank(name, bank_name, bank_no):
                final_bank_name = bank_name
                final_bank_no = bank_no
            else:
                self._log("警告：\"黄金卡\"本次绑定失败。可能该卡有使用次数限制或网站逻辑变更。")
                self._log("...正在切换回\"狩猎模式\"重新寻找新卡...")
                # 黄金卡失效，进入狩猎模式（代码复用下面的循环）
                use_golden = False # 标记以便进入下面的循环
        
        if not use_golden:
            # --- 狩猎模式 ---
            bind_attempts = 0
            while True:
                bind_attempts += 1
                # 根据统计数据动态选择生成新的银行卡信息
                bank_name, bank_no, used_bin = self.data_gen.get_random_bank_and_card(self.stats)
                self._log(f"第 {bind_attempts} 次尝试寻找\"黄金卡\": 使用BIN {used_bin} -> {bank_no}")
                
                success = self.add_bank(name, bank_name, bank_no)

                # 更新统计数据
                if self.stats is not None and self.stats_lock is not None:
                    with self.stats_lock:
                        if success:
                            self.stats[used_bin]['success'] += 1
                        else:
                            self.stats[used_bin]['failure'] += 1
                
                if success:
                    self._log(f"成功找到\"黄金卡\"！: {bank_name} - {bank_no}")
                    final_bank_name = bank_name
                    final_bank_no = bank_no
                    # 发现第一张黄金卡后，将其存入共享变量
                    with self.golden_card_lock:
                        if not self.golden_card: # 双重检查，防止多个线程同时写入
                            self.golden_card['bank_name'] = bank_name
                            self.golden_card['bank_no'] = bank_no
                    break # 成功找到，退出狩猎
                
                self._log("寻找\"黄金卡\"失败，将在2秒后重试...")
                time.sleep(2)

        if not final_bank_name:
            self._log("错误：所有绑卡尝试均失败，工作流终止。")
            return None

        # 4. 签到
        time.sleep(random.uniform(1, 2))
        self._log("\n----步骤4：开始签到----")
        self.sign_in()

        self._log("\n----完整流程结束----")
        return {
            "account": account,
            "password": password,
            "token": new_token,
            "name": name,
            "id_card": id_card,
            # 返回最终绑定成功的银行卡信息
            "bank_name": final_bank_name,
            "bank_no": final_bank_no
        }


# --- Main execution block ---
file_lock = threading.Lock()

def run_and_save_workflow(invite_code, stats, stats_lock, golden_card, golden_card_lock):
    """
    为每个线程创建一个独立的bot实例来运行工作流，
    并使用锁来安全地写入文件。
    """
    bot = ZyguobuRegister(stats, stats_lock, golden_card, golden_card_lock)
    # 这里不需要再打印启动信息，因为_log会自动处理
    result = bot.run_full_workflow(invite_code)
    
    if result:
        # 使用锁来确保文件写入操作的线程安全
        with file_lock:
            try:
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(json.dumps(result, ensure_ascii=False) + "\n")
                # 使用 bot 的 _log 方法来记录文件写入
                bot._log(f"成功将结果写入文件。")
            except IOError as e:
                bot._log(f"文件写入失败: {e}")

if __name__ == "__main__":
    # 交互式输入邀请码
    default_invite_codes = "870276"
    invite_codes_input = input(f"请输入邀请码 (多个用英文逗号,隔开, 默认: {default_invite_codes}): ")
    invite_codes_raw = invite_codes_input.strip() or default_invite_codes
    
    # 解析邀请码
    invite_codes = [code.strip() for code in invite_codes_raw.split(',') if code.strip()]
    if not invite_codes:
        print("错误：未提供有效的邀请码。")
        exit(1)

    # 交互式输入注册数量
    while True:
        try:
            count_input = input("请输入要注册的账号总数 (默认10): ")
            count = int(count_input.strip() or 10)
            if count > 0:
                break
            else:
                print("请输入一个大于0的整数。")
        except ValueError:
            print("无效输入，请输入一个数字。")
            
    # 交互式输入并发线程数
    while True:
        try:
            workers_input = input("请输入并发线程数 (默认5): ")
            max_workers = int(workers_input.strip() or 5)
            if max_workers > 0:
                break
            else:
                print("请输入一个大于0的整数。")
        except ValueError:
            print("无效输入，请输入一个数字。")

    print(f"\n配置: 邀请码={invite_codes}, 总数={count}, 并发数={max_workers}")
    print("任务开始...")

    # 初始化统计字典和锁
    data_gen_for_init = DataGenerator()
    stats = {b['bin']: {'success': 0, 'failure': 0} for b in data_gen_for_init.target_bins}
    stats_lock = threading.Lock()
    # 初始化"黄金卡"共享对象和锁
    golden_card = {}
    golden_card_lock = threading.Lock()

    # 从文件读取已有的黄金卡信息
    temp_register = ZyguobuRegister()
    saved_golden_cards = temp_register.load_golden_cards_from_file()
    if saved_golden_cards:
        # 使用文件中的第一张黄金卡
        first_card = saved_golden_cards[0]
        golden_card['bank_name'] = first_card['bank_name']
        golden_card['bank_no'] = first_card['card_number']
        print(f"从文件读取到黄金卡: {first_card['bank_name']} - {first_card['card_number']}")
    else:
        print("未发现已保存的黄金卡，将进入狩猎模式寻找新的黄金卡")

    # 创建一个邀请码的循环迭代器
    invite_code_cycler = itertools.cycle(invite_codes)

    # 使用线程池执行并发任务
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for i in range(count):
            # 提交任务到线程池
            future = executor.submit(
                run_and_save_workflow, 
                next(invite_code_cycler), 
                stats, 
                stats_lock, 
                golden_card, 
                golden_card_lock
            )
            futures.append(future)
        
        # 等待所有任务完成
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()  # 获取任务结果，可以捕获任务中未处理的异常
            except Exception as exc:
                print(f'一个线程生成了异常: {exc}')

    print("\n\n所有任务执行完毕。")
    # 运行结束后，打印最终的统计结果
    print("\n--- 所有任务完成，BIN成功率统计 ---")
    sorted_stats = sorted(stats.items(), key=lambda item: (item[1]['success'] / (item[1]['success'] + item[1]['failure'] + 1)), reverse=True)
    for bin_code, data in sorted_stats:
        s = data['success']
        f = data['failure']
        total = s + f
        success_rate = (s / total * 100) if total > 0 else 0
        print(f"BIN: {bin_code} | 成功: {s} | 失败: {f} | 成功率: {success_rate:.2f}%")

    if golden_card:
        print("\n--- 本轮发现\"黄金卡\" ---")
        print(f"银行: {golden_card['bank_name']}")
        print(f"卡号: {golden_card['bank_no']}")