#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
验证找到的Token是否真的有效
"""

import requests
import urllib3
from dynamic_cookie_getter import DynamicCookieGetter
from decrypt_analysis import EncryptionSystem

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def quick_test():
    print("🚀 快速验证测试")
    print("=" * 30)
    
    # 初始化
    crypto = EncryptionSystem()
    cookie_getter = DynamicCookieGetter()
    
    # 获取动态cookie
    print("📡 获取动态cookie...")
    cookies = cookie_getter.get_cookies_from_homepage()
    if not cookies:
        print("❌ 无法获取cookie")
        return
    
    cookie_header = "; ".join([f"{k}={v}" for k, v in cookies.items()])
    print(f"✅ Cookie: {cookie_header}")
    
    # 测试空Token
    print("\n🧪 测试空Token...")
    
    # 准备数据
    test_data = {}
    encrypted = crypto.encrypt_data(test_data)
    sign = crypto.generate_sign(test_data)
    
    # 构造请求头
    headers = {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'content-type': 'application/json',
        'cookie': cookie_header,
        'is_app': 'false',
        'origin': 'https://ds-web1.yrpdz.com',
        'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'token': '',  # 空Token
        'sign': sign,
        'transfersecret': encrypted
    }
    
    # 请求体
    request_body = {encrypted: encrypted}
    
    try:
        response = requests.post(
            "https://ds-web1.yrpdz.com/dev-api/api/login/authccode.html",
            headers=headers,
            json=request_body,
            timeout=10,
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        if response.text.strip().startswith('<!DOCTYPE html>'):
            print("❌ 返回HTML页面")
        else:
            print("✅ 返回API响应")
            print(f"响应内容: {response.text}")
            
            # 尝试解密
            try:
                decrypted = crypto.decrypt_data(response.text)
                if decrypted:
                    print(f"✅ 解密成功: {decrypted}")
                else:
                    print("⚠️ 可能是明文响应")
            except:
                print("⚠️ 解密失败")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    quick_test()
