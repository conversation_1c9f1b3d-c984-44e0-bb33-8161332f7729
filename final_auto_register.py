#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终自动注册脚本
基于完全破解的加密系统和认证机制
"""

import base64
import hashlib
import json
import requests
import urllib3
import random
import string
import time
from urllib.parse import urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FinalAutoRegister:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.session = requests.Session()
        
        # 破解的加密参数
        self.aes_key = "wtBfKcqAMug1wbH8"
        self.sign_key = "xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY"
        
        # API接口
        self.apis = {
            "captcha": "/dev-api/api/login/authccode.html",
            "sms": "/dev-api/api/login/smscode.html", 
            "register": "/dev-api/api/login/register.html"
        }
        
        # 动态获取的cookie
        self.cookies = {}
        
        print("🤖 最终自动注册脚本启动")
        print("🔐 基于完全破解的加密系统")
        print("=" * 50)

    def get_dynamic_cookies(self):
        """动态获取有效cookie"""
        print("📡 正在获取动态cookie...")
        
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        }
        
        try:
            # 访问主页获取cookie
            response = self.session.get(
                self.base_url,
                headers=headers,
                timeout=10,
                verify=False
            )
            
            # 访问登录页面
            login_url = f"{self.base_url}/pages/login/login?code=21328050"
            response = self.session.get(
                login_url,
                headers=headers,
                timeout=10,
                verify=False
            )
            
            # 提取cookie
            for cookie in self.session.cookies:
                self.cookies[cookie.name] = cookie.value
            
            print(f"✅ 成功获取 {len(self.cookies)} 个cookie")
            for name, value in self.cookies.items():
                print(f"  🍪 {name}: {value[:20]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取cookie失败: {e}")
            return False

    def encrypt_data(self, data):
        """AES加密数据"""
        try:
            json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
            data_bytes = json_str.encode('utf-8')
            padded_data = pad(data_bytes, AES.block_size)
            cipher = AES.new(self.aes_key.encode('utf-8'), AES.MODE_ECB)
            encrypted = cipher.encrypt(padded_data)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"❌ 加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data):
        """AES解密数据"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = AES.new(self.aes_key.encode('utf-8'), AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)
            json_str = unpadded_data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            print(f"❌ 解密失败: {e}")
            return None

    def generate_sign(self, data):
        """生成MD5签名"""
        try:
            sorted_data = dict(sorted(data.items()))
            query_string = urlencode(sorted_data, doseq=True)
            sign_string = f"{query_string}&key={self.sign_key}"
            return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        except Exception as e:
            print(f"❌ 签名生成失败: {e}")
            return None

    def make_api_request(self, api_path, data, description=""):
        """发送API请求"""
        print(f"\n🚀 {description}")
        print("-" * 30)
        
        try:
            # 加密数据
            encrypted = self.encrypt_data(data)
            if not encrypted:
                return None
            
            # 生成签名
            sign = self.generate_sign(data)
            if not sign:
                return None
            
            # 格式化cookie
            cookie_header = "; ".join([f"{k}={v}" for k, v in self.cookies.items()])
            
            # 构造请求头
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': cookie_header,
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'token': '',  # 使用空token（已验证有效）
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            print(f"📤 请求数据: {data}")
            print(f"🔐 加密后: {encrypted[:30]}...")
            print(f"✍️ 签名: {sign}")
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}{api_path}",
                headers=headers,
                json=request_body,
                timeout=15,
                verify=False
            )
            
            print(f"📥 响应状态: {response.status_code}")
            print(f"📏 响应长度: {len(response.text)}")
            
            # 检查响应类型
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("❌ 返回HTML页面，请求可能被拦截")
                return None
            
            print(f"📄 响应内容: {response.text}")
            
            # 尝试解密响应
            try:
                decrypted_response = self.decrypt_data(response.text)
                if decrypted_response:
                    print(f"🔓 解密响应: {decrypted_response}")
                    return decrypted_response
                else:
                    print("⚠️ 响应可能是明文格式")
                    return response.text
            except:
                print("⚠️ 响应不是加密格式")
                return response.text
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None

    def get_captcha(self):
        """获取图片验证码"""
        return self.make_api_request(
            self.apis["captcha"], 
            {}, 
            "获取图片验证码"
        )

    def send_sms_code(self, phone, captcha_code=""):
        """发送短信验证码"""
        sms_data = {
            "phone": phone,
            "code": captcha_code
        }
        return self.make_api_request(
            self.apis["sms"], 
            sms_data, 
            f"发送短信验证码到 {phone}"
        )

    def register_account(self, phone, password, sms_code, captcha_code=""):
        """注册账号"""
        register_data = {
            "phone": phone,
            "password": password,
            "code": sms_code,
            "captcha": captcha_code
        }
        return self.make_api_request(
            self.apis["register"], 
            register_data, 
            f"注册账号 {phone}"
        )

    def generate_phone(self):
        """生成随机手机号"""
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix

    def generate_password(self, length=8):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))

    def auto_register_process(self):
        """完整的自动注册流程"""
        print("\n🎯 开始自动注册流程")
        print("=" * 50)
        
        # 1. 获取动态cookie
        if not self.get_dynamic_cookies():
            print("❌ 无法获取cookie，注册失败")
            return False
        
        # 2. 生成账号信息
        phone = self.generate_phone()
        password = self.generate_password()
        
        print(f"\n📱 生成账号信息:")
        print(f"手机号: {phone}")
        print(f"密码: {password}")
        
        # 3. 获取图片验证码
        captcha_result = self.get_captcha()
        if not captcha_result:
            print("❌ 获取验证码失败")
            return False
        
        # 4. 发送短信验证码
        sms_result = self.send_sms_code(phone)
        if not sms_result:
            print("❌ 发送短信失败")
            return False
        
        # 5. 手动输入短信验证码
        print("\n⌨️ 请手动输入短信验证码:")
        sms_code = input("短信验证码: ").strip()
        
        if not sms_code:
            print("❌ 未输入短信验证码")
            return False
        
        # 6. 注册账号
        register_result = self.register_account(phone, password, sms_code)
        
        if register_result:
            print("\n🎉 注册成功！")
            print(f"账号: {phone}")
            print(f"密码: {password}")
            return True
        else:
            print("\n❌ 注册失败")
            return False


if __name__ == "__main__":
    # 创建自动注册实例
    auto_register = FinalAutoRegister()
    
    # 执行自动注册
    success = auto_register.auto_register_process()
    
    if success:
        print("\n🎊 自动注册完成！")
    else:
        print("\n💔 注册失败，请检查日志")
