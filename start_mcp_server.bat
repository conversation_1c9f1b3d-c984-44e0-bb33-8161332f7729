@echo off
echo Starting IDA Pro MCP Server...
echo.

REM Set the paths
set VENV_PYTHON=C:\Users\<USER>\Desktop\hook\zhuce\.venv-mcp\Scripts\python.exe
set SERVER_SCRIPT=C:\Users\<USER>\Desktop\hook\zhuce\ida-pro-mcp-new\src\ida_pro_mcp\server.py

REM Check if files exist
if not exist "%VENV_PYTHON%" (
    echo ERROR: Virtual environment Python not found at %VENV_PYTHON%
    pause
    exit /b 1
)

if not exist "%SERVER_SCRIPT%" (
    echo ERROR: Server script not found at %SERVER_SCRIPT%
    pause
    exit /b 1
)

echo Files found, starting server...
echo.

REM Start the MCP server with unsafe mode
"%VENV_PYTHON%" "%SERVER_SCRIPT%" --unsafe --transport stdio

echo.
echo Server stopped.
pause
