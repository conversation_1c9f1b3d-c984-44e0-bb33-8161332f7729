name = "kanong-jingxuan"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[[kv_namespaces]]
binding = "POST_CACHE"
id = "89380612b59b4c5a9e05b61e32539916"

[triggers]
crons = [ "0 */2 * * *" ] # 每2小时执行一次

[vars]
# 【重要】已集成您提供的Gemini API密钥。
# 【安全提示】为了您的账户安全，强烈建议您在完成测试后，为此项目生成一个专用的、限制权限的新API密钥。
GEMINI_API_KEY = "AIzaSyBAapmvw5zq9s3ro2EjGH4NgY0BNhAODgw"
SITE_NAME = "卡农精选"
SITE_DESCRIPTION = "专注卡农论坛优质内容分享"
ENVIRONMENT = "production" 