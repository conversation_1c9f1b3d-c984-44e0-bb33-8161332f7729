#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 简单密码格式测试

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_passwords():
    """测试简单的密码格式"""
    print("🧪 简单密码格式测试")
    print("=" * 40)
    
    # 测试密码列表
    test_passwords = [
        "123456",      # 6位纯数字
        "abc123",      # 小写字母+数字
        "Abc123",      # 大写+小写+数字
        "password1",   # 小写+数字
        "Password1",   # 大小写+数字
        "12345abc",    # 数字+小写
        "123Abc",      # 数字+大小写
        "abcdef",      # 6位纯字母
        "ABCDEF",      # 6位大写字母
        "a1b2c3",      # 交替格式
    ]
    
    print("📋 建议手动测试的密码格式:")
    for i, pwd in enumerate(test_passwords, 1):
        length = len(pwd)
        has_lower = any(c.islower() for c in pwd)
        has_upper = any(c.isupper() for c in pwd)
        has_digit = any(c.isdigit() for c in pwd)
        
        print(f"   {i:2d}. {pwd:12s} (长度:{length}, 小写:{has_lower}, 大写:{has_upper}, 数字:{has_digit})")
    
    print("\n💡 测试建议:")
    print("1. 先测试最简单的格式: 123456")
    print("2. 然后测试混合格式: abc123")
    print("3. 如果还不行，尝试大写字母: Abc123")
    print("4. 最后尝试常见密码: password1")
    
    print("\n🔧 手动测试步骤:")
    print("1. 运行主脚本: python zhuccc1.py")
    print("2. 选择使用自定义密码")
    print("3. 输入上面列表中的密码进行测试")
    print("4. 观察服务器响应，找出接受的格式")

def analyze_current_issue():
    """分析当前问题"""
    print("\n🔍 当前问题分析")
    print("=" * 40)
    
    print("📝 当前情况:")
    print("   密码: y7ojohhkAt (10位)")
    print("   格式: 小写字母 + 大写字母 + 数字")
    print("   长度: 符合6-11位要求")
    print("   服务器响应: '登录密码限制6-11位字符'")
    
    print("\n🤔 可能的原因:")
    print("1. 服务器对字符类型有特殊要求")
    print("2. 不允许某些字符组合")
    print("3. 密码必须以特定字符开头")
    print("4. 加密过程中出现问题")
    print("5. 请求格式不正确")
    
    print("\n💡 解决方案:")
    print("1. 测试最简单的密码格式")
    print("2. 启用代理IP进行注册")
    print("3. 对比成功注册的请求数据")
    print("4. 检查加密算法是否正确")

def show_proxy_setup():
    """显示代理设置说明"""
    print("\n🌐 代理设置说明")
    print("=" * 40)
    
    print("📝 根据你的要求:")
    print("   注册请求: 使用代理IP")
    print("   登录请求: 使用代理IP")
    print("   其他请求: 使用直连")
    
    print("\n🔧 已实现的功能:")
    print("1. 注册时会询问是否启用代理")
    print("2. 如果启用，注册和登录使用代理")
    print("3. 验证码获取等其他请求使用直连")
    
    print("\n🚀 使用方法:")
    print("1. 确保代理配置正确")
    print("2. 运行脚本时选择启用代理")
    print("3. 脚本会自动为注册/登录使用代理")

def main():
    """主函数"""
    print("🧪 密码和代理问题解决方案")
    print("🔧 针对'登录密码限制6-11位字符'错误")
    print("=" * 50)
    
    # 分析当前问题
    analyze_current_issue()
    
    # 测试密码格式
    test_simple_passwords()
    
    # 代理设置说明
    show_proxy_setup()
    
    print("\n" + "=" * 50)
    print("📊 总结:")
    print("1. 密码问题: 尝试更简单的格式")
    print("2. 代理问题: 已实现注册/登录使用代理")
    print("3. 建议: 先用简单密码+代理测试")
    
    print("\n🎯 下一步行动:")
    print("1. 运行 python decrypt_sign_data.py 分析加密数据")
    print("2. 运行主脚本并启用代理")
    print("3. 使用简单密码格式测试")
    print("4. 观察服务器响应变化")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 分析被用户中断")
    except Exception as e:
        print(f"\n💥 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
