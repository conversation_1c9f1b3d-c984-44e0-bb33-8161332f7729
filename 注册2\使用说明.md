# 51代理自动签到脚本使用说明

## 📋 项目概述

本项目为51代理网站提供自动登录和签到功能，支持处理滑块验证码和反爬虫机制。

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.7+
- 稳定的网络连接

### 2. 安装依赖

```bash
# 安装基础依赖（推荐）
pip install requests urllib3

# 或安装完整依赖（如果需要Selenium功能）
pip install -r requirements.txt
```

### 3. 配置账号

编辑任意脚本文件，修改账号信息：

```python
ACCOUNT = "your_username"    # 替换为您的51代理账号
PASSWORD = "your_password"   # 替换为您的51代理密码
```

或设置环境变量：

```bash
# Windows
set DAILI_ACCOUNT=your_username
set DAILI_PASSWORD=your_password

# Linux/Mac
export DAILI_ACCOUNT="your_username"
export DAILI_PASSWORD="your_password"
```

## 📁 脚本说明

### 🔥 推荐使用

#### `51daili_simple_only.py` - 纯requests版本
- **优点**: 无需额外依赖，运行速度快，资源占用少
- **适用**: 网站反爬虫较弱的情况
- **推荐指数**: ⭐⭐⭐⭐⭐

```bash
python 51daili_simple_only.py
```

#### `51daili_smart.py` - 智能检测版本
- **优点**: 自动检测登录方式和验证码类型
- **适用**: 需要分析网站结构的情况
- **推荐指数**: ⭐⭐⭐⭐

```bash
python 51daili_smart.py
```

### 🛠️ 高级功能

#### `51daili_advanced_login.py` - Selenium版本
- **优点**: 支持复杂验证码，模拟真实浏览器
- **缺点**: 需要安装Chrome和ChromeDriver
- **适用**: 网站反爬虫较强的情况

#### `51daili_final.py` - 最终版本
- **优点**: 综合多种登录方式
- **适用**: 作为备用方案

### 🎯 辅助工具

#### `start.py` - 启动界面
提供友好的图形化操作界面

```bash
python start.py
```

#### `scheduler.py` - 定时任务
支持定时自动执行签到

```bash
# 立即执行一次
python scheduler.py --now

# 后台守护进程
python scheduler.py --daemon
```

## ⚙️ 配置选项

### config.py 配置文件

```python
# 基础配置
ACCOUNT = "your_username"          # 账号
PASSWORD = "your_password"         # 密码
HEADLESS = True                    # 无头模式（仅Selenium版本）

# 延迟配置
LOGIN_DELAY = (2, 4)              # 登录延迟范围
TYPE_DELAY = (0.05, 0.15)         # 打字延迟范围
SLIDER_DELAY = (0.01, 0.02)       # 滑块移动延迟

# 重试配置
MAX_RETRY = 3                     # 最大重试次数
RETRY_DELAY = 5                   # 重试间隔
```

## 🕐 定时任务设置

### Windows 任务计划程序

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器：每天
4. 设置操作：
   - 程序：`python.exe`
   - 参数：`51daili_simple_only.py`
   - 起始位置：脚本所在目录

### Linux/Mac Crontab

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天上午9点）
0 9 * * * cd /path/to/script && python 51daili_simple_only.py
```

### 青龙面板

```bash
# 添加定时任务
0 9 * * * python /path/to/51daili_simple_only.py

# 设置环境变量
export DAILI_ACCOUNT="your_username"
export DAILI_PASSWORD="your_password"
```

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install requests urllib3 -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2. 登录失败
- ✅ 确认账号密码正确
- ✅ 检查网络连接
- ✅ 尝试不同的脚本版本
- ✅ 查看日志文件获取详细错误

#### 3. 验证码问题
- ✅ 使用 `51daili_advanced_login.py`（需要安装Selenium）
- ✅ 设置 `HEADLESS = False` 观察验证过程
- ✅ 手动完成一次登录后再运行脚本

#### 4. 网络超时
- ✅ 检查网络连接稳定性
- ✅ 增加超时时间
- ✅ 使用代理（如果需要）

### 日志分析

脚本会生成详细的日志文件：
- `51daili_simple_only.log` - 简化版日志
- `51daili_smart.log` - 智能版日志
- `51daili_advanced.log` - 高级版日志

日志包含：
- 登录过程详情
- 错误信息和原因
- 签到执行状态
- 网络请求响应

## 📊 成功率优化建议

### 1. 选择合适的脚本版本
- 首选：`51daili_simple_only.py`
- 备选：`51daili_smart.py`
- 最后：`51daili_advanced_login.py`

### 2. 优化运行时间
- 避开网站维护时间
- 选择网络稳定的时段
- 建议在上午9-11点或下午2-4点执行

### 3. 降低检测风险
- 不要过于频繁执行（建议每天1-2次）
- 使用随机延迟
- 避免同时运行多个实例

### 4. 网络环境
- 确保网络稳定
- 避免使用公共WiFi
- 如有需要可配置代理

## ⚠️ 注意事项

### 安全提醒
1. **账号安全**: 妥善保管账号密码，建议使用环境变量
2. **合规使用**: 遵守网站服务条款，避免过度使用
3. **数据保护**: 日志文件可能包含敏感信息，注意保护

### 使用限制
1. **频率控制**: 建议每天执行1-2次，避免对服务器造成压力
2. **环境要求**: 确保网络环境稳定，避免在不稳定网络下运行
3. **更新维护**: 网站可能更新验证机制，需要及时更新脚本

### 法律声明
- 本工具仅供学习和个人使用
- 使用者需遵守相关法律法规
- 不得用于商业用途
- 使用风险由使用者自行承担

## 🆘 获取帮助

如果遇到问题：

1. **查看日志**: 检查对应的日志文件获取详细错误信息
2. **检查配置**: 确认账号密码和网络设置正确
3. **尝试不同版本**: 如果一个脚本失败，尝试其他版本
4. **手动测试**: 先手动登录网站确认账号正常

## 🔄 更新日志

- **v1.0**: 基础登录签到功能
- **v1.1**: 添加智能检测功能
- **v1.2**: 优化错误处理和重试机制
- **v1.3**: 添加多种登录方式支持
- **v1.4**: 改进网络连接和超时处理

---

**祝您使用愉快！** 🎉
