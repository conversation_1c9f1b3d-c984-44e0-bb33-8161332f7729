#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WAF绕过测试脚本 - 专门针对302重定向问题
"""

import requests
import json
import time
import hashlib
import base64
import logging
import random
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WAFBypassTester:
    def __init__(self):
        self.session = requests.Session()
        self.aes_key = "46505501ee188273".encode('utf-8')
        self.base_url = "https://zgwdb-app1.rizi666.com/dev-api"
        
    def aes_encrypt(self, data: dict) -> str:
        """已验证正确的AES加密"""
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
        cipher = AES.new(self.aes_key, AES.MODE_ECB)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return "mc543" + base64.b64encode(encrypted).decode('utf-8')
    
    def generate_sign(self, data: dict) -> str:
        """生成签名"""
        timestamp = data.get('time', int(time.time()))
        return hashlib.md5(f"time={timestamp}".encode('utf-8')).hexdigest()
    
    def init_session_carefully(self):
        """谨慎地初始化会话"""
        logger.info("=== 谨慎初始化会话 ===")
        
        # 步骤1: 访问主页
        try:
            main_url = "https://zgwdb-app1.rizi666.com"
            headers = {
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Upgrade-Insecure-Requests": "1"
            }
            
            response = self.session.get(main_url, headers=headers, timeout=10)
            logger.info(f"主页访问: {response.status_code}")
            logger.info(f"获取Cookie: {dict(self.session.cookies)}")
            
            # 等待一下，模拟真实用户行为
            time.sleep(random.uniform(1, 3))
            
        except Exception as e:
            logger.error(f"主页访问失败: {e}")
            return False
            
        # 步骤2: 访问登录页
        try:
            login_url = "https://zgwdb-app1.rizi666.com/pages/login/login"
            headers.update({
                "Referer": main_url,
                "Sec-Fetch-Site": "same-origin"
            })
            
            response = self.session.get(login_url, headers=headers, timeout=10)
            logger.info(f"登录页访问: {response.status_code}")
            
            # 再等待一下
            time.sleep(random.uniform(2, 4))
            
        except Exception as e:
            logger.error(f"登录页访问失败: {e}")
            return False
            
        return True
    
    def test_api_with_different_strategies(self):
        """使用不同策略测试API"""
        logger.info("=== 测试不同的WAF绕过策略 ===")
        
        timestamp = int(time.time())
        request_data = {"time": timestamp}
        encrypted_data = self.aes_encrypt(request_data)
        sign = self.generate_sign(request_data)
        
        api_url = f"{self.base_url}/api/login/authccode.html"
        
        # 策略1: 完全模拟浏览器请求
        strategy1_headers = {
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "content-type": "application/json",
            "origin": "https://zgwdb-app1.rizi666.com",
            "priority": "u=1, i",
            "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sign": sign,
            "token": "",
            "transfersecret": encrypted_data,
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        }
        
        # 策略2: 简化请求头
        strategy2_headers = {
            "accept": "*/*",
            "content-type": "application/json",
            "origin": "https://zgwdb-app1.rizi666.com",
            "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
            "sign": sign,
            "token": "",
            "transfersecret": encrypted_data,
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        }
        
        # 策略3: 分步骤请求
        strategy3_headers = {
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://zgwdb-app1.rizi666.com",
            "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
            "sign": sign,
            "token": "",
            "transfersecret": encrypted_data,
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "x-requested-with": "XMLHttpRequest"
        }
        
        strategies = [
            ("完全模拟浏览器", strategy1_headers),
            ("简化请求头", strategy2_headers),
            ("分步骤请求", strategy3_headers)
        ]
        
        for strategy_name, headers in strategies:
            logger.info(f"\n--- 测试策略: {strategy_name} ---")
            
            try:
                # 添加随机延迟
                time.sleep(random.uniform(1, 2))
                
                response = self.session.post(
                    api_url,
                    headers=headers,
                    json=request_data,
                    timeout=10,
                    allow_redirects=False
                )
                
                logger.info(f"[{strategy_name}] 状态码: {response.status_code}")
                logger.info(f"[{strategy_name}] 响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.info(f"[{strategy_name}] ✅ 成功! 响应: {result}")
                        return True, strategy_name, headers
                    except:
                        logger.info(f"[{strategy_name}] 响应内容: {response.text[:200]}...")
                        
                elif response.status_code == 302:
                    location = response.headers.get('Location', 'Unknown')
                    logger.warning(f"[{strategy_name}] 被重定向到: {location}")
                    
                    # 如果被重定向到微博，说明被WAF拦截了
                    if 'weibo.com' in location:
                        logger.error(f"[{strategy_name}] ❌ 被WAF拦截，重定向到微博")
                    
                else:
                    logger.warning(f"[{strategy_name}] 其他状态码: {response.text[:200]}...")
                    
            except Exception as e:
                logger.error(f"[{strategy_name}] 请求异常: {e}")
        
        return False, None, None
    
    def test_timing_attack(self):
        """测试时间间隔对WAF的影响"""
        logger.info("=== 测试请求时间间隔 ===")
        
        intervals = [0.5, 1, 2, 5, 10]  # 不同的时间间隔（秒）
        
        for interval in intervals:
            logger.info(f"\n--- 测试间隔: {interval}秒 ---")
            
            # 重新初始化会话
            self.session = requests.Session()
            self.init_session_carefully()
            
            # 等待指定时间
            time.sleep(interval)
            
            # 发送请求
            success, strategy, headers = self.test_api_with_different_strategies()
            
            if success:
                logger.info(f"✅ 成功! 最佳间隔: {interval}秒, 策略: {strategy}")
                return True, interval, strategy, headers
            
            logger.info(f"间隔 {interval}秒 失败")
        
        return False, None, None, None

def main():
    """主测试函数"""
    logger.info("开始WAF绕过测试...")
    
    tester = WAFBypassTester()
    
    # 初始化会话
    if not tester.init_session_carefully():
        logger.error("会话初始化失败")
        return
    
    # 测试不同策略
    success, strategy, headers = tester.test_api_with_different_strategies()
    
    if success:
        logger.info(f"🎉 找到有效策略: {strategy}")
        return
    
    # 如果基本策略失败，测试时间间隔
    logger.info("基本策略失败，测试时间间隔...")
    success, interval, strategy, headers = tester.test_timing_attack()
    
    if success:
        logger.info(f"🎉 找到有效方案: 间隔{interval}秒 + {strategy}")
    else:
        logger.error("❌ 所有WAF绕过策略都失败了")
        logger.info("建议:")
        logger.info("1. 检查IP是否被封禁")
        logger.info("2. 尝试使用代理")
        logger.info("3. 分析更多浏览器请求特征")

if __name__ == "__main__":
    main()
