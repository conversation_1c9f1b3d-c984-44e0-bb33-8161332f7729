#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯 - 自动注册脚本 (V18 - 自定义昵称后缀)
#
# 在最终成功版本基础上，增加了批量注册时自定义昵称后缀的功能。

import requests
import json
import base64
import random
import string
import time
import hmac
import hashlib
from Cryptodome.Cipher import AES
from Cryptodome.Util.Padding import pad, unpad

# --- 配置项 (保持不变) ---
REGISTER_URL = "http://ggregesd.kes337f.shop/user/register/v1"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"

# --- 密钥 (保持不变) ---
API_KEY_STRING = "212i919292901"
GLOBAL_AES_KEY = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
GLOBAL_AES_IV = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")
PASSWORD_STATIC_IV = bytes.fromhex('0102030405060708090a0b0c0d0e0f10')

# --- 辅助函数 (保持不变) ---
def generate_random_phone():
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    return prefix + ''.join(random.choices(string.digits, k=8))
def generate_random_password(length=8):
    return ''.join(random.choice(string.ascii_letters + string.digits) for i in range(length))
def generate_random_chinese_name():
    surnames = "赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎"
    given_names = "伟芳娜秀英敏静丽强磊军洋勇杰娟涛明超平刚桂"
    return f"{random.choice(surnames)}{''.join(random.choices(given_names, k=random.randint(1, 2)))}"
def encrypt_login_password(password: str) -> str:
    md5_hash = hashlib.md5(password.encode('utf-8')).digest()
    cipher = AES.new(md5_hash, AES.MODE_CBC, PASSWORD_STATIC_IV)
    return hashlib.md5(cipher.encrypt(pad(md5_hash, AES.block_size))).hexdigest()
def join_param_values(params: dict) -> str:
    sorted_keys = sorted(params.keys()); return "".join([str(params[key]) for key in sorted_keys])
def generate_mac_signature(key: bytes, content: str) -> str:
    return base64.b64encode(hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()).decode('utf-8')
def encrypt_request_data(payload: dict) -> str:
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    return base64.b64encode(cipher.encrypt(pad(json.dumps(payload, separators=(',',':'), ensure_ascii=False).encode('utf-8'), AES.block_size))).decode('utf-8')
def decrypt_response_data(encrypted_data_b64: str) -> dict:
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    return json.loads(unpad(cipher.decrypt(base64.b64decode(encrypted_data_b64)), AES.block_size).decode('utf-8'))

# --- 主函数 (修改点 1: 增加`nickname_suffix`参数) ---
def auto_register(invite_code: str, nickname_suffix: str): # 接收昵称后缀作为参数
    try:
        area_code, sex, birthday = "92", "1", "1753510077"
        
        print("\n--- 正在生成注册信息 ---")
        phone, password = generate_random_phone(), generate_random_password()
        device_model, os_version, serial = "SM-A300M", "14", "081199338734512"
        
        # 使用随机名字和您输入的后缀，来构造最终的昵称
        nickname = f"{generate_random_chinese_name()}-{nickname_suffix}"
        
        print(f"手机号: {phone}\n密码: {password}\n昵称: {nickname}")

        language, salt = "zh", str(int(time.time() * 1000))
        encrypted_password = encrypt_login_password(password)
        
        params_for_mac = {"apiVersion": "76", "areaCode": area_code, "areaId": "0", "birthday": birthday, "cityId": "0", "countryId": "0", "idcard": "", "inviteCode": invite_code, "isSmsRegister": "0", "model": device_model, "name": "", "nickname": nickname, "osVersion": os_version, "password": encrypted_password, "provinceId": "0", "serial": serial, "sex": sex, "smsCode": "", "telephone": phone, "userType": "0", "xmppVersion": "1"}
        joined_values = join_param_values(params_for_mac)
        mac_content_to_sign = API_KEY_STRING + joined_values + salt
        mac_signature = generate_mac_signature(GLOBAL_AES_KEY, mac_content_to_sign)
        
        payload = params_for_mac.copy(); payload['mac'] = mac_signature
        encrypted_data = encrypt_request_data(payload)
        
        random_secret = base64.b64encode(random.getrandbits(128).to_bytes(16, 'big')).decode('utf-8')
        params = {"data": encrypted_data, "deviceId": "android", "language": language, "salt": salt, "secret": random_secret}
        headers = {"User-Agent": f"chat_im/2.1.8 (Linux; U; Android {os_version}; {device_model} Build/UP1A.231005.007)", "Connection": "Keep-Alive"}

        print("\n--- 正在发送注册请求 ---")
        response = requests.get(REGISTER_URL, params=params, headers=headers); response.raise_for_status(); response_json = response.json()
        print("\n--- 服务器原始响应 ---\n", json.dumps(response_json, indent=2, ensure_ascii=False))

        if "data" in response_json and "data" in response_json["data"] and response_json.get("resultCode") == 1:
            decrypted_resp_dict = decrypt_response_data(response_json["data"]["data"])
            print("\n>>> \033[92m响应解密成功!\033[0m")
            print(json.dumps(decrypted_resp_dict, indent=2, ensure_ascii=False))
            print("\n\033[92m>>> 逻辑判断：注册成功! <<<\033[0m")
            
            decrypted_resp_string = json.dumps(decrypted_resp_dict, separators=(',', ':'), ensure_ascii=False)
            with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                f.write(f"{phone}:{password}:{decrypted_resp_string}\n")
            print(f"\n账号、密码及完整响应已成功保存到: {OUTPUT_FILE}")
        else:
            print(f"\n\033[91m>>> 注册失败: {response_json.get('resultMsg', '未知错误')}\033[0m")

    except requests.exceptions.RequestException as e: print(f"\n网络请求错误: {e}")
    except Exception as e: print(f"\n发生未知错误: {e}")

# --- 启动器 ---
if __name__ == "__main__":
    try:
        # -------------------- [修改点 2: 增加交互式输入昵称后缀] --------------------
        main_invite_code = input("请输入要使用的【邀请码】: ").strip()
        if not main_invite_code:
            print("错误：邀请码不能为空。")
        else:
            main_nickname_suffix = input("请输入自定义的【昵称后缀】 (例如: 黄朝凤团队): ").strip()
            if not main_nickname_suffix:
                print("错误：昵称后缀不能为空。")
            else:
                while True:
                    num_str = input("请输入要【注册的数量】 (输入Q退出): ").strip()
                    if num_str.upper() == 'Q': break
                    try:
                        num_to_register = int(num_str)
                        print(f"\n准备使用邀请码 '{main_invite_code}' 和昵称后缀 '-{main_nickname_suffix}' 批量注册 {num_to_register} 个账号。")
                        for i in range(num_to_register):
                            print(f"\n--- [任务 {i + 1} / {num_to_register}] ---")
                            # 将邀请码和昵称后缀一起传入
                            auto_register(main_invite_code, main_nickname_suffix)
                            if i < num_to_register - 1: time.sleep(random.uniform(1, 2))
                        print("\n" + "="*60 + "\n--- 所有注册任务已完成！ ---")
                        break 
                    except ValueError:
                        print("错误：输入的不是有效的数字。")
    except KeyboardInterrupt:
        print("\n\n操作已由用户手动中断。")
        
    print("\n程序已退出。")