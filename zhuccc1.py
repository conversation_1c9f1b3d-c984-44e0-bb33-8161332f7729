#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站 - 自动注册脚本 (带验证码识别)

import requests
import json
import base64
import random
import string
import time
import re
import hashlib
import hmac
from urllib.parse import urlencode
from io import BytesIO
from PIL import Image
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes
from datetime import datetime, timedelta
from typing import Optional, Dict

# --- 配置项 ---
BASE_URL = "https://huanqiu.cgi913.com"
CAPTCHA_URL = f"{BASE_URL}/api/common/getCaptchaImg"
REGISTER_URL = f"{BASE_URL}/api/login/register"
UPLOAD_URL = f"{BASE_URL}/api/common/upload"
REAL_INFO_URL = f"{BASE_URL}/api/user/updateRealInfo"
USER_INFO_URL = f"{BASE_URL}/api/user/userInfo"
ADD_ADDRESS_URL = f"{BASE_URL}/api/usercenter/add_address"
LOTTERY_URL = f"{BASE_URL}/api/turntable/lottery"
OUTPUT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt" # 请确保路径存在
# 保存格式: 手机号:密码:设备名称:用户ID:Token前缀:代理IP:状态

# 身份证图片目录
ID_CARD_DIR = r"C:\Users\<USER>\Desktop\hook\zhuce\id_card_sorted"
ID_CARD_FRONT_DIR = f"{ID_CARD_DIR}\\正面"
ID_CARD_BACK_DIR = f"{ID_CARD_DIR}\\反面"

# 加密配置 (从JavaScript逆向得到)
AES_KEY = "v4NTEx37"  # 从JavaScript代码中找到的密钥
AES_KEY_BYTES = AES_KEY.encode('utf-8')

# 代理配置 (请根据您的51代理账号信息修改)
PROXY_CONFIG = {
    'enabled': True,  # 是否启用代理，设置为True启用
    'packid': "2",
    'uid': "42477",
    'access_name': "ab263263",
    'access_password': "8C84B9BFE1774BFB2443DD34797EE4FB",
    'protocol': 'http',  # 'http' 或 'socks5'
    'time_duration': 31  # 代理时长(分钟)
}

# 验证码识别相关
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✓ ddddocr 验证码识别库已加载")
except ImportError:
    OCR_AVAILABLE = False
    print("⚠ ddddocr 未安装，将使用手动输入验证码模式")

# OCR身份证识别相关
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
    print("✓ OpenCV 图像处理库已加载")
except ImportError:
    CV2_AVAILABLE = False
    print("⚠ OpenCV 未安装，身份证识别功能受限")

# Tesseract OCR身份证识别
try:
    import pytesseract
    # 设置Tesseract路径 (Windows)
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    TESSERACT_AVAILABLE = True
    print("✓ Tesseract OCR 身份证识别库已加载")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("⚠ Tesseract OCR 未安装，将使用其他OCR方案")
except Exception as e:
    TESSERACT_AVAILABLE = False
    print(f"⚠ Tesseract OCR 配置错误: {e}")

# MLKit OCR身份证识别模块
try:
    from mlkit_ocr import MLKitOCR, recognize_id_card_simple
    MLKIT_AVAILABLE = True
    print("✓ MLKit OCR 身份证识别模块已加载")
except ImportError:
    MLKIT_AVAILABLE = False
    print("⚠ MLKit OCR 模块未找到，请确保 mlkit_ocr.py 在同目录下")

# 加密相关
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# --- 代理管理器类 ---

class ProxyManager:
    """
    51代理 (51daili.com) 的客户端。
    用于获取和管理代理IP，确保每个账号使用不同的IP地址。
    """
    API_URL = "http://bapi.51daili.com/getapi2"

    def __init__(self, packid: str, uid: str, access_name: str, access_password: str, protocol: str = 'http', time_duration: int = 10):
        """初始化代理管理器"""
        self.api_params = {
            'linePoolIndex': -1,
            'packid': packid,
            'time': time_duration,
            'qty': 1,
            'port': 1,
            'format': 'json',
            'field': 'ipport,expiretime,regioncode,isptype',
            'dt': 4,
            'ct': 1,
            'dtc': 2,
            'usertype': 17,
            'uid': uid,
            'accessName': access_name,
            'accessPassword': access_password
        }

        self.protocol = protocol.lower()
        self.current_proxy_data = None
        print(f"✓ 代理管理器已初始化 (协议: {self.protocol.upper()})")

    def _fetch_new_proxy(self) -> bool:
        """获取新的代理IP"""
        print("🌐 正在获取新的代理IP...")
        try:
            response = requests.get(self.API_URL, params=self.api_params, timeout=15)
            response.raise_for_status()

            response_text = response.text.strip()
            print(f"📡 代理API响应: {response_text[:100]}...")

            # 处理纯文本格式响应
            if response_text and not (response_text.startswith('{') or response_text.startswith('[')):
                if ':' not in response_text or len(response_text) > 30:
                    print(f"❌ 代理API返回错误: {response_text}")
                    return False

                if ':' in response_text:
                    ip, port = response_text.split(':')
                    expire_time = datetime.now().replace(microsecond=0) + timedelta(minutes=30)

                    self.current_proxy_data = {
                        "ip": ip,
                        "port": port,
                        "expire_time": expire_time
                    }

                    print(f"✅ 获取代理成功: {ip}:{port}")
                    return True

            # 处理JSON格式响应
            try:
                data = response.json()
                print(f"🔍 解析JSON数据: {data}")

                # 51代理API返回格式: {"code":"***","data":[{"ipport":"ip:port","expiretime":"2024-01-01 12:00:00",...}],"msg":"success","success":true}
                if isinstance(data, dict):
                    # 检查是否有错误
                    if data.get('success') == False or data.get('code') != '***':
                        error_msg = data.get('msg', '未知错误')
                        print(f"❌ 代理API错误: {error_msg}")
                        return False

                    # 获取代理数据
                    if 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                        proxy_info = data['data'][0]
                        ip_port = proxy_info.get('ipport', '')

                        if not ip_port or ':' not in ip_port:
                            print(f"❌ 代理数据格式错误: {proxy_info}")
                            return False

                        ip, port = ip_port.split(':')
                        expire_time_str = proxy_info.get('expiretime', '')

                        try:
                            expire_time = datetime.strptime(expire_time_str, "%Y-%m-%d %H:%M:%S")
                        except (ValueError, TypeError):
                            expire_time = datetime.now().replace(microsecond=0) + timedelta(minutes=30)

                        self.current_proxy_data = {
                            "ip": ip,
                            "port": port,
                            "expire_time": expire_time
                        }

                        print(f"✅ 获取代理成功: {ip}:{port}")
                        print(f"⏰ 过期时间: {expire_time}")
                        return True

                print(f"❌ 无法解析代理信息: {data}")
                return False

            except json.JSONDecodeError:
                print(f"❌ 代理API响应格式错误: {response_text}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ 代理API请求失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 代理获取异常: {e}")
            return False

    def get_proxy(self, force_new=True) -> Optional[Dict[str, str]]:
        """获取可用的代理字典"""
        is_expired = False
        if self.current_proxy_data:
            if datetime.now() >= self.current_proxy_data["expire_time"]:
                print("⏰ 当前代理已过期")
                is_expired = True

        if not self.current_proxy_data or is_expired or force_new:
            if force_new and self.current_proxy_data and not is_expired:
                print("🔄 强制获取新代理IP")

            if not self._fetch_new_proxy():
                return None

        if not self.current_proxy_data:
            return None

        ip = self.current_proxy_data['ip']
        port = self.current_proxy_data['port']
        user = self.api_params['accessName']
        pwd = self.api_params['accessPassword']

        proxy_url = f"{self.protocol}://{user}:{pwd}@{ip}:{port}"

        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def release_proxy(self):
        """释放当前代理"""
        if self.current_proxy_data:
            ip_port = f"{self.current_proxy_data['ip']}:{self.current_proxy_data['port']}"
            print(f"🗑️ 释放代理IP: {ip_port}")
            self.current_proxy_data = None
            return True
        return False

# --- 核心加密函数 (基于JavaScript逆向) ---

def evp_bytes_to_key(password: bytes, salt: bytes, key_len: int, iv_len: int) -> tuple:
    """
    模拟OpenSSL的EVP_BytesToKey方法 - 这是CryptoJS使用的密钥派生方法
    """
    import hashlib

    derived_bytes = b''
    block = b''

    while len(derived_bytes) < key_len + iv_len:
        block = hashlib.md5(block + password + salt).digest()
        derived_bytes += block

    return derived_bytes[:key_len], derived_bytes[key_len:key_len + iv_len]

def generate_sign_parameter(username, password):
    """
    生成sign参数 - 使用正确的EVP_BytesToKey方法
    基于解密成功的方法：使用OpenSSL/MD5密钥派生
    """
    try:
        # 1. 构造要加密的数据
        data_to_encrypt = {
            "username": username,
            "password": password
        }

        # 2. 转换为JSON字符串
        json_str = json.dumps(data_to_encrypt, separators=(',', ':'), ensure_ascii=False)
        print(f"要加密的JSON: {json_str}")

        # 3. 生成随机IV和Salt
        iv = get_random_bytes(16)  # 16字节的随机IV
        salt = get_random_bytes(8)  # 8字节的随机Salt

        # 4. 使用EVP_BytesToKey方法派生密钥 (这是关键!)
        key_bytes = AES_KEY.encode('utf-8')
        derived_key, _ = evp_bytes_to_key(key_bytes, salt, 32, 16)

        print(f"派生密钥 (hex): {derived_key.hex()}")

        # 5. AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, iv)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        ciphertext = cipher.encrypt(padded_data)

        # 6. 构造结果对象
        result = {
            "ct": base64.b64encode(ciphertext).decode('utf-8'),
            "iv": iv.hex(),
            "s": salt.hex()
        }

        # 7. 转换为JSON字符串
        sign_value = json.dumps(result, separators=(',', ':'))
        print(f"生成的sign参数: {sign_value}")

        return sign_value

    except Exception as e:
        print(f"生成sign参数时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

# --- 辅助函数 ---

def generate_random_phone():
    """生成随机手机号"""
    prefix = random.choice(['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                           '150', '151', '152', '153', '155', '156', '157', '158', '159',
                           '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'])
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_random_password():
    """生成密码 - 基于解密发现的真实格式 "1111qqqq" """

    # 基于解密发现的密码格式：4位数字 + 4位小写字母
    password_patterns = [
        # 模式1: 重复数字 + 重复字母 (如 1111qqqq)
        lambda: generate_repeat_pattern(),

        # 模式2: 连续数字 + 连续字母 (如 1234abcd)
        lambda: generate_sequence_pattern(),

        # 模式3: 随机数字 + 随机字母 (如 5729xkwp)
        lambda: generate_random_pattern(),

        # 模式4: 简单组合 (如 1234qwer)
        lambda: generate_simple_combo(),
    ]

    # 权重分配：偏向重复模式（因为解密出的是重复模式）
    weights = [0.4, 0.3, 0.2, 0.1]
    pattern_func = random.choices(password_patterns, weights=weights)[0]

    return pattern_func()

def generate_repeat_pattern():
    """生成重复模式密码 (如 1111qqqq, 2222aaaa)"""
    # 选择一个数字重复4次
    digit = random.choice(string.digits)
    digit_part = digit * 4

    # 选择一个小写字母重复4次
    letter = random.choice(string.ascii_lowercase)
    letter_part = letter * 4

    return digit_part + letter_part

def generate_sequence_pattern():
    """生成序列模式密码 (如 1234abcd, 5678efgh)"""
    # 生成连续数字
    start_digit = random.randint(0, 6)  # 确保不会超出范围
    digit_part = ''.join(str(start_digit + i) for i in range(4))

    # 生成连续字母
    start_letter_idx = random.randint(0, 22)  # 确保不会超出范围
    letter_part = ''.join(chr(ord('a') + start_letter_idx + i) for i in range(4))

    return digit_part + letter_part

def generate_random_pattern():
    """生成随机模式密码 (如 5729xkwp)"""
    digit_part = ''.join(random.choices(string.digits, k=4))
    letter_part = ''.join(random.choices(string.ascii_lowercase, k=4))

    return digit_part + letter_part

def generate_simple_combo():
    """生成简单组合密码 (如 1234qwer, 5678asdf)"""
    simple_digits = ['1234', '5678', '9876', '1357', '2468']
    simple_letters = ['qwer', 'asdf', 'zxcv', 'abcd', 'efgh']

    digit_part = random.choice(simple_digits)
    letter_part = random.choice(simple_letters)

    return digit_part + letter_part

def generate_real_phone_number():
    """生成真实格式的手机号"""
    # 中国大陆手机号段 (更真实的号段)
    prefixes = [
        # 中国移动
        '134', '135', '136', '137', '138', '139', '147', '150', '151',
        '152', '157', '158', '159', '178', '182', '183', '184', '187', '188',
        # 中国联通
        '130', '131', '132', '145', '155', '156', '166', '175', '176', '185', '186',
        # 中国电信
        '133', '149', '153', '173', '177', '180', '181', '189', '199'
    ]

    prefix = random.choice(prefixes)
    # 后8位数字
    suffix = ''.join(random.choices(string.digits, k=8))
    return prefix + suffix

def generate_device_fingerprint():
    """
    根据抓包数据格式生成真实的设备指纹
    基于实际iPhone设备的User-Agent格式
    """

    # 基于抓包数据的真实iPhone设备信息
    iphone_devices = [
        {
            "model": "iPhone13,2",  # iPhone 12
            "ios_version": "16_6",
            "device_name": "iPhone 12",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone14,3",  # iPhone 13 Pro
            "ios_version": "16_6",
            "device_name": "iPhone 13 Pro",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone15,2",  # iPhone 14
            "ios_version": "16_6",
            "device_name": "iPhone 14",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone12,1",  # iPhone 11
            "ios_version": "16_6",
            "device_name": "iPhone 11",
            "webkit_version": "605.1.15"
        },
        {
            "model": "iPhone11,8",  # iPhone XR
            "ios_version": "16_6",
            "device_name": "iPhone XR",
            "webkit_version": "605.1.15"
        }
    ]

    # 随机选择设备
    device = random.choice(iphone_devices)

    # 生成设备指纹信息
    fingerprint = {
        "model": device["model"],
        "ios_version": device["ios_version"],
        "device_name": device["device_name"],
        "webkit_version": device["webkit_version"],
        "safari_version": "604.1",

        # 生成唯一设备标识
        "device_id": generate_device_id(),
        "session_id": generate_session_id(),

        # 网络相关
        "connection_type": random.choice(["wifi", "cellular"]),
        "carrier": random.choice(["中国移动", "中国联通", "中国电信"]),

        # 屏幕信息 (iPhone真实分辨率)
        "screen_info": get_iphone_screen_info(device["model"]),

        # 时区和语言
        "timezone": "Asia/Shanghai",
        "language": "zh-CN",
        "locale": "zh_CN"
    }

    return fingerprint

def get_iphone_screen_info(model):
    """根据iPhone型号返回对应的屏幕信息"""
    screen_configs = {
        "iPhone13,2": {"width": 390, "height": 844, "scale": 3.0},  # iPhone 12
        "iPhone14,3": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 13 Pro
        "iPhone15,2": {"width": 393, "height": 852, "scale": 3.0},  # iPhone 14
        "iPhone12,1": {"width": 414, "height": 896, "scale": 2.0},  # iPhone 11
        "iPhone11,8": {"width": 414, "height": 896, "scale": 2.0},  # iPhone XR
    }
    return screen_configs.get(model, {"width": 390, "height": 844, "scale": 3.0})

def generate_device_id():
    """生成设备ID (模拟iOS设备标识符格式)"""
    # iOS设备标识符格式: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    parts = [
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=8)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=4)),
        ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
    ]
    return '-'.join(parts)

def generate_session_id():
    """生成会话ID"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))

# --- 身份信息生成函数 ---

def generate_chinese_name():
    """生成随机中文姓名"""
    # 常见姓氏
    surnames = [
        '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
        '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
        '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
        '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕'
    ]

    # 常见名字用字
    given_names = [
        '伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋',
        '勇', '艳', '杰', '娟', '涛', '明', '超', '秀', '霞', '平',
        '刚', '桂', '英', '华', '玉', '萍', '红', '娥', '玲', '菊',
        '辉', '婷', '云', '帆', '凯', '悦', '思', '晨', '宇', '欣'
    ]

    surname = random.choice(surnames)

    # 70%概率生成两字名，30%概率生成单字名
    if random.random() < 0.7:
        given_name = random.choice(given_names) + random.choice(given_names)
    else:
        given_name = random.choice(given_names)

    return surname + given_name

def generate_id_card_number():
    """生成身份证号码（18位）"""
    # 地区代码（前6位）- 使用真实的地区代码
    area_codes = [
        '110101',  # 北京市东城区
        '110102',  # 北京市西城区
        '120101',  # 天津市和平区
        '130102',  # 石家庄市长安区
        '140105',  # 太原市小店区
        '150102',  # 呼和浩特市新城区
        '210102',  # 沈阳市和平区
        '220102',  # 长春市南关区
        '230103',  # 哈尔滨市南岗区
        '310101',  # 上海市黄浦区
        '320102',  # 南京市玄武区
        '330102',  # 杭州市上城区
        '340102',  # 合肥市瑶海区
        '350102',  # 福州市鼓楼区
        '360102',  # 南昌市东湖区
        '370102',  # 济南市历下区
        '410102',  # 郑州市中原区
        '420102',  # 武汉市江岸区
        '430102',  # 长沙市芙蓉区
        '440103',  # 广州市荔湾区
        '450102',  # 南宁市兴宁区
        '460105',  # 海口市秀英区
        '500101',  # 重庆市万州区
        '510104',  # 成都市锦江区
        '520102',  # 贵阳市南明区
        '530102',  # 昆明市五华区
        '610102',  # 西安市新城区
        '620102',  # 兰州市城关区
        '630102',  # 西宁市城东区
        '640104',  # 银川市兴庆区
        '650102',  # 乌鲁木齐市天山区
    ]

    area_code = random.choice(area_codes)

    # 出生日期（8位）- 生成1980-2000年的日期
    year = random.randint(1980, 2000)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 使用28避免月份天数问题
    birth_date = f"{year:04d}{month:02d}{day:02d}"

    # 顺序码（3位）- 随机生成
    sequence = f"{random.randint(1, 999):03d}"

    # 前17位
    id_17 = area_code + birth_date + sequence

    # 计算校验码（第18位）
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]

    return id_17 + check_code

def generate_real_address():
    """生成真实的中国地址"""
    provinces = [
        "北京市", "天津市", "河北省", "山西省", "内蒙古自治区",
        "辽宁省", "吉林省", "黑龙江省", "上海市", "江苏省",
        "浙江省", "安徽省", "福建省", "江西省", "山东省",
        "河南省", "湖北省", "湖南省", "广东省", "广西壮族自治区",
        "海南省", "重庆市", "四川省", "贵州省", "云南省",
        "西藏自治区", "陕西省", "甘肃省", "青海省", "宁夏回族自治区",
        "新疆维吾尔自治区"
    ]

    cities = {
        "北京市": ["东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区"],
        "上海市": ["黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区"],
        "广东省": ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市"],
        "江苏省": ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市"],
        "浙江省": ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市"],
        "山东省": ["济南市", "青岛市", "淄博市", "枣庄市", "东营市", "烟台市"],
        "河南省": ["郑州市", "开封市", "洛阳市", "平顶山市", "安阳市", "鹤壁市"],
        "四川省": ["成都市", "自贡市", "攀枝花市", "泸州市", "德阳市", "绵阳市"],
        "湖北省": ["武汉市", "黄石市", "十堰市", "宜昌市", "襄阳市", "鄂州市"],
        "湖南省": ["长沙市", "株洲市", "湘潭市", "衡阳市", "邵阳市", "岳阳市"]
    }

    streets = [
        "人民路", "解放路", "建设路", "中山路", "和平路", "友谊路",
        "胜利路", "光明路", "幸福路", "团结路", "民主路", "自由路",
        "繁荣路", "发展路", "振兴路", "复兴路", "新华路", "文化路",
        "教育路", "科技路", "工业路", "商业路", "农业路", "环城路"
    ]

    # 选择省份
    province = random.choice(list(cities.keys()))
    city = random.choice(cities[province])
    street = random.choice(streets)
    number = random.randint(1, 999)

    return f"{province}{city}{street}{number}号"

def generate_detailed_address():
    """生成详细的地址信息 (用于添加地址接口)"""
    # 真实的省市区数据
    address_data = {
        "安徽省": {
            "宿州市": ["埇桥区", "砀山县", "萧县", "灵璧县", "泗县"],
            "合肥市": ["瑶海区", "庐阳区", "蜀山区", "包河区", "长丰县"],
        },
        "江苏省": {
            "南京市": ["玄武区", "秦淮区", "建邺区", "鼓楼区", "浦口区"],
            "苏州市": ["虎丘区", "吴中区", "相城区", "姑苏区", "工业园区"],
        },
        "浙江省": {
            "杭州市": ["上城区", "下城区", "江干区", "拱墅区", "西湖区"],
            "宁波市": ["海曙区", "江北区", "北仑区", "镇海区", "鄞州区"],
        }
    }

    # 区域代码映射
    area_codes = {
        ("安徽省", "宿州市", "埇桥区"): "341302",
        ("安徽省", "合肥市", "瑶海区"): "340102",
        ("江苏省", "南京市", "玄武区"): "320102",
        ("江苏省", "苏州市", "虎丘区"): "320505",
        ("浙江省", "杭州市", "上城区"): "330102",
        ("浙江省", "宁波市", "海曙区"): "330203",
    }

    # 随机选择省市区
    province = random.choice(list(address_data.keys()))
    city = random.choice(list(address_data[province].keys()))
    county = random.choice(address_data[province][city])

    # 生成详细地址
    building_types = ["号楼", "号院", "号小区"]
    building_num = random.randint(1, 99)
    unit = random.randint(1, 6)
    room = random.randint(101, 999)

    detailed_address = f"区{building_num}{random.choice(building_types)}{unit}单元{room}室"

    # 获取区域代码
    key = (province, city, county)
    area_code = area_codes.get(key, "341302")  # 默认使用宿州埇桥区代码

    return province, city, county, detailed_address, area_code

def generate_bank_info():
    """生成真实的银行信息"""
    # 主要银行列表
    banks = [
        "工商银行", "建设银行", "农业银行", "中国银行",
        "交通银行", "招商银行", "浦发银行", "民生银行"
    ]

    # 银行卡号前缀 (真实的银行卡BIN)
    bank_prefixes = {
        "工商银行": ["6222", "6212", "6215"],
        "建设银行": ["6227", "6236", "6217"],
        "农业银行": ["6228", "6230", "6229"],
        "中国银行": ["6216", "6217"],
        "交通银行": ["6222", "6259", "6214"],
        "招商银行": ["6225", "6214"],
        "浦发银行": ["6221", "6225"],
        "民生银行": ["6226", "6288"]
    }

    # 随机选择银行
    bank_name = random.choice(banks)

    # 生成银行卡号
    if bank_name in bank_prefixes:
        prefix = random.choice(bank_prefixes[bank_name])
    else:
        prefix = "6222"  # 默认前缀

    # 生成剩余位数 (银行卡号通常是16-19位)
    remaining_digits = 19 - len(prefix)  # 生成19位卡号
    suffix = ''.join(random.choices(string.digits, k=remaining_digits))

    bank_account = prefix + suffix

    return bank_name, bank_account

def generate_user_agent(fingerprint):
    """
    根据设备指纹生成User-Agent - 完全模拟抓包数据格式
    抓包数据: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
    """
    model = fingerprint["model"]
    ios_version = fingerprint["ios_version"]
    webkit_version = fingerprint["webkit_version"]
    safari_version = fingerprint["safari_version"]

    # 生成随机的Build号 (iOS格式: 15E148)
    build_number = generate_ios_build_number()

    # 构造完全符合抓包格式的User-Agent
    user_agent = (
        f"Mozilla/5.0 (iPhone; CPU iPhone OS {ios_version} like Mac OS X) "
        f"AppleWebKit/{webkit_version} (KHTML, like Gecko) "
        f"Version/{ios_version.replace('_', '.')} Mobile/{build_number} Safari/{safari_version}"
    )

    return user_agent

def generate_ios_build_number():
    """生成iOS Build号格式 (如: 15E148)"""
    # iOS Build号格式: [主版本号][字母][数字]
    major_version = random.choice(['15', '16', '17'])
    letter = random.choice(['A', 'B', 'C', 'D', 'E', 'F', 'G'])
    number = random.randint(100, 999)
    return f"{major_version}{letter}{number}"

def generate_request_headers(fingerprint, extra_headers=None):
    """
    生成完整的请求头 - 基于抓包数据
    """
    user_agent = generate_user_agent(fingerprint)

    # 基础请求头 (完全模拟抓包数据)
    headers = {
        'User-Agent': user_agent,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Host': 'huanqiu.cgi913.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Token': '',  # 从抓包数据中看到的Token字段
    }

    # 添加额外的请求头
    if extra_headers:
        headers.update(extra_headers)

    return headers

def generate_device_fingerprint_for_request():
    """
    为请求生成设备指纹参数
    可能需要在请求中包含的设备相关参数
    """
    fingerprint = generate_device_fingerprint()

    # 可能在请求中需要的设备参数
    device_params = {
        'device_id': fingerprint['device_id'],
        'device_type': 'ios',
        'device_model': fingerprint['model'],
        'os_version': fingerprint['ios_version'].replace('_', '.'),
        'app_version': '1.0.0',  # 假设的应用版本
        'screen_width': fingerprint['screen_info']['width'],
        'screen_height': fingerprint['screen_info']['height'],
        'timezone': fingerprint['timezone'],
        'language': fingerprint['language'],
    }

    return fingerprint, device_params

def get_captcha_image(session):
    """获取验证码图片"""
    try:
        print("正在获取验证码...")
        response = session.get(CAPTCHA_URL, headers={
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Host': 'huanqiu.cgi913.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })

        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 1:
                code_id = data['data']['code_id']
                captcha_base64 = data['data']['captcha_src']

                # 解析base64图片
                if captcha_base64.startswith('data:image/png;base64,'):
                    captcha_base64 = captcha_base64.replace('data:image/png;base64,', '')

                captcha_image = base64.b64decode(captcha_base64)
                return code_id, captcha_image
            else:
                print(f"获取验证码失败: {data.get('msg', '未知错误')}")
                return None, None
        else:
            print(f"请求验证码失败，状态码: {response.status_code}")
            return None, None

    except Exception as e:
        print(f"获取验证码时发生错误: {e}")
        return None, None

def recognize_captcha(captcha_image, max_retries=3):
    """
    智能验证码识别 - 优先使用ddddocr自动识别，失败时提供手动输入
    """
    if OCR_AVAILABLE:
        print("🤖 使用ddddocr自动识别验证码...")

        for attempt in range(max_retries):
            try:
                # 创建OCR实例
                ocr = ddddocr.DdddOcr()

                # 识别验证码
                result = ocr.classification(captcha_image)

                # 验证结果格式 (验证码必须是4位数字)
                if result and result.strip():
                    # 提取数字字符
                    digits_only = ''.join(c for c in result if c.isdigit())

                    if len(digits_only) == 4:
                        print(f"✅ 自动识别成功: {digits_only}")
                        return digits_only
                    elif len(digits_only) > 4:
                        # 如果识别出超过4位数字，取前4位
                        captcha_code = digits_only[:4]
                        print(f"✅ 自动识别成功 (截取前4位): {captcha_code}")
                        return captcha_code
                    else:
                        print(f"⚠️ 识别的数字位数不足: '{result}' -> '{digits_only}' (需要4位数字)")
                else:
                    print(f"⚠️ 识别结果为空或异常: {result}")

                print(f"🔄 第 {attempt + 1}/{max_retries} 次识别失败，准备重试...")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 短暂等待后重试

            except Exception as e:
                print(f"❌ 自动识别异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)

        print("🔄 自动识别多次失败，需要获取新的验证码图片...")
        return 'need_new_captcha'  # 返回特殊标识，表示需要新验证码
    else:
        print("📝 ddddocr未安装，使用手动输入模式...")

    # 手动输入模式
    try:
        # 保存验证码图片供用户查看
        captcha_filename = f"captcha_{int(time.time())}.png"
        with open(captcha_filename, "wb") as f:
            f.write(captcha_image)
        print(f"💾 验证码已保存为: {captcha_filename}")

        # 尝试自动显示图片
        try:
            img = Image.open(BytesIO(captcha_image))
            img.show()
            print("🖼️ 验证码图片已自动打开")
        except Exception as e:
            print(f"⚠️ 无法自动显示图片: {e}")
            print(f"📂 请手动打开文件: {captcha_filename}")

        # 获取用户输入
        while True:
            captcha_code = input("\n🔤 请输入验证码 (输入 'retry' 重新获取): ").strip()

            if captcha_code.lower() == 'retry':
                return 'retry'
            elif captcha_code and len(captcha_code) >= 3:
                print(f"✅ 手动输入验证码: {captcha_code}")
                return captcha_code
            else:
                print("❌ 验证码格式不正确，请重新输入")

    except Exception as e:
        print(f"💥 处理验证码时发生错误: {e}")
        return None

# --- 身份证OCR识别函数 ---

def extract_id_card_info_tesseract(image_path):
    """
    使用Tesseract OCR从身份证正面图片中提取姓名和身份证号码
    """
    try:
        print(f"🔍 正在使用Tesseract OCR识别身份证: {image_path}")

        if not TESSERACT_AVAILABLE:
            print("❌ Tesseract OCR未安装，回退到ddddocr")
            return extract_id_card_info_ddddocr(image_path)

        # 显示身份证图片供用户查看
        try:
            from PIL import Image
            img = Image.open(image_path)
            img.show()
            print("🖼️ 身份证正面图片已自动打开，请查看")
        except Exception as e:
            print(f"⚠️ 无法自动显示图片: {e}")
            print(f"📂 请手动打开查看: {image_path}")

        # 使用OpenCV进行专门的身份证图像预处理
        if CV2_AVAILABLE:
            try:
                print("🔧 开始Tesseract专用身份证图像预处理...")
                import cv2
                import numpy as np

                # 读取图像 - 解决中文路径问题
                try:
                    # 方法1: 使用numpy读取，避免中文路径问题
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    nparr = np.frombuffer(image_data, np.uint8)
                    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                except:
                    # 方法2: 尝试直接读取
                    img = cv2.imread(image_path)

                if img is None:
                    print("❌ 图像读取失败，可能是中文路径问题")
                    return None, None

                print(f"📐 原始图像尺寸: {img.shape}")

                # 1. 转换为灰度图
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

                # 2. 图像去噪 - 使用双边滤波保持边缘
                denoised = cv2.bilateralFilter(gray, 9, 75, 75)

                # 3. 增强对比度
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
                enhanced = clahe.apply(denoised)

                # 4. 锐化处理
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                sharpened = cv2.filter2D(enhanced, -1, kernel)

                # 5. 自适应二值化 - 对身份证文字效果更好
                binary = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 11, 2)

                # 6. 形态学操作 - 连接断开的文字
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 1))
                cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

                # 7. 调整图像大小 - Tesseract在300DPI效果最好
                height, width = cleaned.shape
                target_height = 800  # 目标高度
                if height < target_height:
                    scale_factor = target_height / height
                    new_width = int(width * scale_factor)
                    new_height = target_height
                    cleaned = cv2.resize(cleaned, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                    print(f"📏 图像已调整到: {new_width}x{new_height}")

                # 保存预处理后的图像
                processed_path = image_path.replace('.', '_tesseract_processed.')
                cv2.imwrite(processed_path, cleaned)
                print(f"🔍 Tesseract预处理图像已保存: {processed_path}")

                # 使用预处理后的图像进行OCR
                processed_image = cleaned

                print("✅ Tesseract专用图像预处理完成")
            except Exception as e:
                print(f"⚠️ 图像预处理失败，使用原图: {e}")
                processed_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        else:
            # 没有OpenCV，直接使用原图
            processed_image = image_path

        # 使用Tesseract进行OCR识别
        print("🤖 开始Tesseract OCR识别...")

        # Tesseract配置 - 针对中文身份证优化
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\u4e00-\u9fa5'

        # 多种配置尝试
        configs = [
            r'--oem 3 --psm 6 -l chi_sim',  # 中文简体
            r'--oem 3 --psm 7 -l chi_sim',  # 单行文本
            r'--oem 3 --psm 8 -l chi_sim',  # 单词
            r'--oem 3 --psm 6',             # 默认配置
        ]

        best_result = ""
        for i, config in enumerate(configs):
            try:
                print(f"🔄 尝试配置 {i+1}/{len(configs)}: {config}")

                if isinstance(processed_image, str):
                    # 文件路径
                    result = pytesseract.image_to_string(processed_image, config=config)
                else:
                    # numpy数组
                    result = pytesseract.image_to_string(processed_image, config=config)

                result = result.strip()
                print(f"📝 配置 {i+1} 识别结果: {result[:100]}...")

                # 选择最长的识别结果
                if len(result) > len(best_result):
                    best_result = result

                # 如果找到身份证号码，优先使用这个结果
                import re
                if re.search(r'\d{15,18}', result):
                    print(f"✅ 配置 {i+1} 检测到身份证号码，使用此结果")
                    best_result = result
                    break

            except Exception as e:
                print(f"❌ 配置 {i+1} 识别失败: {e}")
                continue

        result = best_result
        print(f"🎯 Tesseract最终识别结果: {result}")

        # 解析识别结果
        name, id_number = parse_id_card_text(result)

        # 如果自动识别失败，提供手动输入
        if not name or not id_number:
            print("⚠️ Tesseract自动识别不完整，请查看打开的身份证图片手动输入:")
            print(f"📝 完整OCR文本: {result}")

            if not name:
                name = input("请输入姓名: ").strip()

            if not id_number:
                id_number = input("请输入身份证号码: ").strip()

        if name and id_number:
            print(f"✅ 身份证信息获取成功:")
            print(f"   姓名: {name}")
            print(f"   身份证号: {id_number}")
            return name, id_number
        else:
            print("❌ 身份证信息获取失败")
            return None, None

    except Exception as e:
        print(f"❌ Tesseract OCR识别失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def extract_id_card_info_mlkit(image_path):
    """
    使用Google MLKit OCR从身份证正面图片中提取姓名和身份证号码
    """
    try:
        print(f"🎯 开始MLKit身份证识别: {image_path}")

        # 检查Google Cloud Vision API是否可用
        try:
            from google.cloud import vision
            import os

            # 自动设置凭证文件
            credentials_files = [
                'google-cloud-credentials.json',
                'credentials.json',
                'service-account-key.json'
            ]

            credentials_found = False
            for cred_file in credentials_files:
                if os.path.exists(cred_file):
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = os.path.abspath(cred_file)
                    print(f"🔑 使用凭证文件: {cred_file}")
                    credentials_found = True
                    break

            if not credentials_found and not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
                print("❌ 未找到Google Cloud凭证文件")
                return None, None

            # 初始化Vision客户端
            client = vision.ImageAnnotatorClient()
            print("✅ Google Cloud Vision API 客户端初始化成功")

            # 读取图像文件
            with open(image_path, 'rb') as image_file:
                content = image_file.read()

            image = vision.Image(content=content)

            # 配置OCR参数 - 优化中文识别
            image_context = vision.ImageContext(
                language_hints=['zh-CN', 'en']  # 中文简体 + 英文数字
            )

            # 执行文本检测
            print("🤖 开始MLKit OCR识别...")
            response = client.text_detection(
                image=image,
                image_context=image_context
            )

            # 检查错误
            if response.error.message:
                print(f"❌ MLKit API错误: {response.error.message}")
                return None, None

            # 获取文本注释
            texts = response.text_annotations
            if not texts:
                print("❌ MLKit未检测到任何文本")
                return None, None

            # 第一个注释包含完整文本
            full_text = texts[0].description
            print(f"📝 MLKit识别结果: {full_text}")

            # 解析识别结果
            name, id_number = parse_id_card_text(full_text)

            if name and id_number:
                print(f"✅ MLKit识别成功:")
                print(f"   姓名: {name}")
                print(f"   身份证号: {id_number}")
                return name, id_number
            else:
                print("⚠️ MLKit识别不完整")
                return None, None

        except ImportError:
            print("❌ Google Cloud Vision API 未安装")
            print("💡 请运行: pip install google-cloud-vision")
            return None, None
        except Exception as e:
            print(f"❌ MLKit OCR识别失败: {e}")
            return None, None

    except Exception as e:
        print(f"❌ MLKit OCR调用失败: {e}")
        return None, None

def extract_id_card_info_ddddocr(image_path):
    """
    使用ddddocr从身份证正面图片中提取姓名和身份证号码 (备用方案)
    """
    try:
        print(f"🔍 正在使用ddddocr识别身份证: {image_path}")

        if not OCR_AVAILABLE:
            print("❌ ddddocr未安装，无法进行OCR识别")
            return None, None

        # 读取图片
        with open(image_path, 'rb') as f:
            image_bytes = f.read()

        # 使用ddddocr进行OCR识别 - 兼容不同版本
        try:
            # 尝试新版本参数
            ocr = ddddocr.DdddOcr(show_ad=False)
        except TypeError:
            # 旧版本不支持show_ad参数
            ocr = ddddocr.DdddOcr()

        result = ocr.classification(image_bytes)
        print(f"📝 ddddocr识别结果: {result}")

        # 解析识别结果
        name, id_number = parse_id_card_text(result)
        return name, id_number

    except Exception as e:
        print(f"❌ ddddocr识别失败: {e}")
        return None, None

def extract_id_card_info(image_path):
    """
    从身份证正面图片中提取姓名和身份证号码
    优先使用Tesseract OCR，失败时回退到ddddocr
    """
    # 优先使用Google MLKit OCR (最准确)
    if MLKIT_AVAILABLE:
        print("🎯 使用Google MLKit OCR进行身份证识别...")
        name, id_number = extract_id_card_info_mlkit(image_path)
        if name and id_number:
            return name, id_number
        else:
            print("⚠️ MLKit识别失败，尝试Tesseract...")

    # 次选Tesseract OCR
    if TESSERACT_AVAILABLE:
        print("🔄 使用Tesseract OCR进行身份证识别...")
        name, id_number = extract_id_card_info_tesseract(image_path)
        if name and id_number:
            return name, id_number
        else:
            print("⚠️ Tesseract识别失败，尝试ddddocr备用方案...")

    # 最后回退到ddddocr
    if OCR_AVAILABLE:
        print("🔄 使用ddddocr备用方案...")
        name, id_number = extract_id_card_info_ddddocr(image_path)
        if name and id_number:
            return name, id_number

    # 都失败了，手动输入
    print("❌ 所有OCR方案都失败，请手动输入身份证信息:")

    # 显示身份证图片供用户查看
    try:
        from PIL import Image
        img = Image.open(image_path)
        img.show()
        print("🖼️ 身份证正面图片已自动打开，请查看")
    except Exception as e:
        print(f"⚠️ 无法自动显示图片: {e}")
        print(f"📂 请手动打开查看: {image_path}")

    name = input("请输入姓名: ").strip()
    id_number = input("请输入身份证号码: ").strip()

    if name and id_number:
        print(f"✅ 手动输入身份证信息:")
        print(f"   姓名: {name}")
        print(f"   身份证号: {id_number}")
        return name, id_number
    else:
        print("❌ 身份证信息输入不完整")
        return None, None

def parse_id_card_text(ocr_text):
    """
    从OCR识别的文本中解析出姓名和身份证号码
    """
    try:
        import re

        if not ocr_text:
            return None, None

        print(f"🔍 解析OCR文本: {ocr_text}")

        # 身份证号码正则表达式 (18位)
        id_pattern = r'[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]'
        id_matches = re.findall(id_pattern, ocr_text)

        id_number = None
        if id_matches:
            # 找到完整的身份证号码
            for match in re.finditer(id_pattern, ocr_text):
                id_number = match.group()
                break

        # 姓名提取 - 身份证上的姓名通常在特定位置
        name = None

        # 方法1: 查找"姓名"关键字后的内容
        name_patterns = [
            r'姓名[：:\s]*([^\s\d]{2,4})',
            r'姓\s*名[：:\s]*([^\s\d]{2,4})',
            r'名[：:\s]*([^\s\d]{2,4})',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, ocr_text)
            if matches:
                name = matches[0].strip()
                break

        # 方法2: 如果没找到，尝试查找中文姓名模式
        if not name:
            # 查找2-4个连续中文字符，且不是常见的身份证字段
            chinese_pattern = r'[\u4e00-\u9fa5]{2,4}'
            chinese_matches = re.findall(chinese_pattern, ocr_text)

            # 过滤掉常见的身份证字段
            exclude_words = ['中华人民共和国', '居民身份证', '公民身份', '姓名', '性别', '民族',
                           '出生', '住址', '身份证', '有效期', '签发机关', '年月日', '汉族', '满族',
                           '回族', '藏族', '维吾尔', '苗族', '彝族', '壮族', '布依', '朝鲜',
                           '公安局', '派出所', '长期', '临时']

            # 扩展常见姓氏列表
            common_surnames = [
                '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
                '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
                '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
                '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
                '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎',
                '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜',
                '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆',
                '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史',
                '顾', '侯', '邵', '孟', '龙', '万', '段', '雷', '钱', '汤'
            ]

            for match in chinese_matches:
                if len(match) >= 2 and len(match) <= 4:
                    if not any(word in match for word in exclude_words):
                        # 检查是否像姓名（常见姓氏开头）
                        if match[0] in common_surnames:
                            name = match
                            print(f"🎯 通过姓氏匹配找到姓名: {name}")
                            break

        # 方法3: 位置分析法 - 身份证上姓名通常在特定位置
        if not name:
            lines = ocr_text.split('\n')
            for i, line in enumerate(lines):
                line = line.strip()
                # 查找包含中文字符的行
                if re.search(r'[\u4e00-\u9fa5]', line):
                    # 提取该行的中文字符
                    chinese_chars = re.findall(r'[\u4e00-\u9fa5]+', line)
                    for chars in chinese_chars:
                        if 2 <= len(chars) <= 4:
                            if not any(word in chars for word in exclude_words):
                                if chars[0] in common_surnames:
                                    name = chars
                                    print(f"🎯 通过位置分析找到姓名: {name}")
                                    break
                    if name:
                        break

        # 方法3: 手动输入模式
        if not name or not id_number:
            print("⚠️ 自动识别不完整，请手动确认:")

            if not name:
                manual_name = input(f"请输入姓名 (OCR识别: {ocr_text[:50]}...): ").strip()
                if manual_name:
                    name = manual_name

            if not id_number:
                manual_id = input("请输入身份证号码: ").strip()
                if manual_id and len(manual_id) == 18:
                    id_number = manual_id

        return name, id_number

    except Exception as e:
        print(f"❌ 解析OCR文本失败: {e}")
        return None, None

# --- 图片上传和实名认证函数 ---

def get_random_id_card_front_image():
    """随机获取身份证正面图片"""
    import os
    import glob

    try:
        # 获取正面图片
        front_pattern = os.path.join(ID_CARD_FRONT_DIR, "*.*")
        front_images = glob.glob(front_pattern)

        if not front_images:
            print(f"❌ 身份证正面图片目录为空: {ID_CARD_FRONT_DIR}")
            return None

        front_image = random.choice(front_images)

        print(f"📷 选择的身份证正面图片: {os.path.basename(front_image)}")

        return front_image

    except Exception as e:
        print(f"❌ 获取身份证图片失败: {e}")
        return None

def upload_image(session, image_path, token):
    """上传单张图片"""
    try:
        import os

        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return None

        filename = os.path.basename(image_path)

        # 构造multipart/form-data请求
        files = {
            'file': (filename, open(image_path, 'rb'), 'image/jpeg')
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        print(f"📤 上传图片: {filename}")
        response = session.post(UPLOAD_URL, files=files, headers=headers)

        # 关闭文件
        files['file'][1].close()

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    url = result['data']['url']
                    print(f"✅ 图片上传成功: {url}")
                    return url
                else:
                    print(f"❌ 图片上传失败: {result.get('msg', '未知错误')}")
                    return None
            except json.JSONDecodeError:
                print(f"❌ 上传响应解析失败: {response.text}")
                return None
        else:
            print(f"❌ 图片上传请求失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ 图片上传过程中发生错误: {e}")
        return None

def get_user_info(session, token):
    """获取用户信息"""
    try:
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        response = session.get(USER_INFO_URL, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    return result.get('data', {})
                else:
                    print(f"❌ 获取用户信息失败: {result.get('msg', '未知错误')}")
                    return None
            except json.JSONDecodeError:
                print(f"❌ 用户信息响应解析失败: {response.text}")
                return None
        else:
            print(f"❌ 获取用户信息请求失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ 获取用户信息过程中发生错误: {e}")
        return None

def add_address_info(session, token, name, phone_number):
    """添加地址信息"""
    try:
        print("📍 正在添加地址信息...")

        # 生成详细地址信息
        province, city, county, detailed_address, area_code = generate_detailed_address()

        # 生成银行信息
        bank_name, bank_account = generate_bank_info()

        print(f"📋 地址信息:")
        print(f"   姓名: {name}")
        print(f"   手机: {phone_number}")
        print(f"   省份: {province}")
        print(f"   城市: {city}")
        print(f"   区县: {county}")
        print(f"   详细地址: {detailed_address}")
        print(f"   区域代码: {area_code}")
        print(f"   银行: {bank_name}")
        print(f"   银行卡号: {bank_account}")

        # 构造地址数据 (使用form-data格式)
        address_data = {
            'username': name,
            'phone': phone_number,
            'province': province,
            'city': city,
            'address': detailed_address,
            'county': county,
            'areaCode': area_code,
            'is_default': '1',
            'bank_name': bank_name,
            'bank_account': bank_account,
            'ali_account': phone_number  # 支付宝账号使用手机号
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        # 发送请求
        response = session.post(ADD_ADDRESS_URL, data=address_data, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    print("✅ 地址信息添加成功!")
                    return True
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 地址信息添加失败: {error_msg}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ 地址信息响应解析失败: {response.text}")
                return False
        else:
            print(f"❌ 地址信息请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 添加地址信息过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def lottery_draw(session, token):
    """进行抽奖"""
    try:
        print("🎰 正在进行抽奖...")

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Length': '0',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        # 发送抽奖请求
        response = session.post(LOTTERY_URL, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    prize_data = result.get('data', 0)
                    msg = result.get('msg', '恭喜中奖')
                    print(f"🎉 抽奖成功: {msg}")
                    print(f"🏆 奖品数据: {prize_data}")
                    return True, prize_data
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 抽奖失败: {error_msg}")
                    return False, None
            except json.JSONDecodeError:
                print(f"❌ 抽奖响应解析失败: {response.text}")
                return False, None
        else:
            print(f"❌ 抽奖请求失败，状态码: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"❌ 抽奖过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def submit_real_info(session, token, phone_number, name, id_card, address, front_url, back_url):
    """提交实名认证信息"""
    try:
        # 构造实名认证数据
        real_info_data = {
            'nickname': name,
            'sf_type': '1',  # 身份证类型
            'id_card': id_card,
            'sfaddress': address,
            'phone': phone_number,
            'id_card_image': f"{front_url},{back_url}"  # 正面,反面
        }

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Token': token,
            'User-Agent': session.headers.get('User-Agent', '')
        }

        print(f"📋 提交实名认证信息:")
        print(f"   姓名: {name}")
        print(f"   身份证: {id_card}")
        print(f"   地址: {address}")
        print(f"   手机号: {phone_number}")

        response = session.post(REAL_INFO_URL, data=real_info_data, headers=headers)

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📥 实名认证响应: {json.dumps(result, ensure_ascii=False)}")

                if result.get('code') == 1:
                    print("✅ 实名认证提交成功!")
                    return True, None
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 实名认证提交失败: {error_msg}")
                    return False, error_msg
            except json.JSONDecodeError:
                print(f"❌ 实名认证响应解析失败: {response.text}")
                return False, "响应解析失败"
        else:
            print(f"❌ 实名认证请求失败，状态码: {response.status_code}")
            return False, f"请求失败: {response.status_code}"

    except Exception as e:
        print(f"❌ 实名认证过程中发生错误: {e}")
        return False, str(e)

def auto_real_name_verification_with_retry(session, token, phone_number, max_retries=3):
    """自动实名认证流程 - 支持重试机制"""
    try:
        print("\n" + "=" * 50)
        print("🆔 开始自动实名认证流程 (支持重试)")
        print("=" * 50)

        used_id_cards = set()  # 记录已使用的身份证号，避免重复

        for attempt in range(max_retries):
            print(f"\n🔄 实名认证尝试 {attempt + 1}/{max_retries}")

            # 1. 获取身份证正面图片
            front_image = get_random_id_card_front_image()
            if not front_image:
                print("❌ 无法获取身份证图片，跳过实名认证")
                return False

            # 2. OCR识别身份证正面信息
            print(f"🔍 识别身份证信息...")
            name, id_card = extract_id_card_info(front_image)

            if not name or not id_card:
                print("❌ 身份证信息识别失败")
                if attempt < max_retries - 1:
                    print("🔄 尝试使用新的身份证图片...")
                    continue
                else:
                    print("❌ 多次识别失败，跳过实名认证")
                    return False

            # 检查是否已使用过这个身份证号
            if id_card in used_id_cards:
                print(f"⚠️ 身份证号 {id_card} 已在本次尝试中使用过，重新选择...")
                continue

            used_id_cards.add(id_card)

            # 3. 生成地址信息
            address = generate_real_address()

            print(f"\n👤 使用的身份信息:")
            print(f"   姓名: {name} (OCR识别)")
            print(f"   身份证: {id_card} (OCR识别)")
            print(f"   地址: {address} (随机生成)")

            # 4. 上传正面图片
            print(f"\n📤 上传身份证正面...")
            front_url = upload_image(session, front_image, token)
            if not front_url:
                print("❌ 正面图片上传失败")
                if attempt < max_retries - 1:
                    print("🔄 尝试重新上传...")
                    continue
                else:
                    return False

            # 5. 提交实名认证信息 (只使用正面图片)
            print(f"\n📋 提交实名认证...")
            success, error_msg = submit_real_info(session, token, phone_number, name, id_card, address, front_url, front_url)

            if success:
                print("🎉 实名认证完成!")
                print(f"✅ 认证信息: {name} ({id_card})")

                # 获取用户信息确认实名状态
                print(f"\n🔍 确认实名认证状态...")
                user_info = get_user_info(session, token)
                if user_info:
                    sfz_status = user_info.get('sfz_status', 0)
                    isshiming = user_info.get('isshiming', 0)
                    print(f"📋 实名状态: sfz_status={sfz_status}, isshiming={isshiming}")

                return True
            else:
                print(f"❌ 实名认证失败: {error_msg}")

                # 检查是否是"已绑定"错误，需要重试
                if error_msg and ("已绑定" in error_msg or "已存在" in error_msg or "重复" in error_msg or "已经绑定" in error_msg):
                    print("🔄 检测到身份证已被绑定，尝试使用新的身份证...")
                    if attempt < max_retries - 1:
                        print(f"⏳ 等待3秒后重试...")
                        time.sleep(3)
                        continue
                    else:
                        print("❌ 多次重试仍失败，可能所有身份证都已被使用")
                        return False
                else:
                    # 其他错误，不重试
                    print("❌ 实名认证失败，错误不可重试")
                    return False

        print("❌ 实名认证重试次数用尽")
        return False

    except Exception as e:
        print(f"❌ 实名认证流程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def auto_real_name_verification(session, token, phone_number):
    """自动实名认证流程 - 使用OCR识别真实身份信息"""
    try:
        print("\n" + "=" * 50)
        print("🆔 开始自动实名认证流程")
        print("=" * 50)

        # 1. 获取身份证正面图片
        front_image = get_random_id_card_front_image()
        if not front_image:
            print("❌ 无法获取身份证图片，跳过实名认证")
            return False

        # 2. OCR识别身份证正面信息
        print(f"\n� 识别身份证信息...")
        name, id_card = extract_id_card_info(front_image)

        if not name or not id_card:
            print("❌ 身份证信息识别失败，跳过实名认证")
            return False

        # 3. 生成地址信息
        address = generate_real_address()

        print(f"\n👤 使用的身份信息:")
        print(f"   姓名: {name} (OCR识别)")
        print(f"   身份证: {id_card} (OCR识别)")
        print(f"   地址: {address} (随机生成)")

        # 4. 上传正面图片
        print(f"\n📤 上传身份证正面...")
        front_url = upload_image(session, front_image, token)
        if not front_url:
            print("❌ 正面图片上传失败")
            return False

        # 5. 提交实名认证信息 (只使用正面图片)
        print(f"\n📋 提交实名认证...")
        success, error_msg = submit_real_info(session, token, phone_number, name, id_card, address, front_url, front_url)

        if success:
            print("🎉 实名认证完成!")
            print(f"✅ 认证信息: {name} ({id_card})")

            # 获取用户信息确认实名状态
            print(f"\n🔍 确认实名认证状态...")
            user_info = get_user_info(session, token)
            if user_info:
                sfz_status = user_info.get('sfz_status', 0)
                isshiming = user_info.get('isshiming', 0)
                sfnumber = user_info.get('sfnumber', '')
                nickname = user_info.get('nickname', '')

                print(f"📋 实名认证状态:")
                print(f"   身份证状态 (sfz_status): {sfz_status}")
                print(f"   实名状态 (isshiming): {isshiming}")
                print(f"   认证姓名: {nickname}")
                print(f"   身份证号: {sfnumber}")

                if sfz_status == 1:
                    print("✅ 实名认证完全成功!")
                    return True
                else:
                    print("⚠️ 实名认证状态异常，但提交成功")
                    return True
            else:
                print("⚠️ 无法获取用户信息，但实名认证提交成功")
                return True
        else:
            print(f"❌ 实名认证失败: {error_msg}")

            # 检查是否是"已绑定"错误
            if error_msg and ("已绑定" in error_msg or "已存在" in error_msg or "重复" in error_msg):
                print("🔄 检测到身份证已被绑定，建议重新运行脚本使用新的身份证")

            return False

    except Exception as e:
        print(f"❌ 实名认证流程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

# --- 登录函数 ---

def auto_login(phone_number, password, session=None, login_proxy=None):
    """自动登录验证账号"""
    try:
        print(f"\n🔑 正在验证登录: {phone_number}")

        if session is None:
            session = requests.Session()
            # 生成设备指纹和请求头
            fingerprint, _ = generate_device_fingerprint_for_request()
            headers = generate_request_headers(fingerprint)
            session.headers.update(headers)

        # 生成登录的sign参数 (只包含手机号和密码)
        login_sign = generate_sign_parameter(phone_number, password)

        if not login_sign:
            print("❌ 生成登录签名失败")
            return False, None

        # 构造登录请求数据
        login_data = {
            'sign': login_sign
        }

        # 发送登录请求 (使用代理)
        login_url = f"{BASE_URL}/api/login/login"

        # 为登录请求临时设置代理
        original_proxies = session.proxies.copy()
        if login_proxy:
            session.proxies.update(login_proxy)
            print(f"🌐 使用代理发送登录请求")
        else:
            print("📡 使用直连发送登录请求")

        response = session.post(login_url, data=login_data)

        # 恢复原始代理设置
        session.proxies = original_proxies

        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 1:
                    userinfo = result.get('data', {}).get('userinfo', {})
                    token = userinfo.get('token', '')
                    user_id = userinfo.get('user_id', '')

                    print(f"✅ 登录成功!")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   过期时间: {userinfo.get('expires_in', 0)}秒")

                    return True, {
                        'token': token,
                        'user_id': user_id,
                        'userinfo': userinfo
                    }
                else:
                    print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
                    return False, None
            except json.JSONDecodeError:
                print(f"❌ 登录响应解析失败: {response.text}")
                return False, None
        else:
            print(f"❌ 登录请求失败，状态码: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"💥 登录过程中发生错误: {e}")
        return False, None

# --- 主要注册函数 ---

def auto_register():
    """自动注册账号 - 集成设备指纹伪装和代理IP"""
    proxy_manager = None
    try:
        print("=" * 60)
        print("🚀 环球网站自动注册脚本启动")
        print("=" * 60)
        print(f"💾 账号保存路径: {OUTPUT_FILE}")
        print(f"📋 保存格式: 手机号:密码:设备名称:用户ID:Token前缀:代理IP:状态")
        print("=" * 60)

        # 0. 初始化代理管理器 (如果启用)
        if PROXY_CONFIG['enabled']:
            print("🌐 正在初始化代理管理器...")
            try:
                proxy_manager = ProxyManager(
                    packid=PROXY_CONFIG['packid'],
                    uid=PROXY_CONFIG['uid'],
                    access_name=PROXY_CONFIG['access_name'],
                    access_password=PROXY_CONFIG['access_password'],
                    protocol=PROXY_CONFIG['protocol'],
                    time_duration=PROXY_CONFIG['time_duration']
                )

                # 获取代理IP
                proxy_dict = proxy_manager.get_proxy(force_new=True)
                if proxy_dict:
                    print(f"✅ 代理IP获取成功")
                    # 从代理URL中提取IP地址用于显示
                    proxy_url = proxy_dict['http']
                    if '@' in proxy_url:
                        ip_part = proxy_url.split('@')[1]
                        print(f"   代理地址: {ip_part}")
                else:
                    print("❌ 代理IP获取失败，将使用直连")
                    proxy_manager = None
                    proxy_dict = None
            except Exception as e:
                print(f"❌ 代理初始化失败: {e}")
                proxy_manager = None
                proxy_dict = None
        else:
            print("📡 代理功能已禁用，使用直连")
            proxy_dict = None

        # 1. 生成设备指纹
        print("📱 正在生成设备指纹...")
        fingerprint, device_params = generate_device_fingerprint_for_request()

        print(f"🔧 设备信息:")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   型号: {fingerprint['model']}")
        print(f"   iOS版本: {fingerprint['ios_version'].replace('_', '.')}")
        print(f"   屏幕分辨率: {fingerprint['screen_info']['width']}x{fingerprint['screen_info']['height']}")
        print(f"   设备ID: {fingerprint['device_id'][:20]}...")
        print(f"   语言: {fingerprint['language']}")
        print(f"   时区: {fingerprint['timezone']}")

        # 2. 生成随机账号信息 (使用真实手机号)
        phone_number = generate_real_phone_number()
        password = generate_random_password()

        print(f"\n� 生成的账号信息:")
        print(f"   手机号: {phone_number}")
        print(f"   密码: {password} (长度: {len(password)}位，符合6-11位要求)")

        # 3. 创建会话并设置请求头
        session = requests.Session()

        # 准备代理配置 (注册和登录专用)
        register_proxy = None
        if proxy_dict:
            register_proxy = proxy_dict
            print(f"🌐 注册/登录将使用代理: {proxy_dict.get('http', '未知')}")
        else:
            print("⚠️ 注意: 注册和登录建议使用代理IP")
            use_proxy = input("是否启用代理进行注册? (Y/N, 默认N): ").strip().upper()
            if use_proxy == 'Y':
                if proxy_manager:
                    proxy_data = proxy_manager.get_proxy()
                    if proxy_data:
                        register_proxy = {
                            'http': f"http://{proxy_data['username']}:{proxy_data['password']}@{proxy_data['ip']}:{proxy_data['port']}",
                            'https': f"http://{proxy_data['username']}:{proxy_data['password']}@{proxy_data['ip']}:{proxy_data['port']}"
                        }
                        print(f"🌐 启用代理: {register_proxy['http']}")
                    else:
                        print("❌ 获取代理失败，使用直连")
                else:
                    print("❌ 代理管理器未初始化，使用直连")

        print("📡 其他请求(验证码等)使用直连")

        # 生成完整的请求头
        base_headers = generate_request_headers(fingerprint)
        session.headers.update(base_headers)

        print(f"\n📱 User-Agent: {base_headers['User-Agent'][:80]}...")

        # 4. 获取验证码并识别 (智能重试机制)
        max_captcha_retries = 5  # 增加重试次数
        captcha_code = None
        code_id = None

        for captcha_attempt in range(max_captcha_retries):
            print(f"\n🔍 正在获取验证码... (尝试 {captcha_attempt + 1}/{max_captcha_retries})")
            code_id, captcha_image = get_captcha_image(session)

            if not code_id or not captcha_image:
                print("❌ 获取验证码失败")
                if captcha_attempt < max_captcha_retries - 1:
                    print("🔄 等待3秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

            # 5. 识别验证码
            print("🤖 正在识别验证码...")
            captcha_result = recognize_captcha(captcha_image)

            if captcha_result == 'retry':
                print("🔄 用户选择重新获取验证码...")
                continue
            elif captcha_result == 'need_new_captcha':
                print("🔄 OCR识别多次失败，自动获取新的验证码图片...")
                continue
            elif captcha_result and len(captcha_result) == 4 and captcha_result.isdigit():
                captcha_code = captcha_result
                print(f"✅ 验证码识别成功: {captcha_code}")
                break
            else:
                print(f"❌ 验证码识别结果异常: {captcha_result}")
                if captcha_attempt < max_captcha_retries - 1:
                    print("🔄 将获取新的验证码图片...")
                    continue
                else:
                    return False
        else:
            print("❌ 验证码获取/识别失败次数过多")
            return False

        if not captcha_code:
            print("❌ 未能获取有效的验证码")
            return False

        # 6. 获取邀请码和支付密码
        invitation = input("\n🎫 请输入邀请码 (默认: 267524): ").strip()
        if not invitation:
            invitation = "267524"

        paypassword = input("🔐 请输入支付密码 (默认: 147258): ").strip()
        if not paypassword:
            paypassword = "147258"

        # 7. 生成sign参数
        print("\n🔐 正在生成加密签名...")
        sign_value = generate_sign_parameter(phone_number, password)

        if not sign_value:
            print("❌ 生成签名失败")
            return False

        # 8. 构造注册请求数据
        register_data = {
            'sign': sign_value,
            'code_id': code_id,
            'code': captcha_code,
            'invitation': invitation,
            'paypassword': paypassword
        }

        print(f"\n📤 准备发送注册请求...")
        print(f"   设备: {fingerprint['device_name']}")
        print(f"   code_id: {code_id}")
        print(f"   验证码: {captcha_code}")
        print(f"   邀请码: {invitation}")
        print(f"   支付密码: {paypassword}")

        # 9. 发送注册请求
        register_headers = base_headers.copy()
        register_headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://huanqiu.cgi913.com',
            'Referer': 'https://huanqiu.cgi913.com/',
        })

        print("\n🚀 发送注册请求...")

        # 为注册请求临时设置代理
        original_proxies = session.proxies.copy()
        if register_proxy:
            session.proxies.update(register_proxy)
            print(f"🌐 使用代理发送注册请求: {register_proxy.get('http', '未知')}")
        else:
            print("📡 使用直连发送注册请求")

        response = session.post(REGISTER_URL, data=register_data, headers=register_headers)

        # 恢复原始代理设置
        session.proxies = original_proxies

        print(f"\n📥 服务器响应:")
        print(f"   状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")

                if result.get('code') == 1:
                    print("\n🎉 注册成功!")

                    # 获取注册返回的用户信息
                    userinfo = result.get('data', {}).get('userinfo', {})
                    user_id = userinfo.get('user_id', '')
                    token = userinfo.get('token', '')

                    print(f"📋 注册信息:")
                    print(f"   用户ID: {user_id}")
                    print(f"   Token: {token[:20]}...")
                    print(f"   昵称: {userinfo.get('nickname', '')}")

                    # 验证登录功能
                    print("\n🔍 验证登录功能...")
                    login_success, login_info = auto_login(phone_number, password, session, register_proxy)

                    if login_success:
                        print("✅ 登录验证通过!")

                        # 询问是否进行实名认证
                        do_real_name = input("\n🆔 是否进行自动实名认证? (Y/N，默认Y): ").strip().upper()
                        if do_real_name != 'N':
                            print("\n🚀 开始实名认证流程...")
                            real_name_success = auto_real_name_verification(session, token, phone_number)

                            if real_name_success:
                                print("🎉 实名认证完成!")

                                # 获取实名认证后的用户信息
                                final_user_info = get_user_info(session, token)
                                if final_user_info:
                                    final_nickname = final_user_info.get('nickname', '')

                                    # 添加地址信息
                                    print("\n📍 开始添加地址信息...")
                                    address_success = add_address_info(session, token, final_nickname, phone_number)

                                    if address_success:
                                        print("✅ 地址信息添加成功!")

                                        # 进行抽奖
                                        print("\n🎰 开始抽奖...")
                                        lottery_success, prize_data = lottery_draw(session, token)

                                        if lottery_success:
                                            print("🎉 抽奖成功!")
                                            print("✅ 完整流程: 注册 → 登录 → 实名认证 → 添加地址 → 抽奖 全部完成!")

                                            # 保存完整的账号信息
                                            status = f"注册+登录+实名认证+地址+抽奖成功(奖品:{prize_data})"
                                        else:
                                            print("⚠️ 抽奖失败，但其他流程已完成")
                                            status = "注册+登录+实名认证+地址成功,抽奖失败"
                                    else:
                                        print("⚠️ 地址信息添加失败")
                                        status = "注册+登录+实名认证成功,地址添加失败"
                                else:
                                    print("⚠️ 无法获取用户信息")
                                    status = "注册+登录+实名认证成功,后续流程失败"

                                print("🎉 完整流程成功!")

                                # 再次获取用户信息显示最终状态
                                final_user_info = get_user_info(session, token)
                                if final_user_info:
                                    final_nickname = final_user_info.get('nickname', '')
                                    final_sfnumber = final_user_info.get('sfnumber', '')
                                    print(f"🆔 最终认证信息: {final_nickname} ({final_sfnumber})")

                                account_status = status  # 使用之前设置的详细状态
                            else:
                                print("⚠️ 实名认证失败，但账号注册成功")
                                account_status = "注册+登录成功,实名认证失败"
                        else:
                            account_status = "注册+登录成功,跳过实名认证"

                        # 获取代理IP信息用于保存
                        proxy_ip = "直连"
                        if proxy_manager and proxy_manager.current_proxy_data:
                            proxy_ip = f"{proxy_manager.current_proxy_data['ip']}:{proxy_manager.current_proxy_data['port']}"

                        # 保存完整的账号信息 (包含代理IP)
                        account_info = (
                            f"{phone_number}:{password}:{fingerprint['device_name']}:"
                            f"{user_id}:{token[:20]}...:{proxy_ip}:{account_status}\n"
                        )

                        # 确保输出目录存在
                        import os
                        os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)

                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)

                        print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                        print(f"📱 设备信息: {fingerprint['device_name']} ({fingerprint['model']})")
                        print(f"🌐 使用IP: {proxy_ip}")
                        return True
                    else:
                        print("⚠️ 注册成功但登录验证失败")

                        # 获取代理IP信息
                        proxy_ip = "直连"
                        if proxy_manager and proxy_manager.current_proxy_data:
                            proxy_ip = f"{proxy_manager.current_proxy_data['ip']}:{proxy_manager.current_proxy_data['port']}"

                        # 仍然保存账号信息
                        account_info = f"{phone_number}:{password}:{fingerprint['device_name']}:未知:{proxy_ip}:登录验证失败\n"

                        # 确保输出目录存在
                        import os
                        os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)

                        with open(OUTPUT_FILE, "a", encoding="utf-8") as f:
                            f.write(account_info)

                        print(f"✅ 账号信息已保存到: {OUTPUT_FILE}")
                        print(f"🌐 使用IP: {proxy_ip}")
                        return True
                else:
                    print(f"\n❌ 注册失败: {result.get('msg', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"   响应内容 (非JSON): {response.text}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"\n💥 注册过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 释放代理资源
        if proxy_manager:
            proxy_manager.release_proxy()

# --- 主程序入口 ---

if __name__ == "__main__":
    try:
        print("🌟 环球网站自动注册脚本")
        print("🔧 基于JavaScript逆向分析实现")
        print("=" * 50)

        while True:
            success = auto_register()

            if success:
                print("\n✅ 本次注册完成!")
                print(f"💾 账号已保存到: {OUTPUT_FILE}")
                print("💡 使用 python view_accounts.py 查看所有账号")
            else:
                print("\n❌ 本次注册失败!")

            user_input = input("\n是否继续注册下一个账号? (Y/N，直接回车=Y): ").strip().upper()
            if user_input == 'N':
                break
            print("\n" + "=" * 50)

        print("\n👋 感谢使用环球网站自动注册脚本!")
        print("📋 相关工具:")
        print("   python view_accounts.py  # 查看和管理账号")
        print("   python proxy_config.py  # 配置代理设置")

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出。")
    except Exception as e:
        print(f"\n💥 程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()