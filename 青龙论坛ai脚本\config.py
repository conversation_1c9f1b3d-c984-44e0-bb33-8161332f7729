import os

# ======================= 1. 用户配置区 =======================

# --- 爬虫配置 ---
# 要监控的论坛板块URL列表，可以添加多个
# 每个项目包含 'name' (板块名称) 和 'url' (板块链接)
FORUM_URLS = [
    {"name": "推荐热帖", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=225&filter=lastpost&orderby=lastpost"},
    {"name": "羊毛专区", "url": "https://www.51kanong.com/yh-282-1.htm"},
    {"name": "老哥生活", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=2603"},
    {"name": "数藏专区", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=15"},
    {"name": "下款线报", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=23"},
    {"name": "羊毛交流", "url": "https://www.51kanong.com/forum.php?mod=forumdisplay&fid=169&filter=lastpost&orderby=lastpost"},
]

# --- 论坛登录配置 ---
# 如果需要登录论坛获取更多内容，请填写以下信息
# 如果不需要登录，可以留空
FORUM_USERNAME = ""  # 论坛用户名
FORUM_PASSWORD = ""  # 论坛密码

# --- 浏览器配置 ---
# 是否使用无头模式（不显示浏览器窗口）
HEADLESS_MODE = True
# 浏览器等待页面加载的时间（秒）
PAGE_LOAD_WAIT = 5
# 是否启用浏览器缓存
ENABLE_CACHE = True

# --- AI 分析配置 ---
# 您的服务器在国外，可以直接使用Google官方API地址
# 注意：这里不再需要 /gemini 路径
GEMINI_API_HOST = "https://generativelanguage.googleapis.com" 
# 【安全提示】已填入您提供的API Key。强烈建议您在测试后，立即更换为一个新生成的、未公开的密钥。
GEMINI_API_KEY = "AIzaSyBAapmvw5zq9s3ro2EjGH4NgY0BNhAODgw"
# 定义你的分析指令 (Prompt)
AI_PROMPT_TEMPLATE = """
作为一名专业的论坛"羊毛"价值分析师，请你严格、客观地分析以下论坛帖子的内容，并以清晰、简洁、有条理的方式输出。

**分析要求:**
1.  **核心价值判断**: 一句话精准总结这个帖子的核心价值是什么？（例如：XX银行的XX活动、某个APP的漏洞、一个薅羊毛的机会、或是无价值的水帖）
2.  **关键信息提取**:
    *   **活动/福利**: 如果是活动或福利，说明具体是什么？（例如：5元微信立减金、话费券、实物等）
    *   **参与方式**: 如何参与这个活动？（例如：需要下载什么APP、通过什么链接、完成什么任务）
    *   **限制条件**: 有什么限制？（例如：新用户专享、地域限制、需要有实体卡等）
3.  **价值评分**: 从0到10分，给这个帖子的价值打分，并简单说明理由。（10分表示价值极高，不容错过；0分表示毫无价值）
4.  **总结建议**: 根据以上分析，给用户一个明确的、可执行的建议。（例如：建议立即参与、建议XX地区用户尝试、观望一下、价值不高忽略即可）

**输出格式:**
请严格按照以下Markdown格式返回，不要有任何多余的解释或开头语。

```markdown
**AI分析报告**

*   **核心价值**: 
*   **关键信息**:
    *   **福利详情**: 
    *   **参与路径**: 
    *   **限制条件**: 
*   **价值评分**: 
*   **总结建议**: 
```

**原始帖子内容:**
---
**标题**: {title}

**正文**: 
{content}
---
"""

# --- 邮件通知配置 ---
# 根据您之前提供的信息填入
SMTP_SERVER = "smtp.qq.com"
SMTP_PORT = 465
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "djppyahldzyqdcdb"
RECEIVER_EMAIL = "<EMAIL>"

# --- 数据持久化文件 ---
# 用于存储已处理过的主题URL
SEEN_POSTS_FILE = os.path.join(os.path.dirname(__file__), 'seen_posts_51kanong.txt') 