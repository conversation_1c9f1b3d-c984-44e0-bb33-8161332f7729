#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版注册系统 - 集成代理IP绕过WAF
"""

import requests
import json
import time
import hashlib
import base64
import logging
import random
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from proxy_manager import ProxyManager
import ddddocr

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedRegisterBot:
    """增强版注册机器人 - 支持代理IP"""
    
    def __init__(self, use_proxy=True):
        self.use_proxy = use_proxy
        self.session = requests.Session()
        self.aes_key = "46505501ee188273".encode('utf-8')
        self.base_url = "https://zgwdb-app1.rizi666.com/dev-api"
        
        # 初始化代理管理器
        if self.use_proxy:
            self.proxy_manager = ProxyManager(
                packid="2",
                uid="42477", 
                access_name="ab263263",
                access_password="8C84B9BFE1774BFB2443DD34797EE4FB",
                protocol='http',
                time_duration=5  # 5分钟代理时长
            )
            logger.info("✅ 代理管理器已初始化")
        else:
            self.proxy_manager = None
            logger.info("⚠️ 未启用代理模式")
        
        # 初始化验证码识别器
        try:
            self.ocr = ddddocr.DdddOcr()
            logger.info("✅ 验证码识别器初始化成功")
        except Exception as e:
            logger.warning(f"验证码识别器初始化失败: {e}")
            self.ocr = None
    
    def aes_encrypt(self, data: dict) -> str:
        """AES加密 - 已验证正确"""
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
        cipher = AES.new(self.aes_key, AES.MODE_ECB)
        padded_data = pad(json_str.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return "mc543" + base64.b64encode(encrypted).decode('utf-8')
    
    def generate_sign(self, data: dict) -> str:
        """生成签名"""
        timestamp = data.get('time', int(time.time()))
        return hashlib.md5(f"time={timestamp}".encode('utf-8')).hexdigest()
    
    def get_current_proxy(self, max_retries=3):
        """获取当前可用的代理，支持重试"""
        if not self.use_proxy or not self.proxy_manager:
            return None

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试获取代理 (第{attempt+1}次)...")
                proxy = self.proxy_manager.get_proxy(force_new=True)
                if proxy:
                    logger.info(f"✅ 获取到代理: {proxy['http'].split('@')[1] if '@' in proxy['http'] else 'Unknown'}")
                    return proxy
                else:
                    logger.warning(f"第{attempt+1}次获取代理失败")
                    if attempt < max_retries - 1:
                        time.sleep(2)  # 等待2秒后重试
            except Exception as e:
                logger.error(f"第{attempt+1}次代理获取异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)

        logger.error("❌ 所有代理获取尝试都失败了")
        return None
    
    def init_session_with_proxy(self, max_retries=3):
        """使用代理初始化会话，如果代理失败则尝试直连"""
        logger.info("=== 初始化会话 ===")

        for attempt in range(max_retries):
            logger.info(f"--- 第 {attempt + 1} 次尝试 ---")

            # 获取新的代理（如果启用代理模式）
            current_proxy = None
            if self.use_proxy:
                current_proxy = self.get_current_proxy()
                if current_proxy:
                    logger.info("使用代理模式")
                else:
                    logger.warning("代理获取失败，尝试直连模式")
            else:
                logger.info("使用直连模式")

            try:
                # 访问主页
                main_url = "https://zgwdb-app1.rizi666.com"
                headers = {
                    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                    "Accept-Encoding": "gzip, deflate, br"
                }

                response = self.session.get(
                    main_url,
                    headers=headers,
                    proxies=current_proxy,
                    timeout=15,
                    verify=False  # 忽略SSL证书验证
                )

                logger.info(f"主页访问状态码: {response.status_code}")
                logger.info(f"获取Cookie: {dict(self.session.cookies)}")

                if response.status_code == 200:
                    # 等待随机时间
                    delay = random.uniform(2, 4)
                    logger.info(f"等待 {delay:.1f} 秒...")
                    time.sleep(delay)

                    # 访问登录页
                    login_url = "https://zgwdb-app1.rizi666.com/pages/login/login"
                    login_response = self.session.get(
                        login_url,
                        headers={**headers, "Referer": main_url},
                        proxies=current_proxy,
                        timeout=15,
                        verify=False
                    )

                    logger.info(f"登录页状态码: {login_response.status_code}")

                    if login_response.status_code == 200:
                        logger.info("✅ 会话初始化成功")
                        return current_proxy  # 返回使用的代理（可能为None）

                logger.warning(f"第 {attempt + 1} 次初始化失败，状态码: {response.status_code}")

            except Exception as e:
                logger.error(f"第 {attempt + 1} 次初始化异常: {e}")

                # 释放当前代理
                if self.proxy_manager and current_proxy:
                    self.proxy_manager.release_proxy()

            # 等待后重试
            if attempt < max_retries - 1:
                wait_time = random.uniform(3, 6)
                logger.info(f"等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)

        logger.error("❌ 所有初始化尝试都失败了")
        return False  # 明确返回False表示失败
    
    def get_captcha_with_proxy(self, proxy, max_retries=3):
        """使用代理获取验证码"""
        logger.info("=== 获取验证码 ===")
        
        for attempt in range(max_retries):
            logger.info(f"--- 验证码获取第 {attempt + 1} 次尝试 ---")
            
            try:
                timestamp = int(time.time())
                request_data = {"time": timestamp}
                encrypted_data = self.aes_encrypt(request_data)
                sign = self.generate_sign(request_data)
                
                logger.info(f"时间戳: {timestamp}")
                logger.info(f"加密数据: {encrypted_data}")
                logger.info(f"签名: {sign}")
                
                # 精确的请求头
                headers = {
                    "accept": "*/*",
                    "accept-encoding": "gzip, deflate, br, zstd",
                    "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                    "content-type": "application/json",
                    "origin": "https://zgwdb-app1.rizi666.com",
                    "priority": "u=1, i",
                    "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "sign": sign,
                    "token": "",
                    "transfersecret": encrypted_data,
                    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
                }
                
                api_url = f"{self.base_url}/api/login/authccode.html"
                
                # 添加随机延迟
                delay = random.uniform(1, 3)
                logger.info(f"发送请求前等待 {delay:.1f} 秒...")
                time.sleep(delay)
                
                response = self.session.post(
                    api_url,
                    headers=headers,
                    json=request_data,
                    proxies=proxy,
                    timeout=20,
                    allow_redirects=False,
                    verify=False
                )
                
                logger.info(f"验证码API状态码: {response.status_code}")
                logger.info(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.info(f"✅ 验证码获取成功!")
                        logger.info(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        
                        if result.get("code") == 200:
                            return True, result.get("data", {})
                        else:
                            logger.error(f"API返回错误: {result.get('message', '未知错误')}")
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}")
                        logger.error(f"响应内容: {response.text[:500]}")
                        
                elif response.status_code == 302:
                    location = response.headers.get('Location', 'Unknown')
                    logger.error(f"❌ 被重定向到: {location}")
                    
                    if 'weibo.com' in location:
                        logger.error("检测到WAF拦截，尝试更换代理...")
                        # 释放当前代理，下次会获取新的
                        if self.proxy_manager:
                            self.proxy_manager.release_proxy()
                        
                        # 获取新代理
                        new_proxy = self.get_current_proxy()
                        if new_proxy:
                            proxy = new_proxy
                            logger.info("已更换新代理，继续尝试...")
                        else:
                            logger.error("无法获取新代理")
                            break
                else:
                    logger.error(f"其他错误，状态码: {response.status_code}")
                    logger.error(f"响应内容: {response.text[:500]}")
                
            except Exception as e:
                logger.error(f"验证码获取异常: {e}")
                
                # 如果是代理相关错误，尝试更换代理
                if "proxy" in str(e).lower() or "connection" in str(e).lower():
                    logger.info("检测到代理连接问题，尝试更换代理...")
                    if self.proxy_manager:
                        self.proxy_manager.release_proxy()
                    new_proxy = self.get_current_proxy()
                    if new_proxy:
                        proxy = new_proxy
            
            # 重试前等待
            if attempt < max_retries - 1:
                wait_time = random.uniform(3, 6)
                logger.info(f"等待 {wait_time:.1f} 秒后重试...")
                time.sleep(wait_time)
        
        logger.error("❌ 验证码获取失败")
        return False, {}
    
    def recognize_captcha(self, base64_data: str) -> str:
        """识别验证码"""
        if not self.ocr or not base64_data:
            return ""
            
        try:
            if base64_data.startswith("data:image"):
                base64_data = base64_data.split(",")[1]
            
            image_bytes = base64.b64decode(base64_data)
            result = self.ocr.classification(image_bytes)
            logger.info(f"验证码识别结果: {result}")
            return result
        except Exception as e:
            logger.error(f"验证码识别失败: {e}")
            return ""

def main():
    """主函数"""
    logger.info("🚀 启动增强版注册系统（支持代理）")

    # 创建注册机器人
    bot = EnhancedRegisterBot(use_proxy=True)

    # 初始化会话
    proxy = bot.init_session_with_proxy()
    if proxy is False:  # 明确检查False
        logger.error("❌ 会话初始化失败，程序退出")
        return

    logger.info(f"会话初始化成功，代理状态: {'使用代理' if proxy else '直连模式'}")

    # 获取验证码
    success, captcha_data = bot.get_captcha_with_proxy(proxy)

    if success:
        logger.info("🎉 验证码获取成功!")

        # 识别验证码
        captcha_text = bot.recognize_captcha(captcha_data.get("img", ""))
        captcha_key = captcha_data.get("key", "")

        if captcha_text:
            logger.info(f"验证码识别结果: {captcha_text}")
            logger.info(f"验证码Key: {captcha_key}")
            logger.info("✅ 可以继续进行注册流程")
        else:
            logger.error("❌ 验证码识别失败")
    else:
        logger.error("❌ 验证码获取失败")

        # 如果验证码获取失败，尝试直连模式
        if proxy:  # 如果之前使用的是代理
            logger.info("尝试切换到直连模式...")
            bot.use_proxy = False
            direct_proxy = bot.init_session_with_proxy()
            if direct_proxy is not False:
                success, captcha_data = bot.get_captcha_with_proxy(None)
                if success:
                    logger.info("🎉 直连模式验证码获取成功!")
                    captcha_text = bot.recognize_captcha(captcha_data.get("img", ""))
                    if captcha_text:
                        logger.info(f"验证码识别结果: {captcha_text}")
                        logger.info("✅ 直连模式成功!")

if __name__ == "__main__":
    main()
