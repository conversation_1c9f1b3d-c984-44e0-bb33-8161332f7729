#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie分析脚本
分析acw_tc和cdn_sec_tc的生成规律
"""

import time
import hashlib
import random
import string

def analyze_cookie_pattern():
    """分析cookie模式"""
    cookie_value = "b482662717537651904677618e12037b20fcd5bc2c9d26153142038881"
    
    print("🔍 Cookie分析")
    print("=" * 50)
    print(f"Cookie值: {cookie_value}")
    print(f"长度: {len(cookie_value)}")
    
    # 尝试分解cookie
    print("\n📋 可能的组成部分:")
    
    # 前面部分可能是标识符
    prefix = cookie_value[:10]  # b482662717
    print(f"前缀: {prefix}")
    
    # 中间部分可能是时间戳
    timestamp_part = cookie_value[10:20]  # 5376519046
    print(f"时间戳部分: {timestamp_part}")
    
    # 检查是否为时间戳
    try:
        timestamp = int(timestamp_part)
        readable_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
        print(f"如果是时间戳: {readable_time}")
    except:
        print("不是标准时间戳格式")
    
    # 后面部分可能是哈希
    hash_part = cookie_value[20:]
    print(f"哈希部分: {hash_part}")
    print(f"哈希长度: {len(hash_part)}")
    
    return cookie_value

def generate_cookie_attempts():
    """尝试生成cookie"""
    print("\n🚀 尝试生成cookie")
    print("-" * 30)
    
    current_timestamp = int(time.time())
    print(f"当前时间戳: {current_timestamp}")
    
    # 尝试不同的组合
    attempts = []
    
    # 方法1: 固定前缀 + 时间戳 + 随机哈希
    prefix = "b48266271"
    timestamp_str = str(current_timestamp)
    random_hash = ''.join(random.choices('0123456789abcdef', k=40))
    attempt1 = prefix + timestamp_str + random_hash
    attempts.append(("固定前缀+时间戳+随机", attempt1))
    
    # 方法2: MD5(时间戳)
    md5_timestamp = hashlib.md5(str(current_timestamp).encode()).hexdigest()
    attempt2 = "b48266271" + md5_timestamp
    attempts.append(("固定前缀+MD5时间戳", attempt2))
    
    # 方法3: 基于用户代理的哈希
    user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X)"
    ua_hash = hashlib.md5((str(current_timestamp) + user_agent).encode()).hexdigest()
    attempt3 = "b48266271" + ua_hash
    attempts.append(("时间戳+UA哈希", attempt3))
    
    for method, cookie in attempts:
        print(f"{method}: {cookie[:50]}...")
    
    return attempts

def test_cookie_generation():
    """测试cookie生成"""
    print("\n🧪 Cookie生成测试")
    print("=" * 50)
    
    # 分析原始cookie
    analyze_cookie_pattern()
    
    # 生成尝试
    attempts = generate_cookie_attempts()
    
    print("\n💡 建议:")
    print("1. 在浏览器Network面板查看Set-Cookie响应头")
    print("2. 搜索JavaScript中的cookie设置代码")
    print("3. 这些cookie可能是CDN/WAF自动生成的")
    print("4. 可能需要先访问主页获取有效cookie")

if __name__ == "__main__":
    test_cookie_generation()
