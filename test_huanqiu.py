#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 环球网站注册脚本测试

def test_basic_functions():
    """测试基本功能"""
    print("🧪 测试基本功能")
    print("=" * 30)
    
    try:
        # 测试手机号生成
        from zhuccc1 import generate_real_phone_number
        phone = generate_real_phone_number()
        print(f"✅ 手机号生成: {phone}")
        
        # 测试密码生成
        from zhuccc1 import generate_random_password
        password = generate_random_password()
        print(f"✅ 密码生成: {password} (长度: {len(password)})")
        
        # 验证密码格式
        has_letter = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        if has_letter and has_digit and 8 <= len(password) <= 12:
            print("✅ 密码格式正确")
        else:
            print("❌ 密码格式错误")
            return False
        
        # 测试设备指纹生成
        from zhuccc1 import generate_device_fingerprint_for_request
        fingerprint, device_params = generate_device_fingerprint_for_request()
        print(f"✅ 设备指纹生成: {fingerprint['device_name']}")
        
        # 测试签名生成
        from zhuccc1 import generate_sign_parameter
        sign = generate_sign_parameter(phone, password)
        if sign:
            print(f"✅ 签名生成成功: {len(sign)} 字符")
            
            # 验证JSON格式
            import json
            try:
                parsed = json.loads(sign)
                if 'ct' in parsed and 'iv' in parsed and 's' in parsed:
                    print("✅ 签名格式正确")
                    return True
                else:
                    print("❌ 签名格式错误")
                    return False
            except:
                print("❌ 签名JSON解析失败")
                return False
        else:
            print("❌ 签名生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 环球网站注册脚本测试")
    print("🔧 验证核心功能是否正常")
    print("=" * 50)
    
    success = test_basic_functions()
    
    if success:
        print("\n🎉 所有测试通过!")
        print("💡 现在可以运行主脚本:")
        print("   python zhuccc1.py")
    else:
        print("\n❌ 测试失败，请检查代码")
