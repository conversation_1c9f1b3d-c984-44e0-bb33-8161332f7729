import sys
import os
import threading
import time
import random
import json
import itertools
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                            QTextEdit, QSpinBox, QFileDialog,
                            QProgressBar, QMessageBox)
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QRunnable, QThreadPool
import io
from PIL import Image, ImageQt

# 导入注册机类
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from 注册机 import ZyguobuRegister
    from data_generator import DataGenerator
except ImportError as e:
    print(f"无法导入注册机类: {e}")
    print("请确保 注册机.py 和 data_generator.py 文件存在且代码正确")
    sys.exit(1)

# 为工作线程定义信号
class WorkerSignals(QObject):
    status = pyqtSignal(str)
    account = pyqtSignal(dict)
    finished_one_task = pyqtSignal()

# QRunnable工作线程
class Worker(QRunnable):
    def __init__(self, invite_code, password, stats, stats_lock, golden_card, golden_card_lock):
        super(Worker, self).__init__()
        self.invite_code = invite_code
        self.password = password
        self.signals = WorkerSignals()
        # 每个线程拥有自己独立的注册机实例，但共享所有公共资源
        self.register_bot = ZyguobuRegister(stats, stats_lock, golden_card, golden_card_lock)

    def run(self):
        try:
            # 使用线程ID来标识日志来源
            thread_id = threading.get_ident()
            self.signals.status.emit(f"线程 {thread_id} 开始执行任务。")
            result_data = self.register_bot.run_full_workflow(
                self.invite_code,
                self.password
            )
            if result_data:
                self.signals.status.emit(f"线程 {thread_id} 任务成功: 账号 {result_data['account']}")
                self.signals.account.emit(result_data)
            else:
                self.signals.status.emit(f"线程 {thread_id} 任务失败。")
        finally:
            self.signals.finished_one_task.emit()

class RegisterUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.threadpool = QThreadPool()
        self.tasks_completed = 0
        self.total_tasks = 0
        self.is_stopping = False
        # 初始化统计相关变量
        self.stats = {}
        self.stats_lock = threading.Lock()
        self.stats_file = "bin_stats.json"
        # 初始化“黄金卡”相关变量
        self.golden_card = {}
        self.golden_card_lock = threading.Lock()

    def initUI(self):
        self.setWindowTitle('多线程智能学习注册工具 (黄金卡版)')
        self.setGeometry(300, 300, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        control_layout = QHBoxLayout()
        form_layout = QVBoxLayout()
        
        invite_layout = QHBoxLayout()
        invite_layout.addWidget(QLabel('邀请码(用,分隔):'))
        self.invite_code_input = QLineEdit('870276')
        invite_layout.addWidget(self.invite_code_input)
        form_layout.addLayout(invite_layout)
        
        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel('密码:'))
        self.password_input = QLineEdit('123456abc')
        password_layout.addWidget(self.password_input)
        form_layout.addLayout(password_layout)
        
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel('注册总数:'))
        self.count_spin = QSpinBox()
        self.count_spin.setRange(1, 1000)
        self.count_spin.setValue(10)
        count_layout.addWidget(self.count_spin)
        form_layout.addLayout(count_layout)
        
        # 新增：并发线程数控制
        thread_count_layout = QHBoxLayout()
        thread_count_layout.addWidget(QLabel('并发线程数:'))
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 50)
        self.thread_count_spin.setValue(5)
        thread_count_layout.addWidget(self.thread_count_spin)
        form_layout.addLayout(thread_count_layout)
        
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton('开始执行')
        self.start_btn.clicked.connect(self.start_register)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton('停止')
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_register)
        button_layout.addWidget(self.stop_btn)
        
        form_layout.addLayout(button_layout)
        control_layout.addLayout(form_layout, 1)
        
        status_layout = QVBoxLayout()
        status_layout.addWidget(QLabel('实时日志:'))
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        control_layout.addLayout(status_layout, 2)
        
        main_layout.addLayout(control_layout)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(QLabel('成功账号信息 (格式: JSON):'))
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        main_layout.addWidget(self.result_text)
        
        bottom_layout = QHBoxLayout()
        self.save_btn = QPushButton('保存结果')
        self.save_btn.clicked.connect(self.save_accounts)
        bottom_layout.addWidget(self.save_btn)
        
        self.clear_btn = QPushButton('清空结果')
        self.clear_btn.clicked.connect(self.clear_results)
        bottom_layout.addWidget(self.clear_btn)
        
        main_layout.addLayout(bottom_layout)
        
        self.accounts = []
        self.add_status("程序已启动，请设置参数并点击'开始执行'")
    
    def add_status(self, text):
        self.status_text.append(text)
        self.status_text.verticalScrollBar().setValue(self.status_text.verticalScrollBar().maximum())

    def start_register(self):
        invite_codes_raw = self.invite_code_input.text()
        invite_codes = [code.strip() for code in invite_codes_raw.split(',') if code.strip()]
        
        password = self.password_input.text()
        self.total_tasks = self.count_spin.value()
        thread_count = self.thread_count_spin.value()
        
        if not invite_codes:
            QMessageBox.warning(self, "警告", "请输入至少一个有效的邀请码")
            return
        if not password:
            QMessageBox.warning(self, "警告", "请输入密码")
            return
        
        self.clear_results()
        self.is_stopping = False
        self.tasks_completed = 0
        self.progress_bar.setMaximum(self.total_tasks)
        self.progress_bar.setValue(0)
        
        # --- 智能模块初始化 ---
        # 1. 初始化或加载BIN码成功率统计
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    self.stats = json.load(f)
                self.add_status(f"成功从 {self.stats_file} 加载历史统计数据。")
        except Exception as e:
            self.add_status(f"加载历史统计数据失败: {e}，将使用新的统计。")
            self.stats = {}
        data_gen_for_init = DataGenerator()
        for b in data_gen_for_init.target_bins:
            if b['bin'] not in self.stats:
                self.stats[b['bin']] = {'success': 0, 'failure': 0}
        self.add_status("智能学习计分板已初始化。")
        
        # 2. 重置“黄金卡”系统
        self.golden_card.clear()
        self.add_status("“黄金卡”系统已重置，开始新的狩猎。")
        # -------------------------
        
        self.threadpool.setMaxThreadCount(thread_count)
        self.add_status(f"线程池启动，最大并发数: {thread_count}，总任务数: {self.total_tasks}")
        self.add_status(f"将使用以下邀请码循环注册: {invite_codes}")
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        invite_code_cycler = itertools.cycle(invite_codes)
        
        for i in range(self.total_tasks):
            current_invite_code = next(invite_code_cycler)
            worker = Worker(current_invite_code, password, self.stats, self.stats_lock, self.golden_card, self.golden_card_lock)
            worker.signals.status.connect(self.add_status)
            worker.signals.account.connect(self.add_account)
            worker.signals.finished_one_task.connect(self.task_finished)
            self.threadpool.start(worker)
    
    def stop_register(self):
        self.is_stopping = True
        self.add_status("正在发送停止信号...队列中的新任务将被取消。")
        self.threadpool.clear()
        self.stop_btn.setEnabled(False)
        self.add_status(f"当前有 {self.threadpool.activeThreadCount()} 个任务正在运行，将在完成后停止。")
        # 如果已经没有正在运行的线程，则直接更新UI状态
        if self.threadpool.activeThreadCount() == 0:
            self.all_tasks_done()

    def task_finished(self):
        self.tasks_completed += 1
        self.progress_bar.setValue(self.tasks_completed)
        
        if self.is_stopping and self.threadpool.activeThreadCount() == 0:
            self.all_tasks_done()
        elif not self.is_stopping and self.tasks_completed == self.total_tasks:
            self.all_tasks_done()

    def all_tasks_done(self):
        self.add_status("全部任务执行完毕。")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.is_stopping = False

        # --- 任务结束后，打印并保存统计报告 ---
        self.add_status("\n--- 本轮任务结束，BIN成功率统计 ---")
        sorted_stats = sorted(self.stats.items(), key=lambda item: (item[1]['success'] / (item[1]['success'] + item[1]['failure'] + 1)), reverse=True)
        for bin_code, data in sorted_stats:
            s = data['success']
            f = data['failure']
            total = s + f
            success_rate = (s / total * 100) if total > 0 else 0
            self.add_status(f"BIN: {bin_code} | 成功: {s} | 失败: {f} | 成功率: {success_rate:.2f}%")
        
        # 尝试保存最新的统计数据到文件
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=4)
            self.add_status(f"\n已将最新统计数据保存到 {self.stats_file}，下次启动将自动加载。")
        except Exception as e:
            self.add_status(f"\n保存统计数据失败: {e}")
        
        # 报告黄金卡
        if self.golden_card:
            self.add_status("\n--- 本轮发现“黄金卡” ---")
            self.add_status(f"银行: {self.golden_card['bank_name']}")
            self.add_status(f"卡号: {self.golden_card['bank_no']}")
    
    def add_account(self, account_data):
        self.accounts.append(account_data)
        self.result_text.append(json.dumps(account_data, ensure_ascii=False, indent=4))
        self.result_text.append("-" * 20)
    
    def save_accounts(self):
        if not self.accounts:
            QMessageBox.warning(self, "警告", "没有账号可保存")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(self, "保存账号", "accounts.txt", "Text Files (*.txt)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    for account in self.accounts:
                        f.write(json.dumps(account, ensure_ascii=False) + "\n")
                QMessageBox.information(self, "成功", f"成功保存 {len(self.accounts)} 个账号到 {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存账号失败: {str(e)}")
    
    def clear_results(self):
        self.accounts = []
        self.result_text.clear()
        self.status_text.clear()
        self.progress_bar.setValue(0)
        self.add_status("结果已清空")

def main():
    app = QApplication(sys.argv)
    window = RegisterUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 