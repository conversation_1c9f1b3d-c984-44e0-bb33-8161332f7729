#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 测试新的密码生成和加密方法

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_password_generation():
    """测试新的密码生成策略"""
    print("🧪 测试新密码生成策略")
    print("=" * 50)
    
    try:
        from zhuccc1 import generate_random_password
        
        print("📋 生成20个测试密码 (基于解密发现的格式):")
        
        password_stats = {
            'repeat_pattern': 0,    # 1111qqqq 类型
            'sequence_pattern': 0,  # 1234abcd 类型
            'random_pattern': 0,    # 5729xkwp 类型
            'simple_combo': 0,      # 1234qwer 类型
        }
        
        for i in range(20):
            password = generate_random_password()
            
            # 分析密码类型
            if len(password) == 8:
                digits = password[:4]
                letters = password[4:]
                
                # 判断类型
                if len(set(digits)) == 1 and len(set(letters)) == 1:
                    password_type = "重复模式"
                    password_stats['repeat_pattern'] += 1
                elif digits in ['1234', '2345', '3456', '4567', '5678', '6789']:
                    password_type = "序列模式"
                    password_stats['sequence_pattern'] += 1
                elif digits in ['1234', '5678', '9876'] and letters in ['qwer', 'asdf', 'zxcv']:
                    password_type = "简单组合"
                    password_stats['simple_combo'] += 1
                else:
                    password_type = "随机模式"
                    password_stats['random_pattern'] += 1
            else:
                password_type = "其他格式"
            
            print(f"   {i+1:2d}. {password:12s} ({password_type})")
        
        # 统计分析
        print(f"\n📊 统计分析:")
        print(f"   重复模式: {password_stats['repeat_pattern']}/20 ({password_stats['repeat_pattern']*5}%)")
        print(f"   序列模式: {password_stats['sequence_pattern']}/20 ({password_stats['sequence_pattern']*5}%)")
        print(f"   随机模式: {password_stats['random_pattern']}/20 ({password_stats['random_pattern']*5}%)")
        print(f"   简单组合: {password_stats['simple_combo']}/20 ({password_stats['simple_combo']*5}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_encryption():
    """测试新的加密方法"""
    print(f"\n🔐 测试新加密方法")
    print("=" * 50)
    
    try:
        from zhuccc1 import generate_sign_parameter
        
        # 测试用例 - 模拟解密成功的数据
        test_username = "16516546213"
        test_password = "1111qqqq"
        
        print(f"📝 测试数据:")
        print(f"   用户名: {test_username}")
        print(f"   密码: {test_password}")
        
        # 生成sign参数
        sign_result = generate_sign_parameter(test_username, test_password)
        
        if sign_result:
            print(f"✅ 加密成功!")
            print(f"📝 生成的sign参数:")
            print(f"   {sign_result}")
            
            # 解析sign参数
            import json
            sign_data = json.loads(sign_result)
            print(f"\n📋 sign参数结构:")
            print(f"   ct (密文): {sign_data['ct'][:50]}...")
            print(f"   iv (初始向量): {sign_data['iv']}")
            print(f"   s (盐值): {sign_data['s']}")
            
            return True
        else:
            print(f"❌ 加密失败")
            return False
            
    except Exception as e:
        print(f"❌ 加密测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_flow():
    """测试完整流程"""
    print(f"\n🚀 测试完整流程")
    print("=" * 50)
    
    try:
        from zhuccc1 import generate_random_password, generate_sign_parameter
        
        # 生成随机密码
        password = generate_random_password()
        username = "13800138000"  # 测试手机号
        
        print(f"📝 生成的测试数据:")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")
        
        # 生成sign参数
        sign_result = generate_sign_parameter(username, password)
        
        if sign_result:
            print(f"✅ 完整流程测试成功!")
            print(f"📝 可以用于注册的sign参数已生成")
            return True
        else:
            print(f"❌ 完整流程测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 新加密方法测试工具")
    print("🔧 基于解密成功的EVP_BytesToKey方法")
    print("🎯 密码格式: 4位数字 + 4位小写字母")
    print("=" * 60)
    
    # 测试密码生成
    password_test = test_password_generation()
    
    # 测试加密方法
    encryption_test = test_encryption()
    
    # 测试完整流程
    complete_test = test_complete_flow()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    if password_test:
        print("✅ 密码生成策略: 通过")
    else:
        print("❌ 密码生成策略: 失败")
    
    if encryption_test:
        print("✅ 加密方法: 通过")
    else:
        print("❌ 加密方法: 失败")
    
    if complete_test:
        print("✅ 完整流程: 通过")
    else:
        print("❌ 完整流程: 失败")
    
    if password_test and encryption_test and complete_test:
        print(f"\n🎉 所有测试通过! 可以开始注册测试了!")
        print(f"💡 建议:")
        print(f"   1. 运行主脚本: python zhuccc1.py")
        print(f"   2. 启用代理: Y")
        print(f"   3. 使用新的密码格式进行注册")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
