#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JWT Token解码器
"""

import base64
import json

def decode_jwt(token):
    """解码JWT Token"""
    try:
        # JWT格式: header.payload.signature
        parts = token.split('.')
        if len(parts) != 3:
            print("❌ 无效的JWT格式")
            return None
        
        header, payload, signature = parts
        
        # 解码header
        header_decoded = base64.b64decode(header + '==').decode('utf-8')
        header_json = json.loads(header_decoded)
        
        # 解码payload
        payload_decoded = base64.b64decode(payload + '==').decode('utf-8')
        payload_json = json.loads(payload_decoded)
        
        print("🔍 JWT Token解码结果:")
        print("=" * 50)
        print("📋 Header:")
        print(json.dumps(header_json, indent=2, ensure_ascii=False))
        print("\n📋 Payload:")
        print(json.dumps(payload_json, indent=2, ensure_ascii=False))
        print("\n📋 Signature:")
        print(signature)
        
        return {
            'header': header_json,
            'payload': payload_json,
            'signature': signature
        }
        
    except Exception as e:
        print(f"❌ JWT解码失败: {e}")
        return None

if __name__ == "__main__":
    jwt_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tGpffUz22_3iugK6Ca2wlCLig1I8IVkJqjrm4bn0vcQ"
    
    decode_jwt(jwt_token)
