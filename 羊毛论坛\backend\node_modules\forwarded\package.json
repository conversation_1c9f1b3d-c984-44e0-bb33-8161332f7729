{"name": "forwarded", "description": "Parse HTTP X-Forwarded-For header", "version": "0.2.0", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["x-forwarded-for", "http", "req"], "repository": "jshttp/forwarded", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "deep-equal": "1.0.1", "eslint": "7.27.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.4.0", "nyc": "15.1.0"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}