#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中广通证(zgzztk)APP注册系统 - 专用脚本
"""

import requests
import json
import time
import hashlib
import base64
import logging
import ddddocr
import random
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==============================================================================
# 网站配置
# ==============================================================================
SITE_CONFIG = {
    "name": "中广通证 (新)",
    "base_url": "https://zgzztk-web1.gyqmqey.com",
    # !!! 警告：这是一个占位密钥，你需要逆向分析JS找到正确的密钥并替换这里 !!!
    "aes_key": "46505501ee188273",
    "transfersecret_prefix": "",  # 新网站没有前缀
    "sign_method": "time_md5"
}


class CryptoManager:
    """加密管理器 - 基于网站配置进行加密和签名"""
    def __init__(self, config: dict):
        self.config = config
        self.aes_key = self.config['aes_key'].encode('utf-8')
        self.base_url = self.config['base_url'] + "/dev-api"
        logger.info(f"加密管理器已为网站 '{self.config['name']}' 初始化")
        if "PLEASE_FIND" in self.config['aes_key']:
            logger.warning("！！！警告：当前使用的是占位AES密钥，请务必找到真实密钥并更新配置！！！")

    def aes_encrypt(self, data: dict) -> str:
        """AES加密 - 使用ECB模式"""
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
        try:
            cipher = AES.new(self.aes_key, AES.MODE_ECB)
            padded_data = pad(json_str.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            b64_encrypted = base64.b64encode(encrypted).decode('utf-8')
            
            # 根据配置添加前缀
            result = self.config.get('transfersecret_prefix', '') + b64_encrypted
            logger.info(f"AES加密Payload: {json_str}")
            logger.info(f"AES加密结果 (transfersecret): {result}")
            return result
        except Exception as e:
            # 如果是占位密钥导致的padding错误，给出更明确的提示
            if "PLEASE_FIND" in self.config['aes_key']:
                 logger.error(f"AES加密失败: {e} - 这很可能是因为你还没有更新正确的AES密钥。")
            else:
                logger.error(f"AES加密失败: {e}")
            return ""

    def aes_decrypt(self, encrypted_data: str) -> dict:
        """AES解密"""
        try:
            # 移除前缀
            prefix = self.config.get('transfersecret_prefix', '')
            if prefix and encrypted_data.startswith(prefix):
                encrypted_data = encrypted_data[len(prefix):]

            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = AES.new(self.aes_key, AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)
            json_str = unpadded_data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            return {}

    def generate_md5(self, data: str) -> str:
        """MD5哈希"""
        return hashlib.md5(data.encode('utf-8')).hexdigest()

    def generate_sign(self, data: dict) -> str:
        """生成签名 - 基于配置"""
        timestamp = data.get('time', int(time.time()))
        
        if self.config['sign_method'] == 'time_md5':
            sign_str = f"time={timestamp}"
            sign = self.generate_md5(sign_str)
            logger.info(f"签名字符串: '{sign_str}', MD5结果 (sign): {sign}")
            return sign
        
        logger.error(f"未知的签名方法: {self.config['sign_method']}")
        return ""

class SimpleRegisterBot:
    """
    通用网站注册机器人
    """
    def __init__(self, config: dict):
        self.config = config
        self.crypto = CryptoManager(config)
        self.session = requests.Session()
        
        try:
            self.ocr = ddddocr.DdddOcr()
            logger.info("验证码识别器(ddddocr)初始化成功")
        except Exception as e:
            logger.warning(f"验证码识别器初始化失败: {e}")
            self.ocr = None
        
        self.session_initialized = self._initialize_session()

    def _initialize_session(self):
        """
        访问登录页以获取并激活服务端的会话Cookie (例如 acw_tc)。
        """
        logger.info(">>> 步骤0: 初始化会话，访问登录页以获取 WAF Cookie...")
        
        login_page_url = f"{self.config['base_url']}/pages/login/login"
        logger.info(f"正在访问登录页: {login_page_url}")

        page_headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9",
        }

        try:
            response = self.session.get(login_page_url, headers=page_headers, timeout=15)
            logger.info(f"登录页响应状态码: {response.status_code}")
            
            cookies_dict = self.session.cookies.get_dict()
            logger.info(f"初始化后获取到的Cookies: {cookies_dict}")

            if 'acw_tc' in cookies_dict:
                logger.info("✅ 会话初始化成功，已自动获取到 'acw_tc' Cookie。")
                return True
            else:
                logger.warning("⚠️ 会话初始化警告：未能从服务器获取到 'acw_tc' Cookie。")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 会话初始化失败，无法访问登录页: {e}")
            return False

    def get_captcha(self):
        """获取验证码"""
        logger.info("\n--- 步骤1: 获取验证码 ---")
        
        timestamp = int(time.time())
        request_data = {"time": timestamp}

        encrypted_data = self.crypto.aes_encrypt(request_data)
        sign = self.crypto.generate_sign(request_data)

        if not encrypted_data or not sign:
            logger.error("❌ 因加密或签名失败，无法继续获取验证码。")
            return False, {}

        # 构造一个随机的6位code，模拟浏览器行为
        random_code = ''.join(random.choices('0123456789', k=6))
        logger.info(f"为Referer生成随机code: {random_code}")

        api_headers = {
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json",
            "origin": self.config['base_url'],
            "referer": f"{self.config['base_url']}/pages/login/login?code={random_code}",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "sign": sign,
            "token": "",
            "transfersecret": encrypted_data,
        }

        url = f"{self.crypto.base_url}/api/login/authccode.html"

        try:
            logger.info(f"发送验证码请求到: {url}")
            logger.info(f"请求头: {json.dumps(api_headers)}")
            logger.info(f"请求体: {json.dumps(request_data)}")
            
            response = self.session.post(
                url,
                headers=api_headers,
                json=request_data,
                timeout=15
            )

            logger.info(f"验证码接口响应状态码: {response.status_code}")
            logger.info(f"响应头 Content-Type: {response.headers.get('Content-Type')}")

            if 'text/html' in response.headers.get('Content-Type', ''):
                logger.error("❌ 获取验证码失败：服务器返回了HTML页面，请求可能被WAF拦截或重定向。")
                return False, {}

            result = response.json()
            logger.info(f"解析后的JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

            if result.get("code") == 200:
                logger.info("✅ 验证码API请求成功。")
                return True, result.get("data", {})
            else:
                logger.error(f"❌ 验证码API返回错误: {result.get('msg', result.get('message', '未知错误'))}")
                return False, {}

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 验证码请求异常: {e}")
            return False, {}
        except json.JSONDecodeError:
            logger.error(f"❌ 响应JSON解析失败。原始响应内容: \n{response.text[:500]}...")
            return False, {}

    def recognize_captcha_from_base64(self, base64_data: str) -> str:
        """从Base64数据中识别验证码"""
        logger.info("\n--- 步骤2: 识别验证码 ---")
        if not self.ocr:
            logger.error("OCR识别器未初始化，无法识别。")
            return ""
        if not base64_data:
            logger.error("验证码图像数据为空，无法识别。")
            return ""
        
        try:
            if base64_data.startswith("data:image"):
                base64_data = base64_data.split(",", 1)[1]
            image_bytes = base64.b64decode(base64_data)
            result = self.ocr.classification(image_bytes)
            logger.info(f"✅ 验证码识别结果: {result}")
            return result
        except Exception as e:
            logger.error(f"❌ 验证码识别过程中发生错误: {e}")
            return ""
        
    def register_account(self, phone: str, password: str, captcha: str, captcha_key: str = ""):
        """注册账号"""
        logger.info("\n--- 步骤3: 注册账号 ---")
        timestamp = int(time.time())
        
        # 注意：注册接口的加密和签名可能与验证码接口不同，需逆向分析确认
        register_data = {
            "mobile": phone,
            "password": password,
            "confirm_password": password,
            "invitation_code": "SGA5TU", # 邀请码可能需要更新
            "code": captcha,
            "key": captcha_key,
            "time": timestamp
        }
        
        encrypted_data = self.crypto.aes_encrypt(register_data)
        
        # 注册接口的签名逻辑也需要确认
        sign_str = f"mobile={phone}&password={password}&time={timestamp}"
        sign = self.crypto.generate_md5(sign_str)
        logger.info(f"注册请求的签名源字符串: '{sign_str}', 结果: {sign}")

        if not encrypted_data:
            logger.error("❌ 因加密失败，无法继续注册。")
            return False, {}

        # 构造一个随机的6位code，模拟浏览器行为
        random_code = ''.join(random.choices('0123456789', k=6))
        logger.info(f"为Referer生成随机code: {random_code}")
        
        api_headers = {
            "sign": sign, 
            "token": "", 
            "transfersecret": encrypted_data,
            "accept": "*/*",
            "content-type": "application/json",
            "origin": self.config['base_url'],
            "referer": f"{self.config['base_url']}/pages/login/login?code={random_code}",
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        }

        url = f"{self.crypto.base_url}/api/login/register.html"
        request_body = {"time": timestamp} # 同样需要逆向确认
        
        try:
            response = self.session.post(url, headers=api_headers, json=request_body, timeout=10)
            result = response.json()
            
            if result.get("code") == 200:
                logger.info(f"✅ 注册成功！服务器响应: {result.get('msg', result.get('message'))}")
                return True, result
            else:
                logger.error(f"❌ 注册失败: {result.get('msg', result.get('message', '未知错误'))}")
                return False, result
        except Exception as e:
            logger.error(f"❌ 注册请求异常: {e}")
            return False, {}

def main():
    """主函数 - 完整流程测试"""
    logger.info(f"========== 开始为网站 '{SITE_CONFIG['name']}' 执行注册流程 ==========")
    
    bot = SimpleRegisterBot(SITE_CONFIG)
    
    if not bot.session_initialized:
        logger.error("程序终止：由于会话未能成功初始化，无法继续执行。")
        return

    # 步骤1: 获取验证码
    success, captcha_data = bot.get_captcha()

    if success and captcha_data:
        # 步骤2: 识别验证码
        captcha_text = bot.recognize_captcha_from_base64(captcha_data.get("image", ""))
        captcha_key = captcha_data.get("key", "")

        if captcha_text:
            # 步骤3: 注册账号
            logger.info("等待3秒，模拟用户输入...")
            time.sleep(3)
            # 请使用一个真实的、未注册的手机号进行测试
            bot.register_account("***********", "test123456", captcha_text, captcha_key)
        else:
            logger.error("❌ 验证码识别失败，流程中断。")
    else:
        logger.error("❌ 获取验证码失败，流程中断。")

if __name__ == "__main__":
    main()
