#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import random
import json
from datetime import datetime, timedelta
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# 获取脚本所在的目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 配置
OUTPUT_DIR = os.path.join(script_dir, "output")  # 输出目录
FONT_PATH = "C:/Windows/Fonts/simhei.ttf"  # 字体文件路径
TEMPLATE_FRONT = os.path.join(script_dir, "id_card_front_template.png")  # 身份证正面模板
TEMPLATE_BACK = os.path.join(script_dir, "id_card_back_template.png")  # 身份证背面模板

# 创建输出目录
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 计算最后一位校验码的类
class IdentityCard:
    __Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    __Ti = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

    @staticmethod
    def calculate(code):
        sum_value = 0
        for i in range(17):
            sum_value += int(code[i]) * IdentityCard.__Wi[i]
        # 'x' in __Ti should be uppercase 'X' to match standards
        return IdentityCard.__Ti[sum_value % 11].upper()

# 随机生成中文姓名和性别
def generate_name_and_gender():
    # 1 姓氏（所有姓氏）
    surnames = ['赵', '钱', '孙', '李', '周', '吴', '郑', '王', '冯', '陈', '褚', '卫', '蒋', '沈', '韩', '杨', '朱', '秦', '尤', '许',
                     '何', '吕', '施', '张', '孔', '曹', '严', '华', '金', '魏', '陶', '姜', '戚', '谢', '邹', '喻', '柏', '水', '窦', '章',
                     '云', '苏', '潘', '葛', '奚', '范', '彭', '郎', '鲁', '韦', '昌', '马', '苗', '凤', '花', '方', '俞', '任', '袁', '柳',
                     '酆', '鲍', '史', '唐', '费', '廉', '岑', '薛', '雷', '贺', '倪', '汤', '滕', '殷', '罗', '毕', '郝', '邬', '安', '常',
                     '乐', '于', '时', '傅', '皮', '卞', '齐', '康', '伍', '余', '元', '卜', '顾', '孟', '平', '黄', '和', '穆', '萧', '尹',
                     '姚', '邵', '湛', '汪', '祁', '毛', '禹', '狄', '米', '贝', '明', '臧', '计', '伏', '成', '戴', '谈', '宋', '茅', '庞',
                     '熊', '纪', '舒', '屈', '项', '祝', '董', '梁', '杜', '阮', '蓝', '闵', '席', '季', '麻', '强', '贾', '路', '娄', '危',
                     '江', '童', '颜', '郭', '梅', '盛', '林', '刁', '锺', '徐', '丘', '骆', '高', '夏', '蔡', '田', '樊', '胡', '凌', '霍',
                     '虞', '万', '支', '柯', '昝', '管', '卢', '莫', '经', '房', '裘', '缪', '干', '解', '应', '宗', '丁', '宣', '贲', '邓',
                     '郁', '单', '杭', '洪', '包', '诸', '左', '石', '崔', '吉', '钮', '龚', '程', '嵇', '邢', '滑', '裴', '陆', '荣', '翁',
                     '荀', '羊', '於', '惠', '甄', '麹', '家', '封', '芮', '羿', '储', '靳', '汲', '邴', '糜', '松', '井', '段', '富', '巫',
                     '乌', '焦', '巴', '弓', '牧', '隗', '山', '谷', '车', '侯', '宓', '蓬', '全', '郗', '班', '仰', '秋', '仲', '伊', '宫',
                     '甯', '仇', '栾', '暴', '甘', '钭', '厉', '戎', '祖', '武', '符', '刘', '景', '詹', '束', '龙', '叶', '幸', '司', '韶',
                     '郜', '黎', '蓟', '薄', '印', '宿', '白', '怀', '蒲', '邰', '从', '鄂', '索', '咸', '籍', '赖', '卓', '蔺', '屠', '蒙',
                     '池', '乔', '阴', '鬱', '胥', '能', '苍', '双', '闻', '莘', '党', '翟', '谭', '贡', '劳', '逄', '姬', '申', '扶', '堵',
                     '冉', '宰', '郦', '雍', '郤', '璩', '桑', '桂', '濮', '牛', '寿', '通', '边', '扈', '燕', '冀', '郏', '浦', '尚', '农',
                     '温', '别', '庄', '晏', '柴', '瞿', '阎', '充', '慕', '连', '茹', '习', '宦', '艾', '鱼', '容', '向', '古', '易', '慎',
                     '戈', '廖', '庾', '终', '暨', '居', '衡', '步', '都', '耿', '满', '弘', '匡', '国', '文', '寇', '广', '禄', '阙', '东',
                     '欧', '殳', '沃', '利', '蔚', '越', '夔', '隆', '师', '巩', '厍', '聂', '晁', '勾', '敖', '融', '冷', '訾', '辛', '阚',
                     '那', '简', '饶', '空', '曾', '毋', '沙', '乜', '养', '鞠', '须', '丰', '巢', '关', '蒯', '相', '查', '后', '荆', '红',
                     '游', '竺', '权', '逯', '盖', '益', '桓', '公', '万俟', '司马', '上官', '欧阳', '夏侯', '诸葛', '闻人', '东方', '赫连', '皇甫',
                     '尉迟', '公羊', '澹台', '公冶', '宗政', '濮阳', '淳于', '单于', '太叔', '申屠', '公孙', '仲孙', '轩辕', '令狐', '锺离', '宇文',
                     '长孙', '慕容', '鲜于', '闾丘', '司徒', '司空', '亓官', '司寇', '仉', '督', '子车', '颛孙', '端木', '巫马', '公西', '漆雕', '乐正',
                     '壤驷', '公良', '拓跋', '夹谷', '宰父', '穀梁', '晋', '楚', '闫', '法', '汝', '鄢', '涂', '钦', '段干', '百里', '东郭', '南门',
                     '呼延', '归海', '羊舌', '微生', '岳', '帅', '缑', '亢', '况', '後', '有', '琴', '梁丘', '左丘', '东门', '西门', '商', '牟',
                     '佘', '佴', '伯', '赏', '南宫', '墨', '哈', '谯', '笪', '年', '爱', '阳', '佟', '第五', '言', '福']
    # 男孩名字
    male_names = ['壮', '昱杰', '开虎', '凯信', '永斌', '方洲', '长发', '可人', '天弘', '炫锐', '富明', '俊枫',
                  '伟', '刚', '勇', '毅', '俊', '峰', '强', '军', '平', '保', '东', '文', '辉', '力', '明', '永', '健',
                  '世', '广', '志', '义', '兴', '良', '海', '山', '仁', '波', '宁', '贵', '福', '生', '龙', '元', '全',
                  '国', '胜', '学', '祥', '才', '发', '武', '新', '利', '清', '飞', '彬', '富', '顺', '信', '子', '杰',
                  '涛', '昌', '成', '康', '星', '光', '天', '达', '安', '岩', '中', '茂', '进', '林', '有', '坚', '和',
                  '彪', '博', '诚', '先', '敬', '震', '振', '壮', '会', '思', '群', '豪', '心', '邦', '承', '乐', '绍',
                  '功', '松', '善', '厚', '庆', '磊', '民', '友', '裕', '河', '哲', '江', '超', '浩', '亮', '政', '谦',
                  '亨', '奇', '固', '之', '轮', '翰', '朗', '伯', '宏', '言', '若', '鸣', '朋', '斌', '梁', '栋', '维',
                  '启', '克', '伦', '翔', '旭', '鹏', '泽', '晨', '辰', '士', '以', '建', '家', '致', '树', '炎', '德',
                  '行', '时', '泰', '盛', '雄', '琛', '钧', '冠', '策', '腾', '楠', '榕', '风', '航', '弘']
    # 女孩名字
    female_names = ['小玉', '蓝', '琬郡', '琛青', '予舴', '妙妙', '梓茵', '海蓉', '语娜', '馨琦', '晓馥', '佳翊',
                    '秀', '娟', '英', '华', '慧', '巧', '美', '娜', '静', '淑', '惠', '珠', '翠', '雅', '芝', '玉',
                    '萍', '红', '娥', '玲', '芬', '芳', '燕', '彩', '春', '菊', '兰', '凤', '洁', '梅', '琳', '素',
                    '云', '莲', '真', '环', '雪', '荣', '爱', '妹', '霞', '香', '月', '莺', '媛', '艳', '瑞', '凡',
                    '佳', '嘉', '琼', '勤', '珍', '贞', '莉', '桂', '娣', '叶', '璧', '璐', '娅', '琦', '晶', '妍',
                    '茜', '秋', '珊', '莎', '锦', '黛', '青', '倩', '婷', '姣', '婉', '娴', '瑾', '颖', '露', '瑶',
                    '怡', '婵', '雁', '蓓', '纨', '仪', '荷', '丹', '蓉', '眉', '君', '琴', '蕊', '薇', '菁', '梦',
                    '岚', '苑', '婕', '馨', '瑗', '琰', '韵', '融', '园', '艺', '咏', '卿', '聪', '澜', '纯', '毓',
                    '悦', '昭', '冰', '爽', '琬', '茗', '羽', '希', '宁', '欣', '飘', '育', '滢', '馥', '筠', '柔',
                    '竹', '霭', '凝', '晓', '欢', '霄', '枫', '芸', '菲', '寒', '伊', '亚', '宜', '可', '姬', '舒',
                    '影', '荔', '枝', '思', '丽']

    surname = random.choice(surnames)
    sex = random.choice([0, 1])  # 0 for female, 1 for male

    if sex == 1:
        name = random.choice(male_names)
        sex_text = "男"
    else:
        name = random.choice(female_names)
        sex_text = "女"

    return {
        "full_name": surname + name,
        "sex": sex,
        "sex_text": sex_text
    }

# 随机生成身份证号
def generate_id_number(sex): # sex: 0 for female, 1 for male
    # 区域码（前6位） - 使用更广泛的列表
    region_codes = [
        "110101", "110102", "110105", "110106", "110107", "110108", "110109", "110111", 
        "110112", "110113", "110114", "110115", "110116", "110117", "110118", "110119",
        "120101", "120102", "120103", "120104", "120105", "120106", "120110", "120111", 
        "120112", "120113", "120114", "120115", "120116", "120117", "120118", "120119",
        "130102", "130104", "130105", "130107", "130108", "130121", "130123", "130124", 
        "130125", "130126", "130127", "130128", "130129", "130130", "130131", "130132",
        "310101", "310104", "310105", "310106", "310107", "310109", "310110", "310112", 
        "310113", "310114", "310115", "310116", "310117", "310118", "310120", "310151",
        "320102", "320104", "320105", "320106", "320111", "320113", "320114", "320115",
        "320116", "320117", "320118", "320124", "320125",
        "440103", "440104", "440105", "440106", "440111", "440112", "440113", "440114",
        "440115", "440117", "440118",
        "440303", "440304", "440305", "440306", "440307", "440308", "440309", "440310",
        "510104", "510105", "510106", "510107", "510108", "510112", "510113", "510114",
        "510115", "510116", "510117", "510122", "510124", "510129", "510131", "510132",
        "520102", "520103", "520111", "520112", "520113", "520115", "520121", "520122",
        "520123", "520181"
    ]
    region_code = random.choice(region_codes)
    
    # 出生日期（中间8位）
    start_date = datetime(1960, 1, 1)
    end_date = datetime(2005, 12, 31)
    days_between = (end_date - start_date).days
    random_days = random.randint(0, days_between)
    birth_date = start_date + timedelta(days=random_days)
    date_str = birth_date.strftime("%Y%m%d")
    
    # 顺序码（第15-17位）
    sequence_prefix = str(random.randint(0, 99)).zfill(2)
    if sex == 1:  # Male
        gender_digit = str(random.choice([1, 3, 5, 7, 9]))
    else:  # Female
        gender_digit = str(random.choice([0, 2, 4, 6, 8]))
    sequence = sequence_prefix + gender_digit
    
    # 组成17位本体码
    id_base = region_code + date_str + sequence
    
    # 计算校验码
    check_code = IdentityCard.calculate(id_base)
    
    return id_base + check_code

# 生成身份证图片
def generate_id_card(name, id_number, sex_text):
    # 检查模板文件是否存在
    if not os.path.exists(TEMPLATE_FRONT) or not os.path.exists(TEMPLATE_BACK):
        print(f"错误: 模板文件不存在。请确保 {os.path.basename(TEMPLATE_FRONT)} 和 {os.path.basename(TEMPLATE_BACK)} 在当前目录。")
        return None, None
    
    # 检查字体文件是否存在
    if not os.path.exists(FONT_PATH):
        print(f"错误: 字体文件不存在。请确保 {FONT_PATH} 存在。")
        return None, None
    
    try:
        # 加载身份证正面模板
        front_img = Image.open(TEMPLATE_FRONT).convert("RGB")
        draw_front = ImageDraw.Draw(front_img)
        
        # 设置字体
        name_font = ImageFont.truetype(FONT_PATH, 80)  # 姓名字体
        id_font = ImageFont.truetype(FONT_PATH, 80)    # 身份证号字体
        
        # 在身份证上添加姓名
        draw_front.text((411,263), name, fill=(0, 0, 0), font=name_font)
        
        # 在身份证上添加身份证号
        draw_front.text((611,1069), id_number, fill=(0, 0, 0), font=id_font)
        
        # 加载身份证背面模板
        back_img = Image.open(TEMPLATE_BACK).convert("RGB")
        draw_back = ImageDraw.Draw(back_img)
        
        # 设置背面字体和内容
        back_font = ImageFont.truetype(FONT_PATH, 45)
        issuing_authority = "某某市公安局某某分局"
        
        # 生成随机的签发日期和有效期限
        issue_date_start = datetime.now() - timedelta(days=5*365)
        issue_date_end = datetime.now()
        random_days = random.randint(0, (issue_date_end - issue_date_start).days)
        issue_date = issue_date_start + timedelta(days=random_days)
        expire_date = issue_date.replace(year=issue_date.year + random.choice([10, 20]))
        validity_period = f"{issue_date.strftime('%Y.%m.%d')}-{expire_date.strftime('%Y.%m.%d')}"
        
        # 在身份证背面添加签发机关和有效期限
        draw_back.text((903, 943), issuing_authority, fill=(0, 0, 0), font=back_font)
        draw_back.text((921, 1088), validity_period, fill=(0, 0, 0), font=back_font)
        
        # 保存身份证图片
        front_path = os.path.join(OUTPUT_DIR, f"{id_number}_front.png")
        back_path = os.path.join(OUTPUT_DIR, f"{id_number}_back.png")
        
        front_img.save(front_path)
        back_img.save(back_path)
        
        # 保存身份证信息到JSON文件
        person_data = {
            "name": name,
            "id_number": id_number,
            "birth_date": id_number[6:14],
            "gender": sex_text,
            "address": "某省某市某区某街道",
            "issue_date": issue_date.strftime('%Y.%m.%d'),
            "expire_date": expire_date.strftime('%Y.%m.%d')
        }
        
        info_path = os.path.join(OUTPUT_DIR, f"{id_number}_info.json")
        with open(info_path, "w", encoding="utf-8") as f:
            json.dump(person_data, f, ensure_ascii=False, indent=4)
        
        return front_path, back_path
    
    except Exception as e:
        print(f"生成身份证图片时出错: {e}")
        return None, None

def main(num_cards=1):
    """生成指定数量的身份证"""
    print(f"准备生成 {num_cards} 套身份证图片...")
    
    for i in range(num_cards):
        name_info = generate_name_and_gender()
        name = name_info["full_name"]
        sex = name_info["sex"]
        sex_text = name_info["sex_text"]
        
        id_number = generate_id_number(sex)
        
        print(f"  (套 {i+1}/{num_cards}) 成功生成: {id_number}")
        print(f"    姓名: {name}")
        print(f"    身份证号: {id_number}")
        
        front_path, back_path = generate_id_card(name, id_number, sex_text)
        if not front_path or not back_path:
            print("  生成失败，跳过")
            continue
    
    print("\n所有任务完成。")

if __name__ == "__main__":
    main(1)  # 默认生成1套身份证
