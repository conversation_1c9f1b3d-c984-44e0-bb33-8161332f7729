"""
注册机主程序入口
"""
import os
import sys
import time
import random
from datetime import datetime

# 确保当前目录在模块搜索路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=" * 50)
    print("自动注册机 v1.6")
    print("=" * 50)
    
    # 导入注册机模块
    try:
        from ddddocr_patch import pillow_version
        print(f"当前Pillow版本: {'.'.join(map(str, pillow_version))}")
        
        from PIL import Image
        print(f"Pillow版本: {Image.__version__}")
        
        # 简化交互式输入，默认启用设备伪装
        print("\n注册机配置:")
        
        # 代理选择
        use_proxy = True  # 默认使用代理
        print("代理IP: 已启用 (默认)")
        
        # 默认使用51代理
        proxy_type = '51'  
        print(f"代理类型: 51代理 (默认，更可靠)")
                
        use_device_emulation = True  # 默认启用设备伪装
        print("设备伪装: 已启用 (默认)")
        
        # 导入注册机模块
        from 注册机 import RegisterBot
        
        # 尝试导入身份生成器
        try:
            from identity_generator import IdentityGenerator
            print("成功导入身份生成器模块")
        except ImportError:
            print("警告: 未找到身份生成器模块，将使用手动输入的身份信息")
            IdentityGenerator = None
        
        # 创建注册机实例 - 确保使用51代理
        bot = RegisterBot(use_proxy=use_proxy, use_device_emulation=use_device_emulation, proxy_type=proxy_type)
        
        # 测试51代理连接
        if use_proxy and proxy_type == '51':
            print("\n测试51代理连接...")
            try:
                current_ip = bot.get_current_ip()
                if current_ip and current_ip != "未知IP":
                    print(f"✅ 51代理IP获取成功: {current_ip}")
                else:
                    print("⚠️ 51代理IP获取失败，可能影响注册功能")
            except Exception as e:
                print(f"⚠️ 51代理测试异常: {e}")
        
        # 简化的菜单选项
        print("\n请选择操作:")
        print("1. 立即注册账号")
        print("2. 批量注册多个账号")
        print("3. 批量注册并自动实名认证")
        print("4. 定时注册 (每天8:00)")
        print("5. 登录已有账号")
        print("6. 实名认证")
        print("7. 每日签到")
        print("8. 退出")
        
        choice = input("请输入选项 [1]: ").strip() or "1"
        
        if choice == "1":
            # 立即注册单个账号
            invite_code = input("请输入邀请码 (留空则使用默认): ").strip()
            invite_code = invite_code if invite_code else None
            
            # 随机生成手机号和密码
            phone = None
            password = None
            
            # 设置最大尝试次数
            max_attempts = 5
            print(f"将尝试最多 {max_attempts} 次注册...")
            
            # 开始注册
            success, phone, password = bot.register(phone, password, invite_code, max_attempts=max_attempts)
            
            if success and phone and password:
                print("\n===== 注册成功信息 =====")
                print(f"手机号: {phone}")
                print(f"密码: {password}")
                print("========================")
                
                # 检查token文件是否存在
                if os.path.exists("account_tokens.txt"):
                    print("\n最新token信息:")
                    try:
                        with open("account_tokens.txt", "r", encoding="utf-8") as f:
                            lines = f.readlines()
                            if lines:
                                latest_token = lines[-1]
                                print(latest_token)
                    except Exception as e:
                        print(f"读取token信息失败: {e}")
            
        elif choice == "2":
            # 批量注册
            try:
                count = int(input("请输入要注册的账号数量 [3]: ").strip() or "3")
                invite_code = input("请输入邀请码 (留空则使用默认): ").strip()
                invite_code = invite_code if invite_code else None
                
                success_count = 0
                registered_accounts = []
                
                for i in range(count):
                    print(f"\n正在注册第 {i+1}/{count} 个账号...")
                    success, phone, password = bot.register(invite_code=invite_code, max_attempts=5)
                    
                    if success and phone and password:
                        success_count += 1
                        registered_accounts.append((phone, password))
                        print(f"成功注册 {success_count}/{count} 个账号")
                    
                    # 避免频繁请求
                    if i < count - 1:
                        wait_time = random.randint(5, 15)
                        print(f"等待 {wait_time} 秒后继续...")
                        time.sleep(wait_time)
                
                print(f"\n批量注册完成，成功 {success_count}/{count} 个账号")
                
                if registered_accounts:
                    print("\n===== 成功注册的账号 =====")
                    for idx, (phone, password) in enumerate(registered_accounts):
                        print(f"{idx+1}. 手机号: {phone}, 密码: {password}")
                    print("==========================")
                
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == "3":
            # 批量注册并自动实名认证
            try:
                count = int(input("请输入要注册的账号数量 [3]: ").strip() or "3")
                invite_code = input("请输入邀请码 (留空则使用默认): ").strip()
                invite_code = invite_code if invite_code else None
                
                # 确认是否使用随机生成的身份信息
                auto_verify = True
                print("将使用随机生成的真实姓名和身份证号进行实名认证")
                
                # 批量注册并自动实名认证
                successful_accounts = bot.batch_register_with_verify(
                    count=count,
                    invite_code=invite_code,
                    auto_verify=auto_verify
                )
                
                # 显示结果
                if successful_accounts:
                    print("\n===== 批量注册结果 =====")
                    print(f"账号信息已保存到 batch_accounts.txt")
                    
                    # 询问是否显示详细信息
                    show_details = input("是否显示所有账号详细信息? (y/n): ").strip().lower() == 'y'
                    if show_details:
                        for idx, account in enumerate(successful_accounts):
                            print(f"\n账号 {idx+1}:")
                            print(f"  手机号: {account['phone']}")
                            print(f"  密码: {account['password']}")
                            if account.get("token"):
                                print(f"  Token: {account['token'][:20]}...")
                            if account.get("verified"):
                                print(f"  实名状态: 已实名")
                                print(f"  姓名: {account.get('real_name')}")
                                print(f"  身份证: {account.get('id_card')}")
                            else:
                                print(f"  实名状态: 未实名")
                
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == "4":
            # 定时注册 (固定为每天8:00)
            print("已设置定时注册，将在每天 08:00 尝试注册")
            bot.auto_register_at_time(8, 0)
            
        elif choice == "5":
            # 登录已有账号
            phone = input("请输入手机号: ").strip()
            password = input("请输入密码: ").strip()
            
            if phone and password:
                token = bot.login(phone, password)
                if token:
                    print("\n===== 登录成功 =====")
                    print(f"手机号: {phone}")
                    print(f"Token: {token}")
                    print("===================")
            else:
                print("手机号和密码不能为空")
            
        elif choice == "6":
            # 实名认证
            print("\n===== 实名认证 =====")
            phone = input("请输入手机号: ").strip()
            password = input("请输入密码: ").strip()
            
            if not phone or not password:
                print("手机号和密码不能为空")
                return
            
            # 先登录获取token
            token = bot.login(phone, password)
            
            if token:
                # 询问是否使用随机生成的身份信息
                use_random = input("是否使用随机生成的身份信息? (y/n): ").strip().lower() == 'y'
                
                if use_random and IdentityGenerator:
                    identity = IdentityGenerator.generate_identity()
                    real_name = identity["name"]
                    id_card = identity["id_card"]
                    print(f"随机生成的身份信息: 姓名={real_name}, 身份证号={id_card}")
                else:
                    real_name = input("请输入真实姓名: ").strip()
                    id_card = input("请输入身份证号: ").strip()
                
                if real_name and id_card:
                    bot.real_name_verify(token, real_name, id_card)
                else:
                    print("姓名和身份证号不能为空")
            else:
                print("登录失败，无法进行实名认证")
            
        elif choice == "7":
            # 每日签到
            bot.daily_signin()
            
        else:
            print("感谢使用，再见!")
    
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保已安装所有必要的依赖库")
        print("可以运行 'pip install -r requirements.txt' 安装所有依赖")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main() 