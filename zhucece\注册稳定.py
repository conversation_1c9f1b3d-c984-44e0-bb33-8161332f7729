#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国稳定币APP注册系统 - 方案一：通过初始化会话自动获取Cookie
"""

import requests
import json
import time
import hashlib
import base64
import logging
import ddddocr
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CryptoManager:
    """加密管理器 - 基于逆向分析"""
    def __init__(self):
        # 尝试多个可能的密钥
        self.possible_keys = [
            "46505501ee188273",  # 当前使用的密钥
            "mc543",  # 从浏览器调试中看到的前缀
            "mc543OvgzIxgNVwOvE/gyhOj0HPV3n0j4AhdtLbP9fs=",  # 完整的transfersecret值
        ]
        self.aes_key = self.possible_keys[0].encode('utf-8')
        self.base_url = "https://zgwdb-app1.rizi666.com/dev-api"

    def aes_encrypt(self, data: dict) -> str:
        """AES加密 - 已验证正确的实现"""
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)

        try:
            cipher = AES.new(self.aes_key, AES.MODE_ECB)
            padded_data = pad(json_str.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            result = "mc543" + base64.b64encode(encrypted).decode('utf-8')
            logger.info(f"AES加密结果: {result}")
            return result
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            return ""

    def test_encryption_methods(self, data: dict):
        """测试多种加密方法"""
        logger.info("=== 测试加密方法 ===")
        json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)

        methods = [
            {
                "name": "AES-ECB + Base64",
                "func": lambda: self._aes_ecb_encrypt(json_str)
            },
            {
                "name": "Base64 Only",
                "func": lambda: base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
            },
            {
                "name": "MD5 Hash",
                "func": lambda: self.generate_md5(json_str)
            }
        ]

        results = {}
        for method in methods:
            try:
                result = method["func"]()
                results[method["name"]] = result
                logger.info(f"{method['name']}: {result}")
            except Exception as e:
                logger.error(f"{method['name']} 失败: {e}")

        return results

    def _aes_ecb_encrypt(self, text: str) -> str:
        """纯AES-ECB加密"""
        cipher = AES.new(self.aes_key, AES.MODE_ECB)
        padded_data = pad(text.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode('utf-8')

    def aes_decrypt(self, encrypted_data: str) -> dict:
        """AES解密"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = AES.new(self.aes_key, AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)
            json_str = unpadded_data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            return {}

    def generate_md5(self, data: str) -> str:
        """MD5哈希"""
        return hashlib.md5(data.encode('utf-8')).hexdigest()

    def generate_sign(self, data: dict) -> str:
        """生成签名 - 基于浏览器逆向分析"""
        # 方法1: 基于时间戳的简单MD5
        timestamp = data.get('time', int(time.time()))
        sign1 = self.generate_md5(f"time={timestamp}")

        # 方法2: 基于所有参数的MD5
        params = []
        for key in sorted(data.keys()):
            params.append(f"{key}={data[key]}")
        param_str = "&".join(params)
        sign2 = self.generate_md5(param_str)

        # 方法3: 固定盐值的MD5
        salt = "your_secret_salt"  # 这个需要从JS中找到
        sign3 = self.generate_md5(f"{param_str}{salt}")

        logger.info(f"签名方法1 (time): {sign1}")
        logger.info(f"签名方法2 (params): {sign2}")
        logger.info(f"签名方法3 (salt): {sign3}")

        return sign1  # 默认使用方法1

class SimpleRegisterBot:
    """
    实现了方案一的注册机器人:
    1. 在初始化时先访问登录页，让session自动获取并存储WAF Cookie (如 acw_tc)。
    2. 后续所有API请求都使用这个带有有效Cookie的session对象。
    """
    def __init__(self):
        self.crypto = CryptoManager()
        # 创建一个会话对象，它将在所有请求中自动管理Cookie
        self.session = requests.Session()
        
        # 识别器初始化
        try:
            self.ocr = ddddocr.DdddOcr()
            logger.info("验证码识别器初始化成功")
        except Exception as e:
            logger.warning(f"验证码识别器初始化失败: {e}")
            self.ocr = None
        
        # === 核心改动：初始化会话以获取Cookie ===
        self.session_initialized = self._initialize_session()

    def _initialize_session(self):
        """
        方案一的核心：访问登录页以获取并激活服务端的会话Cookie (例如 acw_tc)。
        这个步骤模拟了浏览器首先加载页面的行为，是绕过WAF的关键。
        """
        logger.info(">>> 步骤0: 初始化会话，访问登录页以获取 WAF Cookie...")

        login_page_url = "https://zgwdb-app1.rizi666.com/pages/login/login"

        # 模拟一个普通浏览器的头信息去访问页面
        page_headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Upgrade-Insecure-Requests": "1"
        }

        try:
            # 使用 self.session 发送请求。requests库会自动处理服务器返回的 Set-Cookie
            logger.info(f"正在访问登录页: {login_page_url}")
            response = self.session.get(login_page_url, headers=page_headers, timeout=15)

            logger.info(f"登录页响应状态码: {response.status_code}")
            logger.info(f"登录页响应头: {dict(response.headers)}")
            logger.info(f"登录页最终URL: {response.url}")
            logger.info(f"登录页Content-Type: {response.headers.get('Content-Type', 'N/A')}")

            # 检查响应内容
            if response.status_code == 200:
                logger.info(f"登录页响应长度: {len(response.text)}")

                # 检查 session 中是否成功设置了关键的 Cookie
                cookies_dict = self.session.cookies.get_dict()
                logger.info(f"获取到的Cookies: {cookies_dict}")

                if 'acw_tc' in cookies_dict:
                    logger.info("✅ 会话初始化成功，已自动获取到 'acw_tc' Cookie。")
                    return True
                else:
                    logger.warning("⚠️ 会话初始化警告：未能从服务器获取到 'acw_tc' Cookie。")
                    logger.info("尝试直接访问API端点来触发Cookie设置...")

                    # 尝试访问API基础路径
                    api_base_url = "https://zgwdb-app1.rizi666.com/dev-api"
                    api_response = self.session.get(api_base_url, headers=page_headers, timeout=10)
                    logger.info(f"API基础路径响应状态码: {api_response.status_code}")

                    cookies_dict = self.session.cookies.get_dict()
                    logger.info(f"访问API后的Cookies: {cookies_dict}")

                    return 'acw_tc' in cookies_dict
            else:
                logger.error(f"登录页访问失败，状态码: {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 会话初始化失败，无法访问登录页: {e}")
            return False

    def test_domain_access(self):
        """测试域名访问和DNS解析"""
        logger.info("=== 测试域名访问和DNS解析 ===")

        # 首先测试DNS解析
        import socket
        try:
            domain = "zgwdb-app1.rizi666.com"
            ip_address = socket.gethostbyname(domain)
            logger.info(f"DNS解析结果: {domain} -> {ip_address}")
        except Exception as e:
            logger.error(f"DNS解析失败: {e}")
            return

        # 测试不同的访问方式
        test_configs = [
            {
                "url": "https://zgwdb-app1.rizi666.com",
                "allow_redirects": True,
                "description": "主域名(允许重定向)"
            },
            {
                "url": "https://zgwdb-app1.rizi666.com",
                "allow_redirects": False,
                "description": "主域名(禁止重定向)"
            },
            {
                "url": f"https://{ip_address}",
                "allow_redirects": False,
                "description": "直接IP访问",
                "headers": {"Host": "zgwdb-app1.rizi666.com"}
            },
            {
                "url": "https://zgwdb-app1.rizi666.com/dev-api/api/login/authccode.html",
                "allow_redirects": False,
                "description": "API端点(禁止重定向)"
            }
        ]

        for config in test_configs:
            try:
                logger.info(f"测试: {config['description']}")
                logger.info(f"URL: {config['url']}")

                headers = config.get('headers', {})
                response = self.session.get(
                    config['url'],
                    timeout=10,
                    allow_redirects=config['allow_redirects'],
                    headers=headers
                )

                logger.info(f"状态码: {response.status_code}")
                logger.info(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
                logger.info(f"响应长度: {len(response.text)}")
                logger.info(f"最终URL: {response.url}")

                if 'Location' in response.headers:
                    logger.info(f"重定向目标: {response.headers['Location']}")

                logger.info("---")
            except Exception as e:
                logger.error(f"访问失败: {e}")
                logger.info("---")

    def _send_raw_request(self, url, headers, data):
        """发送原始HTTP请求，完全模拟浏览器"""
        import urllib.parse
        import ssl
        import socket

        logger.info("使用原始HTTP请求...")

        # 解析URL
        parsed = urllib.parse.urlparse(url)
        host = parsed.hostname
        port = parsed.port or 443
        path = parsed.path

        # 构建HTTP请求
        request_body = json.dumps(data, separators=(',', ':'))

        # 获取Cookie字符串
        cookie_str = "; ".join([f"{cookie.name}={cookie.value}" for cookie in self.session.cookies])

        # 构建完整的HTTP请求头
        http_request = f"POST {path} HTTP/1.1\r\n"
        http_request += f"Host: {host}\r\n"

        # 按照真实请求的顺序添加请求头
        header_order = [
            'accept', 'accept-encoding', 'accept-language', 'content-length',
            'content-type', 'origin', 'priority', 'referer', 'sec-fetch-dest',
            'sec-fetch-mode', 'sec-fetch-site', 'sign', 'token', 'transfersecret',
            'user-agent'
        ]

        for header_name in header_order:
            if header_name in headers:
                value = headers[header_name]
                # 特殊处理token字段 - 确保它是空的但存在
                if header_name == 'token' and value == '':
                    http_request += f"{header_name}:\r\n"
                else:
                    http_request += f"{header_name}: {value}\r\n"

        # 添加Cookie
        if cookie_str:
            http_request += f"Cookie: {cookie_str}\r\n"

        # 添加Content-Length
        http_request += f"Content-Length: {len(request_body.encode('utf-8'))}\r\n"
        http_request += f"Connection: close\r\n"
        http_request += "\r\n"
        http_request += request_body

        logger.info(f"原始HTTP请求:\n{http_request}")

        try:
            # 创建SSL连接
            context = ssl.create_default_context()
            sock = socket.create_connection((host, port), timeout=10)
            ssock = context.wrap_socket(sock, server_hostname=host)

            # 发送请求
            ssock.send(http_request.encode('utf-8'))

            # 接收响应
            response_data = b""
            while True:
                chunk = ssock.recv(4096)
                if not chunk:
                    break
                response_data += chunk

            ssock.close()

            # 解析响应
            response_str = response_data.decode('utf-8', errors='ignore')
            logger.info(f"原始HTTP响应:\n{response_str[:1000]}...")

            # 创建一个模拟的response对象
            class MockResponse:
                def __init__(self, response_str):
                    lines = response_str.split('\r\n')
                    status_line = lines[0]
                    self.status_code = int(status_line.split()[1])

                    # 解析头部
                    self.headers = {}
                    body_start = 0
                    for i, line in enumerate(lines[1:], 1):
                        if line == '':
                            body_start = i + 1
                            break
                        if ':' in line:
                            key, value = line.split(':', 1)
                            self.headers[key.strip()] = value.strip()

                    # 解析响应体
                    self.text = '\r\n'.join(lines[body_start:])

                def json(self):
                    return json.loads(self.text)

            return MockResponse(response_str)

        except Exception as e:
            logger.error(f"原始HTTP请求失败: {e}")
            raise

    def get_captcha(self):
        """获取验证码 - 增强版反反爬"""
        logger.info("=== 步骤1: 获取验证码 ===")

        # 先测试域名访问
        self.test_domain_access()

        timestamp = int(time.time())
        request_data = {"time": timestamp}

        # 使用已验证正确的加密方法
        encrypted_data = self.crypto.aes_encrypt(request_data)
        sign = self.crypto.generate_sign(request_data)

        logger.info(f"加密数据: {encrypted_data}")
        logger.info(f"签名: {sign}")

        # 计算请求体的Content-Length
        request_body_str = json.dumps(request_data, separators=(',', ':'))
        content_length = len(request_body_str.encode('utf-8'))

        # 请求API的头信息 - 完全按照抓包数据，增加更多变体
        base_headers = {
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "content-type": "application/json",
            "origin": "https://zgwdb-app1.rizi666.com",
            "priority": "u=1, i",
            "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        }

        # 尝试多种请求头组合
        header_variants = [
            {
                **base_headers,
                "sign": sign,
                "token": "",
                "transfersecret": encrypted_data,
                "content-length": str(content_length),
                "name": "标准请求头"
            },
            {
                **base_headers,
                "sign": sign,
                "transfersecret": encrypted_data,
                "name": "无token请求头"
            },
            {
                **base_headers,
                "transfersecret": encrypted_data,
                "name": "无sign请求头"
            },
            {
                **base_headers,
                "name": "纯净请求头"
            }
        ]

        # 使用第一个请求头变体作为默认
        api_headers = header_variants[0].copy()
        api_headers.pop('name', None)  # 移除name字段

        logger.info(f"计算的Content-Length: {content_length}")
        logger.info(f"请求体字符串: {request_body_str}")

        # 如果自动获取Cookie失败，使用备用Cookie
        if not self.session.cookies.get('acw_tc'):
            logger.warning("未检测到acw_tc Cookie，尝试使用备用Cookie...")
            backup_cookies = {
                'acw_tc': 'b482661517532890784836565e37aa615429b0c587bd4e60703e7996f1',
                'cdn_sec_tc': 'b482661517532890784836565e37aa615429b0c587bd4e60703e7996f1'
            }
            for name, value in backup_cookies.items():
                self.session.cookies.set(name, value)
        # session会自动合并 'User-Agent' 等初始化时设置的通用头和这里的API专用头
        url = f"{self.crypto.base_url}/api/login/authccode.html"

        try:
            logger.info(f"发送请求到: {url}")
            logger.info(f"请求头: {api_headers}")
            logger.info(f"请求体: {request_data}")

            # 添加随机延迟，模拟真实用户行为
            import random
            delay = random.uniform(2, 5)
            logger.info(f"等待 {delay:.1f} 秒，模拟真实用户行为...")
            time.sleep(delay)

            # 尝试多种请求方式
            attempts = [
                {
                    "url": url,
                    "description": "原始请求(模拟浏览器)",
                    "allow_redirects": False,
                    "method": "raw"
                },
                {
                    "url": url,
                    "description": "标准请求",
                    "allow_redirects": True
                },
                {
                    "url": url,
                    "description": "禁止重定向请求",
                    "allow_redirects": False
                }
            ]

            # 如果有IP地址，也尝试直接IP请求
            try:
                import socket
                domain = "zgwdb-app1.rizi666.com"
                ip_address = socket.gethostbyname(domain)
                ip_url = url.replace(domain, ip_address)
                ip_headers = api_headers.copy()
                ip_headers["Host"] = domain

                attempts.append({
                    "url": ip_url,
                    "description": "直接IP请求",
                    "allow_redirects": False,
                    "headers": ip_headers
                })
            except:
                pass

            response = None
            for attempt in attempts:
                try:
                    logger.info(f"尝试: {attempt['description']}")

                    if attempt.get('method') == 'raw':
                        # 使用原始HTTP请求
                        response = self._send_raw_request(url, api_headers, request_data)
                    else:
                        # 使用requests库
                        headers = attempt.get('headers', api_headers)
                        response = self.session.post(
                            attempt['url'],
                            headers=headers,
                            json=request_data,
                            timeout=10,
                            allow_redirects=attempt['allow_redirects']
                        )

                    logger.info(f"[{attempt['description']}] 状态码: {response.status_code}")

                    # 如果是成功的JSON响应，就使用这个
                    if (response.status_code == 200 and
                        'application/json' in response.headers.get('Content-Type', '')):
                        logger.info(f"✅ {attempt['description']} 成功!")
                        break

                except Exception as e:
                    logger.error(f"[{attempt['description']}] 失败: {e}")
                    continue

            if not response:
                logger.error("所有请求尝试都失败了")
                return False, {}

            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            logger.info(f"最终请求URL: {response.url}")
            logger.info(f"是否发生重定向: {len(response.history) > 0}")
            if response.history:
                logger.info(f"重定向历史: {[r.url for r in response.history]}")

            # 只打印响应内容的前500字符，避免日志过长
            response_preview = response.text[:500] + "..." if len(response.text) > 500 else response.text
            logger.info(f"响应内容预览: {response_preview}")

            # 检查是否还是返回HTML (被拦截的标志)
            if 'text/html' in response.headers.get('Content-Type', ''):
                logger.error("❌ 获取验证码失败：服务器返回了HTML页面，请求被重定向或WAF拦截。")
                logger.error("可能的原因：1. 域名不正确 2. WAF拦截 3. 需要特殊的请求头")
                return False, {}

            # 尝试解析JSON
            try:
                result = response.json()
                logger.info(f"解析后的JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                if result.get("code") == 200:
                    logger.info("✅ 验证码API请求成功，收到JSON数据。")
                    return True, result.get("data", {})
                else:
                    logger.error(f"❌ 验证码API返回错误: {result.get('message', '未知错误')}")
                    return False, {}
            except json.JSONDecodeError as e:
                logger.error(f"❌ 响应JSON解析失败: {e}")
                logger.error(f"原始响应内容: {response.text}")

                # 尝试解密响应内容
                if response.text:
                    logger.info("尝试解密响应内容...")
                    try:
                        # 如果响应是加密的，尝试解密
                        decrypted = self.crypto.aes_decrypt(response.text.strip())
                        logger.info(f"解密后的响应: {decrypted}")
                    except Exception as decrypt_e:
                        logger.error(f"解密失败: {decrypt_e}")

                return False, {}

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 请求异常: {e}")
            return False, {}

    def recognize_captcha_from_base64(self, base64_data: str) -> str:
        """从Base64数据中识别验证码"""
        logger.info("\n--- 步骤2: 识别验证码 ---")
        if not self.ocr or not base64_data: return ""
        if base64_data.startswith("data:image"): base64_data = base64_data.split(",")[1]
        image_bytes = base64.b64decode(base64_data)
        result = self.ocr.classification(image_bytes)
        logger.info(f"验证码识别结果: {result}")
        return result
        
    def register_account(self, phone: str, password: str, captcha: str, captcha_key: str = ""):
        """注册账号"""
        logger.info("=== 步骤3: 注册账号 ===")
        timestamp = int(time.time())
        register_data = {"mobile": phone, "password": password, "confirm_password": password, "invitation_code": "SGA5TU", "code": captcha, "key": captcha_key, "time": timestamp}
        encrypted_data = self.crypto.aes_encrypt(register_data)
        sign = self.crypto.generate_md5(f"mobile={phone}&password={password}&time={timestamp}")
        
        # 更新专用的API头， session会自动合并
        api_headers = {
            "sign": sign, 
            "token": "", 
            "transfersecret": encrypted_data,
            "accept": "*/*",
            "content-type": "application/json",
            "origin": "https://zgwdb-app1.rizi666.com",
            "referer": "https://zgwdb-app1.rizi666.com/pages/login/login"
        }

        url = f"{self.crypto.base_url}/api/login/register.html"
        request_body = {"time": timestamp}
        
        try:
            response = self.session.post(url, headers=api_headers, json=request_body, timeout=10)
            result = response.json()
            if result.get("code") == 200:
                logger.info("✅ 注册成功！")
                return True, result
            else:
                logger.error(f"❌ 注册失败: {result.get('message', '未知错误')}")
                return False, result
        except Exception as e:
            logger.error(f"❌ 注册请求异常: {e}")
            return False, {}

def main():
    """主函数 - 完整流程测试"""
    # 创建注册机器人实例时，会自动完成会话初始化
    bot = SimpleRegisterBot()
    
    # 如果会话初始化失败，直接退出
    if not bot.session_initialized:
        logger.error("程序终止：由于会话未能成功初始化，无法继续执行。")
        return

    # 步骤1: 获取验证码
    success, captcha_data = bot.get_captcha()

    if success:
        # 步骤2: 识别验证码
        logger.info("\n--- 步骤2: 识别验证码 ---")
        captcha_text = bot.recognize_captcha_from_base64(captcha_data.get("image", ""))
        captcha_key = captcha_data.get("key", "")

        if captcha_text:
            # 步骤3: 注册账号
            logger.info("等待3秒，模拟用户输入...")
            time.sleep(3)
            bot.register_account("***********", "test123456", captcha_text, captcha_key)
        else:
            logger.error("❌ 验证码识别失败。")
    else:
        logger.error("❌ 流程中断，获取验证码失败。")

if __name__ == "__main__":
    main()