#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MLKit OCR 安装脚本
自动安装和配置Google Cloud Vision API
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误: {e.stderr}")
        return False

def check_credentials():
    """检查凭证文件"""
    credentials_files = [
        'google-cloud-credentials.json',
        'credentials.json',
        'service-account-key.json'
    ]
    
    for file in credentials_files:
        if os.path.exists(file):
            print(f"✅ 找到凭证文件: {file}")
            return file
    
    print("❌ 未找到凭证文件")
    return None

def test_mlkit():
    """测试MLKit OCR"""
    try:
        print("🧪 测试MLKit OCR...")
        from mlkit_ocr import MLKitOCR
        
        ocr = MLKitOCR()
        if ocr.is_available:
            print("✅ MLKit OCR 测试成功")
            return True
        else:
            print("❌ MLKit OCR 不可用")
            return False
    except Exception as e:
        print(f"❌ MLKit OCR 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 MLKit OCR 安装和配置")
    print("=" * 50)
    
    # 1. 安装Google Cloud Vision API
    if not install_package("google-cloud-vision"):
        print("❌ 安装失败，请手动运行: pip install google-cloud-vision")
        return
    
    # 2. 检查凭证文件
    credentials_file = check_credentials()
    if not credentials_file:
        print("\n💡 请按照以下步骤设置凭证:")
        print("1. 访问 Google Cloud Console")
        print("2. 创建服务账号并下载JSON密钥文件")
        print("3. 将文件重命名为 'google-cloud-credentials.json'")
        print("4. 放置在项目根目录")
        return
    
    # 3. 设置环境变量
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = os.path.abspath(credentials_file)
    print(f"🔑 环境变量已设置: {credentials_file}")
    
    # 4. 测试MLKit OCR
    if test_mlkit():
        print("\n🎉 MLKit OCR 安装和配置完成！")
        print("现在可以使用高精度的身份证识别功能了")
    else:
        print("\n❌ 配置有问题，请检查:")
        print("1. Google Cloud项目是否启用了Vision API")
        print("2. 服务账号是否有正确的权限")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
