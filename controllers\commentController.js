const Comment = require('../models/commentModel');
const Deal = require('../models/dealModel');
const Notification = require('../models/notificationModel');

// @desc    创建评论
// @route   POST /api/comments
// @access  Private
exports.createComment = async (req, res) => {
  try {
    // 设置评论作者
    req.body.author = req.user.id;

    // 检查活动是否存在
    const deal = await Deal.findById(req.body.deal);
    if (!deal) {
      return res.status(404).json({
        success: false,
        message: '未找到相关活动'
      });
    }

    // 创建评论
    const comment = await Comment.create(req.body);

    // 填充评论作者信息
    await comment.populate({
      path: 'author',
      select: 'username avatar creditScore level'
    });

    // 如果是回复评论，创建通知
    if (req.body.parent) {
      const parentComment = await Comment.findById(req.body.parent);
      
      // 确保不给自己发通知
      if (parentComment && parentComment.author.toString() !== req.user.id) {
        await Notification.create({
          recipient: parentComment.author,
          sender: req.user.id,
          type: 'reply',
          content: `${req.user.username} 回复了你的评论`,
          deal: req.body.deal,
          comment: comment._id
        });
      }
    } 
    // 如果是直接评论活动，给活动作者发通知
    else if (deal.author.toString() !== req.user.id) {
      await Notification.create({
        recipient: deal.author,
        sender: req.user.id,
        type: 'comment',
        content: `${req.user.username} 评论了你的活动`,
        deal: req.body.deal,
        comment: comment._id
      });
    }

    res.status(201).json({
      success: true,
      data: comment
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    获取活动的所有评论
// @route   GET /api/comments/deal/:dealId
// @access  Public
exports.getDealComments = async (req, res) => {
  try {
    // 获取主评论（非回复）
    const mainComments = await Comment.find({ 
      deal: req.params.dealId,
      parent: null,
      isDeleted: false
    })
    .populate({
      path: 'author',
      select: 'username avatar creditScore level'
    })
    .sort({ createdAt: -1 });

    // 获取所有回复
    for (let comment of mainComments) {
      const replies = await Comment.find({
        parent: comment._id,
        isDeleted: false
      })
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .sort({ createdAt: 1 });

      comment._doc.replies = replies;
    }

    res.status(200).json({
      success: true,
      count: mainComments.length,
      data: mainComments
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    删除评论
// @route   DELETE /api/comments/:id
// @access  Private
exports.deleteComment = async (req, res) => {
  try {
    const comment = await Comment.findById(req.params.id);

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    // 确保用户是评论作者或管理员
    if (comment.author.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        message: '未授权删除此评论'
      });
    }

    // 软删除评论
    comment.isDeleted = true;
    comment.content = '此评论已删除';
    await comment.save();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    更新评论
// @route   PUT /api/comments/:id
// @access  Private
exports.updateComment = async (req, res) => {
  try {
    let comment = await Comment.findById(req.params.id);

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    // 确保用户是评论作者
    if (comment.author.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        message: '未授权修改此评论'
      });
    }

    // 仅允许修改内容和参与成功状态
    const allowedUpdates = {
      content: req.body.content,
      isSuccess: req.body.isSuccess,
      images: req.body.images
    };

    // 过滤掉未定义的字段
    Object.keys(allowedUpdates).forEach(key => 
      allowedUpdates[key] === undefined && delete allowedUpdates[key]
    );

    comment = await Comment.findByIdAndUpdate(
      req.params.id,
      allowedUpdates,
      {
        new: true,
        runValidators: true
      }
    );

    // 更新活动的成功/失败计数
    if (req.body.isSuccess !== undefined) {
      const deal = await Deal.findById(comment.deal);
      if (deal) {
        if (req.body.isSuccess) {
          deal.successReports = (deal.successReports || 0) + 1;
        } else {
          deal.failureReports = (deal.failureReports || 0) + 1;
        }
        await deal.save();
      }
    }

    res.status(200).json({
      success: true,
      data: comment
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    点赞评论
// @route   POST /api/comments/:id/like
// @access  Private
exports.likeComment = async (req, res) => {
  try {
    const comment = await Comment.findById(req.params.id);

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    comment.likes = (comment.likes || 0) + 1;
    await comment.save();

    res.status(200).json({
      success: true,
      data: comment
    });
  } catch (err) {
    res.status(400).json({
      success: false,
      message: err.message
    });
  }
}; 