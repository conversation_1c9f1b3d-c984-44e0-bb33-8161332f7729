#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import requests
import base64
from PIL import Image
from io import BytesIO

def get_captcha():
    """获取验证码图片"""
    # 设置请求头
    headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Referer": "https://fnxb.xtelh.cn/pages/register/index?code=V4TBZG",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "Priority": "u=1, i"
    }
    
    # 设置代理
    proxies = {
        "http": "http://127.0.0.1:10808",
        "https": "http://127.0.0.1:10808"
    }
    
    # 发送请求
    url = "https://fnxb.xtelh.cn/app-api/app/captcha/generate"
    try:
        response = requests.get(url, headers=headers, proxies=proxies)
        
        # 检查响应状态
        if response.status_code == 200:
            # 获取验证码key
            captcha_key = response.headers.get('captcha-key')
            print(f"验证码key: {captcha_key}")
            
            # 保存验证码图片
            captcha_path = "captcha.png"
            with open(captcha_path, "wb") as f:
                f.write(response.content)
            
            print(f"验证码图片已保存为: {captcha_path}")
            
            # 显示图片
            try:
                img = Image.open(captcha_path)
                img.show()
            except Exception as e:
                print(f"无法显示图片: {e}")
            
            return captcha_key
        else:
            print(f"获取验证码失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求异常: {e}")
        return None

if __name__ == "__main__":
    captcha_key = get_captcha()
    if captcha_key:
        print("请输入验证码:")
        captcha_text = input().strip().lower()
        print(f"输入的验证码: {captcha_text}")
        print(f"验证码key: {captcha_key}")
    else:
        print("获取验证码失败") 