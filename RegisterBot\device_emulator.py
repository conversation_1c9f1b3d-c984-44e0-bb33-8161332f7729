import random
import json

# ==============================================================================
# 1. 核心参数数据池
# 我们在这里定义了制造一个虚拟安卓设备所需的所有"零件"。
# 这些数据都来源于真实世界的设备，以确保生成指纹的真实性。
# ==============================================================================
ANDROID_VERSIONS = [
    {"version": "13", "platform_version": "13.0.0"},
    {"version": "12", "platform_version": "12.0.0"},
    {"version": "11", "platform_version": "11.0.0"},
    {"version": "14", "platform_version": "14.0.0"},
]

DEVICE_MODELS = [
    # Samsung
    "SM-S908U", "SM-S918B", "SM-F936U", "SM-G998B", "SM-G991U", 
    "SM-A536U", "SM-A736B", "SM-M536B", "Galaxy Z Fold4", "SM-G781B",
    # Xiaomi
    "2201116SC", "2109119DG", "M2102K1G", "Redmi Note 11 Pro", "Redmi K50", 
    "Mi 11 Ultra", "2203121C", "MIX 4", "Redmi Note 10", "22041216C",
    # Huawei
    "P50 Pro", "Mate 40 Pro", "Nova 9", "P40 Pro+", "LIO-AN00", 
    "TAS-AN00", "JAD-AL50", "ELS-NX9", "OCE-AN10", "HMA-AL00",
    # Oppo
    "CPH2305", "Find X5 Pro", "Reno8 Pro", "A96", "K10", 
    "PFTM10", "PGEM10", "CPH2201", "OPPO Find N", "CPH2371",
    # Vivo
    "V2186A", "X80 Pro", "S15 Pro", "iQOO 9 Pro", "V2145A", 
    "V2046A", "V2130", "V2170A", "V2227A", "NEX 3 5G",
    # OnePlus
    "OnePlus 10 Pro", "NE2213", "IN2023", "OnePlus 9RT", "LE2121", 
    "OnePlus 8T", "HD1907", "GM1917", "AC2003", "BE2029",
    # Sony
    "XQ-BC72", "Xperia 1 IV", "XQ-CT72", "SO-52B", "SOG04",
    # 其他品牌
    "Realme GT 2 Pro", "Motorola Edge 30 Pro", "Asus ROG Phone 6", "Nothing Phone (1)", "ZTE Axon 40 Ultra"
]

CHROME_VERSIONS = [
    # (完整版本号, 主版本号)
    ("112.0.5615.136", "112"),
    ("114.0.5735.196", "114"),
    ("120.0.6099.210", "120"),
    ("124.0.6367.113", "124"),
]

# ==============================================================================
# 2. 物理设备参数生成器
# 负责生成IMEI、MAC地址等硬件层面的信息。
# ==============================================================================
def _generate_physical_specs(model, android_version_info):
    """
    内部函数，生成设备的物理硬件规格。
    """
    resolutions = ["1080x2400", "1440x3200", "720x1600"]
    return {
        "model": model,
        "platform": "Android",
        "platform_version": android_version_info["version"],
        "imei": ''.join([str(random.randint(0, 9)) for _ in range(15)]),
        "mac_address": "02:00:00:%02x:%02x:%02x" % (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)),
        "android_id": ''.join(random.choices('0123456789abcdef', k=16)),
        "screen_resolution": random.choice(resolutions),
    }

# ==============================================================================
# 3. 浏览器指纹生成器
# 负责根据抽取的"零件"，组装出逻辑一致的浏览器请求头。
# ==============================================================================
def _generate_browser_headers(model, android_version_info, chrome_version_info):
    """
    内部函数，根据选定的组件生成一套完整的、一致的浏览器请求头。
    """
    full_chrome_version, major_chrome_version = chrome_version_info
    android_version = android_version_info["version"]
    
    # 构造 User-Agent 字符串
    user_agent = f"Mozilla/5.0 (Linux; Android {android_version}; {model}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{full_chrome_version} Mobile Safari/537.36"
    
    # 构造 Client Hints 请求头
    headers = {
        "User-Agent": user_agent,
        "sec-ch-ua": f'"Chromium";v="{major_chrome_version}", "Google Chrome";v="{major_chrome_version}", "Not-A.Brand";v="99"',
        "sec-ch-ua-mobile": "?1", # ?1 表示是移动设备
        "sec-ch-ua-platform": '"Android"',
        "sec-ch-ua-platform-version": f'"{android_version_info["platform_version"]}"',
        "sec-ch-ua-model": f'"{model}"',
        "Accept-Language": "zh-CN,zh;q=0.9",
    }
    return headers

# ==============================================================================
# 4. 主函数 / 总开关
# 对外提供唯一接口，一键生成一部完整的、独一无二的虚拟设备。
# ==============================================================================
def generate_complete_android_profile():
    """
    生成一个完整的、包含硬件信息和浏览器指纹的安卓设备档案。
    这是本模块对外提供的唯一核心功能。

    Returns:
        dict: 一个包含 'physical_specs' 和 'headers' 的字典。
    """
    # 1. 随机挑选"零件"
    android_version_info = random.choice(ANDROID_VERSIONS)
    model = random.choice(DEVICE_MODELS)
    chrome_version_info = random.choice(CHROME_VERSIONS)

    # 2. "生产"硬件和浏览器指纹
    physical_specs = _generate_physical_specs(model, android_version_info)
    browser_headers = _generate_browser_headers(model, android_version_info, chrome_version_info)

    # 3. 组装成最终档案并返回
    return {
        "physical_specs": physical_specs,
        "headers": browser_headers
    }


# ==============================================================================
# 演示区域已被完全删除，以避免在任何环境下产生语法错误。
# ============================================================================== 