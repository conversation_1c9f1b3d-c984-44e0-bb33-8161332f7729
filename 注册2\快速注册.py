import sys
import os
import random
import string
import logging
from 注册机2 import BMQBRegistration

# 配置日志输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("QuickRegister")

def generate_random_phone():
    """生成随机手机号"""
    prefixes = ['139', '138', '137', '136', '135', '134', '159', '158', '157', 
                '150', '151', '152', '188', '187', '182', '183', '184', '178']
    prefix = random.choice(prefixes)
    suffix = ''.join(random.choice('0123456789') for _ in range(8))
    return prefix + suffix

def generate_random_password():
    """生成随机密码"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(8))

def main():
    """快速注册并解密结果"""
    # 交互式输入邀请码
    invite_code_to_use = "555555" # 默认值
    try:
        user_input = input(f"请输入邀请码 (直接回车使用默认值: {invite_code_to_use}): ")
        if user_input.strip():
            invite_code_to_use = user_input.strip()
    except (EOFError, RuntimeError):
        logger.info("非交互式环境，将使用默认邀请码。")

    logger.info(f"将使用邀请码: {invite_code_to_use}")

    # 初始化注册对象，传入邀请码
    registrator = BMQBRegistration(invite_code=invite_code_to_use)
    
    # 生成随机手机号和密码
    phone = generate_random_phone()
    password = generate_random_password()
    
    print(f"\n======= 开始注册过程 =======")
    print(f"手机号: {phone}")
    print(f"密码: {password}")
    print(f"邀请码: {registrator.invite_code}")
    
    # 获取验证码
    print("\n[1] 获取验证码...")
    auth_code = registrator.get_auth_code()
    if not auth_code:
        print("❌ 获取验证码失败")
        return
    print(f"✓ 验证码: {auth_code}")
    
    # 注册账号
    print("\n[2] 提交注册请求...")
    reg_result = registrator.register(phone, password, auth_code)
    
    print("\n======= 注册结果 =======")
    if not reg_result:
        print("❌ 注册请求失败，无响应")
        return
        
    # 检查解密结果
    if reg_result.get("code") == 200:
        print(f"✓ 注册成功: {phone}")
        print("\n账号信息:")
        print(f"手机号: {phone}")
        print(f"密码: {password}")
        
        # 保存账号信息
        with open("成功账号.txt", "a", encoding="utf-8") as f:
            f.write(f"{phone}----{password}\n")
        print("\n账号信息已保存至成功账号.txt")
    else:
        print(f"❌ 注册失败: {reg_result.get('message', '未知错误')}")
        
    print("\n详细响应信息:")
    print(reg_result)

if __name__ == "__main__":
    main() 