import requests
import random
import string
import base64
import time
import ddddocr
import os
import json
import re

# --- 1. 配置模块 ---
CAPTCHA_URL = "https://cpdd.stchina17.com/api/v1/captcha"
REGISTER_URL = "https://cpdd.stchina17.com/api/v1/register"
VERIFY_URL = "https://cpdd.stchina17.com/api/v1/user/verify-identity"
ACCOUNT_SAVE_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\zhuce1.0\注册实名成功.txt"

# --- 2. 工具函数 ---
def generate_random_phone():
    prefixes = ['134', '135', '136', '137', '138', '139', '150', '151', '152', '155', '157', '158', '159', '182', '183', '187', '188', '178', '198']
    return random.choice(prefixes) + ''.join(random.choices(string.digits, k=8))

def generate_random_password(length=8):
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def generate_random_identity():
    """生成随机的姓名和身份证号码"""
    # 常见姓氏
    surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎']
    
    # 常见名字
    given_names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平', '刚', '桂英', '建华', '建国', '建军', '志强', '志明', '秀兰', '秀珍', '春梅', '海燕', '雪梅', '国华', '国强', '国庆', '建平', '建设', '建新', '建中', '建民', '建文', '建武', '建业', '建英', '建忠', '建州']
    
    # 生成随机姓名
    name = random.choice(surnames) + random.choice(given_names)
    
    # 生成随机身份证号码 (18位)
    # 地区码 (前6位) - 使用一些常见的地区码
    area_codes = ['110101', '110102', '110105', '110106', '110107', '110108', '110109', '110111', '120101', '120102', '120103', '120104', '120105', '120106', '120110', '120111', '130101', '130102', '130104', '130105', '130107', '130108', '130109', '130111', '140101', '140105', '140106', '140107', '140108', '140109', '140110', '140121']
    area_code = random.choice(area_codes)
    
    # 出生日期 (8位) - 生成1970-2000年之间的日期
    year = random.randint(1970, 2000)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 使用28避免月份天数问题
    birth_date = f"{year:04d}{month:02d}{day:02d}"
    
    # 顺序码 (3位) - 随机生成
    sequence = f"{random.randint(1, 999):03d}"
    
    # 前17位
    id_17 = area_code + birth_date + sequence
    
    # 计算校验码 (第18位)
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]
    
    id_number = id_17 + check_code
    
    return {"name": name, "id_number": id_number}

# --- 3. 核心API请求模块 ---
def get_common_headers(token=None):
    headers = { 
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1', 
        'Accept': 'application/json, text/plain, */*', 
        'Content-Type': 'application/json', 
        'Origin': 'https://www.wmjd5.com', 
        'Referer': 'https://www.wmjd5.com/'
    }
    if token: 
        headers['Authorization'] = f'Bearer {token}'
    return headers

def get_and_solve_captcha(session, ocr_instance):
    print("  正在获取验证码...")
    try:
        response = session.get(CAPTCHA_URL, headers=get_common_headers(), timeout=10)
        data = response.json()
        if not data.get("status"): 
            print(f"  ❌ 验证码获取失败: {data.get('message')}")
            return None, None
        captcha_key, image_b64 = data['data']['captcha_key'], data['data']['captcha_image'].split(',')[1]
        captcha_code = ocr_instance.classification(base64.b64decode(image_b64))
        print(f"  🤖 AI识别结果: {captcha_code}")
        return captcha_key, captcha_code
    except Exception as e: 
        print(f"  ❌ 验证码请求异常: {e}")
        return None, None

def register_account(session, payload):
    print(f"  正在使用邀请码 {payload['invitation_code']} 进行注册...")
    try:
        response = session.post(REGISTER_URL, headers=get_common_headers(), json=payload, timeout=15)
        data = response.json()
        if data.get("status"): 
            print("  ✅ \033[92m注册成功!\033[0m")
            return data["data"]["token"]
        else: 
            print(f"  ❌ \033[91m注册失败: {data.get('message', '未知错误')}\033[0m")
            return None
    except Exception as e: 
        print(f"  ❌ 注册请求异常: {e}")
        return None

def verify_identity(session, token, name, id_number):
    print(f"    ...正在使用【{name}】进行实名认证...")
    payload = {"name": name, "idNo": id_number}
    try:
        response = session.post(VERIFY_URL, headers=get_common_headers(token), json=payload, timeout=10)
        data = response.json()
        if data.get("status"): 
            print(f"    ✅ \033[92m【{name}】实名认证成功!\033[0m")
            return True
        else: 
            print(f"    -  \033[91m认证失败: {data.get('message', '未知错误')}，将尝试下一个身份。\033[0m")
            return False
    except Exception as e: 
        print(f"    -  \033[91m认证请求异常: {e}，将尝试下一个身份。\033[0m")
        return False

def save_account_info(info):
    try:
        output_dir = os.path.dirname(ACCOUNT_SAVE_FILE)
        if not os.path.exists(output_dir) and output_dir: 
            os.makedirs(output_dir)
        
        # 保存格式：手机号:密码:姓名:身份证:token:注册时间
        output_line = f"{info['phone']}:{info['password']}:{info['name']}:{info['id_number']}:{info['token']}:{info['register_time']}\n"
        
        with open(ACCOUNT_SAVE_FILE, 'a', encoding='utf-8') as f: 
            f.write(output_line)
        print(f"  💾 \033[92m账号信息已成功保存至 {ACCOUNT_SAVE_FILE}\033[0m")
    except Exception as e: 
        print(f"  ❌ 保存文件时出错: {e}")

# --- 4. 主逻辑模块 ---
def main():
    print("=" * 60)
    print("🎯 注册+实名认证模块 v1.0")
    print(f"--- 成功账号将保存在: {os.path.abspath(ACCOUNT_SAVE_FILE)} ---")
    print("=" * 60)
    
    try: 
        ocr = ddddocr.DdddOcr()
        print("✅ ddddocr库初始化成功。")
    except Exception as e: 
        print(f"❌ ddddocr库初始化失败: {e}")
        return
    
    print("✅ 将使用随机生成的身份信息进行认证。")
    
    invitation_code = input("请输入您的邀请码: ").strip()
    if not invitation_code: 
        print("邀请码不能为空！")
        return
    
    while True:
        try: 
            num_to_register = int(input(f"您希望成功注册多少个账号? ").strip())
            break
        except ValueError: 
            print("请输入一个有效的数字。")
            
    successful_accounts_created = 0
    while successful_accounts_created < num_to_register:
        print(f"\n\n--- 🚀 开始执行任务，目标：成功创建第 {successful_accounts_created + 1}/{num_to_register} 个账号 ---")
        session = requests.Session()
        
        phone, password = generate_random_phone(), generate_random_password()
        captcha_key, captcha_code = get_and_solve_captcha(session, ocr)
        if not captcha_key or not captcha_code: 
            print("   获取验证码失败，2秒后重试...")
            time.sleep(2)
            continue
            
        register_payload = {
            "phone": phone, 
            "password": password, 
            "password_confirmation": password, 
            "transaction_passcode": "147258", 
            "transaction_passcode_confirmation": "147258", 
            "invitation_code": invitation_code, 
            "captcha_key": captcha_key, 
            "captcha_value": captcha_code
        }
        
        token = register_account(session, register_payload)
        if not token: 
            print("   注册失败，2秒后重试...")
            time.sleep(2)
            continue
        
        # 实名认证循环
        verified_identity = None
        print("  ▶️ [身份认证循环] 注册成功，开始使用随机生成的身份信息...")
        max_attempts = 50  # 最大尝试次数
        attempt_count = 0
        
        while True:
            attempt_count += 1
            if attempt_count > max_attempts: 
                print(f"\n❌ 严重错误：已尝试 {max_attempts} 次随机身份生成，均认证失败！程序终止。")
                return
            
            # 生成随机身份信息
            identity_to_try = generate_random_identity()
            name, id_number = identity_to_try["name"], identity_to_try["id_number"]
            
            if verify_identity(session, token, name, id_number): 
                verified_identity = identity_to_try
                break
            time.sleep(1)
        
        successful_accounts_created += 1
        print("  🎉🎉🎉 \033[92m恭喜！账号注册+实名认证完成！\033[0m")
        
        account_details = {
            "phone": phone, 
            "password": password, 
            "name": verified_identity["name"], 
            "id_number": verified_identity["id_number"],
            "token": token, 
            "register_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        save_account_info(account_details)
        
        if successful_accounts_created < num_to_register:
            print(f"--- 任务完成，{random.randint(3,6)}秒后开始下一个任务... ---")
            time.sleep(random.randint(3, 6))

    print("\n\n==================== 注册+实名认证任务完成 ====================")
    print(f"目标注册总数: {num_to_register}")
    print(f"已成功创建总数: {successful_accounts_created}")
    print(f"所有成功记录已保存至: {os.path.abspath(ACCOUNT_SAVE_FILE)}")
    print("="*60)

if __name__ == "__main__":
    main()
