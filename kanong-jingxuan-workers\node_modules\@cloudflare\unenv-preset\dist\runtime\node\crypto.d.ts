export { Cipher, Decipher } from "unenv/node/crypto";
export declare const Certificate: typeof import("crypto").Certificate, checkPrime: typeof import("crypto").checkPrime, checkPrimeSync: typeof import("crypto").checkPrimeSync, constants: typeof import("crypto").constants, Cipheriv: any, createCipheriv: typeof import("crypto").createCipheriv, createDecipheriv: typeof import("crypto").createDecipheriv, createDiffieHellman: typeof import("crypto").createDiffieHellman, createDiffieHellmanGroup: typeof import("crypto").createDiffieHellmanGroup, createECDH: typeof import("crypto").createECDH, createHash: typeof import("crypto").createHash, createHmac: typeof import("crypto").createHmac, createPrivateKey: typeof import("crypto").createPrivateKey, createPublicKey: typeof import("crypto").createPublic<PERSON>ey, createSecretKey: typeof import("crypto").createSecretKey, createSign: typeof import("crypto").createSign, createVerify: typeof import("crypto").createVerify, Decipheriv: any, diffieHellman: typeof import("crypto").diffieHellman, DiffieHellman: typeof import("crypto").DiffieHellman, DiffieHellmanGroup: import("crypto").DiffieHellmanGroupConstructor, ECDH: typeof import("crypto").ECDH, fips: boolean, generateKey: typeof import("crypto").generateKey, generateKeyPair: typeof import("crypto").generateKeyPair, generateKeyPairSync: typeof import("crypto").generateKeyPairSync, generateKeySync: typeof import("crypto").generateKeySync, generatePrime: typeof import("crypto").generatePrime, generatePrimeSync: typeof import("crypto").generatePrimeSync, getCipherInfo: typeof import("crypto").getCipherInfo, getCiphers: typeof import("crypto").getCiphers, getCurves: typeof import("crypto").getCurves, getDiffieHellman: typeof import("crypto").getDiffieHellman, getFips: typeof import("crypto").getFips, getHashes: typeof import("crypto").getHashes, getRandomValues: typeof import("crypto").getRandomValues, hash: typeof import("crypto").hash, Hash: typeof import("crypto").Hash, hkdf: typeof import("crypto").hkdf, hkdfSync: typeof import("crypto").hkdfSync, Hmac: typeof import("crypto").Hmac, KeyObject: typeof import("crypto").KeyObject, pbkdf2: typeof import("crypto").pbkdf2, pbkdf2Sync: typeof import("crypto").pbkdf2Sync, privateDecrypt: typeof import("crypto").privateDecrypt, privateEncrypt: typeof import("crypto").privateEncrypt, publicDecrypt: typeof import("crypto").publicDecrypt, publicEncrypt: typeof import("crypto").publicEncrypt, randomBytes: typeof import("crypto").randomBytes, randomFill: typeof import("crypto").randomFill, randomFillSync: typeof import("crypto").randomFillSync, randomInt: typeof import("crypto").randomInt, randomUUID: typeof import("crypto").randomUUID, scrypt: typeof import("crypto").scrypt, scryptSync: typeof import("crypto").scryptSync, secureHeapUsed: typeof import("crypto").secureHeapUsed, setEngine: typeof import("crypto").setEngine, setFips: typeof import("crypto").setFips, sign: typeof import("crypto").sign, Sign: typeof import("crypto").Sign, subtle: import("crypto").webcrypto.SubtleCrypto, timingSafeEqual: typeof import("crypto").timingSafeEqual, verify: typeof import("crypto").verify, Verify: typeof import("crypto").Verify, X509Certificate: typeof import("crypto").X509Certificate;
export declare const webcrypto: any;
declare const _default: any;
export default _default;
