{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"command": "C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\.venv-clean\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\ida-pro-mcp\\src\\ida_pro_mcp\\server.py", "--unsafe"], "timeout": 1800, "disabled": false, "env": {"IDA_MCP_ENABLE_ALL_TOOLS": "true"}}}, "ida_mcp_tools": {"basic_tools": [{"name": "check_connection", "description": "Check if the IDA plugin is running", "parameters": []}, {"name": "get_metadata", "description": "Get metadata about the current IDB", "parameters": []}, {"name": "get_current_address", "description": "Get the address currently selected by the user", "parameters": []}, {"name": "get_current_function", "description": "Get the function currently selected by the user", "parameters": []}], "function_tools": [{"name": "get_function_by_name", "description": "Get a function by its name", "parameters": ["name"]}, {"name": "get_function_by_address", "description": "Get a function by its address", "parameters": ["address"]}, {"name": "list_functions", "description": "List all functions in the database (paginated)", "parameters": ["offset", "count"]}, {"name": "decompile_function", "description": "Decompile a function at the given address", "parameters": ["address"]}, {"name": "disassemble_function", "description": "Get assembly code for a function", "parameters": ["start_address"]}, {"name": "rename_function", "description": "Rename a function", "parameters": ["function_address", "new_name"]}, {"name": "set_function_prototype", "description": "Set a function's prototype", "parameters": ["function_address", "prototype"]}], "data_tools": [{"name": "convert_number", "description": "Convert a number to different representations", "parameters": ["text", "size"]}, {"name": "list_strings", "description": "List all strings in the database (paginated)", "parameters": ["offset", "count"]}, {"name": "list_strings_filter", "description": "List matching strings in the database (paginated, filtered)", "parameters": ["offset", "count", "filter"]}, {"name": "list_globals", "description": "List all globals in the database (paginated)", "parameters": ["offset", "count"]}, {"name": "list_globals_filter", "description": "List matching globals in the database (paginated, filtered)", "parameters": ["offset", "count", "filter"]}, {"name": "list_local_types", "description": "List all Local types in the database", "parameters": []}], "analysis_tools": [{"name": "get_xrefs_to", "description": "Get all cross references to the given address", "parameters": ["address"]}, {"name": "get_xrefs_to_field", "description": "Get all cross references to a named struct field", "parameters": ["struct_name", "field_name"]}, {"name": "get_entry_points", "description": "Get all entry points in the database", "parameters": []}], "modification_tools": [{"name": "set_comment", "description": "Set a comment for a given address", "parameters": ["address", "comment"]}, {"name": "rename_local_variable", "description": "Rename a local variable in a function", "parameters": ["function_address", "old_name", "new_name"]}, {"name": "rename_global_variable", "description": "Rename a global variable", "parameters": ["old_name", "new_name"]}, {"name": "set_global_variable_type", "description": "Set a global variable's type", "parameters": ["variable_name", "new_type"]}, {"name": "set_local_variable_type", "description": "Set a local variable's type", "parameters": ["function_address", "variable_name", "new_type"]}, {"name": "declare_c_type", "description": "Create or update a local type from a C declaration", "parameters": ["c_declaration"]}], "unsafe_debug_tools": [{"name": "dbg_get_registers", "description": "Get all registers and their values (debugging only)", "parameters": [], "unsafe": true}, {"name": "dbg_get_call_stack", "description": "Get the current call stack (debugging only)", "parameters": [], "unsafe": true}, {"name": "dbg_list_breakpoints", "description": "List all breakpoints in the program", "parameters": [], "unsafe": true}, {"name": "dbg_start_process", "description": "Start the debugger", "parameters": [], "unsafe": true}, {"name": "dbg_exit_process", "description": "Exit the debugger", "parameters": [], "unsafe": true}, {"name": "dbg_continue_process", "description": "Continue the debugger", "parameters": [], "unsafe": true}, {"name": "dbg_run_to", "description": "Run the debugger to the specified address", "parameters": ["address"], "unsafe": true}, {"name": "dbg_set_breakpoint", "description": "Set a breakpoint at the specified address", "parameters": ["address"], "unsafe": true}, {"name": "dbg_delete_breakpoint", "description": "Delete a breakpoint at the specified address", "parameters": ["address"], "unsafe": true}, {"name": "dbg_enable_breakpoint", "description": "Enable or disable a breakpoint", "parameters": ["address", "enable"], "unsafe": true}]}, "tool_summary": {"total_tools": 41, "basic_tools": 4, "function_tools": 7, "data_tools": 6, "analysis_tools": 3, "modification_tools": 6, "unsafe_debug_tools": 10, "note": "All tools are enabled including unsafe debugging functions"}}