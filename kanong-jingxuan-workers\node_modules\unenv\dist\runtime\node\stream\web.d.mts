import type nodeStreamWeb from "node:stream/web";
export declare const ReadableStream: unknown;
export declare const ReadableStreamDefaultReader: unknown;
export declare const ReadableStreamBYOBReader: unknown;
export declare const ReadableStreamBYOBRequest: unknown;
export declare const ReadableByteStreamController: unknown;
export declare const ReadableStreamDefaultController: unknown;
export declare const TransformStream: unknown;
export declare const TransformStreamDefaultController: unknown;
export declare const WritableStream: unknown;
export declare const WritableStreamDefaultWriter: unknown;
export declare const WritableStreamDefaultController: unknown;
export declare const ByteLengthQueuingStrategy: unknown;
export declare const CountQueuingStrategy: unknown;
export declare const TextEncoderStream: unknown;
export declare const TextDecoderStream: unknown;
export declare const DecompressionStream: unknown;
export declare const CompressionStream: unknown;
declare const _default: typeof nodeStreamWeb;
export default _default;
