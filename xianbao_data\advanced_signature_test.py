#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 高级签名算法测试工具

import requests
import json
import base64
import time
import hmac
import hashlib
import urllib.parse

# 新账号信息
ACCOUNT_INFO = {
    "userId": "107477",
    "access_token": "5e6f579c8ade4601ba410bfc8b244199",
    "httpKey": "hcCgwwhEHhNtf2YVdIj/Bg==",
    "payKey": "RsMQ8X1HowIgXt5tlvLA3A==",
    "loginToken": "ef7d2a9b89954258a0db82b46a1d4b32",
    "messageKey": "iQAUc+alFz14QNbHhf5yfw=="
}

BASE_URL = "http://23.248.226.178:8095"
API_KEY_STRING = "212i919292901"

def test_signature_variations(packet_id="6885f9780e75c97d1d99a74c"):
    """测试多种签名算法变体"""
    print("🔬 高级签名算法测试")
    print("=" * 60)
    
    user_id = ACCOUNT_INFO["userId"]
    access_token = ACCOUNT_INFO["access_token"]
    http_key = ACCOUNT_INFO["httpKey"]
    language = "zh"
    
    # 固定时间戳便于调试
    test_salt = str(int(time.time() * 1000))
    
    print(f"📋 测试参数:")
    print(f"   用户ID: {user_id}")
    print(f"   访问令牌: {access_token}")
    print(f"   红包ID: {packet_id}")
    print(f"   语言: {language}")
    print(f"   时间戳: {test_salt}")
    print(f"   HTTP密钥: {http_key}")
    
    try:
        sign_key = base64.b64decode(http_key)
        print(f"   解码后密钥: {sign_key.hex()}")
    except Exception as e:
        print(f"❌ 密钥解码失败: {e}")
        return
    
    # 测试多种签名组合
    signature_tests = [
        {
            "name": "原始算法",
            "parts": [API_KEY_STRING, user_id, access_token, packet_id, language, test_salt],
            "description": "API_KEY + userID + token + id + language + salt"
        },
        {
            "name": "不含API_KEY",
            "parts": [user_id, access_token, packet_id, language, test_salt],
            "description": "userID + token + id + language + salt"
        },
        {
            "name": "参数字母顺序",
            "parts": [API_KEY_STRING, user_id, access_token, packet_id, language, test_salt],
            "description": "API_KEY + userID + token + 参数按字母顺序 + salt"
        },
        {
            "name": "只有核心参数",
            "parts": [packet_id, language, access_token, test_salt],
            "description": "id + language + token + salt"
        },
        {
            "name": "包含loginToken",
            "parts": [API_KEY_STRING, user_id, access_token, ACCOUNT_INFO["loginToken"], packet_id, language, test_salt],
            "description": "API_KEY + userID + token + loginToken + id + language + salt"
        },
        {
            "name": "URL参数格式",
            "parts": [f"id={packet_id}", f"language={language}", f"access_token={access_token}", f"salt={test_salt}"],
            "description": "URL参数格式拼接"
        },
        {
            "name": "JSON格式",
            "parts": [json.dumps({"id": packet_id, "language": language, "access_token": access_token, "salt": test_salt}, separators=(',', ':'))],
            "description": "JSON格式参数"
        }
    ]
    
    print(f"\n🔍 测试不同签名组合:")
    
    for i, test in enumerate(signature_tests, 1):
        print(f"\n--- 测试 {i}: {test['name']} ---")
        print(f"   描述: {test['description']}")
        
        content_to_sign = "".join(test["parts"])
        print(f"   签名内容: {content_to_sign}")
        
        try:
            signature = base64.b64encode(
                hmac.new(sign_key, content_to_sign.encode('utf-8'), hashlib.md5).digest()
            ).decode('utf-8')
            
            print(f"   生成签名: {signature}")
            
            # 测试这个签名
            success = test_api_call(packet_id, language, access_token, test_salt, signature, test["name"])
            
            if success:
                print(f"   ✅ 签名正确!")
                return signature
            
        except Exception as e:
            print(f"   ❌ 签名生成失败: {e}")
    
    return None

def test_api_call(packet_id, language, access_token, salt, secret, test_name):
    """测试API调用"""
    try:
        headers = {
            "User-Agent": "chat_im/2.1.8 (Linux; U; Android 10; 22041216UC Build/UP1A.231005.007)",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        params = {
            "id": packet_id,
            "language": language,
            "access_token": access_token,
            "salt": salt,
            "secret": secret
        }
        
        response = requests.get(
            f"{BASE_URL}/redPacket/getRedPacket", 
            params=params, 
            headers=headers, 
            timeout=10
        )
        
        print(f"   📤 请求URL: {response.url}")
        print(f"   📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                result_code = result.get("resultCode")
                result_msg = result.get("resultMsg", "")
                
                print(f"   📋 响应: {result_code} - {result_msg}")
                
                if result_code == 1:
                    print(f"   ✅ 成功! ({test_name})")
                    return True
                elif result_code == 1030102:
                    print(f"   🔑 令牌失效 (签名可能正确但令牌问题)")
                elif result_code == 100102:
                    print(f"   📦 红包不存在 (签名可能正确)")
                elif result_code == 104004:
                    print(f"   💰 红包已领完 (签名可能正确)")
                else:
                    print(f"   ❌ 其他错误")
                    
            except:
                print(f"   ❌ 响应解析失败: {response.text[:100]}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    return False

def test_different_endpoints():
    """测试不同的API端点"""
    print(f"\n🌐 测试不同API端点")
    print("=" * 40)
    
    endpoints = [
        "/redPacket/getRedPacket",
        "/redPacket/openRedPacket", 
        "/user/info",
        "/user/profile",
        "/api/user/info"
    ]
    
    user_id = ACCOUNT_INFO["userId"]
    access_token = ACCOUNT_INFO["access_token"]
    
    for endpoint in endpoints:
        print(f"\n--- 测试端点: {endpoint} ---")
        
        try:
            headers = {
                "User-Agent": "chat_im/2.1.8 (Linux; U; Android 10; 22041216UC Build/UP1A.231005.007)"
            }
            
            params = {
                "access_token": access_token,
                "userId": user_id
            }
            
            response = requests.get(
                f"{BASE_URL}{endpoint}",
                params=params,
                headers=headers,
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   响应: {json.dumps(result, ensure_ascii=False)[:100]}...")
                except:
                    print(f"   响应: {response.text[:100]}...")
            else:
                print(f"   错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   异常: {e}")

def main():
    print("🔬 高级签名算法测试工具")
    print("=" * 60)
    
    print("选择测试模式:")
    print("1. 测试签名算法变体")
    print("2. 测试不同API端点")
    print("3. 全面测试")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        packet_id = input("请输入红包ID (回车使用默认): ").strip()
        if not packet_id:
            packet_id = "6885f9780e75c97d1d99a74c"
        test_signature_variations(packet_id)
    elif choice == "2":
        test_different_endpoints()
    else:
        packet_id = input("请输入红包ID (回车使用默认): ").strip()
        if not packet_id:
            packet_id = "6885f9780e75c97d1d99a74c"
        test_signature_variations(packet_id)
        test_different_endpoints()

if __name__ == "__main__":
    main()
