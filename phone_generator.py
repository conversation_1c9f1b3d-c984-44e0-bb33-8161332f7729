#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手机号生成器 - 生成用于扫描的手机号
"""

import random
import os
from datetime import datetime

class PhoneGenerator:
    def __init__(self):
        # 中国移动号段
        self.china_mobile_prefixes = [
            '134', '135', '136', '137', '138', '139',  # 2G
            '147', '150', '151', '152', '157', '158', '159',  # 3G
            '178', '182', '183', '184', '187', '188',  # 4G
            '198'  # 5G
        ]
        
        # 中国联通号段
        self.china_unicom_prefixes = [
            '130', '131', '132',  # 2G
            '145', '155', '156',  # 3G
            '166', '175', '176', '185', '186',  # 4G
            '196'  # 5G
        ]
        
        # 中国电信号段
        self.china_telecom_prefixes = [
            '133', '153', '173', '177', '180', '181', '189', '199'
        ]
        
        # 虚拟运营商号段
        self.virtual_prefixes = [
            '170', '171', '162', '165', '167'
        ]
        
        # 所有号段
        self.all_prefixes = (self.china_mobile_prefixes + 
                           self.china_unicom_prefixes + 
                           self.china_telecom_prefixes + 
                           self.virtual_prefixes)
    
    def generate_phone_with_prefix(self, prefix, count=1):
        """
        根据指定前缀生成手机号
        
        Args:
            prefix (str): 3位前缀
            count (int): 生成数量
            
        Returns:
            list: 手机号列表
        """
        phones = []
        for _ in range(count):
            # 生成8位随机数字
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            phone = prefix + suffix
            phones.append(phone)
        return phones
    
    def generate_sequential_phones(self, prefix, start_num=0, count=100):
        """
        生成连续的手机号
        
        Args:
            prefix (str): 3位前缀
            start_num (int): 起始数字
            count (int): 生成数量
            
        Returns:
            list: 手机号列表
        """
        phones = []
        for i in range(count):
            # 8位数字，从start_num开始递增
            suffix = str(start_num + i).zfill(8)
            phone = prefix + suffix
            phones.append(phone)
        return phones
    
    def generate_random_phones(self, count=100, operator=None):
        """
        生成随机手机号
        
        Args:
            count (int): 生成数量
            operator (str): 运营商类型 ('mobile', 'unicom', 'telecom', 'virtual', None)
            
        Returns:
            list: 手机号列表
        """
        if operator == 'mobile':
            prefixes = self.china_mobile_prefixes
        elif operator == 'unicom':
            prefixes = self.china_unicom_prefixes
        elif operator == 'telecom':
            prefixes = self.china_telecom_prefixes
        elif operator == 'virtual':
            prefixes = self.virtual_prefixes
        else:
            prefixes = self.all_prefixes
        
        phones = []
        for _ in range(count):
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            phone = prefix + suffix
            phones.append(phone)
        
        return phones
    
    def generate_pattern_phones(self, pattern, count=100):
        """
        根据模式生成手机号
        
        Args:
            pattern (str): 模式，如 "166XXXXXXXX" (X表示随机数字)
            count (int): 生成数量
            
        Returns:
            list: 手机号列表
        """
        phones = []
        for _ in range(count):
            phone = ""
            for char in pattern:
                if char.upper() == 'X':
                    phone += str(random.randint(0, 9))
                else:
                    phone += char
            phones.append(phone)
        return phones
    
    def save_phones_to_file(self, phones, filename=None):
        """
        保存手机号到文件
        
        Args:
            phones (list): 手机号列表
            filename (str): 文件名，如果为None则自动生成
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"phones_{timestamp}.txt"
        
        # 确保目录存在
        os.makedirs("phone_lists", exist_ok=True)
        filepath = os.path.join("phone_lists", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for phone in phones:
                f.write(f"{phone}\n")
        
        print(f"已保存 {len(phones)} 个手机号到: {filepath}")
        return filepath
    
    def load_phones_from_file(self, filepath):
        """
        从文件加载手机号
        
        Args:
            filepath (str): 文件路径
            
        Returns:
            list: 手机号列表
        """
        phones = []
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    phone = line.strip()
                    if phone and phone.isdigit() and len(phone) == 11:
                        phones.append(phone)
        except Exception as e:
            print(f"读取文件失败: {e}")
        return phones
    
    def validate_phone(self, phone):
        """
        验证手机号格式
        
        Args:
            phone (str): 手机号
            
        Returns:
            bool: 是否有效
        """
        if not phone or len(phone) != 11 or not phone.isdigit():
            return False
        
        prefix = phone[:3]
        return prefix in self.all_prefixes
    
    def get_operator_info(self, phone):
        """
        获取手机号运营商信息
        
        Args:
            phone (str): 手机号
            
        Returns:
            str: 运营商信息
        """
        if not self.validate_phone(phone):
            return "无效号码"
        
        prefix = phone[:3]
        
        if prefix in self.china_mobile_prefixes:
            return "中国移动"
        elif prefix in self.china_unicom_prefixes:
            return "中国联通"
        elif prefix in self.china_telecom_prefixes:
            return "中国电信"
        elif prefix in self.virtual_prefixes:
            return "虚拟运营商"
        else:
            return "未知运营商"

def main():
    generator = PhoneGenerator()
    
    print("=== 手机号生成器 ===")
    print("1. 指定前缀生成")
    print("2. 连续号码生成")
    print("3. 随机号码生成")
    print("4. 模式生成")
    print("5. 验证手机号")
    
    choice = input("请选择功能 (1-5): ").strip()
    
    if choice == "1":
        prefix = input("请输入3位前缀: ").strip()
        count = int(input("请输入生成数量 (默认100): ").strip() or "100")
        phones = generator.generate_phone_with_prefix(prefix, count)
        
    elif choice == "2":
        prefix = input("请输入3位前缀: ").strip()
        start_num = int(input("请输入起始数字 (默认0): ").strip() or "0")
        count = int(input("请输入生成数量 (默认100): ").strip() or "100")
        phones = generator.generate_sequential_phones(prefix, start_num, count)
        
    elif choice == "3":
        count = int(input("请输入生成数量 (默认100): ").strip() or "100")
        print("运营商选择: mobile(移动), unicom(联通), telecom(电信), virtual(虚拟), 回车(全部)")
        operator = input("请选择运营商: ").strip() or None
        phones = generator.generate_random_phones(count, operator)
        
    elif choice == "4":
        pattern = input("请输入模式 (如 166XXXXXXXX): ").strip()
        count = int(input("请输入生成数量 (默认100): ").strip() or "100")
        phones = generator.generate_pattern_phones(pattern, count)
        
    elif choice == "5":
        phone = input("请输入手机号: ").strip()
        is_valid = generator.validate_phone(phone)
        operator = generator.get_operator_info(phone)
        print(f"手机号: {phone}")
        print(f"有效性: {'有效' if is_valid else '无效'}")
        print(f"运营商: {operator}")
        return
        
    else:
        print("无效选择")
        return
    
    # 显示生成的手机号
    print(f"\n生成了 {len(phones)} 个手机号:")
    for i, phone in enumerate(phones[:10]):  # 只显示前10个
        operator = generator.get_operator_info(phone)
        print(f"{i+1}. {phone} ({operator})")
    
    if len(phones) > 10:
        print(f"... 还有 {len(phones) - 10} 个")
    
    # 询问是否保存
    save = input("\n是否保存到文件? (y/n): ").strip().lower()
    if save == 'y':
        filename = input("请输入文件名 (回车使用默认名): ").strip() or None
        generator.save_phones_to_file(phones, filename)

if __name__ == "__main__":
    main()
