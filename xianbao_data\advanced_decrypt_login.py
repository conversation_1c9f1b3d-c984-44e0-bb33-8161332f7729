#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 高级登录流程解密工具 - 尝试多种密钥组合

import base64
import json
import urllib.parse
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib

def try_decrypt_with_multiple_keys(encrypted_data_b64):
    """尝试使用多种密钥组合解密"""
    
    # 可能的密钥组合
    key_combinations = [
        # 原始密钥
        {
            "name": "原始注册密钥",
            "key": "947989c9aadc9fad7f21ebc026373f24",
            "iv": "0102030405060708090a0b0c0d0e0f10"
        },
        # 可能的登录专用密钥
        {
            "name": "登录专用密钥1",
            "key": "0123456789abcdef0123456789abcdef",
            "iv": "0102030405060708090a0b0c0d0e0f10"
        },
        # 基于账号信息生成的密钥
        {
            "name": "账号相关密钥",
            "key": hashlib.md5("16683135568".encode()).hexdigest(),
            "iv": "0102030405060708090a0b0c0d0e0f10"
        },
        # 基于设备信息的密钥
        {
            "name": "设备相关密钥",
            "key": hashlib.md5("android".encode()).hexdigest(),
            "iv": "0102030405060708090a0b0c0d0e0f10"
        },
        # 可能的固定密钥变体
        {
            "name": "密钥变体1",
            "key": "947989c9aadc9fad7f21ebc026373f24",
            "iv": "00000000000000000000000000000000"
        },
        # 尝试使用MAC地址相关的密钥
        {
            "name": "MAC相关密钥",
            "key": hashlib.md5("8mHfI4nGgZ5GnhO4o+G3EA==".encode()).hexdigest(),
            "iv": "0102030405060708090a0b0c0d0e0f10"
        }
    ]
    
    for combo in key_combinations:
        try:
            key = bytes.fromhex(combo["key"])
            iv = bytes.fromhex(combo["iv"])
            
            ciphertext = base64.b64decode(encrypted_data_b64)
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted_padded_bytes = cipher.decrypt(ciphertext)
            decrypted_bytes = unpad(decrypted_padded_bytes, AES.block_size)
            
            # 尝试解析为JSON
            try:
                result = json.loads(decrypted_bytes.decode('utf-8'))
                return combo["name"], result
            except:
                # 如果不是JSON，检查是否是有效文本
                text = decrypted_bytes.decode('utf-8', errors='ignore')
                if len(text) > 10 and text.isprintable():
                    return combo["name"], text
                    
        except Exception as e:
            continue
    
    return None, "所有密钥组合都失败"

def analyze_login_code_response():
    """分析登录验证码响应"""
    print("🔍 分析请求1响应: 登录验证码")
    print("=" * 50)
    
    encrypted_code = "yZmXRAV1z0ggOkNBNr9hRO+HZCT4lokG5TYIqo6SeeyDSUGeOg0c0e0N5Q/nWRENCRUyooo3QJPctlh4LZOVYwzpFr/V/mqXJUb9gnvjRCOW0v1joynccpTUZyhP95m0VIAIo84kaw3tHhTXsa7dBsWheUImWfLJvm/NcqxHIIk="
    
    key_name, result = try_decrypt_with_multiple_keys(encrypted_code)
    
    if key_name:
        print(f"✅ 使用 {key_name} 解密成功:")
        if isinstance(result, dict):
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"   {result}")
    else:
        print(f"❌ 解密失败: {result}")
        
        # 尝试分析原始数据
        try:
            raw_data = base64.b64decode(encrypted_code)
            print(f"\n📋 原始数据分析:")
            print(f"   Base64解码长度: {len(raw_data)} 字节")
            print(f"   前16字节 (可能是IV): {raw_data[:16].hex()}")
            print(f"   数据部分: {raw_data[16:32].hex()}...")
        except:
            print("   无法分析原始数据")

def analyze_private_key_response():
    """分析私钥响应"""
    print(f"\n🔍 分析请求2响应: 登录私钥")
    print("=" * 50)
    
    encrypted_key = "Ua9Eq5K+Ik05osXtW/vANyxPK3uiAXh4DlKi/sTLdiHzOZWsLab3Vusu0xbNR6tqzbJ9wTIh+/xBdz/+3grsLcSFr5l8w6F9dUr13hp+9QccauYMHPF/HWiZQoipUtBGAUZw9igMGSXJD62uDRYgIjcUxGk5DloTVr2NgKC/iq6BsOoLYupmO47ohGU6HQnzNsrL6WJkNZrpBsFayeeLE+q6F/oS89Yt6cvv2cKZx38klYoR2KDEoOGOTk0ANsX9P6JNtmvxpLiRvZhzHwA3uLrykzW8fHTKQPu7PGjO1PRguNqXu7mdX4BYuDugaERlvZGjB1qiAxeg5V053rSDyDkrQTw1UhiOXz69944YeDhrDle/x3k9JLGqupuNNrN3DUnzuyBJ9qohWDkpRW+IzMxI9t/UZ2WQKQv8dNO52pR8LOL3OU8E0aHmgAhHwdM6Ksvtf9y6nJ5d1CEAvXpPpFp3bOUWiqWKokkWNn9FMjXbqecRcZrzvIjZ3BoiN0oEOrnzZllAUJ8vfNy25tebJIJxFjMvCXOo/SRyBSvBFkkY1+lci0jHEUhgJA1yInSEH2bS4vlgei3b9Ng/UdsJGWC1WXis+QJLadEwCnDhFZe19THJ6d1QUpyXdbAjpP61nDuRv266h74ZnMbMUo3TDIw56lBdHG9w9qZi3FtCquxOM8tELc1RybZGPRYt+LGBxHI7kXXe54TBHXeuUIvZ6/bk1GjltMAgkFHdFoi0TjHYZBm+FNAqOnCNfcNHBkOmaStF5824imQ/b9Pu6BchXCpjYIhKMoabHTCYiWf2IB8VZRdhXbMo3XGQkQsoVaIe"
    
    key_name, result = try_decrypt_with_multiple_keys(encrypted_key)
    
    if key_name:
        print(f"✅ 使用 {key_name} 解密成功:")
        if isinstance(result, dict):
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"   {result}")
    else:
        print(f"❌ 解密失败: {result}")

def analyze_login_request_and_response():
    """分析登录请求和响应"""
    print(f"\n🔍 分析请求3: 登录请求和响应")
    print("=" * 50)
    
    # 请求中的加密数据
    encrypted_request = "60GQxFZcKvJa67P7uo1ujiwnK70P0YpV1y63XtlhsjyQGqybTChHe7kAFyX0d/n9+AUeBqp7Yf/tygavwlI6yOMfzpVZ3n755yNzp4iRMzJciq+PTM4Hq8HTXL8ijK6BHgEVA+wUTnrodEIyKCB/zkgBkNp6IylW1a4dmQAJrkRC5xjkwvb8E/8+6iFVNMm2"
    
    # 响应中的加密数据
    encrypted_response = "M48ih3EwPHysqVzgNQAJFsqj8NKt7GQH0mTPafIACncIP524OicHs6ZqA/QxZhlAfaa8zm1RJ/tC9LgSxiEQwLOo5lMMkPnPa+0gseeK4R6hhy5YBVR9U6oMMs1jxbmkSFOv2X3D/7SsOi0tB3+DmNYWso4+LM7euciD1qX+EjgBe5/Hhxho9oXU7O0fBryRUN70sqHp+8LFPa6kW95c/qok+1HBpjzm208ZKe8OwKHybq5+P1TYeSmHfI3yMRTLXNY5ExrHEHdni4yhmXvrDsGS6pGHzKmzBYCeiUMZg1pT6LKenPUCUcnZ+AZI7H3YcwkjqAdLksvGBvvN1LBlPLcD1TnAu6vE6SJnoZJlk5Yi4FyipuE8xVBdsLa/6R1FXzZOEFpHcXoCQm8U0V5epDYxNEPm+H8QrbGfJf79/vm0tIpkLM8jU//jxLuJXF4YGpPODnRNuwvTiMDlk2q3zMKqvO6mZi+kzY+47Bdj/H8HYBoa2syfopWWMXtz/cdX9f+tK17FuaWxBf5VePEPZ/UH1Wy00nLup0j4N/NQCbocf3zo7BA8dybwaTVzMdkXa9qYKaszAaO1UyVr7cuVwIGG1rjiUGHkk0toM68wO/KKAapJ8SgYUyqBus82iq8JEjupFIfQNhspiW6lxCXtPTAURmLd7jTADIGxqi6YWlOE3Dt6lniPwoYgDQNCJcdYGgLWHFiP+NxJl86L5NkJA3m7MbssGelP9vMMAhae9Sz+o/hGk6UkEfyAqOCc7fJukz3dP9JSRGPU95zte/wMpDfw4VXN9uLHcdAoVV7q/ZquR4Q2HS7tXKCo/45z5fo6c+II4nuIyHoRPuJqMdWbDEAlGC5Dgx72lK+KA68kMMIBwoI0YrdpU6LQroKEWSJMufzSTmc5uz9FgydbvVzYKm838moGIXgTN8fMviQ2v6ZSyRGRagRAKPBhG/tPbgPJ+rGF/CkPExFRAUrxcN/withChR46Haywheg5NQYpMBm+HygBuWbPmsLakTLWsvwKEFKv9wNQl0ZuxmQbOSpTxGbxM09xNY7jKVo+87RoDViufqtHWBRfrGn9IWr156zdhbQH6bAAolZy72tLWXFex/VSwSKK9beS/nIiQobkYiYyc8KxWbgVaOrWVB6Ei6obQ8UngwdciVQSJwnybzW5Uu3kDz18M61y9jU8blvmagH0tEZIXefw74scI6tqzYJMFKfD/89Pt/QqYeLFo1rl6XiW06oLZQOHbl/mHt015zn2RCzUuLvoZSGJiw5lGcD9+AJe0iDcluOpGsXlqm5+eXpofI0wblejlZLlnE4CD+5mUQyY+fnV13D3Xx3rjGNwbBvVg1YH8li0eKUJcvTeX0558f/KzUSAnVCdrES0SFW6lVq3/MD0DNEXf6biyqJM1otTel1IJ3ZLptYpYVUwnlE9eVvndENpLIbrkONtk1yzJ/vmsNYdbMkn1tsVihlsIaXdMaK8WVwd9EOriOFkMqXi5xJDDDwUGq1aOJ5Goq8QPJjA8NhB5nUUdNjb71KhjyZ0vNOCveBMY9ps2UazNkWy4FOqPzENwG/Glguyl10="
    
    print("🔐 尝试解密登录请求数据...")
    key_name, result = try_decrypt_with_multiple_keys(encrypted_request)
    
    if key_name:
        print(f"✅ 请求解密成功 (使用 {key_name}):")
        if isinstance(result, dict):
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"   {result}")
    else:
        print(f"❌ 请求解密失败: {result}")
    
    print(f"\n🔐 尝试解密登录响应数据...")
    key_name, result = try_decrypt_with_multiple_keys(encrypted_response)
    
    if key_name:
        print(f"✅ 响应解密成功 (使用 {key_name}):")
        if isinstance(result, dict):
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"   {result}")
    else:
        print(f"❌ 响应解密失败: {result}")

def main():
    print("🔬 高级登录流程解密工具")
    print("=" * 60)
    
    analyze_login_code_response()
    analyze_private_key_response()
    analyze_login_request_and_response()
    
    print(f"\n💡 分析结论:")
    print(f"   1. 登录流程可能使用了不同的加密密钥")
    print(f"   2. 密钥可能基于账号、设备或会话信息动态生成")
    print(f"   3. 需要进一步分析密钥生成算法")
    print(f"   4. 可能需要从前面的请求中提取密钥信息")

if __name__ == "__main__":
    main()
