import requests
import time
from retrying import retry
from utils import logger

class QianchuanConfig:
    """千川接码平台配置"""
    # 您的千川平台用户名
    USERNAME = "bb263"  # <--- 在这里替换为您的千川用户名
    # 您的千川平台密码
    PASSWORD = "111qqq"  # <--- 在这里替换为您的千川密码
    # 您的千川渠道ID
    CHANNEL_ID = "1523062517935378440"  # <--- 已根据您提供的信息更新
    # 要获取的手机号类型: 0=全部, 4=非虚拟, 5=虚拟
    OPERATOR = 0
    # 是否启用千川
    ENABLED = False

class QianchuanClient:
    """千川API客户端，用于接码操作"""
    BASE_URL = "https://api.qc86.shop/api"

    def __init__(self, config):
        """
        初始化客户端。
        :param config: 包含USERNAME, PASSWORD, CHANNEL_ID的QianchuanConfig对象
        """
        self.config = config
        self.token = None
        # 确保第一时间登录获取token
        self._ensure_token()

    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def _login_and_get_token(self):
        """登录千川平台以获取并存储API token，包含自动重试逻辑。"""
        if not self.config.USERNAME or not self.config.PASSWORD:
            raise ValueError("请在QianchuanConfig中设置千川的用户名和密码。")
            
        url = f"{self.BASE_URL}/login"
        params = {
            "username": self.config.USERNAME,
            "password": self.config.PASSWORD
        }
        
        logger.info("[千川] 正在登录以获取Token...")
        response = requests.get(url, params=params, timeout=20)
        response.raise_for_status()
        data = response.json()
        
        if data.get("success"):
            token_value = data.get("data", {}).get("token")
            if not token_value:
                raise Exception("千川登录成功，但服务器未返回Token。")
            self.token = token_value
            logger.info(f"[千川] 成功获取Token: {self.token[:10]}...")
            return self.token
        else:
            raise Exception(f"千川登录失败: {data.get('msg', '未知错误')}")

    def _ensure_token(self):
        """检查并确保self.token有效，如果无效则调用登录方法获取。"""
        if self.token is None:
            self._login_and_get_token()
        return self.token

    @retry(stop_max_attempt_number=5, wait_fixed=3000)
    def get_phone_number(self):
        """
        从千川平台获取一个手机号。
        返回: (True, (phone, location)) 成功元组或 (False, error_message) 失败元组。
        """
        try:
            token = self._ensure_token()
            url = f"{self.BASE_URL}/getPhone"
            params = {
                "token": token,
                "channelId": self.config.CHANNEL_ID,
                "operator": self.config.OPERATOR  # 使用配置中定义的类型
            }
            logger.info(f"[千川] 正在从渠道(ID: {self.config.CHANNEL_ID})获取手机号 (类型: {self.config.OPERATOR})...")
            response = requests.get(url, params=params, timeout=20)
            response.raise_for_status()
            data = response.json()

            if data.get("success") and data.get("data", {}).get("mobile"):
                phone = data["data"]["mobile"]
                location = "千川"  # 千川API不返回归属地，使用平台名作为标识
                logger.info(f"[千川] 成功获取到手机号: {phone}")
                return True, (phone, location)
            else:
                error_msg = data.get("msg") or data.get("data", {}).get("message", "获取失败，未知原因")
                logger.error(f"[千川] 获取手机号失败: {error_msg}")
                if "当前渠道无可用号码" in error_msg:
                    logger.warning("[千川] 渠道无可用号码，等待10秒后重试...")
                    time.sleep(10)
                return False, error_msg

        except requests.exceptions.RequestException as e:
            logger.error(f"[千川] 获取手机号时网络异常: {e}")
            raise  # 触发重试
        except Exception as e:
            logger.error(f"[千川] 获取手机号时发生未知异常: {e}")
            return False, str(e)

    def get_sms_code(self, phone):
        """
        获取指定手机号的短信验证码，会进行轮询。
        :param phone: 要获取验证码的手机号。
        :return: 验证码字符串, 如果超时未获取到则返回 None。
        """
        try:
            token = self._ensure_token()
            url = f"{self.BASE_URL}/getCode"
            params = {
                "token": token,
                "channelId": self.config.CHANNEL_ID,
                "phoneNum": phone
            }
            
            logger.info(f"[千川] 开始为手机号 {phone} 获取验证码，将尝试最多60次 (每次间隔2秒)...")
            for i in range(60): # 总共轮询 120 秒
                if i > 0:
                    time.sleep(2) # 从第二次尝试开始，每次等待2秒
                logger.info(f"[千川] 第 {i + 1}/60 次尝试获取验证码...")
                response = requests.get(url, params=params, timeout=20)
                response.raise_for_status()
                data = response.json()
                
                if data.get("success") and data.get("data", {}).get("code"):
                    code = data["data"]["code"]
                    message = data["data"]["modle"]
                    logger.info(f"[千川] 成功获取到验证码: {code}，完整短信内容: {message}")
                    return code
            
            logger.warning(f"[千川] 已达到最大尝试次数，仍未获取到手机号 {phone} 的验证码。")
            return None

        except Exception as e:
            logger.error(f"[千川] 获取验证码过程中发生异常: {e}")
            return None

    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def add_to_blacklist(self, phone):
        """将手机号加入黑名单。"""
        try:
            token = self._ensure_token()
            url = f"{self.BASE_URL}/phoneCollectAdd"
            params = {
                "token": token,
                "channelId": self.config.CHANNEL_ID,
                "phoneNo": phone,
                "type": 0  # 根据文档，传0
            }
            
            logger.info(f"[千川] 正在将手机号 {phone} 加入黑名单...")
            response = requests.get(url, params=params, timeout=20)
            response.raise_for_status()
            data = response.json()
            
            if data.get("success"):
                logger.info(f"[千川] 手机号 {phone} 已成功加入黑名单。")
                return True
            else:
                logger.error(f"[千川] 拉黑手机号 {phone} 失败: {data.get('msg', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"[千川] 拉黑手机号 {phone} 时发生异常: {e}")
            return False

    def release_phone_number(self, phone):
        """
        释放手机号 (在千川平台中，此操作等同于拉黑)。
        """
        logger.info(f"[千川] 正在释放手机号 {phone} (实际操作为拉黑)...")
        return self.add_to_blacklist(phone) 