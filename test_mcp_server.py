#!/usr/bin/env python3
"""
Test script for MCP server
"""
import subprocess
import sys
import time
import os

def test_mcp_server():
    """Test if the MCP server can start properly"""
    print("Testing MCP Server...")
    
    # Path to the virtual environment and server
    venv_python = r"C:\Users\<USER>\Desktop\hook\zhuce\.venv-mcp\Scripts\python.exe"
    server_script = r"C:\Users\<USER>\Desktop\hook\zhuce\ida-pro-mcp-new\src\ida_pro_mcp\server.py"
    
    # Check if files exist
    if not os.path.exists(venv_python):
        print(f"❌ Virtual environment Python not found: {venv_python}")
        return False
        
    if not os.path.exists(server_script):
        print(f"❌ Server script not found: {server_script}")
        return False
    
    print("✅ Files exist, testing server startup...")
    
    try:
        # Test server help command
        result = subprocess.run([
            venv_python, server_script, "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ MCP Server help command works")
            print("Server options available:")
            for line in result.stdout.split('\n'):
                if line.strip() and ('--' in line or 'usage:' in line):
                    print(f"  {line.strip()}")
            return True
        else:
            print(f"❌ Server help failed with return code: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Server test timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_mcp_import():
    """Test if MCP can be imported in the virtual environment"""
    print("\nTesting MCP import...")
    
    venv_python = r"C:\Users\<USER>\Desktop\hook\zhuce\.venv-mcp\Scripts\python.exe"
    
    try:
        result = subprocess.run([
            venv_python, "-c", "from mcp.server.fastmcp import FastMCP; print('MCP FastMCP imported successfully')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ " + result.stdout.strip())
            return True
        else:
            print(f"❌ MCP import failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing MCP import: {e}")
        return False

def main():
    print("🔧 MCP Server Diagnostic Tool")
    print("=" * 50)
    
    # Test MCP import
    import_ok = test_mcp_import()
    
    # Test server startup
    server_ok = test_mcp_server()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  MCP Import: {'✅ PASS' if import_ok else '❌ FAIL'}")
    print(f"  Server Test: {'✅ PASS' if server_ok else '❌ FAIL'}")
    
    if import_ok and server_ok:
        print("\n🎉 All tests passed! MCP server should work properly.")
        print("\n📝 Next steps:")
        print("  1. Restart your IDE (VS Code/Cursor/Claude)")
        print("  2. Restart IDA Pro if you're using it")
        print("  3. The MCP server should now connect successfully")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        
    return import_ok and server_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
