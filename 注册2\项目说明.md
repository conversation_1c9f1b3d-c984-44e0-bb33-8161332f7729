# 51代理自动签到项目说明

## 项目结构

```
51daili-auto-signin/
├── 51daili_simple_login.py      # 简化版登录脚本（使用预设Cookie）
├── 51daili_advanced_login.py    # 高级版登录脚本（处理验证码）
├── slider_solver.py             # 滑块验证码解决器
├── scheduler.py                 # 定时任务调度器
├── config.py                    # 配置文件
├── start.py                     # 启动脚本（Python）
├── start.bat                    # 启动脚本（Windows批处理）
├── install_dependencies.py     # 依赖安装脚本
├── requirements.txt             # Python依赖列表
├── README.md                    # 项目说明文档
├── 项目说明.md                  # 中文项目说明
└── logs/                        # 日志目录（自动创建）
    ├── 51daili_simple.log
    ├── 51daili_advanced.log
    └── scheduler.log
```

## 核心功能模块

### 1. 登录模块
- **简化版** (`51daili_simple_login.py`): 使用预设Cookie，适合反爬虫较弱的情况
- **高级版** (`51daili_advanced_login.py`): 使用Selenium，支持复杂验证

### 2. 验证码处理 (`slider_solver.py`)
- 滑块验证码识别
- 人类化滑动轨迹生成
- 多种验证码类型支持

### 3. 定时调度 (`scheduler.py`)
- 支持多个时间点执行
- 后台守护进程模式
- 手动触发功能

### 4. 配置管理 (`config.py`)
- 集中化配置管理
- 环境变量支持
- 灵活的参数调整

## 使用流程

### 快速开始
1. 双击 `start.bat` (Windows) 或运行 `python start.py`
2. 选择"安装依赖"
3. 选择"配置账号信息"，编辑 `config.py`
4. 选择"立即执行签到"测试功能
5. 选择"启动定时调度器"设置自动执行

### 手动执行
```bash
# 简化版（推荐先尝试）
python 51daili_simple_login.py

# 高级版（如果简化版失败）
python 51daili_advanced_login.py

# 定时调度
python scheduler.py --daemon
```

## 技术特点

### 反爬虫对抗
1. **浏览器指纹伪造**
   - 修改navigator.webdriver属性
   - 伪造浏览器插件信息
   - 随机User-Agent

2. **行为模拟**
   - 随机延迟
   - 鼠标轨迹模拟
   - 人类化打字速度

3. **环境伪装**
   - 禁用自动化标识
   - 设置真实浏览器环境
   - 模拟正常用户行为

### 验证码破解
1. **滑块验证**
   - 图像识别定位缺口
   - 物理学轨迹生成
   - 随机抖动增加真实性

2. **JS验证**
   - 执行反检测脚本
   - 模拟用户交互
   - 绕过常见检测点

## 配置说明

### 基础配置
```python
# 账号信息
ACCOUNT = "your_username"
PASSWORD = "your_password"

# 运行模式
HEADLESS = True  # 无头模式，设为False可看到浏览器操作过程
```

### 高级配置
```python
# 延迟设置（秒）
LOGIN_DELAY = (2, 4)        # 登录页面加载延迟
TYPE_DELAY = (0.05, 0.15)   # 打字延迟
SLIDER_DELAY = (0.01, 0.02) # 滑块移动延迟

# 重试设置
MAX_RETRY = 3               # 最大重试次数
RETRY_DELAY = 5             # 重试间隔
```

## 常见问题解决

### 1. 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. ChromeDriver问题
- 脚本会自动下载匹配的ChromeDriver
- 如果失败，请手动下载并放到PATH中

### 3. 验证码识别失败
- 设置 `HEADLESS = False` 观察过程
- 调整滑块配置参数
- 检查网络连接稳定性

### 4. 登录失败
- 确认账号密码正确
- 检查网站是否更新验证机制
- 查看日志文件获取详细信息

## 部署建议

### 本地部署
- 适合个人使用
- 可以观察执行过程
- 便于调试和配置

### 服务器部署
```bash
# 安装依赖
pip install -r requirements.txt

# 后台运行
nohup python scheduler.py --daemon > /dev/null 2>&1 &

# 或使用systemd服务
sudo systemctl enable 51daili-signin.service
```

### 青龙面板部署
```bash
# 添加定时任务
0 9 * * * python /path/to/51daili_advanced_login.py

# 设置环境变量
export DAILI_ACCOUNT="your_username"
export DAILI_PASSWORD="your_password"
```

## 安全注意事项

1. **账号安全**
   - 使用环境变量存储敏感信息
   - 定期更换密码
   - 避免在公共环境运行

2. **合规使用**
   - 遵守网站服务条款
   - 控制请求频率
   - 避免对服务器造成压力

3. **数据保护**
   - 日志文件可能包含敏感信息
   - 定期清理日志
   - 不要分享配置文件

## 更新维护

### 版本更新
- 关注网站验证机制变化
- 及时更新反爬虫策略
- 优化验证码识别算法

### 监控建议
- 设置执行结果通知
- 监控成功率变化
- 记录异常情况

## 技术支持

如果遇到问题：
1. 查看日志文件获取详细错误信息
2. 检查网站是否有更新
3. 尝试调整配置参数
4. 更新到最新版本

## 免责声明

本工具仅供学习和研究使用，使用者需要：
- 遵守相关法律法规
- 遵守网站服务条款
- 承担使用风险
- 不得用于商业用途
