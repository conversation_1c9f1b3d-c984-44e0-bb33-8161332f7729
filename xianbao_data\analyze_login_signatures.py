#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 分析登录流程的签名算法

import base64
import hmac
import hashlib
import urllib.parse

def analyze_request_1_signature():
    """分析第一个请求的签名"""
    print("🔍 分析请求1签名: /auth/getLoginCode")
    print("=" * 60)
    
    # 请求参数
    params = {
        "areaCode": "92",
        "deviceId": "android",
        "account": "***********", 
        "mac": "8mHfI4nGgZ5GnhO4o+G3EA==",
        "language": "zh",
        "salt": "*************",
        "secret": "Gv1xpOZ3OW+W2BOuN4MWsA=="
    }
    
    print("📋 请求参数:")
    for key, value in params.items():
        print(f"   {key}: {value}")
    
    # 分析签名
    secret = params["secret"]
    salt = params["salt"]
    
    try:
        secret_bytes = base64.b64decode(secret)
        print(f"\n🔐 签名分析:")
        print(f"   签名 (Base64): {secret}")
        print(f"   签名长度: {len(secret_bytes)} 字节")
        print(f"   签名 (十六进制): {secret_bytes.hex()}")
        
        # 尝试不同的签名内容组合
        test_combinations = [
            # 组合1: 所有参数按顺序
            "92android***********" + params["mac"] + "zh" + salt,
            # 组合2: 按字母顺序
            params["account"] + params["areaCode"] + params["deviceId"] + params["language"] + params["mac"] + salt,
            # 组合3: 只有核心参数
            params["account"] + params["mac"] + salt,
            # 组合4: 包含API密钥
            "212i919292901" + params["account"] + params["mac"] + salt
        ]
        
        # 可能的密钥来源
        possible_keys = [
            # 从已知的httpKey
            "hcCgwwhEHhNtf2YVdIj/Bg==",
            # 从MAC地址
            params["mac"],
            # 固定密钥
            "212i919292901",
            # 账号相关
            params["account"]
        ]
        
        print(f"\n🧪 尝试验证签名:")
        for i, content in enumerate(test_combinations, 1):
            print(f"\n--- 组合 {i} ---")
            print(f"   签名内容: {content}")
            
            for key_source in possible_keys:
                try:
                    if key_source.endswith("=="):
                        # Base64密钥
                        key = base64.b64decode(key_source)
                    else:
                        # 字符串密钥
                        key = key_source.encode('utf-8')
                    
                    # 生成HMAC-MD5签名
                    generated_sig = base64.b64encode(
                        hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()
                    ).decode('utf-8')
                    
                    if generated_sig == secret:
                        print(f"   ✅ 匹配! 密钥: {key_source}")
                        return True
                    else:
                        print(f"   ❌ 不匹配 (密钥: {key_source[:20]}...)")
                        
                except Exception as e:
                    print(f"   ❌ 错误 (密钥: {key_source[:20]}...): {e}")
        
    except Exception as e:
        print(f"❌ 签名分析失败: {e}")
    
    return False

def analyze_request_2_signature():
    """分析第二个请求的签名"""
    print(f"\n🔍 分析请求2签名: /authkeys/getLoginPrivateKey")
    print("=" * 60)
    
    params = {
        "userId": "107477",
        "mac": "MEm0lcY/LeDJUqouB/Efdw==",
        "language": "zh",
        "salt": "1753674050373",
        "secret": "o9Ay9s56Oe0CghSyODARKw=="
    }
    
    print("📋 请求参数:")
    for key, value in params.items():
        print(f"   {key}: {value}")
    
    secret = params["secret"]
    salt = params["salt"]
    
    try:
        secret_bytes = base64.b64decode(secret)
        print(f"\n🔐 签名分析:")
        print(f"   签名 (Base64): {secret}")
        print(f"   签名长度: {len(secret_bytes)} 字节")
        print(f"   签名 (十六进制): {secret_bytes.hex()}")
        
        # 尝试不同的签名内容组合
        test_combinations = [
            # 组合1: 所有参数按顺序
            params["userId"] + params["mac"] + params["language"] + salt,
            # 组合2: 按字母顺序
            params["language"] + params["mac"] + salt + params["userId"],
            # 组合3: 包含API密钥
            "212i919292901" + params["userId"] + params["mac"] + salt
        ]
        
        # 可能的密钥来源
        possible_keys = [
            "hcCgwwhEHhNtf2YVdIj/Bg==",  # httpKey
            params["mac"],  # MAC地址
            "212i919292901",  # API密钥
            params["userId"]  # 用户ID
        ]
        
        print(f"\n🧪 尝试验证签名:")
        for i, content in enumerate(test_combinations, 1):
            print(f"\n--- 组合 {i} ---")
            print(f"   签名内容: {content}")
            
            for key_source in possible_keys:
                try:
                    if key_source.endswith("=="):
                        key = base64.b64decode(key_source)
                    else:
                        key = key_source.encode('utf-8')
                    
                    generated_sig = base64.b64encode(
                        hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()
                    ).decode('utf-8')
                    
                    if generated_sig == secret:
                        print(f"   ✅ 匹配! 密钥: {key_source}")
                        return True
                    else:
                        print(f"   ❌ 不匹配 (密钥: {key_source[:20]}...)")
                        
                except Exception as e:
                    print(f"   ❌ 错误 (密钥: {key_source[:20]}...): {e}")
        
    except Exception as e:
        print(f"❌ 签名分析失败: {e}")
    
    return False

def analyze_request_3_signature():
    """分析第三个请求的签名"""
    print(f"\n🔍 分析请求3签名: /user/login/v1")
    print("=" * 60)
    
    # 从URL解析参数
    url = "http://23.248.226.178:8095/user/login/v1?data=60GQxFZcKvJa67P7uo1ujiwnK70P0YpV1y63XtlhsjyQGqybTChHe7kAFyX0d%2Fn9%2BAUeBqp7Yf%2FtygavwlI6yOMfzpVZ3n755yNzp4iRMzJciq%2BPTM4Hq8HTXL8ijK6BHgEVA%2BwUTnrodEIyKCB%2FzkgBkNp6IylW1a4dmQAJrkRC5xjkwvb8E%2F8%2B6iFVNMm2&userId=107477&deviceId=android&language=zh&salt=1753674050662&secret=amVchC6qgzROOrvRk42b4A%3D%3D"
    
    parsed = urllib.parse.urlparse(url)
    params = urllib.parse.parse_qs(parsed.query)
    
    # 转换为字典
    param_dict = {}
    for key, values in params.items():
        param_dict[key] = values[0] if values else ""
    
    print("📋 请求参数:")
    for key, value in param_dict.items():
        if key == "data":
            print(f"   {key}: {value[:50]}... (已截断)")
        else:
            print(f"   {key}: {value}")
    
    secret = urllib.parse.unquote(param_dict["secret"])
    salt = param_dict["salt"]
    
    try:
        secret_bytes = base64.b64decode(secret)
        print(f"\n🔐 签名分析:")
        print(f"   签名 (Base64): {secret}")
        print(f"   签名长度: {len(secret_bytes)} 字节")
        print(f"   签名 (十六进制): {secret_bytes.hex()}")
        
        # 尝试不同的签名内容组合
        data_param = urllib.parse.unquote(param_dict["data"])
        
        test_combinations = [
            # 组合1: 包含data参数
            data_param + param_dict["userId"] + param_dict["deviceId"] + param_dict["language"] + salt,
            # 组合2: 不包含data
            param_dict["userId"] + param_dict["deviceId"] + param_dict["language"] + salt,
            # 组合3: 包含API密钥
            "212i919292901" + param_dict["userId"] + data_param + salt
        ]
        
        possible_keys = [
            "hcCgwwhEHhNtf2YVdIj/Bg==",  # httpKey
            "212i919292901",  # API密钥
            param_dict["userId"]  # 用户ID
        ]
        
        print(f"\n🧪 尝试验证签名:")
        for i, content in enumerate(test_combinations, 1):
            print(f"\n--- 组合 {i} ---")
            print(f"   签名内容: {content[:100]}... (已截断)")
            
            for key_source in possible_keys:
                try:
                    if key_source.endswith("=="):
                        key = base64.b64decode(key_source)
                    else:
                        key = key_source.encode('utf-8')
                    
                    generated_sig = base64.b64encode(
                        hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()
                    ).decode('utf-8')
                    
                    if generated_sig == secret:
                        print(f"   ✅ 匹配! 密钥: {key_source}")
                        return True
                    else:
                        print(f"   ❌ 不匹配 (密钥: {key_source[:20]}...)")
                        
                except Exception as e:
                    print(f"   ❌ 错误 (密钥: {key_source[:20]}...): {e}")
        
    except Exception as e:
        print(f"❌ 签名分析失败: {e}")
    
    return False

def main():
    print("🔐 登录流程签名算法分析")
    print("=" * 60)
    
    success1 = analyze_request_1_signature()
    success2 = analyze_request_2_signature()
    success3 = analyze_request_3_signature()
    
    print(f"\n🎯 分析总结:")
    print(f"   请求1签名验证: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   请求2签名验证: {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"   请求3签名验证: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if not any([success1, success2, success3]):
        print(f"\n💡 建议:")
        print(f"   1. 登录流程可能使用了动态生成的密钥")
        print(f"   2. 密钥可能来自前一个请求的响应")
        print(f"   3. 可能需要分析完整的登录会话流程")
        print(f"   4. 签名算法可能不是标准的HMAC-MD5")

if __name__ == "__main__":
    main()
