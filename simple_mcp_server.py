#!/usr/bin/env python3
"""
Simple IDA Pro MCP Server
"""
import json
import sys
import http.client
from typing import Any, List

# Try to import MCP
try:
    from mcp.server.fastmcp import FastMCP
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

# IDA connection settings
IDA_HOST = "127.0.0.1"
IDA_PORT = 13337

def make_ida_request(method: str, params: List[Any] = None) -> Any:
    """Make a JSON-RPC request to IDA plugin"""
    if params is None:
        params = []
    
    try:
        conn = http.client.HTTPConnection(IDA_HOST, IDA_PORT, timeout=5)
        request = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params,
            "id": 1
        }
        
        conn.request("POST", "/mcp", json.dumps(request), {
            "Content-Type": "application/json"
        })
        
        response = conn.getresponse()
        data = json.loads(response.read().decode())
        conn.close()
        
        if "error" in data:
            return f"IDA Error: {data['error']['message']}"
        
        result = data.get("result", "success")
        return result if result is not None else "success"
        
    except Exception as e:
        return f"Connection failed: {str(e)}"

if MCP_AVAILABLE:
    # Create MCP server
    mcp = FastMCP("IDA Pro MCP")

    @mcp.tool()
    def check_connection() -> str:
        """Check if the IDA plugin is running"""
        result = make_ida_request("check_connection")
        return str(result)

    @mcp.tool()
    def get_metadata() -> str:
        """Get metadata about the current IDB"""
        result = make_ida_request("get_metadata")
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def get_function_by_name(name: str) -> str:
        """Get a function by its name"""
        result = make_ida_request("get_function_by_name", [name])
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def get_function_by_address(address: str) -> str:
        """Get a function by its address"""
        result = make_ida_request("get_function_by_address", [address])
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def get_current_address() -> str:
        """Get the address currently selected by the user"""
        result = make_ida_request("get_current_address")
        return str(result)

    @mcp.tool()
    def get_current_function() -> str:
        """Get the function currently selected by the user"""
        result = make_ida_request("get_current_function")
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def list_functions(offset: int = 0, count: int = 10) -> str:
        """List all functions in the database (paginated)"""
        result = make_ida_request("list_functions", [offset, count])
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def convert_number(text: str, size: int = 4) -> str:
        """Convert a number (decimal, hexadecimal) to different representations"""
        result = make_ida_request("convert_number", [text, size])
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def decompile_function(address: str) -> str:
        """Decompile a function at the given address"""
        result = make_ida_request("decompile_function", [address])
        return str(result)

    @mcp.tool()
    def disassemble_function(start_address: str) -> str:
        """Get assembly code for a function"""
        result = make_ida_request("disassemble_function", [start_address])
        if isinstance(result, dict):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def get_xrefs_to(address: str) -> str:
        """Get all cross references to the given address"""
        result = make_ida_request("get_xrefs_to", [address])
        if isinstance(result, (dict, list)):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def get_entry_points() -> str:
        """Get all entry points in the database"""
        result = make_ida_request("get_entry_points")
        if isinstance(result, (dict, list)):
            return json.dumps(result, indent=2)
        return str(result)

    @mcp.tool()
    def set_comment(address: str, comment: str) -> str:
        """Set a comment for a given address"""
        result = make_ida_request("set_comment", [address, comment])
        return str(result)

    @mcp.tool()
    def rename_function(function_address: str, new_name: str) -> str:
        """Rename a function"""
        result = make_ida_request("rename_function", [function_address, new_name])
        return str(result)

    def main():
        """Run the MCP server"""
        print("Starting IDA Pro MCP Server...")
        try:
            mcp.run()
        except KeyboardInterrupt:
            print("Server stopped")
        except Exception as e:
            print(f"Server error: {e}")

else:
    def main():
        print("Error: MCP library not available")
        print("Please install with: pip install mcp")
        sys.exit(1)

if __name__ == "__main__":
    main()
