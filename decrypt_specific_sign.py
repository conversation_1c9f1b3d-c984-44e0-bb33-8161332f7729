# 文件名: decrypt.py
# (版本 2 - 使用正确的密钥派生方法)
import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from hashlib import md5

def evp_bytes_to_key(password: bytes, salt: bytes, key_len: int, iv_len: int) -> tuple[bytes, bytes]:
    """
    一个精确模仿 OpenSSL EVP_BytesToKey 方法的函数，使用MD5哈希。
    """
    derived_bytes = b''
    block = b''
    while len(derived_bytes) < key_len + iv_len:
        block = md5(block + password + salt).digest()
        derived_bytes += block
    
    return derived_bytes[:key_len], derived_bytes[key_len:key_len + iv_len]

# --- 您要解密的数据 ---

# 截图中提供的、密码为 "1111qqqq" 的数据
sign_data = {
    "ct": "EuDto+uwQH+PIKr9K1+nd2CvQtF0D+XudmSTDgBMwSpIZOWaEYDWZUclJsRWR5asDAEtFNWnkbO7FKH3+B90pA==",
    "iv": "5f9644c3ac0572a83c1dcd536d8a26f7",
    "s":  "f3da36c82900b988"
}

# 写在前端代码里的主密钥
passphrase = "v4NTEx37"

# --- 解密过程开始 ---

print("开始解密 (使用正确的OpenSSL/MD5密钥派生方法)...")
print("-" * 50)

try:
    # 1. 将所有输入从字符串格式转换为字节格式
    ciphertext = base64.b64decode(sign_data["ct"])
    iv_from_json = bytes.fromhex(sign_data["iv"])
    salt = bytes.fromhex(sign_data["s"])
    key_bytes = passphrase.encode('utf-8')

    # 2. 【关键修正】使用正确的 evp_bytes_to_key 函数派生密钥。
    #    我们需要一个 32 字节 (256-bit) 的密钥。
    #    这个函数同时也能生成一个iv，但我们将使用JSON中提供的那一个。
    final_key, _ = evp_bytes_to_key(key_bytes, salt, 32, 16)
    
    print(f"派生出的最终密钥 (十六进制): {final_key.hex()}")

    # 3. 创建AES解密器，模式为CBC，使用JSON中提供的iv
    cipher = AES.new(final_key, AES.MODE_CBC, iv_from_json)

    # 4. 执行解密
    decrypted_padded_data = cipher.decrypt(ciphertext)

    # 5. 移除填充
    plaintext_bytes = unpad(decrypted_padded_data, AES.block_size)

    # 6. 将结果解码为字符串
    plaintext = plaintext_bytes.decode('utf-8')

    # --- 打印最终结果 ---
    print("\n✅ 解密成功！")
    pretty_json = json.dumps(json.loads(plaintext), indent=4, ensure_ascii=False)
    print("解密后的内容是:\n", pretty_json)

except Exception as e:
    print("\n❌ 解密失败！")
    print("错误详情:", str(e))