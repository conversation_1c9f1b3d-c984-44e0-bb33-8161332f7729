const Notification = require('../models/notificationModel');

// @desc    获取当前用户的通知
// @route   GET /api/notifications
// @access  Private
exports.getUserNotifications = async (req, res) => {
  try {
    // 分页
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // 条件查询
    const query = { 
      recipient: req.user.id 
    };

    // 是否只查看未读
    if (req.query.unread === 'true') {
      query.read = false;
    }

    // 按时间倒序查询
    const notifications = await Notification.find(query)
      .populate({
        path: 'sender',
        select: 'username avatar'
      })
      .populate({
        path: 'deal',
        select: 'title'
      })
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // 获取总数
    const total = await Notification.countDocuments(query);

    // 获取未读消息数量
    const unreadCount = await Notification.countDocuments({ 
      recipient: req.user.id,
      read: false
    });

    // 分页结果
    const pagination = {
      total,
      unreadCount,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: notifications.length,
      pagination,
      data: notifications
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    标记通知为已读
// @route   PUT /api/notifications/:id/read
// @access  Private
exports.markAsRead = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 确保用户是通知的接收者
    if (notification.recipient.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        message: '未授权访问此通知'
      });
    }

    notification.read = true;
    await notification.save();

    res.status(200).json({
      success: true,
      data: notification
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    标记所有通知为已读
// @route   PUT /api/notifications/read-all
// @access  Private
exports.markAllAsRead = async (req, res) => {
  try {
    await Notification.updateMany(
      { recipient: req.user.id, read: false },
      { read: true }
    );

    res.status(200).json({
      success: true,
      message: '所有通知已标记为已读'
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    删除通知
// @route   DELETE /api/notifications/:id
// @access  Private
exports.deleteNotification = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 确保用户是通知的接收者
    if (notification.recipient.toString() !== req.user.id) {
      return res.status(401).json({
        success: false,
        message: '未授权删除此通知'
      });
    }

    await notification.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
}; 