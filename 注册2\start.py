#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动脚本 - 提供友好的用户界面
"""

import os
import sys
import subprocess
import time

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    51代理自动签到工具                          ║
    ║                                                              ║
    ║  功能: 自动登录、处理验证码、执行签到                          ║
    ║  支持: 滑块验证、JS反爬虫、定时任务                           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖"""
    print("正在检查依赖...")
    
    required_packages = [
        'requests', 'selenium', 'opencv-python', 
        'Pillow', 'numpy', 'webdriver-manager', 'schedule'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"× {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: python install_dependencies.py")
        return False
    
    print("\n✓ 所有依赖已安装")
    return True

def check_config():
    """检查配置"""
    print("\n正在检查配置...")
    
    try:
        from config import ACCOUNT, PASSWORD
        
        if not ACCOUNT or ACCOUNT == "your_username":
            print("× 请在config.py中设置正确的账号")
            return False
        
        if not PASSWORD or PASSWORD == "your_password":
            print("× 请在config.py中设置正确的密码")
            return False
        
        print(f"✓ 账号: {ACCOUNT}")
        print("✓ 密码: ****")
        return True
        
    except ImportError:
        print("× 配置文件config.py不存在")
        return False

def run_script(script_name):
    """运行脚本"""
    try:
        subprocess.run([sys.executable, script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"脚本执行失败: {e}")
    except KeyboardInterrupt:
        print("\n用户中断执行")

def main_menu():
    """主菜单"""
    while True:
        clear_screen()
        print_banner()
        
        print("请选择操作:")
        print("1. 安装依赖")
        print("2. 立即执行签到 (简化版)")
        print("3. 立即执行签到 (高级版)")
        print("4. 启动定时调度器")
        print("5. 检查系统状态")
        print("6. 配置账号信息")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-6): ").strip()
        
        if choice == '1':
            print("\n正在安装依赖...")
            run_script("install_dependencies.py")
            input("\n按回车键继续...")
            
        elif choice == '2':
            print("\n正在执行简化版签到...")
            run_script("51daili_simple_login.py")
            input("\n按回车键继续...")
            
        elif choice == '3':
            print("\n正在执行高级版签到...")
            run_script("51daili_advanced_login.py")
            input("\n按回车键继续...")
            
        elif choice == '4':
            print("\n启动定时调度器...")
            run_script("scheduler.py")
            input("\n按回车键继续...")
            
        elif choice == '5':
            print("\n系统状态检查:")
            print("-" * 40)
            deps_ok = check_dependencies()
            config_ok = check_config()
            
            if deps_ok and config_ok:
                print("\n✓ 系统状态正常，可以正常使用")
            else:
                print("\n× 系统状态异常，请先解决上述问题")
            
            input("\n按回车键继续...")
            
        elif choice == '6':
            print("\n配置账号信息:")
            print("-" * 40)
            print("请编辑 config.py 文件，修改以下配置:")
            print("ACCOUNT = \"your_username\"")
            print("PASSWORD = \"your_password\"")
            print("\n或者设置环境变量:")
            print("export DAILI_ACCOUNT=\"your_username\"")
            print("export DAILI_PASSWORD=\"your_password\"")
            
            input("\n按回车键继续...")
            
        elif choice == '0':
            print("\n感谢使用！")
            break
            
        else:
            print("\n无效选项，请重新选择")
            time.sleep(1)

if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n程序已退出")
    except Exception as e:
        print(f"\n程序出错: {e}")
        input("按回车键退出...")
