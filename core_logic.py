import requests
import json
import time
import random
import os
import sys  # 导入sys模块
import threading
import queue
from proxy_manager import ProxyManager  # 导入51代理管理器
from proxy_91_manager import Proxy91Manager, Proxy91Config # 导入91代理管理器
from haozu import YeyeYunClient, YeyeYunConfig # 导入椰子云API模块
from phone import HaozhumaClient, HaozhumaConfig # 导入好猪马API模块
from qianchuan import QianchuanClient, QianchuanConfig # 导入千川API模块
from device_emulator import generate_complete_android_profile # 导入新的"设备生产线"
from sms_channel1 import SmsChannel1Client, SmsChannel1Config # 导入通道1短信验证码模块
from sms_channel2 import SmsChannel2Client, SmsChannel2Config # 导入通道2短信验证码模块
from sms_channel6 import SmsChannel6, SmsChannel6Config # 导入通道6短信验证码模块
from retrying import retry
from utils import logger
import re # 导入正则表达式模块

# ==============================================================================
# ----------------------------- 1. 全局配置区 ----------------------------------
# ==============================================================================

# --- 线程安全配置 ---
# 用于保护文件写入操作，防止多线程冲突
log_lock = threading.Lock()

class ThreadSafeCounter:
    """线程安全的计数器"""
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    def increment(self):
        with self._lock:
            self._value += 1
    
    @property
    def value(self):
        return self._value


# --- 目标注册网站配置 ---
class RegistrationConfig:
    # 目标网站的URL
    BASE_URL = "https://www.xlcfxndv.vip"
    # 恢复固定的、可交互修改的邀请码
    INVITE_CODE = "78027556"
    # 支持多个邀请码
    INVITE_CODES = []
    # 注册成功后的延迟时间范围（秒）
    MIN_SUCCESS_DELAY_SECONDS = 180  # 3分钟
    MAX_SUCCESS_DELAY_SECONDS = 300  # 5分钟
    # 是否使用自定义时间范围
    USE_CUSTOM_DELAY = False
    # 是否使用多个邀请码
    USE_MULTIPLE_INVITE_CODES = False


# --- 本地文件配置 ---
DESKTOP_PATH = os.path.join(os.path.expanduser('~'), 'Desktop')

# 重要提示: 请确保您的桌面上存在名为 40.txt 的文件。
INFO_FILE_PATH = os.path.join(DESKTOP_PATH, '40.txt')
# 账号信息和日志将保存到您的桌面上。
ACCOUNT_INFO_PATH = os.path.join(DESKTOP_PATH, 'account_info.json')
ACCOUNTS_LOG_PATH = os.path.join(DESKTOP_PATH, '账号.txt')


# --- 51代理配置 ---
class Proxy51Config:
    # 是否启用代理
    ENABLE_PROXY = True
    # 您的套餐ID
    PACK_ID = "2"
    # 您的用户ID
    UID = "42477"
    # 权限校验名称
    ACCESS_NAME = "ab263263"
    # 权限校验密码
    ACCESS_PASSWORD = "8C84B9BFE1774BFB2443DD34797EE4FB"
    # 代理协议 ('http' 或 'socks5')
    PROTOCOL = "http"
    # 代理使用时长(分钟)
    TIME_DURATION = 2


# --- 千川接码平台配置 (新增) ---
# 注意：请在使用前，务必在 qianchuan.py 文件中填入您的 用户名 和 密码
# QianchuanConfig.ENABLED = True  # <-- 如果要使用千川，请取消此行的注释


# ==============================================================================
# ----------------------------- 2. 各平台配置区 --------------------------------
# ==============================================================================

# --- 椰子云配置 ---
# (YeyeYunConfig 类的定义保持不变)


# --- 好猪马配置 ---
# (HaozhumaConfig 类的定义保持不变)


# --- 千川平台配置 ---
# (此类已移至 qianchuan.py, 这里仅作逻辑占位，实际配置请修改 qianchuan.py 文件)

# --- 通道1短信平台配置 ---
# 注意：请在使用前，务必在 sms_channel1.py 文件中填入您的 用户名 和 密码
# SmsChannel1Config.ENABLED = False  # <-- 如果要使用通道1，请取消此行的注释并设为True

# --- 通道2短信平台配置 ---
# 注意：请在使用前，务必在 sms_channel2.py 文件中填入您的 用户名 和 密码
# SmsChannel2Config.ENABLED = True  # <-- 如果要使用通道2，请取消此行的注释

# --- 通道6短信平台配置 ---
# 注意：请在使用前，务必在 sms_channel6.py 文件中填入您的 用户名 和 密码
SmsChannel6Config.ENABLED = True  # 默认启用通道6
SmsChannel6Config.PROJECT_ID = "64457"  # 使用远通科技项目ID


# ==============================================================================
# ------------------------ 3. 主注册脚本与辅助函数 ----------------------------
def generic_execute_registration_attempt(website, platform_client, user_info, password, phone):
    """
    【统一执行引擎】执行单次注册尝试。包含重试逻辑。
    此函数不获取或释放手机号，只负责执行注册的核心步骤。
    返回: 'SUCCESS', 'REAL_NAME_USED', 'FAIL'
    """
    thread_name = threading.current_thread().name
    max_retries = 20  # 大幅增加重试次数，以应对服务器繁忙

    for attempt in range(max_retries):
        try:
            print(f"[{thread_name}] 尝试使用手机号 {phone} 为「{user_info['name']}」注册... (尝试 {attempt + 1}/{max_retries})")
            reg_session = website.session
            
            # 1. 获取验证会话ID
            print(f"\n[{thread_name}] --- 步骤 1/4: 获取注册网站的验证会话ID ---")
            captcha_url = f"{RegistrationConfig.BASE_URL}/api/member.Register/captcha?_t={int(time.time() * 1000)}"
            res_captcha = reg_session.get(captcha_url, timeout=30)
            res_captcha.raise_for_status() # 检查HTTP错误
            captcha_id = res_captcha.json()['data']['captcha_id']
            print(f"[{thread_name}] 成功获取 captcha_id: {captcha_id}")

            # 2. 请求发送短信
            print(f"\n[{thread_name}] --- 步骤 2/4: 请求注册网站向 {phone} 发送短信 ---")
            sms_url = f"{RegistrationConfig.BASE_URL}/api/sms.Sms/send"
            sms_headers = reg_session.headers.copy()
            sms_headers['Content-Type'] = 'application/x-www-form-urlencoded'
            
            # 增加一个2-5秒的随机延迟，模拟人工操作
            time.sleep(random.randint(2, 5))
            
            res_sms = reg_session.post(sms_url, data={'phone': phone}, headers=sms_headers, timeout=30)
            res_sms.raise_for_status()
            sms_response_json = res_sms.json()
            print(f"[{thread_name}] 发送短信请求成功，服务器响应:", sms_response_json.get('msg'))
            
            # 核心改进：如果服务器明确表示发送失败，则更换IP并重试，而不是立即放弃该手机号
            if "失败" in sms_response_json.get("msg", ""):
                print(f"[{thread_name}] 网站拒绝发送短信 ({sms_response_json.get('msg')})。更换代理IP后将重试。")
                website.apply_new_proxy() # 更换IP
                continue # 进入下一次循环，使用相同的手机号和用户信息，但使用新IP和会话

            # 3. 获取短信验证码 (通过统一的平台客户端接口)
            print(f"\n[{thread_name}] --- 步骤 3/4: 从接码平台获取短信验证码 ---")
            sms_code = platform_client.get_sms_code(phone)
            if not sms_code:
                print(f"[{thread_name}] 未能获取到验证码，本次尝试失败。")
                return 'FAIL'

            # 4. 提交最终注册信息
            print(f"\n[{thread_name}] --- 步骤 4/4: 提交所有信息进行最终注册 ---")
            
            # 选择邀请码：如果启用了多邀请码模式，则从列表中随机选择一个
            invite_code = RegistrationConfig.INVITE_CODE
            if RegistrationConfig.USE_MULTIPLE_INVITE_CODES and RegistrationConfig.INVITE_CODES:
                invite_code = random.choice(RegistrationConfig.INVITE_CODES)
                print(f"[{thread_name}] 从邀请码列表中选择了邀请码: {invite_code}")
            
            final_payload = {
                "phone": phone, "password": password, "check_password": password, "qq": "",
                "name": user_info['name'], "id_number": user_info['id_number'],
                "invite_code": invite_code,
                "captcha_id": captcha_id, "captcha_code": "",
                "regist_type": 1, "phone_code": sms_code
            }
            reg_url = f"{RegistrationConfig.BASE_URL}/api/member.Register/register"
            final_res = reg_session.post(reg_url, json=final_payload, timeout=30)
            final_res.raise_for_status()
            response_json = final_res.json()
            print(f"[{thread_name}] 最终服务器响应: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            
            # 分析响应结果
            if response_json.get("code") == 200:
                print(f"\n[{thread_name}] --- 注册成功，自动登录并执行签到和抽奖 ---")
                website.login(phone, password)
                website.sign_in()
                website.draw_lottery()
                append_account_to_log(phone, password, user_info['name'], user_info['id_number'], invite_code)
                
                # 注册成功后，通过统一接口拉黑手机号
                # 注意：好猪马平台在get_sms_code成功时已自动拉黑，这里重复调用也无妨，椰子云则需要在这里拉黑
                platform_client.add_to_blacklist(phone)

                return 'SUCCESS'
            elif "已实名" in response_json.get("msg", "") or "身份证已存在" in response_json.get("msg", ""):
                print(f"[{thread_name}] 注册失败: 用户「{user_info['name']}」信息已被使用。")
                return 'REAL_NAME_USED'
            elif "用户已注册" in response_json.get("msg", ""):
                print(f"[{thread_name}] 注册失败: 手机号 {phone} 已被注册。")
                return 'PHONE_REGISTERED'
            elif "系统繁忙" in response_json.get("msg", "") or "操作太频繁" in response_json.get("msg", ""):
                print(f"[{thread_name}] 遇到服务器繁忙/操作频繁 ({response_json.get('msg')})，3秒后将自动重试...")
                time.sleep(3)
                continue  # 进入下一次主循环重试，不浪费手机号和实名信息
            else:
                print(f"[{thread_name}] 注册失败，原因: {response_json.get('msg', '未知错误')}")
                return 'FAIL'

        except requests.exceptions.RequestException as e:
            print(f"❌ [{thread_name}] 执行注册时发生网络异常: {e}.")
            if attempt < max_retries - 1:
                print(f"[{thread_name}] 等待后更换代理并重试...")
                time.sleep(random.randint(3, 5))
                website.apply_new_proxy() # 关键：更换代理IP
                continue
            else:
                print(f"❌ [{thread_name}] 网络异常，已达到最大重试次数。")
                return 'FAIL'
        except Exception as e:
            print(f"❌ [{thread_name}] 执行注册尝试时发生未预料的异常: {e}")
            # 将用户信息放回队列，以防万一
            return 'FAIL'

    # 如果循环完成所有重试都未成功
    print(f"❌ [{thread_name}] 已达到最大重试次数，注册失败。")
    return 'FAIL'


def generic_registration_worker(platform_client, proxy_manager, user_info_queue, pwd_choice, password_for_all, successful_registrations, num_to_register):
    """
    【统一线程工作函数】
    - 获取手机号
    - 循环从队列中获取用户，直到注册成功或手机号注册失败
    - 智能处理"已实名"错误 (椰子云优秀逻辑)
    """
    thread_name = threading.current_thread().name
    # 每个线程拥有自己独立的网站客户端，以管理自己的会话和代理
    website = WebsiteClient(RegistrationConfig.BASE_URL, proxy_manager)

    while not user_info_queue.empty() and successful_registrations.value < num_to_register:
        phone = None
        phone_location = "未知"

        try:
            print(f"[{thread_name}] 正在获取一个新手机号来开始一系列注册尝试...")
            
            # 直接使用platform_client的get_phone_number方法
            success, result = platform_client.get_phone_number()
            if not success:
                print(f"[{thread_name}] 无法获取手机号 ({result})，线程将等待5秒后重试。")
                time.sleep(5)
                continue
            
            # 解包手机号和归属地
            phone, phone_location = result

            # 获取到一个手机号后，循环使用它来尝试注册队列中的用户 (这是椰子云的核心优势逻辑)
            while not user_info_queue.empty() and successful_registrations.value < num_to_register:
                user_info = None
                try:
                    user_info = user_info_queue.get_nowait()
                except queue.Empty:
                    print(f"[{thread_name}] 实名信息队列已空。")
                    break # 队列已空，此手机号的任务结束

                # 决定当前账号的密码
                current_password = password_for_all if pwd_choice == "1" else generate_random_password()
                
                # 执行单次注册尝试
                status = generic_execute_registration_attempt(website, platform_client, user_info, current_password, phone)

                if status == 'SUCCESS':
                    successful_registrations.increment()
                    phone = None # 关键：成功后将phone设为None，防止finally块中再次释放
                    
                    # --- 新增：注册成功后等待 ---
                    # 检查是否还有更多账号需要注册，如果没有，就不需要等待了
                    if successful_registrations.value < num_to_register:
                        delay = random.randint(
                            RegistrationConfig.MIN_SUCCESS_DELAY_SECONDS,
                            RegistrationConfig.MAX_SUCCESS_DELAY_SECONDS
                        )
                        min_seconds = RegistrationConfig.MIN_SUCCESS_DELAY_SECONDS
                        max_seconds = RegistrationConfig.MAX_SUCCESS_DELAY_SECONDS
                        
                        if RegistrationConfig.USE_CUSTOM_DELAY:
                            print(f"[{thread_name}] ✅ 注册成功！线程将休眠 {delay}秒 (自定义范围: {min_seconds}-{max_seconds}秒)，以模拟真实用户行为...")
                        else:
                            print(f"[{thread_name}] ✅ 注册成功！线程将休眠 {delay}秒 (默认范围: {min_seconds}-{max_seconds}秒)，以模拟真实用户行为...")
                        time.sleep(delay)
                    
                    break 
                
                elif status == 'REAL_NAME_USED':
                    print(f"[{thread_name}] 用户「{user_info['name']}」信息无效，继续使用手机号 {phone} 和下一个实名信息...")
                    continue 
                
                elif status == 'PHONE_REGISTERED':
                    print(f"[{thread_name}] 此手机号已被注册，将拉黑并更换。用户「{user_info['name']}」已放回队列。")
                    platform_client.add_to_blacklist(phone)
                    phone = None # 关键：设为None，防止finally块重复释放/拉黑
                    try:
                        user_info_queue.put(user_info)
                    except Exception as e:
                        print(f"[{thread_name}] 放回用户到队列失败: {e}")
                    break # 跳出内部循环，去获取新手机号

                elif status == 'FAIL':
                    print(f"[{thread_name}] 手机号 {phone} 注册失败。更换手机号并把用户「{user_info['name']}」放回队列。")
                    try:
                        user_info_queue.put(user_info)
                    except Exception as e:
                        print(f"[{thread_name}] 放回用户到队列失败: {e}")
                    break # 跳出内部的用户循环，去获取新手机号
        
        finally:
            if phone:
                print(f"[{thread_name}] 本轮任务结束，等待20秒后释放号码 {phone} ...")
                time.sleep(20)
                platform_client.release_phone_number(phone)


def generate_random_password(length=None):
    """生成8-12位随机密码，包含英文字母和数字"""
    if not length:
        length = random.randint(8, 12)  # 如果未指定长度，则在8-12之间随机选择
    
    # 确保密码包含至少一个数字和一个字母
    letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    digits = "0123456789"
    
    # 生成基本密码，确保至少有一个字母和一个数字
    password = [random.choice(letters)]  # 至少一个字母
    password.append(random.choice(digits))  # 至少一个数字
    
    # 填充剩余长度
    remaining_length = length - 2
    all_chars = letters + digits
    password.extend([random.choice(all_chars) for _ in range(remaining_length)])
    
    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)

def read_user_info(file_path):
    """
    读取并验证用户信息。
    - 只接受2-10位纯中文的姓名。
    - 自动跳过不符合格式的条目。
    """
    print(f"正在从 {file_path} 读取信息...")
    user_list = []
    invalid_name_count = 0
    # 正则表达式：严格匹配2到10个中文字符
    chinese_name_pattern = re.compile(r'^[\u4e00-\u9fa5]{2,10}$')

    if not os.path.exists(file_path):
        print(f"❌ 错误: 信息文件不存在于 {file_path}")
        return []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if '----' in line:
                    parts = line.split('----')
                    if len(parts) >= 2:
                        name = parts[0].strip()
                        id_number = parts[1].strip()
                        
                        # 验证姓名是否为2-10位中文字符
                        if chinese_name_pattern.match(name):
                            user_list.append({"name": name, "id_number": id_number})
                        else:
                            # 如果不符合，则跳过并计数
                            invalid_name_count += 1
        
        print(f"✅ 成功读取 {len(user_list)} 条有效信息。")
        if invalid_name_count > 0:
            print(f"⚠️  已自动跳过 {invalid_name_count} 条姓名格式不符的信息 (例如: 非中文、长度错误等)。")
        return user_list
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {e}")
        return []

def manual_input_user_info():
    """手动输入实名信息"""
    user_list = []
    print("\n--- 手动输入实名信息 ---")
    try:
        num_users = int(input("请输入要添加的实名信息数量: ").strip())
        for i in range(num_users):
            print(f"\n--- 输入第 {i+1}/{num_users} 个实名信息 ---")
            name = input("请输入姓名: ").strip()
            id_number = input("请输入身份证号码: ").strip()
            
            if name and id_number:
                user_list.append({"name": name, "id_number": id_number})
                print(f"✅ 已添加: {name}")
            else:
                print("❌ 姓名或身份证号码不能为空，此条信息已跳过")
        
        print(f"\n✅ 成功手动添加 {len(user_list)} 条实名信息")
        return user_list
    except ValueError:
        print("❌ 请输入有效的数字")
        return manual_input_user_info()  # 递归重试
    except Exception as e:
        print(f"❌ 输入过程中发生错误: {e}")
        return []

def select_user(user_list):
    if not user_list: return None
    print("\n请选择要用于注册的身份信息:"); [print(f"  [{i+1}] {u['name']}") for i, u in enumerate(user_list)]
    while True:
        try: choice = int(input(f"请输入序号 (1-{len(user_list)}): ")); return user_list[choice-1] if 1<=choice<=len(user_list) else print("无效序号")
        except ValueError: print("请输入数字")

def append_account_to_log(phone, password, name=None, id_number=None, invite_code=None):
    """将注册成功的账号信息追加到记录文件中，只记录账号和密码（线程安全）"""
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    log_entry = (
        f"{current_time} | {phone} | {password} | 邀请码: {invite_code}\n"
    )
    with log_lock:
        with open(ACCOUNTS_LOG_PATH, 'a', encoding='utf-8') as f:
            f.write(log_entry)


# ==============================================================================
# --------------------- 4. 登录、签到和抽奖功能模块 ---------------------------
# ==============================================================================

class WebsiteClient:
    """封装所有与目标网站交互的逻辑，包括注册、登录、签到、抽奖等"""
    
    def __init__(self, base_url, proxy_client=None):
        self.base_url = base_url
        self.session = requests.Session()
        
        # --- 核心改造: 为每个客户端实例生成一个唯一且持久的设备档案 ---
        self.device_profile = generate_complete_android_profile()
        
        # 1. 应用该设备的浏览器指纹到请求头
        self.session.headers.update(self.device_profile['headers'])
        
        # 2. 更新通用请求头
        self.session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': base_url,
            'Referer': f"{base_url}/"
        })

        self.api_token = None
        self.proxy_client = proxy_client  # 保存代理客户端引用
        
        # 使用更简洁的方式打印伪装信息
        print(f"网站客户端已初始化，伪装 User-Agent: {self.session.headers.get('User-Agent', '未知')}")
        
        # 如果有代理客户端，立即应用一个代理
        if self.proxy_client:
            self.apply_new_proxy()
    
    def test_proxy(self, proxies, test_url="https://www.baidu.com", timeout=10):
        """测试代理是否可用
        
        参数:
        - proxies: 代理配置字典
        - test_url: 用于测试的URL，默认使用百度
        - timeout: 超时时间（秒）
        
        返回:
        - True: 代理可用
        - False: 代理不可用
        """
        print(f"正在测试代理可用性: {proxies.get('http', '未知')}...")
        
        # 创建一个临时会话用于测试
        test_session = requests.Session()
        test_session.proxies.update(proxies)
        # 修正: 测试时也应使用和主会话一致的User-Agent
        test_session.headers.update({'User-Agent': self.session.headers['User-Agent']})
        
        try:
            start_time = time.time()
            response = test_session.get(test_url, timeout=timeout)
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ 代理测试成功! 响应时间: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ 代理测试失败: HTTP状态码 {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 代理测试失败: {e}")
            return False
    
    def apply_new_proxy(self):
        """应用一个新的代理到会话中，强制获取全新的IP"""
        if not self.proxy_client:
            return False
            
        try:
            # 获取并测试代理，最多尝试3次
            max_attempts = 3
            for attempt in range(1, max_attempts + 1):
                # 设置force_new=True强制获取新IP，确保不会重用之前的代理
                proxies = self.proxy_client.get_proxy(force_new=True)
                if not proxies:
                    print("❌ 无法获取代理IP")
                    return False
                
                # 测试代理是否可用
                if self.test_proxy(proxies):
                    # 代理测试通过，应用到当前会话
                    self.session.proxies.update(proxies)
                    print(f"✅ 已应用新的可用代理IP: {proxies.get('http', '未知')}")
                    return True
                else:
                    print(f"⚠️ 代理不可用，正在尝试获取新代理 (尝试 {attempt}/{max_attempts})...")
                    # 释放不可用的代理
                    self.proxy_client.release_proxy()
            
            print("❌ 已尝试多次但未能获取可用代理，将使用直接连接")
            # 清除之前的代理设置
            if 'http' in self.session.proxies:
                del self.session.proxies['http']
            if 'https' in self.session.proxies:
                del self.session.proxies['https']
            return False
                
        except Exception as e:
            print(f"❌ 应用代理IP时出错: {e}")
            return False
    
    def login(self, phone, password):
        """登录目标网站并获取ApiToken"""
        print(f"\n--- 正在登录网站 (手机号: {phone}) ---")
        try:
            login_url = f"{self.base_url}/api/member.Login/login"
            login_data = {"phone": phone, "password": password}
            
            response = self.session.post(login_url, json=login_data, timeout=10)
            data = response.json()
            
            if data.get("code") == 200 and "data" in data and "token" in data["data"]:
                self.api_token = data["data"]["token"]
                # 设置API令牌头
                self.session.headers.update({'ApiToken': self.api_token})
                # 保存账号信息到本地
                self.save_account_info(phone, password, self.api_token)
                print(f"✅ 登录成功！用户名: {data['data'].get('nickname', '未知')}")
                return True
            else:
                print(f"❌ 登录失败: {data.get('msg', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 登录网站时发生错误: {e}")
            return False
    
    def save_account_info(self, phone, password, token):
        """保存账号信息到本地文件"""
        try:
            account_info = {
                "phone": phone,
                "password": password,
                "api_token": token,
                "last_login_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            with open(ACCOUNT_INFO_PATH, "w", encoding="utf-8") as f:
                json.dump(account_info, f, ensure_ascii=False, indent=2)
            print(f"✅ 账号信息已保存到: {ACCOUNT_INFO_PATH}")
        except Exception as e:
            print(f"❌ 保存账号信息失败: {e}")
    
    def load_account_info(self):
        """从本地文件加载账号信息"""
        try:
            if os.path.exists(ACCOUNT_INFO_PATH):
                with open(ACCOUNT_INFO_PATH, "r", encoding="utf-8") as f:
                    account_info = json.load(f)
                print("✅ 成功加载账号信息")
                return account_info
            else:
                print("⚠️ 未找到已保存的账号信息")
                return None
        except Exception as e:
            print(f"❌ 加载账号信息失败: {e}")
            return None
    
    def sign_in(self):
        """执行每日签到"""
        if not self.api_token:
            print("❌ 未登录，无法签到")
            return False
        
        print("\n--- 正在执行每日签到 ---")
        try:
            sign_url = f"{self.base_url}/api/sign.Sign/sign"
            response = self.session.post(sign_url, json={}, timeout=10)
            data = response.json()
            
            if data.get("code") == 200:
                print(f"✅ 签到成功！信息: {data.get('msg', '未知')}")
                if "data" in data:
                    print(f"   奖励详情: {json.dumps(data['data'], ensure_ascii=False)}")
                return True
            elif data.get("code") == 500 and "已签到" in data.get("msg", ""):
                print(f"⚠️ 今日已签到: {data.get('msg', '未知')}")
                return True
            else:
                print(f"❌ 签到失败: {data.get('msg', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 签到时发生错误: {e}")
            return False
    
    def draw_lottery(self):
        """执行抽奖"""
        if not self.api_token:
            print("❌ 未登录，无法抽奖")
            return False
        
        print("\n--- 正在执行抽奖 ---")
        try:
            lottery_url = f"{self.base_url}/api/drawing.Drawing/start"
            response = self.session.post(lottery_url, json={}, timeout=10)
            data = response.json()
            
            if data.get("code") == 200:
                print(f"✅ 抽奖成功！")
                if "data" in data:
                    print(f"   抽奖结果: {json.dumps(data['data'], ensure_ascii=False)}")
                return True
            elif data.get("code") == 500 and ("次数不足" in data.get("msg", "") or "已用完" in data.get("msg", "")):
                print(f"⚠️ 抽奖次数已用完: {data.get('msg', '未知')}")
                return True
            else:
                print(f"❌ 抽奖失败: {data.get('msg', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 抽奖时发生错误: {e}")
            return False


# ==============================================================================
# ----------------------------- 4. 主程序入口 --------------------------------
# ==============================================================================

def main():
    """主函数：负责启动和协调整个注册流程"""
    try:
        # --- 欢迎和基础配置 ---
        print("\n" + "="*70)
        print("            ▶▶▶ 网站注册/登录/签到/抽奖全自动工具 ◀◀◀")
        print("="*70 + "\n")

        proxy_manager = None  # 先初始化为None

        # --- 代理选择 ---
        use_proxy_choice = input("是否使用代理IP? (推荐使用，但如遇问题可尝试不使用)\n  [1] 使用代理IP (默认)\n  [2] 不使用代理，直接连接\n请选择 (1/2，默认1): ").strip() or "1"
        
        if use_proxy_choice == '1':
            proxy_provider_choice = input("\n请选择代理服务商:\n  [1] 51代理 (账号密码模式)\n  [2] 91代理 (IP白名单模式)\n请输入选择 (1 或 2，默认为1): ").strip() or "1"
            
            if proxy_provider_choice == '1':
                print("\n--- 正在初始化 51代理IP 管理器 ---")
                proxy_manager = ProxyManager(
                    packid=Proxy51Config.PACK_ID,
                    uid=Proxy51Config.UID,
                    access_name=Proxy51Config.ACCESS_NAME,
                    access_password=Proxy51Config.ACCESS_PASSWORD,
                    protocol=Proxy51Config.PROTOCOL,
                    time_duration=Proxy51Config.TIME_DURATION
                )
            elif proxy_provider_choice == '2':
                print("\n--- 正在初始化 91代理IP 管理器 ---")
                config = Proxy91Config() # 使用默认或从某处加载配置
                config.TRADE_NO = input(f"请输入91代理订单号 (trade_no) (默认为: {config.TRADE_NO}): ") or config.TRADE_NO
                config.SECRET = input(f"请输入91代理密钥 (secret) (直接回车使用默认配置): ") or config.SECRET
                proxy_manager = Proxy91Manager(config)
                print("✅ 91代理管理器已初始化。")
                print("--- 正在进行91代理服务预检查 ---")
                if proxy_manager.pre_check():
                     print("✅ 91代理服务预检查通过！")
                else:
                     print("❌ 91代理服务预检查失败，请检查配置或网络。程序将不使用代理继续...")
                     proxy_manager = None # 检查失败则退回至无代理状态
            else:
                print("无效的代理服务商选择，将不使用代理。")
        else:
            print("✅ 已选择不使用代理IP。")
            
        # --- 从这里开始是无论是否使用代理，都应该执行的通用流程 ---
        
        # --- 模式选择 ---
        print("\n" + "="*70)
        print("\n请选择操作模式:")
        print("  [1] 新账号注册 (默认)")
        print("  [2] 登录已有账号并签到抽奖")
        mode = input("请输入数字选择模式 (1 或 2，默认为1): ").strip() or '1'
        
        if mode == '1':
            # --- 注册流程 ---
            print("\n--- 正在准备注册流程 ---")
            
            # --- 恢复：邀请码配置 ---
            print("\n--- 邀请码配置 ---")
            invite_choice = input("是否使用多个邀请码? (1-是, 2-否, 默认2): ").strip() or "2"
            if invite_choice == '1':
                RegistrationConfig.USE_MULTIPLE_INVITE_CODES = True
                codes_input = input("请输入邀请码，用英文逗号 (,) 分隔: ").strip()
                RegistrationConfig.INVITE_CODES = [code.strip() for code in codes_input.split(',') if code.strip()]
                if not RegistrationConfig.INVITE_CODES:
                    print("未输入有效邀请码，将使用默认邀请码。")
                    RegistrationConfig.USE_MULTIPLE_INVITE_CODES = False
                else:
                    print(f"✅ 已配置多个邀请码: {RegistrationConfig.INVITE_CODES}")
            else:
                RegistrationConfig.USE_MULTIPLE_INVITE_CODES = False
                default_code = RegistrationConfig.INVITE_CODE
                user_code = input(f"请输入单个邀请码 (直接回车使用默认值: {default_code}): ").strip()
                if user_code:
                    RegistrationConfig.INVITE_CODE = user_code
                print(f"✅ 已配置单个邀请码: {RegistrationConfig.INVITE_CODE}")


            # --- 恢复：成功后延迟配置 ---
            print("\n--- 成功后延迟配置 ---")
            delay_choice = input("是否自定义注册成功后的等待时间? (1-是, 2-否, 默认2): ").strip() or "2"
            if delay_choice == '1':
                RegistrationConfig.USE_CUSTOM_DELAY = True
                try:
                    min_delay = int(input("请输入最小延迟时间 (秒): ").strip())
                    max_delay = int(input("请输入最大延迟时间 (秒): ").strip())
                    if min_delay > max_delay:
                        print("最小延迟不能大于最大延迟，将使用默认值。")
                        RegistrationConfig.USE_CUSTOM_DELAY = False
                    else:
                        RegistrationConfig.MIN_SUCCESS_DELAY_SECONDS = min_delay
                        RegistrationConfig.MAX_SUCCESS_DELAY_SECONDS = max_delay
                        print(f"✅ 已配置延迟范围: {min_delay} - {max_delay} 秒。")
                except ValueError:
                    print("输入无效，将使用默认延迟。")
                    RegistrationConfig.USE_CUSTOM_DELAY = False
            else:
                RegistrationConfig.USE_CUSTOM_DELAY = False
                print(f"✅ 将使用默认延迟范围: {RegistrationConfig.MIN_SUCCESS_DELAY_SECONDS} - {RegistrationConfig.MAX_SUCCESS_DELAY_SECONDS} 秒。")


            num_to_register = int(input("\n请输入本次要注册的账号数量: "))
            max_threads = int(input("请输入要开启的线程数 (推荐1-10): "))

            pwd_choice = input("\n所有账号是否使用相同密码? (1-是, 2-否, 默认为2): ").strip() or "2"
            password_for_all = ""
            if pwd_choice == "1":
                password_for_all = input("请输入通用密码: ").strip()

            print("\n" + "="*25 + " 选择接码平台 " + "="*25)
            print("1. 椰子云 (YeyeYun)")
            print("2. 好猪马 (Haozhuma)")
            print("3. 千川 (Qianchuan)")
            print("4. 通道1 (SMS-Channel1)")
            print("5. 通道2 (SMS-Channel2)")
            print("6. 通道6 (远通科技)")
            print("7. 手动模式 (手动输入手机号和验证码)")
            print("="*70)
        
            platform_choice = input("请选择要使用的接码平台 (输入数字 1-7): ").strip()

            # 定义手动平台客户端类（用于选项7）
            class ManualPlatformClient:
                """手动输入验证码的平台模拟类"""
                def __init__(self):
                    pass
                
                def login_and_initialize(self):
                    """模拟登录"""
                    print("手动模式无需登录")
                    return True
                
                def get_phone_number(self, specified_phone=None):
                    """手动输入手机号"""
                    if specified_phone:
                        return True, (specified_phone, "手动指定")
                    
                    print("\n--- 手动输入手机号 ---")
                    phone = input("请输入要使用的手机号: ").strip()
                    if phone and len(phone) == 11:
                        return True, (phone, "手动输入")
                    else:
                        return False, "手机号输入无效"
                
                def get_sms_code(self, phone_number, project_id=None, timeout=None):
                    """手动输入验证码"""
                    print(f"\n--- 等待短信验证码 (手机号: {phone_number}) ---")
                    print("请在手机上查收短信验证码，然后在下方输入")
                    code = input("请输入收到的验证码: ").strip()
                    if code:
                        return code
                    else:
                        return None
                
                def add_to_blacklist(self, phone_number):
                    """模拟拉黑功能"""
                    print(f"手动模式: 已将手机号 {phone_number} 标记为已使用")
                    return True
                
                def release_phone_number(self, phone_number):
                    """模拟释放功能"""
                    print(f"手动模式: 已释放手机号 {phone_number}")
                    return True
            
            platform_client = None
            if platform_choice == '1':
                print("\n--- 椰子云平台配置 ---")
                # 交互式输入账号和密码
                default_username = getattr(YeyeYunConfig, 'USERNAME', '')
                username_prompt = "请输入椰子云登录账号: "
                if default_username:
                    username_prompt = f"请输入椰子云登录账号 (直接回车使用: {default_username}): "
                
                username_input = input(username_prompt).strip()
                final_username = username_input or default_username

                password_prompt = "请输入椰子云登录密码 (如已配置可直接回车): "
                password_input = input(password_prompt).strip()
                default_password = getattr(YeyeYunConfig, 'PASSWORD', '')
                final_password = password_input or default_password

                if not final_username or not final_password:
                    print("\n❌ 错误: 必须提供有效的椰子云账号和密码。")
                    return

                # 动态更新配置
                YeyeYunConfig.USERNAME = final_username
                YeyeYunConfig.PASSWORD = final_password

                # 初始化客户端并执行登录
                try:
                    platform_client = YeyeYunClient(YeyeYunConfig)
                    if not platform_client.login():
                        print("❌ 椰子云登录失败，请检查账号密码或网络。程序退出。"); return
                except Exception as e:
                    print(f"\n❌ 初始化或登录椰子云客户端失败: {e}"); return

                # --- 2. 选择项目 (修复：重新插入此关键逻辑) ---
                print("\n--- 椰子云项目选择 ---")
                exclusive_projects = platform_client.get_exclusive_projects()
                project_id_input = None

                if exclusive_projects:
                    print("检测到以下专属项目:")
                    for i, proj in enumerate(exclusive_projects):
                        # 修正：正确提取项目名和关键的"专属对接码" (key_)
                        proj_name = proj.get('name', '未知项目名')
                        proj_key = proj.get('key_', '未知对接码')
                        print(f"  [{i+1}] {proj_name} (对接码: {proj_key})")
                    
                    print("\n  [D] 使用配置文件中的默认项目ID/对接码")
                    print("  [M] 手动输入其他项目ID或对接码")
                    choice = input("请选择一个项目 (输入序号, D, 或 M): ").strip().upper()

                    if choice.isdigit() and 1 <= int(choice) <= len(exclusive_projects):
                        proj = exclusive_projects[int(choice) - 1]
                        # 修正：使用 'key_' 作为 project_id
                        project_id_input = proj.get('key_')
                    elif choice == 'M':
                        project_id_input = input("请输入项目ID或专属对接码: ").strip()
                    else: # 默认选择 'D'
                        project_id_input = YeyeYunConfig.PROJECT_ID
                        print(f"已选择使用默认项目ID/对接码: {project_id_input}")
                else:
                    print("未检测到专属项目。")
                    use_default = input(f"是否使用配置文件中的默认项目ID ({YeyeYunConfig.PROJECT_ID})? (Y/N): ").strip().upper()
                    if use_default == 'Y':
                        project_id_input = YeyeYunConfig.PROJECT_ID
                    else:
                        project_id_input = input("请输入您的项目ID或专属对接码: ").strip()
                
                if not project_id_input:
                    print("❌ 未选择有效的项目ID或对接码，程序退出。"); return

                # --- 3. 动态应用配置 ---
                YeyeYunConfig.PROJECT_ID = project_id_input
                print(f"✅ 已选择并配置【椰子云】平台，使用项目ID/对接码: {YeyeYunConfig.PROJECT_ID}")

                # 新增：让用户选择手机号类型
                print("\n请为椰子云平台选择要获取的手机号类型:")
                print("  [0] 全部 (默认)")
                print("  [1] 实卡")
                print("  [2] 虚卡")
                card_type_choice = input("请输入选择 (0, 1, 或 2，默认为0): ").strip() or "0"
                if card_type_choice in ['0', '1', '2']:
                    # 这个值将直接传递给客户端的 get_phone_number 方法
                    YeyeYunConfig.CARD_TYPE = int(card_type_choice)
                    type_map = {'0': '全部', '1': '实卡', '2': '虚卡'}
                    print(f"✅ 已选择类型: {type_map[card_type_choice]}")
                else:
                    YeyeYunConfig.CARD_TYPE = 0
                    print('无效输入，将使用默认类型"0-全部"。')

            elif platform_choice == '2':
                print("\n--- 好猪马平台配置 ---")
                # 交互式输入API Token
                default_api_token = getattr(HaozhumaConfig, 'API_TOKEN', '')
                prompt = "请输入好猪马 API Token: "
                if default_api_token and default_api_token != "your_api_token":
                    prompt = f"请输入好猪马 API Token (直接回车使用默认值: ...{default_api_token[-4:]}): "
                
                api_token_input = input(prompt).strip()
                final_api_token = api_token_input or default_api_token

                if not final_api_token or final_api_token == "your_api_token":
                    print("\n❌ 错误: 未提供有效的好猪马 API Token。")
                    return
                
                # 动态更新配置
                HaozhumaConfig.API_TOKEN = final_api_token
                platform_client = HaozhumaClient(HaozhumaConfig)
                print("✅ 已选择并配置【好猪马】平台。")

            elif platform_choice == '3':
                print("\n--- 千川平台配置 ---")
                QianchuanConfig.ENABLED = True

                # 交互式输入用户名和密码
                default_username = getattr(QianchuanConfig, 'USERNAME', '')
                username_prompt = "请输入千川用户名: "
                if default_username and default_username != "your_username":
                    username_prompt = f"请输入千川用户名 (直接回车使用默认值: {default_username}): "
                
                username_input = input(username_prompt).strip()
                final_username = username_input or default_username

                password_prompt = "请输入千川密码 (如已配置可直接回车): "
                password_input = input(password_prompt).strip()
                default_password = getattr(QianchuanConfig, 'PASSWORD', '')
                final_password = password_input or default_password

                if not final_username or final_username == "your_username" or not final_password:
                    print("\n❌ 错误: 必须提供有效的千川用户名和密码。")
                    return

                # 动态更新配置
                QianchuanConfig.USERNAME = final_username
                QianchuanConfig.PASSWORD = final_password
                
                # 新增：让用户选择手机号类型
                print("\n请为千川平台选择要获取的手机号类型:")
                print("  [0] 全部 (默认)")
                print("  [4] 非虚拟卡")
                print("  [5] 虚拟卡")
                op_choice = input("请输入选择 (0, 4, 或 5，默认为0): ").strip() or "0"
                if op_choice in ['0', '4', '5']:
                    QianchuanConfig.OPERATOR = int(op_choice)
                    print(f"✅ 已选择类型: {op_choice}")
                else:
                    QianchuanConfig.OPERATOR = 0
                    print('无效输入，将使用默认类型"0-全部"。')

                try:
                    platform_client = QianchuanClient(QianchuanConfig)
                    print("✅ 已选择并配置【千川】平台。")
                except Exception as e:
                    print(f"\n错误: 初始化千川客户端失败: {e}")
                    print("请检查您的网络连接以及在 qianchuan.py 中的配置。")
                    return
            elif platform_choice == '4':
                print("\n--- 通道1平台配置 ---")
                # 交互式输入账号和密码
                default_username = getattr(SmsChannel1Config, 'USERNAME', '')
                username_prompt = "请输入通道1登录账号: "
                if default_username and default_username != "your_username":
                    username_prompt = f"请输入通道1登录账号 (直接回车使用默认值: {default_username}): "
                
                username_input = input(username_prompt).strip()
                final_username = username_input or default_username

                password_prompt = "请输入通道1登录密码 (如已配置可直接回车): "
                password_input = input(password_prompt).strip()
                default_password = getattr(SmsChannel1Config, 'PASSWORD', '')
                final_password = password_input or default_password

                if not final_username or final_username == "your_username" or not final_password:
                    print("\n❌ 错误: 必须提供有效的通道1账号和密码。")
                    return
                
                # 交互式输入项目ID
                project_id_prompt = "请输入通道1项目ID: "
                default_project_id = getattr(SmsChannel1Config, 'PROJECT_ID', '')
                if default_project_id and default_project_id != "your_project_id":
                    project_id_prompt = f"请输入通道1项目ID (直接回车使用默认值: {default_project_id}): "
                
                project_id_input = input(project_id_prompt).strip()
                final_project_id = project_id_input or default_project_id

                # 动态更新配置
                SmsChannel1Config.ENABLED = True
                SmsChannel1Config.USERNAME = final_username
                SmsChannel1Config.PASSWORD = final_password
                SmsChannel1Config.PROJECT_ID = final_project_id

                try:
                    platform_client = SmsChannel1Client(SmsChannel1Config)
                    if not platform_client.login():
                        print("❌ 通道1登录失败，请检查账号密码或网络。程序退出。"); return
                    print("✅ 已选择并配置【通道1】平台。")
                except Exception as e:
                    print(f"\n❌ 初始化或登录通道1平台失败: {e}"); return
            elif platform_choice == '6':
                print("\n--- 通道6 (远通科技) 平台配置 ---")
                # 交互式输入账号和密码
                default_username = getattr(SmsChannel6Config, 'USERNAME', '')
                username_prompt = "请输入通道6登录账号: "
                if default_username and default_username != "your_username":
                    username_prompt = f"请输入通道6登录账号 (直接回车使用默认值: {default_username}): "
                
                username_input = input(username_prompt).strip()
                final_username = username_input or default_username

                password_prompt = "请输入通道6登录密码 (如已配置可直接回车): "
                password_input = input(password_prompt).strip()
                default_password = getattr(SmsChannel6Config, 'PASSWORD', '')
                final_password = password_input or default_password

                if not final_username or final_username == "your_username" or not final_password:
                    print("\n❌ 错误: 必须提供有效的通道6账号和密码。")
                    return
                
                # 交互式输入服务器信息
                server_prompt = f"请输入通道6服务器地址 (直接回车使用默认值: {SmsChannel6Config.SERVER}): "
                server_input = input(server_prompt).strip()
                if server_input:
                    SmsChannel6Config.SERVER = server_input
                
                port_prompt = f"请输入通道6服务器端口 (直接回车使用默认值: {SmsChannel6Config.PORT}): "
                port_input = input(port_prompt).strip()
                if port_input:
                    SmsChannel6Config.PORT = port_input

                # 项目ID已经在配置中设置为64457
                print(f"✅ 项目ID已配置为: {SmsChannel6Config.PROJECT_ID} (远通科技)")
                
                # 询问是否要配置运营商和卡类型
                if input("是否需要配置号码类型和运营商? (y/n, 默认n): ").strip().lower() == 'y':
                    operator = input("请输入运营商 (0=不限, 1=移动, 2=联通, 3=电信, 默认0): ").strip() or '0'
                    SmsChannel6Config.OPERATOR = int(operator)
                    
                    card_type = input("请输入卡类型 (0=不限, 1=虚拟号段, 2=正常号段, 默认0): ").strip() or '0'
                    SmsChannel6Config.CARD_TYPE = int(card_type)
                    
                    area = input("请输入区域(省份名称，如广东，不填则不限): ").strip()
                    if area:
                        SmsChannel6Config.AREA = area

                # 动态更新配置
                SmsChannel6Config.ENABLED = True
                SmsChannel6Config.USERNAME = final_username
                SmsChannel6Config.PASSWORD = final_password

                try:
                    platform_client = SmsChannel6(SmsChannel6Config)
                    if not platform_client.login():
                        print("❌ 通道6登录失败，请检查账号密码或网络。程序退出。"); return
                    print("✅ 已选择并配置【通道6 (远通科技)】平台。")
                except Exception as e:
                    print(f"\n❌ 初始化或登录通道6平台失败: {e}"); return
            elif platform_choice == '7':
                platform_client = ManualPlatformClient()
                print("已选择【手动模式】。")
            else:
                print("无效的输入，程序退出。")
                return
            
            if not platform_client:
                print("平台客户端初始化失败，程序退出。")
                return

            user_list = read_user_info(INFO_FILE_PATH)
            if not user_list:
                print(f"错误: 无法从 {INFO_FILE_PATH} 读取用户信息。")
                return
            
            user_info_queue = queue.Queue()
            for user in user_list:
                user_info_queue.put(user)

            successful_registrations = ThreadSafeCounter()
            
            threads = []
            print("\n" + "="*25 + " 注册流程开始 " + "="*25)
            for i in range(max_threads):
                thread = threading.Thread(
                    target=generic_registration_worker,
                    args=(platform_client, proxy_manager, user_info_queue, pwd_choice, password_for_all, successful_registrations, num_to_register),
                    name=f"注册线程-{i+1}"
                )
                threads.append(thread)
                thread.start()
                time.sleep(0.5)

            for thread in threads:
                thread.join()

            print("\n" + "="*25 + " 注册流程结束 " + "="*25)
            print(f"总共成功注册了 {successful_registrations.value} 个账号。")
            
        elif mode == '2':
            # --- 手动登录流程 ---
            print("\n--- 正在准备登录流程 ---")
            website = WebsiteClient(RegistrationConfig.BASE_URL, proxy_manager)
            
            phone = input("请输入手机号: ").strip()
            password = input("请输入密码: ").strip()
            
            if not phone or not password:
                sys.exit("手机号和密码不能为空!")

            if website.login(phone, password):
                print(f"✅ 账号 {phone} 登录成功。")
                website.sign_in()
                website.draw_lottery()
            else:
                print(f"⚠️  账号 {phone} 登录失败。")
        else:
            print("无效输入，程序退出。")
            sys.exit()

        print("\n--- 所有任务执行完毕 ---")

    except KeyboardInterrupt:
        print("\n程序已被用户中断。")
    except Exception as e:
        logger.error(f"主程序发生严重错误: {e}", exc_info=True)
        print(f"发生未知错误，请查看日志: {e}")

if __name__ == '__main__':
    main()