#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 密码生成修复测试脚本

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_password_generation():
    """测试密码生成功能"""
    print("🧪 密码生成功能测试")
    print("=" * 40)
    
    try:
        from zhuccc1 import generate_random_password
        
        print("📋 生成10个测试密码:")
        
        all_valid = True
        for i in range(10):
            password = generate_random_password()
            length = len(password)
            
            # 检查长度
            length_valid = 6 <= length <= 11
            
            # 检查是否包含字母和数字
            has_letter = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            
            # 检查是否只包含小写字母和数字
            valid_chars = all(c.islower() or c.isdigit() for c in password)
            
            is_valid = length_valid and has_letter and has_digit and valid_chars
            
            status = "✅" if is_valid else "❌"
            print(f"   {i+1:2d}. {password} (长度: {length}) {status}")
            
            if not is_valid:
                all_valid = False
                if not length_valid:
                    print(f"       ❌ 长度不符合要求 (需要6-11位)")
                if not has_letter:
                    print(f"       ❌ 缺少字母")
                if not has_digit:
                    print(f"       ❌ 缺少数字")
                if not valid_chars:
                    print(f"       ❌ 包含无效字符")
        
        print(f"\n📊 测试结果:")
        print(f"   要求: 6-11位小写字母和数字混合")
        print(f"   结果: {'✅ 全部通过' if all_valid else '❌ 存在问题'}")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_requirements():
    """测试服务器要求"""
    print("\n🧪 服务器要求验证")
    print("=" * 40)
    
    print("📋 服务器错误信息: '登录密码限制6-11位字符'")
    print("📋 我们的密码生成规则:")
    print("   - 长度: 6-11位")
    print("   - 字符: 小写字母 + 数字")
    print("   - 保证: 至少1个字母 + 至少1个数字")
    
    # 测试边界情况
    try:
        from zhuccc1 import generate_random_password
        
        print("\n🔍 边界测试:")
        
        # 生成多个密码检查长度分布
        lengths = []
        for _ in range(100):
            password = generate_random_password()
            lengths.append(len(password))
        
        min_len = min(lengths)
        max_len = max(lengths)
        
        print(f"   生成100个密码的长度范围: {min_len}-{max_len}")
        
        if min_len >= 6 and max_len <= 11:
            print("   ✅ 长度范围符合服务器要求")
            return True
        else:
            print("   ❌ 长度范围不符合服务器要求")
            return False
            
    except Exception as e:
        print(f"❌ 边界测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 密码生成修复验证")
    print("🔧 验证密码生成是否符合服务器要求")
    print("=" * 50)
    
    tests = [
        ("密码生成功能", test_password_generation),
        ("服务器要求验证", test_server_requirements)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        result = test_func()
        results.append(result)
        
        if result:
            print(f"✅ {test_name}通过")
        else:
            print(f"❌ {test_name}失败")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过! 密码生成已修复!")
        print("💡 现在密码符合服务器要求:")
        print("   - 长度: 6-11位字符")
        print("   - 格式: 小写字母 + 数字混合")
        print("   - 保证: 至少包含1个字母和1个数字")
    else:
        print("\n⚠️ 部分测试失败，需要进一步修复")
    
    return passed_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
