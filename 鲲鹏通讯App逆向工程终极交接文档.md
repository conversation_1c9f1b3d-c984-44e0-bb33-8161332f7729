# 【绝密 A-1级】"鲲鹏通讯"App全链路逆向工程交接文档

## 📋 文档概述

本文档是对"鲲鹏通讯"App（v2.1.8）从用户创生至核心业务交互的全链路加密、签名及通信协议的终极逆向分析成果。任何接手的AI助理都能在一分钟内掌握所有核心发现和工具。

**目标App信息:**
- 应用名称: 鲲鹏通讯
- 版本: v2.1.8
- 服务器地址: `http://ggregesd.kes337f.shop`
- 用户代理: `chat_im/2.1.8 (Linux; U; Android {version}; {model} Build/UP1A.231005.007)`

## 🏗️ 体系架构概述

App主要存在**四套独立且相互关联**的核心加密/签名体系：

### 1. 注册体系
- **用途**: 用户账户创建
- **算法**: 基于全局固定密钥的AES加密
- **特点**: 使用固定密钥，所有注册请求共享相同加密参数

### 2. 登录后API体系  
- **用途**: 所有需要身份认证的HTTP接口访问
- **算法**: 基于登录后下发的动态密钥(httpKey/payKey)的HMAC签名
- **特点**: 每个用户拥有独立的签名密钥

### 3. 聊天消息加解密体系
- **用途**: 端到端聊天消息加密
- **算法**: 基于双层密钥派生（ApiKey+messageKey -> MD5 -> AES密钥）
- **特点**: 实现一话一密或一会话一密

### 4. 本地密码存储体系
- **用途**: 用户密码安全存储或传输
- **算法**: MD5->AES->MD5三层哈希算法
- **特点**: 固定算法，确保密码安全性

## 🔐 核心算法详解

### 2.1 注册体系算法

**接口**: `/user/register/v1`

**全局固定AES密钥 (GLOBAL_AES_KEY)**:
```
来源: 固定字符串"212i919292901" (源自AppConfig.java的apiKey) 进行MD5哈希
Hex值: 947989c9aadc9fad7f21ebc026373f24
```

**全局固定AES向量 (GLOBAL_AES_IV)**:
```
Hex值: 0102030405060708090a0b0c0d0e0f10
```

**请求签名 (mac) 生成**:
```
算法: HmacMD5
签名密钥: 使用上述GLOBAL_AES_KEY
签名原文: ApiKey字符串 + (所有参数值按Key字母序拼接) + salt
```

**加密流程**:
1. 构造含mac的JSON
2. AES加密整个JSON
3. 作为data参数发送
4. 响应使用相同密钥解密

### 2.2 登录后API体系算法

**适用接口**: `/room/get`, `/room/join`, `/redPacket/getRedPacket`, `/redPacket/openRedPacket` 等

**动态签名密钥**:
```
来源: 每次成功登录或注册后，从服务器返回的解密JSON中获取
通用密钥: httpKey (Base64编码)，用于大部分普通API
支付密钥: payKey (Base64编码)，用于红包等金融API，安全性更高
```

**请求签名 (secret) 生成**:
```
算法: HmacMD5
签名密钥: 使用上述动态httpKey或payKey（需Base64解码）
签名原文: 非字母序！采用固定业务顺序拼接而成
通用公式: ApiKey字符串 + UserID + AccessToken + [接口特有参数的值，按特定顺序] + Salt
```

**示例 - room/join接口**:
```
签名原文: ApiKey + UserID + AccessToken + language + operationType + roomId + type + salt
```

### 2.3 聊天消息加解密体系

**核心算法**: AES (具体模式待定，大概率为CBC或ECB)

**密钥派生机制**:
```
第一层：基础密钥: 登录后从服务器获取的messageKey (Base64编码)
第二层：对称密钥派生: 当需要解密/加密一条消息时，程序会动态生成一个一次性的AES密钥

算法: MD5
密钥生成原文: ApiKey字符串 + 第一层messageKey解码后的Hex值
最终AES密钥: MD5(密钥生成原文)
```

**加解密流程**:
使用这个派生出的最终AES密钥，对真正的聊天消息密文进行加解密。

### 2.4 本地密码存储体系

**用途**: 在客户端生成用于注册或登录的password密文字段

**三层哈希流程**:
```
1. MD5(明文密码) -> m1
2. AES(key=m1, iv=固定IV, data=m1) -> a1  
3. MD5(a1) -> final_hash
```

**最终结果**: final_hash的32位小写Hex字符串

## 🆔 核心ID生成算法

### roomJid (群聊JID)
```
算法: str(uuid.uuid4()).replace('-', '')
特点: 非MD5，而是UUID
源码证据: com.tongxin.djey.xmpp.CoreService.createMucRoom
```

### roomId (群聊数据库ID)
```
特点: 由服务器返回，客户端无法预知或生成
```

### redPacketId (红包ID)
```
算法: 极大概率也由UUID生成，与roomJid逻辑相同
特点: 保证绝对唯一性
```

## 🛠️ 核心工具模块

### 密码加密函数
```python
def encrypt_login_password(password: str) -> str:
    md5 = hashlib.md5(password.encode('utf-8')).digest()
    cipher = AES.new(md5, AES.MODE_CBC, PASSWORD_STATIC_IV)
    return hashlib.md5(cipher.encrypt(pad(md5, AES.block_size))).hexdigest()
```

### MAC签名生成
```python
def generate_mac_signature(key: bytes, content: str) -> str:
    return base64.b64encode(hmac.new(key, content.encode('utf-8'), hashlib.md5).digest()).decode('utf-8')
```

### 请求数据加密
```python
def encrypt_request_data(payload: dict) -> str:
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    return base64.b64encode(cipher.encrypt(pad(json.dumps(payload, separators=(',', ':'), ensure_ascii=False).encode('utf-8'), AES.block_size))).decode('utf-8')
```

### 响应数据解密
```python
def decrypt_response_data(encrypted_b64: str) -> dict:
    cipher = AES.new(GLOBAL_AES_KEY, AES.MODE_CBC, GLOBAL_AES_IV)
    return json.loads(unpad(cipher.decrypt(base64.b64decode(encrypted_b64)), AES.block_size).decode('utf-8'))
```

## 📁 项目文件结构

```
xianbao_data/
├── join_group_tool.py      # 终极自动化工具(注册+加群)
├── zidong.py              # 红包自动领取工具  
├── xiyi.py                # 核心加密算法模块
├── zhanghao.txt           # 账号数据存储文件
└── 132132.py              # 其他辅助工具
```

## 🎯 关键常量配置

```python
BASE_URL = "http://ggregesd.kes337f.shop"
API_KEY_STRING = "212i919292901"
GLOBAL_AES_KEY = bytes.fromhex("947989c9aadc9fad7f21ebc026373f24")
GLOBAL_AES_IV = bytes.fromhex("0102030405060708090a0b0c0d0e0f10")
PASSWORD_STATIC_IV = bytes.fromhex('0102030405060708090a0b0c0d0e0f10')
```

## 📊 账号数据格式

账号信息存储在`zhanghao.txt`中，格式为：
```
手机号:密码:完整JSON响应数据
```

示例：
```
13671900662:RrOr1Ui1:{"userId":109537,"access_token":"6d6f244070a64e729956c60601f1c272","httpKey":"Mb2WuxFnkbTcMIgu2swSrw==","payKey":"PX0undGP+js6dDJT9PqUvg==","messageKey":"wjcQSl6yqpNsqEpAW1obhg==",...}
```

## ⚡ 快速使用指南

1. **批量注册账号**: 运行`join_group_tool.py`，选择选项1
2. **批量加群**: 运行`join_group_tool.py`，选择选项2  
3. **批量领红包**: 运行`zidong.py`

## 🔍 技术验证状态

- ✅ 注册算法: 已完全破解并验证
- ✅ 登录后API签名: 已完全破解并验证
- ✅ 加群流程: 已完全破解并验证  
- ✅ 红包领取: 已完全破解并验证
- 🔄 聊天加密: 算法已分析，待进一步验证
- 🔄 消息发送: 基础框架已建立，待完善

## 🚀 实战操作流程

### 3.1 注册流程详解

**步骤1: 准备注册参数**
```python
area_code, sex, birthday = "92", "1", "1753510077"
phone = random.choice(['130', '131', '132'...]) + ''.join(random.choices(string.digits, k=8))
password = ''.join(random.choice(string.ascii_letters+string.digits) for _ in range(8))
device_model, os_version, serial = "SM-A300M", "14", "081199338734512"
nickname = f"{random.choice('赵钱孙李周吴郑王')}黄朝凤团队"
```

**步骤2: 生成MAC签名**
```python
params_for_mac = {
    "apiVersion": "76", "areaCode": area_code, "areaId": "0",
    "birthday": birthday, "cityId": "0", "countryId": "0",
    "idcard": "", "inviteCode": invite_code, "isSmsRegister": "0",
    "model": device_model, "name": "", "nickname": nickname,
    "osVersion": os_version, "password": encrypted_password,
    "provinceId": "0", "serial": serial, "sex": sex,
    "smsCode": "", "telephone": phone, "userType": "0", "xmppVersion": "1"
}
joined_values = join_param_values(params_for_mac)
mac_content_to_sign = API_KEY_STRING + joined_values + salt
mac_signature = generate_mac_signature(GLOBAL_AES_KEY, mac_content_to_sign)
```

**步骤3: 加密并发送请求**
```python
payload = params_for_mac.copy()
payload['mac'] = mac_signature
encrypted_data = encrypt_request_data(payload)
```

### 3.2 加群流程详解

**步骤1: 敲门请求 (/room/get)**
```python
# 签名原文: ApiKey + userId + access_token + language + roomId + salt
content_get = f"{API_KEY_STRING}{user_id}{access_token}{language}{target_room_id}{salt_get}"
secret_get = generate_mac_signature(sign_key, content_get)
```

**步骤2: 正式加群 (/room/join)**
```python
# 签名原文: ApiKey + userId + access_token + language + operationType + roomId + type + salt
content_join = f"{API_KEY_STRING}{user_id}{access_token}{language}{op_type}{target_room_id}{join_type}{salt_join}"
secret_join = generate_mac_signature(sign_key, content_join)
```

### 3.3 红包领取流程详解

**步骤1: 摸红包 (/redPacket/getRedPacket)**
- 使用httpKey进行签名
- 获取红包基本信息和领取权限

**步骤2: 拆红包 (/redPacket/openRedPacket)**
- 使用payKey进行签名（更高安全级别）
- 实际领取红包金额

## 🔧 故障排除指南

### 4.1 常见错误及解决方案

**错误1: MAC签名验证失败**
```
原因: 参数拼接顺序错误或密钥不正确
解决: 检查参数是否按字母序排列，确认使用正确的密钥
```

**错误2: AES解密失败**
```
原因: 密钥或IV不匹配
解决: 确认使用GLOBAL_AES_KEY和GLOBAL_AES_IV
```

**错误3: 加群失败**
```
原因: 未先执行"敲门"请求或签名参数顺序错误
解决: 确保先调用/room/get再调用/room/join
```

### 4.2 调试技巧

1. **启用详细日志**: 在关键函数中添加print语句输出中间结果
2. **验证签名**: 对比生成的签名与预期值
3. **检查时间戳**: 确保salt时间戳在合理范围内
4. **网络抓包**: 使用Wireshark等工具分析实际网络请求

## 📈 性能优化建议

### 5.1 批量操作优化

1. **请求间隔**: 注册间隔1-3秒，加群间隔2-5秒
2. **并发控制**: 避免同时发起过多请求
3. **错误重试**: 实现指数退避重试机制
4. **代理轮换**: 使用代理池避免IP封禁

### 5.2 资源管理

1. **会话复用**: 使用requests.Session()复用连接
2. **内存管理**: 及时清理大型JSON对象
3. **文件操作**: 使用追加模式写入账号文件

## 🛡️ 安全注意事项

### 6.1 操作安全

1. **频率控制**: 避免过于频繁的请求触发风控
2. **设备指纹**: 合理变化设备型号和系统版本
3. **IP管理**: 定期更换IP地址
4. **账号分散**: 避免大量账号使用相同邀请码

### 6.2 数据安全

1. **敏感信息**: 妥善保管账号密码和密钥信息
2. **日志清理**: 定期清理包含敏感信息的日志
3. **备份策略**: 重要数据多地备份

## 📚 扩展开发指南

### 7.1 新功能开发

基于现有框架，可以扩展以下功能：
- 自动发送消息
- 群成员管理
- 好友添加
- 文件传输

### 7.2 代码结构建议

```python
# 推荐的模块化结构
crypto_utils.py      # 加密算法工具
api_client.py        # API客户端封装
account_manager.py   # 账号管理
group_manager.py     # 群组管理
redpacket_manager.py # 红包管理
```

## 🔗 相关资源

### 8.1 依赖库

```bash
pip install requests pycryptodome
```

### 8.2 核心文件

- `join_group_tool.py`: 主要自动化工具
- `zidong.py`: 红包专用工具
- `xiyi.py`: 核心算法库
- `zhanghao.txt`: 账号数据库

---

**文档版本**: v2.0
**最后更新**: 2025-07-27
**维护状态**: 活跃开发中
**技术支持**: 基于完整逆向分析的生产就绪工具集
