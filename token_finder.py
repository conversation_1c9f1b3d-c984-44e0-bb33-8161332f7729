#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token查找器
尝试不同的Token值来找到正确的认证方式
"""

import requests
import urllib3
import json
from dynamic_cookie_getter import DynamicCookieGetter
from decrypt_analysis import EncryptionSystem

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TokenFinder:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.crypto = EncryptionSystem()
        self.cookie_getter = DynamicCookieGetter()
        
        # 获取动态cookie
        self.cookies = self.cookie_getter.run_dynamic_cookie_test()
        
    def format_cookie_header(self, cookies):
        """格式化cookie为请求头格式"""
        if not cookies:
            return ""
        cookie_pairs = []
        for name, value in cookies.items():
            cookie_pairs.append(f"{name}={value}")
        return "; ".join(cookie_pairs)

    def test_token_value(self, token_value, description):
        """测试特定的Token值"""
        print(f"\n🧪 测试Token: {description}")
        print(f"Token值: {token_value}")
        print("-" * 40)
        
        try:
            # 准备测试数据
            test_data = {}
            encrypted = self.crypto.encrypt_data(test_data)
            sign = self.crypto.generate_sign(test_data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return False
            
            # 构造请求头
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': self.format_cookie_header(self.cookies),
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'priority': 'u=1, i',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'token': token_value,
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/dev-api/api/login/authccode.html",
                headers=headers,
                json=request_body,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)}")
            print(f"响应前100字符: {response.text[:100]}...")
            
            # 判断是否成功
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("❌ 返回HTML页面，Token可能无效")
                return False
            elif response.status_code == 200:
                print("✅ 返回非HTML响应，Token可能有效")
                
                # 尝试解密响应
                try:
                    decrypted = self.crypto.decrypt_data(response.text)
                    if decrypted:
                        print(f"✅ 响应解密成功: {decrypted}")
                    else:
                        print("⚠️ 响应解密失败，可能是明文")
                        print(f"完整响应: {response.text}")
                except Exception as e:
                    print(f"⚠️ 解密异常: {e}")
                    print(f"完整响应: {response.text}")
                
                return True
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

    def test_different_tokens(self):
        """测试不同的Token值"""
        print("🔍 Token查找测试")
        print("=" * 50)
        
        # 测试不同的Token值
        token_tests = [
            ("transfersecret", "原始默认值"),
            ("", "空Token"),
            ("null", "null值"),
            ("undefined", "undefined值"),
            ("guest", "访客Token"),
            ("anonymous", "匿名Token"),
            ("public", "公共Token"),
            ("default", "默认Token"),
            ("register", "注册Token"),
            ("login", "登录Token"),
        ]
        
        successful_tokens = []
        
        for token_value, description in token_tests:
            success = self.test_token_value(token_value, description)
            if success:
                successful_tokens.append((token_value, description))
        
        # 总结结果
        print("\n" + "=" * 50)
        print("🎯 测试总结")
        print("-" * 20)
        
        if successful_tokens:
            print("✅ 找到有效的Token:")
            for token, desc in successful_tokens:
                print(f"  - {desc}: '{token}'")
        else:
            print("❌ 未找到有效的Token")
            print("可能需要:")
            print("1. 先获取有效的用户Token")
            print("2. 或者注册接口有不同的认证方式")
            print("3. 或者需要其他的请求头参数")
        
        return successful_tokens

    def test_no_token_request(self):
        """测试不带Token的请求"""
        print("\n🧪 测试不带Token的请求")
        print("-" * 40)
        
        try:
            # 准备测试数据
            test_data = {}
            encrypted = self.crypto.encrypt_data(test_data)
            sign = self.crypto.generate_sign(test_data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return False
            
            # 构造请求头 (不包含token)
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': self.format_cookie_header(self.cookies),
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'priority': 'u=1, i',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/dev-api/api/login/authccode.html",
                headers=headers,
                json=request_body,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)}")
            print(f"响应前100字符: {response.text[:100]}...")
            
            if not response.text.strip().startswith('<!DOCTYPE html>'):
                print("✅ 不需要Token！")
                return True
            else:
                print("❌ 仍然需要Token")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False


if __name__ == "__main__":
    finder = TokenFinder()
    
    # 测试不同的Token值
    successful_tokens = finder.test_different_tokens()
    
    # 测试不带Token的请求
    no_token_success = finder.test_no_token_request()
    
    print(f"\n🎉 最终结果:")
    if successful_tokens:
        print(f"找到 {len(successful_tokens)} 个有效Token")
    elif no_token_success:
        print("不需要Token即可访问API")
    else:
        print("需要进一步分析Token获取方式")
