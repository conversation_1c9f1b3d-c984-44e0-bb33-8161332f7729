import requests
import base64
import os
import json
import warnings
import urllib3
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import rsa
import ddddocr
import random
import string
import time
import sys
import os
import datetime
import threading  # 恢复多线程支持
import queue     # 恢复队列支持

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore", category=urllib3.exceptions.InsecureRequestWarning)

# 动态添加当前脚本所在目录到sys.path
dir_path = os.path.dirname(os.path.abspath(__file__))
if dir_path not in sys.path:
    sys.path.append(dir_path)

from proxy_manager import ProxyManager # 导入ProxyManager (改为绝对导入)

# 定义50种常见的中国手机设备User-Agent
CHINESE_MOBILE_USER_AGENTS = [
    "Mozilla/5.0 (Linux; Android 10; SM-G9810) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Mobile Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 11; Redmi Note 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.16 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.87 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; Huawei P30) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Oppo Reno 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Vivo X60) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; OnePlus 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.65 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Xiaomi 11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Meizu 17) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; honor V30) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.87 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; Redmi K20 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Nova 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.88 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Realme X50 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; Zenfone 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Legion Phone Duel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; POCO F3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Black Shark 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; Nubia Red Magic 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; iQOO 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Redmi K40) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Realme GT) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; OnePlus 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Xiaomi 12) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.58 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Vivo S10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.79 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Huawei P40 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; Samsung SM-N976B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; M2011K2C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; VOG-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; PCT-AL10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; ELE-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; ONEPLUS A6000) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; MI 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.87 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; CPH2207) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.58 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; V2023A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.79 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; IN2010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Redmi K30 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.100 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; M2007J17C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; RMX2001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; BKL-AL20) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; MAR-LX1M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Pixel 4 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; GM1910) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 9; GM1917) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.87 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; PD2002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.58 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 10; SM-A705F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.79 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; RMX3031) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Mobile Safari/537.36"
]

def get_random_user_agent():
    return random.choice(CHINESE_MOBILE_USER_AGENTS)

# 公钥d，从您提供的JavaScript代码中获取
# 请确保这是完整的公钥字符串
PUBLIC_KEY_D_BASE64 = """MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv9PdHGOpTtDJWj6W9AFpXXPehVgFCCNwtEDru/WarfCCRaAIaMoUq2/RBMci+ULyf+2MKjqx70IqDonW9lJdlOgYBExI/0otIMwmCqW+yV3F4MLgRRN8YEjffGZTQIJoEgYwvYBjIRGQSC4ce98q/50nX+7Ne9QbuxO2FtBjUwrFMxdtcsrgo70hxWbtDgD678aDJRgonbldm1RqCwUb1ye8FSFlk5AXU2BETx2vT5eXyqGg8dwuAOw09Y0yjrWjd18Z+gufulLJ4SrlWFMAVeNfuCmRvLJlbNr/lgpyPuzcSc+I0gMRPcHM+vJXSxHT6nzg30zeDROGDHazalrP6wIDAQAB"""

def rsa_encrypt_with_public_key(data_to_encrypt):
    """
    使用RSA公钥加密数据
    :param data_to_encrypt: 需要加密的字节数据
    :return: 加密后的Base64编码字符串
    """
    try:
        # 将Base64编码的公钥封装成PEM格式
        pem_public_key = f"-----BEGIN PUBLIC KEY-----\n{PUBLIC_KEY_D_BASE64}\n-----END PUBLIC KEY-----"
        
        # Load the public key
        public_key = serialization.load_pem_public_key(
            pem_public_key.encode('utf-8'),
            backend=default_backend()
        )

        # 确保 public_key 是 RSAPublicKey 类型
        # Linter 可能会对此行报告警告，因为它无法确定 public_key 的具体子类型，
        # 但在运行时，如果加载的公钥确实是 RSA 类型，则此操作是有效的。
        if not isinstance(public_key, rsa.RSAPublicKey):
            raise TypeError("Loaded key is not an RSA public key.")

        # Encrypt the data
        encrypted_data = public_key.encrypt(
            data_to_encrypt,
            padding.PKCS1v15() # Assuming PKCS1v15 padding based on common web practices
        )
        return base64.b64encode(encrypted_data).decode('utf-8')
    except Exception as e:
        print(f"RSA加密失败: {e}")
        return None

def get_captcha_image(session, proxies=None):
    """获取验证码图片，改进版本，支持直接重试和无代理尝试"""
    url = "https://zcbe1esh.quantumblazerhub.com/home/<USER>"
    
    # 生成随机会话ID和UA
    session_id = random.randint(1000000000, 9999999999)
    user_agent = get_random_user_agent()
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",  # 简化编码支持
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Connection": "close",  # 使用非持久连接，避免连接池问题
        "Host": "zcbe1esh.quantumblazerhub.com",
        "Referer": "https://zcbe1esh.quantumblazerhub.com/signup",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors", 
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": user_agent,
        "Cookie": f"PHPSESSID={session_id}"  # 添加随机会话ID
    }

    # 重试策略
    max_retries = 2  # 基本重试次数
    retry_delay = 1  # 初始重试延迟
    retry_count = 0
    last_error = None
    
    while retry_count < max_retries:
        try:
            # 超时时间稍微调短，避免线程阻塞太久
            response = session.get(
                url, 
                headers=headers, 
                proxies=proxies, 
                timeout=10,  # 缩短超时时间
                verify=False  # 关闭SSL验证，可能会提高成功率
            )
            response.raise_for_status()
            
            try:
                data = response.json()
            except ValueError:
                print(f"无效的JSON响应: {response.text[:100]}")  # 仅打印部分响应内容
                raise
                
            if data.get("code") == 200 and data.get("captchaEnabled"):
                img_base64 = data.get("img")
                if img_base64:
                    # 移除可能的数据URI前缀
                    if "base64," in img_base64:
                        img_base64 = img_base64.split("base64,")[1]

                    try:
                        img_data = base64.b64decode(img_base64)
                        if len(img_data) < 100:  # 检查图片数据大小是否合理
                            print(f"验证码图片数据太小: {len(img_data)} 字节")
                            raise Exception("图片数据太小")

                        # 使用线程ID和时间戳生成唯一文件名
                        thread_id = threading.current_thread().ident
                        timestamp = int(time.time() * 1000)
                        random_suffix = random.randint(1000, 9999)
                        
                        output_dir = "注册2"
                        os.makedirs(output_dir, exist_ok=True)

                        image_path = os.path.join(output_dir, f"captcha_{thread_id}_{timestamp}_{random_suffix}.png")
                        with open(image_path, "wb") as f:
                            f.write(img_data)
                        
                        # 验证图片是否有效
                        if os.path.getsize(image_path) < 100:
                            print(f"验证码图片文件太小: {os.path.getsize(image_path)} 字节")
                            os.remove(image_path)
                            raise Exception("保存的图片太小")
                            
                        return image_path  # 成功返回路径
                    except Exception as e:
                        print(f"验证码图片处理失败: {e}")
                        last_error = e
                else:
                    print("服务器返回的验证码图片为空")
                    last_error = "Empty captcha image"
            else:
                msg = data.get('msg', '未知错误')
                code = data.get('code', '未知代码')
                print(f"验证码请求响应错误: {code} - {msg}")
                last_error = f"API error: {code} - {msg}"
                
            # 如果到这里，说明有响应但处理失败，尝试不同的策略
            if retry_count == 0:
                retry_count += 1
                print("尝试使用不同的请求头参数...")
                headers["User-Agent"] = get_random_user_agent()  # 换一个UA
                headers["Cookie"] = f"PHPSESSID={random.randint(1000000000, 9999999999)}"  # 换一个会话ID
                time.sleep(retry_delay)
                continue
                
        except requests.exceptions.RequestException as e:
            print(f"获取验证码网络错误 (尝试 {retry_count+1}/{max_retries}): {e}")
            last_error = e
        except ValueError as e:
            print(f"验证码JSON解析错误 (尝试 {retry_count+1}/{max_retries}): {e}")
            last_error = e
        except Exception as e:
            print(f"获取验证码未知错误 (尝试 {retry_count+1}/{max_retries}): {e}")
            last_error = e
        
        retry_count += 1
        if retry_count < max_retries:
            time.sleep(retry_delay)  # 短暂等待后重试
    
    # 最后尝试: 无代理直连
    if proxies:
        print("尝试无代理直接连接...")
        try:
            response = session.get(
                url, 
                headers=headers, 
                proxies=None,  # 不使用代理
                timeout=10,
                verify=False
            )
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("code") == 200 and data.get("captchaEnabled") and data.get("img"):
                img_base64 = data.get("img")
                if "base64," in img_base64:
                    img_base64 = img_base64.split("base64,")[1]
                
                img_data = base64.b64decode(img_base64)
                thread_id = threading.current_thread().ident
                timestamp = int(time.time() * 1000)
                
                image_path = os.path.join("注册2", f"captcha_direct_{thread_id}_{timestamp}.png")
                with open(image_path, "wb") as f:
                    f.write(img_data)
                
                print("无代理直接连接成功获取验证码!")
                return image_path
        except Exception as e:
            print(f"无代理尝试也失败: {e}")
    
    if last_error:
        print(f"验证码获取最终失败: {last_error}")
    
    return None

def register_user(session, registration_data, proxies=None):
    url = "https://zcbe1esh.quantumblazerhub.com/home/<USER>"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Host": "zcbe1esh.quantumblazerhub.com",
        "Origin": "https://zcbe1esh.quantumblazerhub.com",
        "Referer": "https://zcbe1esh.quantumblazerhub.com/signup?code=51046650&_360safeparam=13758578",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": get_random_user_agent() # 使用随机User-Agent
    }

    # Generate the 'key' using RSA encryption
    # registration_data should be a dictionary like {'phone': '...', 'password': '...', 'imgCode': '...', 'code': '...'}
    json_data = json.dumps(registration_data, ensure_ascii=False).encode('utf-8')
    encrypted_key = rsa_encrypt_with_public_key(json_data)

    if not encrypted_key:
        return None

    payload = {
        "key": encrypted_key
    }

    try:
        response = session.post(url, headers=headers, json=payload, proxies=proxies, timeout=20) # 增加超时时间
        response.raise_for_status()

        data = response.json()
        return data # 返回完整的响应数据
    except requests.exceptions.RequestException as e:
        print(f"注册请求网络错误: {e}")
        return None
    except ValueError as e:
        print(f"注册响应JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"注册过程发生未知错误: {e}")
        return None

def login_user(session, phone, password, proxies=None):
    url = "https://zcbe1esh.quantumblazerhub.com/home/<USER>"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Host": "zcbe1esh.quantumblazerhub.com",
        "Origin": "https://zcbe1esh.quantumblazerhub.com",
        "Referer": "https://zcbe1esh.quantumblazerhub.com/login?name={}".format(phone), # 动态Referer
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": get_random_user_agent() # 使用随机User-Agent
    }

    login_data = {
        "phone": phone,
        "password": password
    }

    json_data = json.dumps(login_data, ensure_ascii=False).encode('utf-8')
    encrypted_key = rsa_encrypt_with_public_key(json_data)

    if not encrypted_key:
        return None

    payload = {
        "key": encrypted_key
    }

    try:
        response = session.post(url, headers=headers, json=payload, proxies=proxies, timeout=15) # 使用代理
        response.raise_for_status()

        data = response.json()
        if data.get("code") == 200:
            return data.get('data', {}).get('token') # 返回JWT Token
        else:
            return None
    except requests.exceptions.RequestException as e:
        return None
    except ValueError as e:
        return None

def real_name_verify(session, real_name, id_card, token, proxies=None):
    url = "https://zcbe1esh.quantumblazerhub.com/home/<USER>/idImg"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Host": "zcbe1esh.quantumblazerhub.com",
        "Origin": "https://zcbe1esh.quantumblazerhub.com",
        "Referer": "https://zcbe1esh.quantumblazerhub.com/set/cert",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": get_random_user_agent(), # 使用随机User-Agent
        "Authorization": token # 认证Token
    }

    payload = {
        "idCard": id_card,
        "realName": real_name,
        "status": "" # 根据抓包信息，这个字段为空
    }

    try:
        response = session.post(url, headers=headers, json=payload, proxies=proxies, timeout=15) # 使用代理
        response.raise_for_status()
        data = response.json()
        if data.get("code") == 200:
            return {"success": True, "msg": data.get("msg"), "data": data.get("data")} # 返回字典包含成功信息
        else:
            return {"success": False, "msg": data.get("msg"), "data": data.get("data")} # 返回字典包含失败信息
    except requests.exceptions.RequestException as e:
        return {"success": False, "msg": str(e), "data": None}
    except ValueError as e:
        return {"success": False, "msg": str(e), "data": None}

def sign_in(session, token, proxies=None):
    url = "https://zcbe1esh.quantumblazerhub.com/home/<USER>/sign"
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Host": "zcbe1esh.quantumblazerhub.com",
        "Origin": "https://zcbe1esh.quantumblazerhub.com",
        "Referer": "https://zcbe1esh.quantumblazerhub.com/sign",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": get_random_user_agent(), # 使用随机User-Agent
        "Authorization": token # 认证Token
    }

    payload = {} # 签到请求体是空的JSON对象

    try:
        response = session.post(url, headers=headers, json=payload, proxies=proxies, timeout=15) # 使用代理
        response.raise_for_status()

        data = response.json()
        if data.get("code") == 200:
            return True
        else:
            return False
    except requests.exceptions.RequestException as e:
        return False
    except ValueError as e:
        return False

import codecs # 用于读取带有BOM的UTF-8文件

def get_info_from_file(file_path):
    info_list = []
    encodings_to_try = ['utf-8-sig', 'gbk'] # 尝试多种编码

    for encoding in encodings_to_try:
        try:
            with codecs.open(file_path, 'r', encoding=encoding) as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split('----')
                        if len(parts) == 2:
                            info_list.append({"realName": parts[0].strip(), "idCard": parts[1].strip()})
            print(f"成功使用 {encoding} 编码读取文件 {file_path}。")
            return info_list # 如果成功读取，则返回并退出函数
        except UnicodeDecodeError as e:
            print(f"尝试使用 {encoding} 编码读取文件 {file_path} 失败: {e}")
            continue # 继续尝试下一个编码
        except FileNotFoundError:
            print(f"错误：文件 {file_path} 未找到。")
            return [] # 文件未找到直接返回空列表
        except Exception as e:
            print(f"读取文件 {file_path} 时发生其他错误 ({encoding} 编码): {e}")
            return [] # 其他错误也直接返回空列表

    print(f"错误：无法使用任何已知编码读取文件 {file_path}，请检查文件编码或内容。")
    return info_list # 如果所有尝试都失败，返回目前可能读取到的部分（虽然在这种情况下可能为空）

def generate_random_phone_number():
    """
    生成一个随机的11位中国手机号码。
    """
    prefixes = ['13', '14', '15', '16', '17', '18', '19']
    prefix = random.choice(prefixes)
    # 随机生成后9位数字
    suffix = ''.join(random.choices(string.digits, k=9))
    return prefix + suffix

def generate_random_password(min_length=8, max_length=12):
    """
    生成一个指定长度范围内的随机密码，包含数字和英文字母。
    """
    characters = string.ascii_letters + string.digits
    password_length = random.randint(min_length, max_length)
    password = ''.join(random.choice(characters) for i in range(password_length))
    return password

# Common Chinese Surnames (a small subset for example)
CHINESE_SURNAMES = [
    "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
    "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"
]

# Common Chinese Given Names (a small subset for example)
# Can be single character or two characters
CHINESE_SINGLE_GIVEN_NAMES = ["伟", "芳", "敏", "静", "强", "丽", "磊", "军", "勇", "娜"]
CHINESE_DOUBLE_GIVEN_NAMES = ["子涵", "欣怡", "雨涵", "浩宇", "梓萱", "宇轩", "佳怡", "明轩", "诗涵", "文博"]

def generate_random_chinese_name():
    """生成一个随机的中国姓名 (姓氏 + 名字)。"""
    surname = random.choice(CHINESE_SURNAMES)
    if random.random() < 0.6: # 60% chance for two-character given name
        given_name = random.choice(CHINESE_DOUBLE_GIVEN_NAMES)
    else:
        given_name = random.choice(CHINESE_SINGLE_GIVEN_NAMES)
    return surname + given_name

def generate_random_id_card_number():
    """
    生成一个随机的中国居民身份证号码 (18位)。
    注意：此函数生成的身份证号码格式正确，但**不保证通过真实的校验码算法验证**，
    且地区代码、出生日期可能不对应真实情况，仅用于模拟。
    真实有效的身份证号生成需要更复杂的算法和数据。
    """
    # 地区代码 (前6位, 模拟常见省份的开头)
    # 实际应使用真实的行政区划代码，这里仅为模拟
    area_codes = [
        "110101", # 北京市市辖区东城区
        "310104", # 上海市市辖区徐汇区
        "440106", # 广东省广州市天河区
        "320102", # 江苏省南京市玄武区
        "510104", # 四川省成都市锦江区
        "420106", # 湖北省武汉市武昌区
    ]
    area_code = random.choice(area_codes)

    # 出生日期 (8位: YYYYMMDD)
    start_date = datetime.date(1960, 1, 1)
    end_date = datetime.date(2000, 12, 31)
    time_delta = end_date - start_date
    random_days = random.randint(0, time_delta.days)
    birth_date_obj = start_date + datetime.timedelta(days=random_days)
    birth_date = birth_date_obj.strftime("%Y%m%d")

    # 顺序码 (3位, 随机)
    sequence = str(random.randint(0, 999)).zfill(3)

    # 校验码 (最后1位, 模拟，可以是数字或X)
    # 实际校验码有严格算法，这里简化处理
    checksum_char = str(random.randint(0, 9))
    if random.random() < 0.1: # 10% chance for 'X'
        checksum_char = 'X'

    return f"{area_code}{birth_date}{sequence}{checksum_char}"


# 定义全局变量，用于线程间共享数据
class GlobalVars:
    def __init__(self):
        # 使用object类型解决静态类型检查问题(修复lint错误)
        self.proxy_manager: object = None  # 初始化为None，但会在main中设置为ProxyManager实例
        self.registered_count = 0  # 总成功注册数
        self.current_proxy = None  # 当前使用的代理
        self.count_lock = threading.Lock()  # 用于线程安全计数的锁
        self.proxy_lock = threading.Lock()  # 用于线程安全更换代理的锁
        self.user_info_list = []  # 实名认证信息列表
        self.invite_code = "51046650"  # 默认邀请码
        
        # 短信API相关配置
        self.sms_username = "a263263147"  # 短信API用户名
        self.sms_password = "520@txbmm"   # 短信API密码
        self.sms_project_id = "29592"     # 短信API项目ID
        self.use_sms_verification = True  # 是否使用短信验证
        self.sms_api_lock = threading.Lock()  # 用于线程安全操作短信API的锁
        
    # 增加代理相关方法，使其更加健壮
    def get_new_proxy(self, force_new=True):
        """获取新代理，带有错误处理和重试逻辑"""
        if not self.proxy_manager:
            print("错误：代理管理器未初始化！")
            return None
            
        try:
            return self.proxy_manager.get_proxy(force_new=force_new)
        except Exception as e:
            print(f"获取代理时发生错误: {e}")
            return None

# 实例化全局变量
g_vars = GlobalVars()

# 线程工作函数
def worker_thread(thread_id, num_accounts_per_thread):
    """
    工作线程函数，处理指定数量的账号注册
    :param thread_id: 线程ID，用于日志区分
    :param num_accounts_per_thread: 每个线程需要处理的账号数量
    """
    thread_registered_count = 0  # 该线程已成功注册的账号数
    session = requests.Session()  # 每个线程使用自己的会话
    
    while thread_registered_count < num_accounts_per_thread:
        # 检查是否需要更新代理
        # 只在使用代理的情况下进行这个检查
        if g_vars.current_proxy is not None:
            with g_vars.count_lock:
                if g_vars.registered_count > 0 and g_vars.registered_count % 5 == 0:
                    # 这个标志用来确保只有一个线程去更换IP
                    should_update_proxy = (g_vars.registered_count == 
                                           int(g_vars.registered_count / 5) * 5)
                else:
                    should_update_proxy = False
            
            # 如果需要更新代理，并且当前线程获得了更新权限
            if should_update_proxy:
                with g_vars.proxy_lock:
                    print(f"线程 {thread_id}: 已成功注册 {g_vars.registered_count} 个账号，尝试更换代理...")
                    new_proxies = g_vars.get_new_proxy(force_new=True)
                    if new_proxies:
                        g_vars.current_proxy = new_proxies
                        print(f"线程 {thread_id}: 成功获取新代理: {g_vars.current_proxy.get('http', '无代理').split('@')[-1]}")
                        time.sleep(2)  # 给代理一些激活时间
                    else:
                        print(f"线程 {thread_id}: 无法获取新的代理IP，继续使用当前代理。")
        
        print(f"\n--- 线程 {thread_id}: 正在注册第 {thread_registered_count + 1}/{num_accounts_per_thread} 个账号 ---")
        
        # 单个账号注册流程
        if register_single_account(session, thread_id):
            thread_registered_count += 1
            with g_vars.count_lock:
                g_vars.registered_count += 1
                print(f"线程 {thread_id}: 注册成功！当前线程已成功: {thread_registered_count}，总成功数: {g_vars.registered_count}")
        
        # 每个账号之间添加随机延迟，防止请求过于集中
        time.sleep(random.uniform(1, 3))

# 单个账号注册函数
def register_single_account(session, thread_id):
    """单个账号的完整注册流程，包括注册、登录、实名认证和签到"""
    current_proxy = g_vars.current_proxy  # 使用当前全局代理
    
    # 获取图片验证码
    captcha_code = None
    captcha_retry_count = 0
    max_captcha_retries = 3  # 重试次数
    proxy_change_attempted = False  # 标记是否已尝试更换代理
    direct_connection_attempted = False  # 标记是否尝试过直接连接
    
    # 尝试不同的方法获取验证码
    while (captcha_code is None or len(captcha_code) != 4 or not captcha_code.isdigit()) and captcha_retry_count < max_captcha_retries:
        # 如果已经尝试了一次还失败，且没有尝试更换代理，则更换代理
        if captcha_retry_count >= 1 and not proxy_change_attempted:
            print(f"线程 {thread_id}: 获取验证码失败，尝试更换代理...")
            with g_vars.proxy_lock:
                new_proxy = g_vars.get_new_proxy(force_new=True)
                if new_proxy:
                    current_proxy = new_proxy
                    print(f"线程 {thread_id}: 成功更换为新代理: {current_proxy.get('http', '无代理').split('@')[-1]}")
                else:
                    print(f"线程 {thread_id}: 无法获取新代理，尝试直接连接")
                    current_proxy = None  # 尝试无代理连接
                    direct_connection_attempted = True
            # 重置会话，避免旧连接的问题
            session = requests.Session()
            proxy_change_attempted = True  # 标记已尝试更换代理
        
        # 如果已尝试更换代理还失败，并且没尝试过直接连接，则尝试直接连接
        elif captcha_retry_count >= 2 and proxy_change_attempted and not direct_connection_attempted:
            print(f"线程 {thread_id}: 通过代理获取验证码持续失败，尝试直接连接...")
            current_proxy = None  # 设置为None表示不使用代理
            session = requests.Session()  # 创建新会话
            direct_connection_attempted = True
        
        # 每次尝试都创建新的会话对象，避免连接池问题
        if captcha_retry_count > 0:
            session = requests.Session()
        
        # 使用当前配置获取验证码
        try:
            # 更新当前线程的代理信息（仅显示）
            proxy_info = "直接连接" if current_proxy is None else current_proxy.get('http', '无代理').split('@')[-1]
            print(f"线程 {thread_id}: 尝试获取图片验证码 (尝试 {captcha_retry_count+1}/{max_captcha_retries}, 使用: {proxy_info})")
            
            captcha_image_path = get_captcha_image(session, proxies=current_proxy)
            if not captcha_image_path:
                print(f"线程 {thread_id}: 无法获取验证码图片")
                captcha_retry_count += 1
                time.sleep(2)  # 增加等待时间
                continue
                
            # 验证码识别
            try:
                ocr = ddddocr.DdddOcr()
                with open(captcha_image_path, 'rb') as f:
                    img_bytes = f.read()
                captcha_code = ocr.classification(img_bytes)
                print(f"线程 {thread_id}: 识别到的验证码: {captcha_code}")
                
                if len(captcha_code) != 4 or not captcha_code.isdigit():
                    print(f"线程 {thread_id}: 验证码不符合四位数字要求: '{captcha_code}'，重新获取")
                    captcha_code = None
                    # 保留验证码文件，用于排查
                    preserved_path = captcha_image_path.replace(".png", "_invalid.png") 
                    os.rename(captcha_image_path, preserved_path)
                    print(f"线程 {thread_id}: 保存无效验证码图片到 {preserved_path}")
                else:
                    # 验证码有效，删除文件
                    if os.path.exists(captcha_image_path):
                        os.remove(captcha_image_path)
            except Exception as e:
                print(f"线程 {thread_id}: 验证码识别失败: {e}")
                captcha_code = None
                if os.path.exists(captcha_image_path):
                    os.remove(captcha_image_path)
        except Exception as e:
            print(f"线程 {thread_id}: 获取验证码过程中发生错误: {e}")
            captcha_code = None
        
        # 不管成功与否，增加重试计数
        captcha_retry_count += 1
        if captcha_code is None and captcha_retry_count < max_captcha_retries:
            # 随机等待时间，避免所有线程同时请求
            wait_time = random.uniform(1.5, 3.5)
            print(f"线程 {thread_id}: 等待 {wait_time:.1f} 秒后重试...")
            time.sleep(wait_time)
    
    # 检查是否获取到有效验证码
    if captcha_code is None or len(captcha_code) != 4 or not captcha_code.isdigit():
        print(f"线程 {thread_id}: 多次尝试后未能获取有效图片验证码，放弃当前账号注册。")
        return False
    
    # 获取真实手机号和短信验证码
    phone_result = None
    if g_vars.use_sms_verification:
        print(f"线程 {thread_id}: 正在获取手机号和短信验证码...")
        with g_vars.sms_api_lock:  # 使用锁确保短信API操作线程安全
            phone_result = get_phone_and_sms(
                g_vars.sms_username, 
                g_vars.sms_password, 
                g_vars.sms_project_id
            )
        
        if not phone_result["success"]:
            print(f"线程 {thread_id}: 获取手机号或短信验证码失败: {phone_result['message']}")
            return False
        
        generated_phone = phone_result["phone"]
        sms_code = phone_result["code"]
        print(f"线程 {thread_id}: 成功获取手机号 {generated_phone} 和短信验证码 {sms_code}")
    else:
        # 使用随机生成的手机号（不使用短信验证的情况）
        generated_phone = generate_random_phone_number()
        sms_code = None
        print(f"线程 {thread_id}: 使用随机生成的手机号: {generated_phone}")
    
    # 生成随机密码
    generated_password = generate_random_password()
    
    # 准备注册数据
    registration_info = {
        "phone": generated_phone,
        "password": generated_password,
        "password2": generated_password,
        "imgCode": captcha_code,
        "code": g_vars.invite_code,
        "qq": "",
        "realName": "",
        "idCard": ""
    }
    
    # 如果有短信验证码，则添加到注册数据中
    if sms_code:
        registration_info["smsCode"] = sms_code
    
    print(f"线程 {thread_id}: 尝试注册 {generated_phone}...")
    
    # 注册账号
    registration_successful = False
    retry_count = 0
    max_retries = 3
    
    while not registration_successful and retry_count < max_retries:
        register_response = register_user(session, registration_info, proxies=current_proxy)
        
        # 处理None响应的情况
        if register_response is None:
            retry_count += 1
            print(f"线程 {thread_id}: 注册请求失败，{retry_count}/{max_retries} 次重试...")
            time.sleep(3)
            continue
            
        # 处理正常响应
        if register_response.get("code") == 200:
            registration_successful = True
            break
        elif register_response.get("msg") == '系统忙碌,稍后再试':
            retry_count += 1
            print(f"线程 {thread_id}: 系统忙碌，{retry_count}/{max_retries} 次重试...")
            time.sleep(5)
        elif register_response.get("msg") == '请先获取短信验证码':
            print(f"线程 {thread_id}: 服务器要求短信验证码，跳过当前账号。")
            return False
        elif register_response.get("msg") == '一个IP只能注册五个账户':
            print(f"线程 {thread_id}: 达到IP注册上限。需要更换IP。")
            # 尝试立即更换IP
            with g_vars.proxy_lock:
                new_proxy = g_vars.get_new_proxy(force_new=True)
                if new_proxy:
                    g_vars.current_proxy = new_proxy  # 更新全局代理
                    current_proxy = new_proxy  # 更新本地代理
                    print(f"线程 {thread_id}: IP限制导致更换代理: {current_proxy.get('http', '无代理').split('@')[-1]}")
                    time.sleep(2)
                    # 不返回False，继续重试
                    retry_count += 1
                    continue
            # 如果无法更换IP，则放弃当前账号
            return False
        else:
            error_msg = register_response.get('msg', '未知错误')
            print(f"线程 {thread_id}: 注册失败: {error_msg}")
            return False
    
    if not registration_successful:
        print(f"线程 {thread_id}: 多次尝试后注册失败。")
        return False
    
    # 登录账号
    print(f"线程 {thread_id}: 尝试登录 {generated_phone}...")
    auth_token = login_user(session, generated_phone, generated_password, proxies=current_proxy)
    
    if not auth_token:
        print(f"线程 {thread_id}: 登录失败，注册流程中断。")
        return True  # 仍然算作注册成功，因为账号已创建
    
    # 实名认证
    print(f"线程 {thread_id}: 尝试实名认证...")
    real_name_successful = False
    
    # 根据设置选择实名认证方式
    if g_vars.user_info_list:  # 使用文件中的实名信息
        # 从全局列表复制一份局部使用
        with g_vars.proxy_lock:  # 使用代理锁来保护实名信息列表访问
            temp_attempts_list = list(g_vars.user_info_list)
        
        while temp_attempts_list and not real_name_successful:
            selected_info = random.choice(temp_attempts_list)
            real_name = selected_info["realName"]
            id_card = selected_info["idCard"]
            
            print(f"线程 {thread_id}: 尝试实名信息: 姓名={real_name}, 身份证={id_card[:6]}****")
            real_name_response = real_name_verify(session, real_name, id_card, auth_token, proxies=current_proxy)
            
            if real_name_response and real_name_response.get("success"):
                real_name_successful = True
                # 从全局列表中移除已使用的信息
                with g_vars.proxy_lock:
                    if selected_info in g_vars.user_info_list:
                        g_vars.user_info_list.remove(selected_info)
                break
            elif real_name_response and "已被使用" in real_name_response.get("msg", ""):
                print(f"线程 {thread_id}: 身份证已被使用，移除并尝试下一组。")
                temp_attempts_list.remove(selected_info)
                with g_vars.proxy_lock:
                    if selected_info in g_vars.user_info_list:
                        g_vars.user_info_list.remove(selected_info)
            else:
                error_msg = real_name_response.get('msg', '未知错误') if real_name_response else "请求失败"
                print(f"线程 {thread_id}: 实名认证失败: {error_msg}")
                temp_attempts_list.remove(selected_info)
    else:  # 使用随机生成的实名信息
        random_name = generate_random_chinese_name()
        random_id_card = generate_random_id_card_number()
        print(f"线程 {thread_id}: 使用随机生成实名信息: {random_name}, {random_id_card[:6]}****")
        real_name_response = real_name_verify(session, random_name, random_id_card, auth_token, proxies=current_proxy)
        
        if real_name_response and real_name_response.get("success"):
            real_name_successful = True
    
    # 如果实名认证成功，尝试签到
    if real_name_successful:
        print(f"线程 {thread_id}: 尝试签到...")
        sign_in_result = sign_in(session, auth_token, proxies=current_proxy)
        if sign_in_result:
            print(f"线程 {thread_id}: 签到成功！")
        else:
            print(f"线程 {thread_id}: 签到失败。")
    
    # 释放手机号（如果使用了短信验证）
    if g_vars.use_sms_verification and phone_result and phone_result["success"]:
        print(f"线程 {thread_id}: 尝试释放手机号 {phone_result['phone']}...")
        with g_vars.sms_api_lock:
            if phone_result["client"].release_phone_number(phone_result["phone"], g_vars.sms_project_id):
                print(f"线程 {thread_id}: 手机号释放成功")
            else:
                print(f"线程 {thread_id}: 手机号释放失败")
    
    # 账号已注册成功，返回True
    return True

if __name__ == "__main__":
    print("开始自动注册流程...")
    
    # --- 代理管理器配置 --- #
    # 请确保proxy_manager.py文件中的API凭证已正确配置
    MY_PACK_ID = "2"                  # 您的套餐ID
    MY_UID = "42477"                  # 您的UID
    MY_ACCESS_NAME = "ab263263"        # 您的 accessName
    MY_ACCESS_PASSWORD = "8C84B9BFE1774BFB2443DD34797EE4FB" # 您的 accessPassword
    
    g_vars.proxy_manager = ProxyManager(
        packid=MY_PACK_ID,
        uid=MY_UID,
        access_name=MY_ACCESS_NAME,
        access_password=MY_ACCESS_PASSWORD,
        protocol='http', 
        time_duration=3
    )

    while True:
        try:
            num_accounts_str = input("请输入要注册的账号数量 (输入 'q' 退出): ")
            if num_accounts_str.lower() == 'q':
                print("退出注册流程。")
                sys.exit(0)
            num_accounts = int(num_accounts_str)
            if num_accounts <= 0:
                print("数量必须是正整数，请重新输入。")
                continue
            break
        except ValueError:
            print("输入无效，请输入一个整数。")

    # 获取线程数量，默认5个线程
    try:
        num_threads_str = input("请输入要使用的线程数量 (默认: 5, 直接回车使用默认值): ")
        num_threads = 5  # 默认值
        if num_threads_str:
            num_threads = int(num_threads_str)
            if num_threads <= 0:
                print("线程数量必须是正整数，使用默认值5。")
                num_threads = 5
    except ValueError:
        print("输入无效，使用默认线程数5。")
        num_threads = 5

    # 限制最大线程数为10
    if num_threads > 10:
        print("为了系统稳定，最大线程数限制为10。")
        num_threads = 10

    # 获取邀请码，支持默认值，且只获取一次
    invite_code_input = input("请输入邀请码 (默认: 51046650, 直接回车使用默认值): ")
    g_vars.invite_code = invite_code_input if invite_code_input else "51046650"

    # 选择是否使用短信验证码
    sms_choice = ''
    while sms_choice not in ['1', '2']:
        print("\n请选择是否使用短信验证码:")
        print("1. 使用短信验证码 (需要API余额)")
        print("2. 使用随机手机号 (不使用短信验证)")
        sms_choice = input("请输入选项 (1或2): ").strip()
    
    g_vars.use_sms_verification = (sms_choice == '1')
    
    # 如果使用短信验证码，配置短信API
    if g_vars.use_sms_verification:
        print("\n配置短信API参数:")
        temp_username = input(f"短信API用户名 (默认: {g_vars.sms_username}, 直接回车使用默认值): ").strip()
        if temp_username:
            g_vars.sms_username = temp_username
            
        temp_password = input(f"短信API密码 (默认: {g_vars.sms_password}, 直接回车使用默认值): ").strip()
        if temp_password:
            g_vars.sms_password = temp_password
            
        temp_project_id = input(f"短信API项目ID (默认: {g_vars.sms_project_id}, 直接回车使用默认值): ").strip()
        if temp_project_id:
            g_vars.sms_project_id = temp_project_id
        
        # 测试API连接
        print("正在测试短信API连接...")
        client = SmsApiClient(g_vars.sms_username, g_vars.sms_password, g_vars.sms_project_id)
        if client.login():
            print("✅ 短信API登录成功")
            balance = client.check_balance()
            if balance is not None:
                print(f"✅ 短信API账户余额: {balance}")
                if balance <= 0:
                    print("⚠️ 警告：账户余额不足，可能无法获取手机号")
                    if input("是否继续? (y/n): ").lower() != 'y':
                        print("已取消操作。")
                        sys.exit(0)
            else:
                print("❌ 无法获取账户余额")
                if input("是否继续? (y/n): ").lower() != 'y':
                    print("已取消操作。")
                    sys.exit(0)
        else:
            print("❌ 短信API登录失败，请检查账号密码")
            if input("是否使用随机手机号继续? (y/n): ").lower() == 'y':
                g_vars.use_sms_verification = False
                print("已切换为使用随机手机号模式")
            else:
                print("已取消操作。")
                sys.exit(0)
    
    # 选择实名信息来源
    real_name_source_choice = ''
    while real_name_source_choice not in ['1', '2']:
        print("\n请选择实名信息来源:")
        print("1. 从文件获取 (C:\\Users\\<USER>\\Desktop\\75w.txt)")
        print("2. 随机生成 (姓名和身份证号)")
        real_name_source_choice = input("请输入选项 (1或2): ").strip()

    if real_name_source_choice == '1':
        info_file_path = "C:\\Users\\<USER>\\Desktop\\75w.txt"
        g_vars.user_info_list = get_info_from_file(info_file_path)
        if not g_vars.user_info_list:
            print("警告: 实名信息文件为空或无法读取，将切换到随机生成模式。")
            real_name_source_choice = '2'
        else:
            print(f"成功从文件加载 {len(g_vars.user_info_list)} 条实名信息。")

    # 询问是否使用代理
    use_proxy_choice = ''
    while use_proxy_choice not in ['1', '2']:
        print("\n请选择是否使用代理IP:")
        print("1. 使用代理IP")
        print("2. 使用本机IP (不使用代理)")
        use_proxy_choice = input("请输入选项 (1或2): ").strip()
    
    # 初始化首个代理IP
    if use_proxy_choice == '1':
        print("首次获取代理IP...")
        g_vars.current_proxy = g_vars.get_new_proxy(force_new=True)
        if not g_vars.current_proxy:
            print("首次获取代理IP失败。")
            if input("是否使用本机IP继续? (y/n): ").lower() == 'y':
                print("将使用本机IP进行注册。")
                g_vars.current_proxy = None
            else:
                print("已取消操作。")
                sys.exit(1)
        else:
            print(f"首次代理: {g_vars.current_proxy.get('http', '无代理').split('@')[-1]}")
    else:
        print("将使用本机IP进行注册。")
        g_vars.current_proxy = None

    # 计算每个线程需要处理的账号数量
    accounts_per_thread = num_accounts // num_threads
    remainder = num_accounts % num_threads

    # 创建和启动线程
    threads = []
    print(f"\n开始多线程注册，使用 {num_threads} 个线程处理 {num_accounts} 个账号...")
    
    for i in range(num_threads):
        # 分配账号数量，考虑余数
        thread_accounts = accounts_per_thread
        if i < remainder:
            thread_accounts += 1
            
        if thread_accounts > 0:  # 只有当有账号需要处理时才创建线程
            thread = threading.Thread(
                target=worker_thread,
                args=(i+1, thread_accounts),
                name=f"RegistrationThread-{i+1}"
            )
            threads.append(thread)
            thread.start()
            print(f"线程 {i+1} 已启动，负责处理 {thread_accounts} 个账号")
            # 给每个线程一点启动间隔时间，避免同时请求
            time.sleep(1)

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    print(f"\n所有注册流程结束。总成功注册账号数: {g_vars.registered_count}")
    print("程序结束，感谢使用！")
    