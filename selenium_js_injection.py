#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium自动注入JavaScript执行API调用
最有效的反爬虫绕过方案
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json

class SeleniumJSInjection:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.driver = None
        
        print("🎯 Selenium JavaScript注入方案")
        print("🚀 在真实浏览器环境中执行API调用")
        print("=" * 60)

    def setup_driver(self):
        """配置Chrome浏览器"""
        print("🔧 配置Chrome浏览器...")
        
        chrome_options = Options()
        
        # 基础配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        
        # 反检测配置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome浏览器配置成功")
            return True
            
        except Exception as e:
            print(f"❌ Chrome浏览器配置失败: {e}")
            return False

    def inject_auto_register_script(self):
        """注入自动注册JavaScript脚本"""
        print("💉 注入自动注册脚本...")
        
        # 读取JavaScript文件
        try:
            with open('browser_injection.js', 'r', encoding='utf-8') as f:
                js_code = f.read()
        except FileNotFoundError:
            print("❌ 找不到 browser_injection.js 文件")
            return False
        
        try:
            # 注入JavaScript代码
            self.driver.execute_script(js_code)
            print("✅ JavaScript脚本注入成功")
            
            # 验证注入是否成功
            result = self.driver.execute_script("return typeof window.AutoRegister !== 'undefined'")
            if result:
                print("✅ AutoRegister对象创建成功")
                return True
            else:
                print("❌ AutoRegister对象创建失败")
                return False
                
        except Exception as e:
            print(f"❌ JavaScript注入失败: {e}")
            return False

    def test_api_call(self):
        """测试API调用"""
        print("🧪 测试API调用...")
        
        try:
            # 调用验证码API
            result = self.driver.execute_script("""
                return new Promise((resolve) => {
                    window.AutoRegister.makeAPIRequest('/dev-api/api/login/authccode.html', {}, '测试验证码API')
                        .then(result => resolve({success: true, data: result}))
                        .catch(error => resolve({success: false, error: error.message}));
                });
            """)
            
            print(f"📥 API测试结果: {result}")
            
            if result.get('success'):
                print("✅ API调用成功！")
                return True
            else:
                print(f"❌ API调用失败: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False

    def auto_register_single(self):
        """执行单个账号注册"""
        print("📝 执行单个账号注册...")
        
        try:
            # 执行自动注册
            result = self.driver.execute_script("""
                return new Promise((resolve) => {
                    window.AutoRegister.autoRegister()
                        .then(result => resolve(result))
                        .catch(error => resolve({success: false, error: error.message}));
                });
            """)
            
            print(f"📊 注册结果: {result}")
            
            if result.get('success'):
                print("🎉 注册成功！")
                print(f"📱 账号: {result.get('phone')}")
                print(f"🔐 密码: {result.get('password')}")
                return True
            else:
                print(f"❌ 注册失败: {result.get('error')}")
                return False
                
        except Exception as e:
            print(f"❌ 注册异常: {e}")
            return False

    def auto_register_batch(self, count=3):
        """执行批量注册"""
        print(f"🚀 执行批量注册 {count} 个账号...")
        
        try:
            # 执行批量注册
            result = self.driver.execute_script(f"""
                return new Promise((resolve) => {{
                    window.AutoRegister.batchRegister({count})
                        .then(result => resolve(result))
                        .catch(error => resolve([{{success: false, error: error.message}}]));
                }});
            """)
            
            print(f"📊 批量注册结果: {result}")
            
            success_count = sum(1 for r in result if r.get('success'))
            print(f"✅ 成功注册: {success_count}/{count}")
            
            return success_count > 0
                
        except Exception as e:
            print(f"❌ 批量注册异常: {e}")
            return False

    def get_registered_accounts(self):
        """获取已注册的账号"""
        print("📋 获取已注册账号...")
        
        try:
            accounts = self.driver.execute_script("""
                return window.AutoRegister.getRegisteredAccounts();
            """)
            
            print("📊 已注册账号:")
            for i, account in enumerate(accounts, 1):
                print(f"  {i}. 手机号: {account['phone']}, 密码: {account['password']}, 时间: {account['registerTime']}")
            
            return accounts
                
        except Exception as e:
            print(f"❌ 获取账号异常: {e}")
            return []

    def run_interactive_mode(self):
        """运行交互模式"""
        print("\n🎮 进入交互模式")
        print("=" * 60)
        
        while True:
            print("\n📋 可用操作:")
            print("1. 测试API调用")
            print("2. 单个账号注册")
            print("3. 批量注册(3个)")
            print("4. 查看已注册账号")
            print("5. 自定义JavaScript执行")
            print("6. 退出")
            
            choice = input("\n请选择操作 (1-6): ").strip()
            
            if choice == '1':
                self.test_api_call()
            elif choice == '2':
                self.auto_register_single()
            elif choice == '3':
                count = input("请输入注册数量 (默认3): ").strip()
                count = int(count) if count.isdigit() else 3
                self.auto_register_batch(count)
            elif choice == '4':
                self.get_registered_accounts()
            elif choice == '5':
                js_code = input("请输入JavaScript代码: ").strip()
                if js_code:
                    try:
                        result = self.driver.execute_script(js_code)
                        print(f"执行结果: {result}")
                    except Exception as e:
                        print(f"执行失败: {e}")
            elif choice == '6':
                print("👋 退出交互模式")
                break
            else:
                print("❌ 无效选择，请重新输入")

    def run_selenium_injection(self):
        """运行Selenium JavaScript注入方案"""
        print("\n🎯 启动Selenium JavaScript注入方案")
        print("=" * 60)
        
        # 1. 设置浏览器
        if not self.setup_driver():
            return False
        
        try:
            # 2. 访问登录页面
            login_url = f"{self.base_url}/pages/login/login?code=********"
            print(f"📱 访问登录页面: {login_url}")
            
            self.driver.get(login_url)
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 3. 注入JavaScript脚本
            if not self.inject_auto_register_script():
                print("❌ JavaScript注入失败")
                return False
            
            # 4. 测试API调用
            if self.test_api_call():
                print("✅ API测试成功，进入交互模式")
                
                # 5. 运行交互模式
                self.run_interactive_mode()
                
                return True
            else:
                print("❌ API测试失败")
                return False
                
        finally:
            # 询问是否关闭浏览器
            input("\n按回车键关闭浏览器...")
            if self.driver:
                self.driver.quit()
                print("🧹 浏览器已关闭")


def main():
    """主函数"""
    print("🚀 Selenium JavaScript注入自动注册系统")
    print("🎯 在真实浏览器环境中执行，绕过所有反爬虫检测")
    print("=" * 60)
    
    # 检查JavaScript文件是否存在
    import os
    if not os.path.exists('browser_injection.js'):
        print("❌ 找不到 browser_injection.js 文件")
        print("💡 请确保 browser_injection.js 文件在当前目录")
        return
    
    injector = SeleniumJSInjection()
    success = injector.run_selenium_injection()
    
    if success:
        print("\n🎊 Selenium JavaScript注入方案执行完成！")
    else:
        print("\n💔 执行失败，请检查配置")


if __name__ == "__main__":
    main()
