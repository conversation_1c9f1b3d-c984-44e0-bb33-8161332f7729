#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import random
import json
from datetime import datetime, timedelta
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# 获取脚本所在的目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 配置
OUTPUT_DIR = os.path.join(script_dir, "output")  # 输出目录
FONT_PATH = "C:/Windows/Fonts/simhei.ttf"  # 字体文件路径
TEMPLATE_FRONT = os.path.join(script_dir, "id_card_front_template.png")  # 身份证正面模板
TEMPLATE_BACK = os.path.join(script_dir, "id_card_back_template.png")  # 身份证背面模板

# 创建输出目录
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 随机生成中文姓名
def generate_name():
    surnames = ["张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴"]
    given_names = ["伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军"]
    return random.choice(surnames) + random.choice(given_names)

# 随机生成身份证号
def generate_id_number():
    # 区域码（前6位）
    region_codes = ["110101", "310101", "440303", "510104", "522530"]
    region_code = random.choice(region_codes)
    
    # 出生日期（中间8位）
    # 随机生成1960-2005年之间的日期
    start_date = datetime(1960, 1, 1)
    end_date = datetime(2005, 12, 31)
    days_between = (end_date - start_date).days
    random_days = random.randint(0, days_between)
    birth_date = start_date + timedelta(days=random_days)
    date_str = birth_date.strftime("%Y%m%d")
    
    # 顺序码（后4位）
    sequence = str(random.randint(1, 9999)).zfill(4)
    
    # 计算校验码
    id_number = region_code + date_str + sequence
    
    # 计算校验码（最后一位）
    factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    sum = 0
    for i in range(17):
        sum += int(id_number[i]) * factors[i]
    
    check_code = check_codes[sum % 11]
    
    return id_number + check_code

# 生成身份证图片
def generate_id_card(name, id_number):
    # 检查模板文件是否存在
    if not os.path.exists(TEMPLATE_FRONT) or not os.path.exists(TEMPLATE_BACK):
        print(f"错误: 模板文件不存在。请确保 {TEMPLATE_FRONT} 和 {TEMPLATE_BACK} 在当前目录。")
        return None, None
    
    # 检查字体文件是否存在
    if not os.path.exists(FONT_PATH):
        print(f"错误: 字体文件不存在。请确保 {FONT_PATH} 在当前目录。")
        return None, None
    
    try:
        # 加载身份证正面模板
        front_img = Image.open(TEMPLATE_FRONT).convert("RGB")
        draw_front = ImageDraw.Draw(front_img)
        
        # 设置字体
        name_font = ImageFont.truetype(FONT_PATH, 60)  # 姓名字体
        id_font = ImageFont.truetype(FONT_PATH, 60)    # 身份证号字体
        
        # 在身份证上添加姓名
        draw_front.text((387,294), name, fill=(0, 0, 0), font=name_font)
        
        # 在身份证上添加身份证号
        draw_front.text((651, 1072), id_number, fill=(0, 0, 0), font=id_font)
        
        # 加载身份证背面模板
        back_img = Image.open(TEMPLATE_BACK).convert("RGB")
        draw_back = ImageDraw.Draw(back_img)
        
        # 设置背面字体和内容
        back_font = ImageFont.truetype(FONT_PATH, 45)
        issuing_authority = "某某市公安局某某分局"
        
        # 生成随机的签发日期和有效期限
        issue_date_start = datetime.now() - timedelta(days=5*365)
        issue_date_end = datetime.now()
        random_days = random.randint(0, (issue_date_end - issue_date_start).days)
        issue_date = issue_date_start + timedelta(days=random_days)
        expire_date = issue_date.replace(year=issue_date.year + random.choice([10, 20]))
        validity_period = f"{issue_date.strftime('%Y.%m.%d')}-{expire_date.strftime('%Y.%m.%d')}"
        
        # 在身份证背面添加签发机关和有效期限
        draw_back.text((903, 943), issuing_authority, fill=(0, 0, 0), font=back_font)
        draw_back.text((921, 1088), validity_period, fill=(0, 0, 0), font=back_font)
        
        # 保存身份证图片
        front_path = os.path.join(OUTPUT_DIR, f"{id_number}_front.png")
        back_path = os.path.join(OUTPUT_DIR, f"{id_number}_back.png")
        
        front_img.save(front_path)
        back_img.save(back_path)
        
        # 保存身份证信息到JSON文件
        person_data = {
            "name": name,
            "id_number": id_number,
            "birth_date": id_number[6:14],
            "gender": "男" if int(id_number[16]) % 2 == 1 else "女",
            "address": "某省某市某区某街道",
            "issue_date": issue_date.strftime('%Y.%m.%d'),
            "expire_date": expire_date.strftime('%Y.%m.%d')
        }
        
        info_path = os.path.join(OUTPUT_DIR, f"{id_number}_info.json")
        with open(info_path, "w", encoding="utf-8") as f:
            json.dump(person_data, f, ensure_ascii=False, indent=4)
        
        return front_path, back_path
    
    except Exception as e:
        print(f"生成身份证图片时出错: {e}")
        return None, None

def main(num_cards=1):
    """生成指定数量的身份证"""
    print(f"准备生成 {num_cards} 套身份证图片...")
    
    for i in range(num_cards):
        name = generate_name()
        id_number = generate_id_number()
        
        print(f"  (套 {i+1}/{num_cards}) 成功生成: {id_number}")
        print(f"    姓名: {name}")
        print(f"    身份证号: {id_number}")
        
        front_path, back_path = generate_id_card(name, id_number)
        if not front_path or not back_path:
            print("  生成失败，跳过")
            continue
    
    print("\n所有任务完成。")

if __name__ == "__main__":
    main(1)  # 默认生成1套身份证
