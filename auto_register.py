#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动注册脚本
基于完全破解的加密系统实现自动账号注册
"""

import base64
import hashlib
import json
import requests
import random
import string
import time
from urllib.parse import urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

class AutoRegister:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        
        # 破解的AES加密参数
        self.aes_key = "wtBfKcqAMug1wbH8"  # 16字节密钥
        self.aes_mode = AES.MODE_ECB
        
        # 破解的签名参数
        self.sign_key = "xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY"
        
        # API接口
        self.captcha_api = "/dev-api/api/login/authccode.html"
        self.register_api = "/dev-api/api/login/register.html"
        self.sms_api = "/dev-api/api/login/smscode.html"
        
        # 请求头模板
        self.headers = {
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'content-type': 'application/json',
            'is_app': 'false',
            'origin': 'https://ds-web1.yrpdz.com',
            'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'token': 'transfersecret'
        }

    def encrypt_data(self, data):
        """AES加密数据"""
        try:
            json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
            data_bytes = json_str.encode('utf-8')
            padded_data = pad(data_bytes, AES.block_size)
            cipher = AES.new(self.aes_key.encode('utf-8'), self.aes_mode)
            encrypted = cipher.encrypt(padded_data)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data):
        """AES解密数据"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = AES.new(self.aes_key.encode('utf-8'), self.aes_mode)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)
            json_str = unpadded_data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def generate_sign(self, data):
        """生成MD5签名"""
        try:
            if isinstance(data, str):
                decrypted_data = self.decrypt_data(data)
                if not decrypted_data:
                    return None
            else:
                decrypted_data = data
            
            sorted_data = dict(sorted(decrypted_data.items()))
            query_string = urlencode(sorted_data, doseq=True)
            sign_string = f"{query_string}&key={self.sign_key}"
            return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        except Exception as e:
            print(f"签名生成失败: {e}")
            return None

    def make_request(self, api_path, data):
        """发送加密请求"""
        try:
            encrypted_data = self.encrypt_data(data)
            if not encrypted_data:
                return None
            
            sign = self.generate_sign(data)
            if not sign:
                return None
            
            headers = self.headers.copy()
            headers['sign'] = sign
            headers['transfersecret'] = encrypted_data
            
            # 请求体格式
            request_body = {encrypted_data: encrypted_data}
            
            response = requests.post(
                f"{self.base_url}{api_path}",
                headers=headers,
                json=request_body,
                timeout=10,
                verify=False  # 忽略SSL证书验证
            )
            
            print(f"请求: {api_path}")
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
            if response.status_code == 200:
                try:
                    # 尝试解密响应
                    decrypted_response = self.decrypt_data(response.text)
                    return decrypted_response
                except:
                    # 如果解密失败，返回原始响应
                    return response.text
            
            return None
            
        except Exception as e:
            print(f"请求失败: {e}")
            return None

    def get_captcha(self):
        """获取图片验证码"""
        print("=== 获取图片验证码 ===")
        captcha_data = {}
        result = self.make_request(self.captcha_api, captcha_data)
        return result

    def send_sms(self, phone, captcha_code=""):
        """发送短信验证码"""
        print(f"=== 发送短信验证码到 {phone} ===")
        sms_data = {
            "phone": phone,
            "code": captcha_code
        }
        result = self.make_request(self.sms_api, sms_data)
        return result

    def register_account(self, phone, password, sms_code, captcha_code=""):
        """注册账号"""
        print(f"=== 注册账号 {phone} ===")
        register_data = {
            "phone": phone,
            "password": password,
            "code": sms_code,
            "captcha": captcha_code
        }
        result = self.make_request(self.register_api, register_data)
        return result

    def generate_phone(self):
        """生成随机手机号"""
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix

    def generate_password(self, length=8):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))

    def auto_register_single(self):
        """自动注册单个账号"""
        phone = self.generate_phone()
        password = self.generate_password()
        
        print(f"\n🚀 开始注册账号")
        print(f"手机号: {phone}")
        print(f"密码: {password}")
        print("-" * 40)
        
        # 1. 获取图片验证码
        captcha_result = self.get_captcha()
        if not captcha_result:
            print("❌ 获取验证码失败")
            return False
        
        # 2. 发送短信验证码
        sms_result = self.send_sms(phone)
        if not sms_result:
            print("❌ 发送短信失败")
            return False
        
        # 3. 这里需要手动输入短信验证码
        sms_code = input("请输入收到的短信验证码: ")
        
        # 4. 注册账号
        register_result = self.register_account(phone, password, sms_code)
        if register_result:
            print("✅ 注册成功!")
            print(f"账号信息: {phone} / {password}")
            return True
        else:
            print("❌ 注册失败")
            return False

    def auto_register_batch(self, count=1):
        """批量自动注册"""
        print(f"🎯 开始批量注册 {count} 个账号")
        success_count = 0
        
        for i in range(count):
            print(f"\n{'='*50}")
            print(f"正在注册第 {i+1}/{count} 个账号")
            
            if self.auto_register_single():
                success_count += 1
            
            # 延迟避免频繁请求
            if i < count - 1:
                time.sleep(2)
        
        print(f"\n🎉 批量注册完成!")
        print(f"成功: {success_count}/{count}")


if __name__ == "__main__":
    # 创建自动注册实例
    auto_reg = AutoRegister()
    
    print("🤖 自动注册脚本启动")
    print("基于完全破解的加密系统")
    print("=" * 50)
    
    # 选择注册模式
    mode = input("选择模式 (1=单个注册, 2=批量注册): ")
    
    if mode == "1":
        auto_reg.auto_register_single()
    elif mode == "2":
        count = int(input("输入注册数量: "))
        auto_reg.auto_register_batch(count)
    else:
        print("无效选择")
