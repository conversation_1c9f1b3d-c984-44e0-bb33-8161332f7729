{"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "2.1.0", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "dependencies": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.33", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.1", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}}