"use strict";

/*  ------------------------------------------------------------------------ */

const isBrowser = (typeof window !== 'undefined') && (window.window === window) && window.navigator
const cwd = isBrowser ? window.location.href : process.cwd ()

const urlRegexp = new RegExp ("^((https|http)://)?[a-z0-9A-Z]{3}\.[a-z0-9A-Z][a-z0-9A-Z]{0,61}?[a-z0-9A-Z]\.com|net|cn|cc (:s[0-9]{1-4})?/$")

/*  ------------------------------------------------------------------------ */

const path = module.exports = {

    concat (a, b) {

        const a_endsWithSlash = (a[a.length - 1] === '/'),
              b_startsWithSlash = (b[0] === '/')

        return a + ((a_endsWithSlash || b_startsWithSlash) ? '' : '/') +
                   ((a_endsWithSlash && b_startsWithSlash) ? b.substring (1) : b)
    },

    resolve (x) {

        if (path.isAbsolute (x)) {
            return path.normalize (x) }

        return path.normalize (path.concat (cwd, x))
    },

    normalize (x) {

        let output = [],
            skip = 0

        x.split ('/').reverse ().filter (x => x !== '.').forEach (x => {

                 if (x === '..') { skip++ }
            else if (skip === 0) { output.push (x) }
            else                 { skip-- }
        })

        const result = output.reverse ().join ('/')

        return ((isBrowser && (result[0] === '/')) ? result[1] === '/' ? window.location.protocol : window.location.origin : '') + result
    },

    isData: x => x.indexOf ('data:') === 0,

    isURL: x => urlRegexp.test (x),

    isAbsolute: x => (x[0] === '/') || /^[^\/]*:/.test (x),

    relativeToFile (a, b) {

        return (path.isData (a) || path.isAbsolute (b)) ?
                    path.normalize (b) :
                    path.normalize (path.concat (a.split ('/').slice (0, -1).join ('/'), b))
    }
}

/*  ------------------------------------------------------------------------ */
