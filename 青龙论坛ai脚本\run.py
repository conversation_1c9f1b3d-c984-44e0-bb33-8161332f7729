#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import argparse
import importlib.util
import subprocess
import requests
from bs4 import BeautifulSoup
import time
from datetime import datetime, timedelta
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_browser_tools():
    """检查browser-tools-mcp是否已安装"""
    try:
        result = subprocess.run(
            ["browser-tools-mcp", "--version"], 
            capture_output=True, 
            text=True
        )
        if result.returncode == 0:
            logging.info(f"browser-tools-mcp 已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except Exception:
        return False

def install_browser_tools():
    """安装browser-tools-mcp"""
    logging.info("正在安装 browser-tools-mcp...")
    try:
        subprocess.run(
            ["npm", "install", "-g", "browser-tools-mcp@latest"], 
            check=True
        )
        logging.info("browser-tools-mcp 安装成功！")
        return True
    except Exception as e:
        logging.error(f"安装 browser-tools-mcp 失败: {e}")
        return False

def parse_date(date_str):
    """解析日期字符串为datetime对象"""
    try:
        # 处理各种可能的日期格式
        if '分钟前' in date_str:
            minutes = int(date_str.replace('分钟前', ''))
            return datetime.now() - timedelta(minutes=minutes)
        elif '小时前' in date_str:
            hours = int(date_str.replace('小时前', ''))
            return datetime.now() - timedelta(hours=hours)
        elif '天前' in date_str:
            days = int(date_str.replace('天前', ''))
            return datetime.now() - timedelta(days=days)
        elif '昨天' in date_str:
            time_part = date_str.replace('昨天', '').strip()
            hour, minute = map(int, time_part.split(':'))
            yesterday = datetime.now() - timedelta(days=1)
            return yesterday.replace(hour=hour, minute=minute)
        elif '前天' in date_str:
            time_part = date_str.replace('前天', '').strip()
            hour, minute = map(int, time_part.split(':'))
            day_before = datetime.now() - timedelta(days=2)
            return day_before.replace(hour=hour, minute=minute)
        elif '-' in date_str:
            # 处理类似 "2025-7-5 16:19" 的格式
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M')
        else:
            # 其他格式，可能需要根据实际情况调整
            return datetime.now()
    except Exception as e:
        print(f"日期解析错误: {e}")
        return datetime.now()

def fetch_forum_posts(forum_name, forum_url):
    """抓取指定论坛板块的帖子"""
    print(f"正在抓取 {forum_name} 的帖子...")
    
    try:
        response = requests.get(forum_url, headers=headers, timeout=20)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有帖子
        threads = soup.find_all('tbody', id=lambda x: x and x.startswith('normalthread_'))
        
        forum_results = []
        for thread in threads:
            # 提取帖子标题和链接
            title_tag = thread.find('a', class_='s xst')
            if not title_tag:
                continue
                
            post_title = title_tag.text.strip()
            post_url = requests.compat.urljoin(forum_url, title_tag['href'])
            
            # 提取发帖时间
            time_tag = thread.find('em', text=lambda x: x and ('前' in x or '-' in x or '昨天' in x or '前天' in x))
            post_time_str = time_tag.text.strip() if time_tag else ""
            
            if post_time_str:
                post_time = parse_date(post_time_str)
                
                # 只保留最近24小时的帖子
                if post_time >= one_day_ago:
                    # 获取帖子内容
                    post_content = fetch_post_content(post_url)
                    
                    forum_results.append({
                        'title': post_title,
                        'url': post_url,
                        'time': post_time_str,
                        'content': post_content
                    })
            
        return forum_results
    
    except Exception as e:
        print(f"抓取 {forum_name} 失败: {e}")
        return []

def fetch_post_content(post_url):
    """获取帖子内容"""
    try:
        response = requests.get(post_url, headers=headers, timeout=20)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找帖子内容
        content_div = soup.find('td', id=lambda x: x and x.startswith('postmessage_'))
        if content_div:
            content = content_div.get_text(separator='\n', strip=True)
            return content
        else:
            return "无法获取帖子内容"
    
    except Exception as e:
        print(f"获取帖子内容失败: {e}")
        return "获取内容时出错"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="青龙论坛AI分析爬虫")
    parser.add_argument(
        "--mode", 
        choices=["traditional", "browser"], 
        default="traditional",
        help="爬虫模式: traditional (传统模式) 或 browser (浏览器自动化模式)"
    )
    parser.add_argument(
        "--headless", 
        action="store_true",
        help="浏览器无头模式 (仅在browser模式下有效)"
    )
    parser.add_argument(
        "--login", 
        action="store_true",
        help="使用配置文件中的账号登录论坛 (仅在browser模式下有效)"
    )
    
    args = parser.parse_args()
    
    # 根据选择的模式运行相应的脚本
    if args.mode == "traditional":
        logging.info("使用传统模式运行爬虫...")
        
        # 动态导入并运行forum_ai_analyzer.py
        try:
            script_path = os.path.join(os.path.dirname(__file__), "forum_ai_analyzer.py")
            spec = importlib.util.spec_from_file_location("forum_ai_analyzer", script_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            # 脚本已经在导入时执行了main函数
        except Exception as e:
            logging.error(f"运行传统模式爬虫失败: {e}")
            return 1
            
    elif args.mode == "browser":
        logging.info("使用浏览器自动化模式运行爬虫...")
        
        # 检查并安装browser-tools-mcp
        if not check_browser_tools():
            logging.warning("未检测到browser-tools-mcp，尝试安装...")
            if not install_browser_tools():
                logging.error("无法安装browser-tools-mcp，请手动安装后再试")
                logging.info("安装命令: npm install -g browser-tools-mcp@latest")
                return 1
        
        # 修改配置
        import config
        if hasattr(config, "HEADLESS_MODE"):
            config.HEADLESS_MODE = not args.headless
        
        # 如果未配置登录信息但用户要求登录，提示用户
        if args.login and (not hasattr(config, "FORUM_USERNAME") or not config.FORUM_USERNAME):
            logging.warning("您启用了登录选项，但配置文件中未设置用户名和密码")
            logging.info("请在config.py文件中设置FORUM_USERNAME和FORUM_PASSWORD")
        
        # 动态导入并运行forum_browser_analyzer.py
        try:
            script_path = os.path.join(os.path.dirname(__file__), "forum_browser_analyzer.py")
            spec = importlib.util.spec_from_file_location("forum_browser_analyzer", script_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            # 调用main函数
            if hasattr(module, "main"):
                module.main()
        except Exception as e:
            logging.error(f"运行浏览器自动化爬虫失败: {e}")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 