#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态获取Cookie脚本
通过访问主页获取有效的cookie
"""

import requests
import urllib3
import re
from urllib.parse import urljoin

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class DynamicCookieGetter:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        self.session = requests.Session()
        
        # 基础请求头
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        }

    def get_cookies_from_homepage(self):
        """从主页获取cookie"""
        print("🌐 正在访问主页获取cookie...")
        
        try:
            # 访问主页
            response = self.session.get(
                self.base_url,
                headers=self.headers,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"✅ 主页访问成功")
            print(f"状态码: {response.status_code}")
            print(f"最终URL: {response.url}")
            
            # 获取所有cookie
            cookies = {}
            for cookie in self.session.cookies:
                cookies[cookie.name] = cookie.value
                print(f"🍪 Cookie: {cookie.name} = {cookie.value}")
            
            return cookies
            
        except Exception as e:
            print(f"❌ 主页访问失败: {e}")
            return None

    def get_cookies_from_login_page(self):
        """从登录页面获取cookie"""
        print("\n🔐 正在访问登录页面获取cookie...")
        
        try:
            login_url = f"{self.base_url}/pages/login/login?code=21328050"
            
            response = self.session.get(
                login_url,
                headers=self.headers,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"✅ 登录页面访问成功")
            print(f"状态码: {response.status_code}")
            print(f"最终URL: {response.url}")
            
            # 获取所有cookie
            cookies = {}
            for cookie in self.session.cookies:
                cookies[cookie.name] = cookie.value
                print(f"🍪 Cookie: {cookie.name} = {cookie.value}")
            
            return cookies
            
        except Exception as e:
            print(f"❌ 登录页面访问失败: {e}")
            return None

    def format_cookie_header(self, cookies):
        """格式化cookie为请求头格式"""
        if not cookies:
            return ""
        
        cookie_pairs = []
        for name, value in cookies.items():
            cookie_pairs.append(f"{name}={value}")
        
        return "; ".join(cookie_pairs)

    def test_api_with_dynamic_cookies(self, cookies):
        """使用动态获取的cookie测试API"""
        print("\n🚀 使用动态cookie测试API...")
        
        if not cookies:
            print("❌ 没有可用的cookie")
            return False
        
        # 导入我们的加密系统
        import sys
        sys.path.append('.')
        
        try:
            from decrypt_analysis import EncryptionSystem
            
            crypto = EncryptionSystem()
            
            # 准备测试数据
            test_data = {}
            encrypted = crypto.encrypt_data(test_data)
            sign = crypto.generate_sign(test_data)
            
            if not encrypted or not sign:
                print("❌ 加密或签名失败")
                return False
            
            # 构造请求头
            api_headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'cookie': self.format_cookie_header(cookies),
                'is_app': 'false',
                'origin': 'https://ds-web1.yrpdz.com',
                'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
                'token': 'transfersecret',
                'sign': sign,
                'transfersecret': encrypted
            }
            
            # 构造请求体
            request_body = {encrypted: encrypted}
            
            # 发送API请求
            api_url = f"{self.base_url}/dev-api/api/login/authccode.html"
            
            response = requests.post(
                api_url,
                headers=api_headers,
                json=request_body,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"API请求结果:")
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)}")
            print(f"响应内容: {response.text[:200]}...")
            
            # 检查是否是HTML响应（被重定向）
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("⚠️ 返回HTML页面，可能仍被重定向")
                return False
            else:
                print("✅ 返回非HTML响应，可能是有效的API响应")
                
                # 尝试解密响应
                try:
                    decrypted = crypto.decrypt_data(response.text)
                    if decrypted:
                        print(f"✅ 响应解密成功: {decrypted}")
                    else:
                        print("⚠️ 响应解密失败，可能是明文")
                except:
                    print("⚠️ 响应不是加密格式")
                
                return True
            
        except ImportError:
            print("❌ 无法导入加密系统")
            return False
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            return False

    def run_dynamic_cookie_test(self):
        """运行动态cookie测试"""
        print("🧪 动态Cookie获取测试")
        print("=" * 50)
        
        # 1. 从主页获取cookie
        homepage_cookies = self.get_cookies_from_homepage()
        
        # 2. 从登录页面获取cookie
        login_cookies = self.get_cookies_from_login_page()
        
        # 3. 合并cookie
        all_cookies = {}
        if homepage_cookies:
            all_cookies.update(homepage_cookies)
        if login_cookies:
            all_cookies.update(login_cookies)
        
        print(f"\n📋 最终获取的cookie:")
        for name, value in all_cookies.items():
            print(f"  {name}: {value}")
        
        # 4. 测试API
        if all_cookies:
            success = self.test_api_with_dynamic_cookies(all_cookies)
            
            if success:
                print("\n🎉 动态cookie获取成功！")
                print("可以使用这些cookie进行API请求")
            else:
                print("\n⚠️ 动态cookie可能还不够，需要进一步分析")
        else:
            print("\n❌ 未获取到任何cookie")
        
        return all_cookies


if __name__ == "__main__":
    getter = DynamicCookieGetter()
    cookies = getter.run_dynamic_cookie_test()
