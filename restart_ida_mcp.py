#!/usr/bin/env python3
"""
重启IDA Pro MCP服务器以启用所有41个工具
"""

import json
import http.client
import time
import subprocess
import sys

def check_ida_connection():
    """检查IDA Pro MCP连接"""
    try:
        conn = http.client.HTTPConnection('127.0.0.1', 13337, timeout=5)
        request = {'jsonrpc': '2.0', 'method': 'get_metadata', 'params': [], 'id': 1}
        conn.request('POST', '/mcp', json.dumps(request), {'Content-Type': 'application/json'})
        response = conn.getresponse()
        data = json.loads(response.read().decode())
        conn.close()
        return True, data.get('result', {})
    except Exception as e:
        return False, str(e)

def main():
    print('🔄 IDA Pro MCP 服务器重启助手')
    print('=' * 50)
    
    # 检查当前连接状态
    print('1. 检查当前连接状态...')
    connected, result = check_ida_connection()
    
    if connected:
        print('✅ IDA Pro MCP服务器正在运行')
        print(f'   当前文件: {result.get("path", "Unknown")}')
        print('   但Cursor可能需要重启以识别所有工具')
    else:
        print('❌ IDA Pro MCP服务器未运行')
        print(f'   错误: {result}')
    
    print('\n2. 解决方案:')
    print('   要启用所有41个工具，请按以下步骤操作:')
    print()
    
    print('📋 步骤1: 在IDA Pro中重启MCP插件')
    print('   - 在IDA Pro中，按 Ctrl+Alt+M (或 Edit -> Plugins -> MCP)')
    print('   - 如果插件已运行，先停止它')
    print('   - 然后重新启动插件')
    print('   - 确保插件使用了--unsafe参数')
    print()
    
    print('📋 步骤2: 验证服务器启动')
    print('   - 插件启动后，应该在端口13337上运行')
    print('   - 可以运行此脚本来验证连接')
    print()
    
    print('📋 步骤3: 重启Cursor')
    print('   - 完全关闭Cursor编辑器')
    print('   - 重新启动Cursor')
    print('   - 检查MCP Tools面板')
    print()
    
    print('🔧 当前配置:')
    print('   - Python路径: C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\.venv-clean\\Scripts\\python.exe')
    print('   - 服务器脚本: ida-pro-mcp\\src\\ida_pro_mcp\\server.py')
    print('   - 启用unsafe功能: ✅')
    print('   - 超时时间: 1800秒')
    print()
    
    print('💡 提示:')
    print('   - 确保IDA Pro已加载二进制文件')
    print('   - 某些工具需要在调试模式下才能使用')
    print('   - 修改工具会改变IDA数据库，请谨慎使用')
    print()
    
    # 持续检查连接
    print('🔍 持续检查连接状态 (按Ctrl+C退出):')
    try:
        while True:
            connected, result = check_ida_connection()
            if connected:
                print(f'✅ {time.strftime("%H:%M:%S")} - 连接正常')
                print(f'   文件: {result.get("path", "Unknown")}')
                print('   🎉 服务器运行正常！现在可以重启Cursor了')
                break
            else:
                print(f'❌ {time.strftime("%H:%M:%S")} - 等待连接...')
            time.sleep(3)
    except KeyboardInterrupt:
        print('\n⏹️ 检查已停止')
    
    print('\n✅ 完成！如果服务器正在运行，请重启Cursor以查看所有41个工具')

if __name__ == "__main__":
    main()
