"""
ddddocr库补丁文件，用于解决新版Pillow不支持ANTIALIAS的问题
"""
import os
import sys
from PIL import Image

# 检查Pillow版本
pillow_version = tuple(map(int, Image.__version__.split('.')))

# 如果是Pillow 10.0.0或更高版本，需要修补
if pillow_version >= (10, 0, 0):
    print("检测到Pillow版本 >= 10.0.0，应用ddddocr补丁...")
    
    # 获取ddddocr库的路径
    ddddocr_path = None
    for path in sys.path:
        init_file = os.path.join(path, 'ddddocr', '__init__.py')
        if os.path.exists(init_file):
            ddddocr_path = init_file
            break
    
    if ddddocr_path:
        print(f"找到ddddocr库路径: {ddddocr_path}")
        
        # 读取文件内容
        with open(ddddocr_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换ANTIALIAS为Resampling.LANCZOS
        if 'Image.ANTIALIAS' in content:
            modified_content = content.replace('Image.ANTIALIAS', 'Image.Resampling.LANCZOS')
            
            # 备份原始文件
            backup_path = ddddocr_path + '.bak'
            if not os.path.exists(backup_path):
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"原始文件已备份到: {backup_path}")
            
            # 写入修改后的内容
            with open(ddddocr_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("成功修补ddddocr库，解决ANTIALIAS问题")
        else:
            print("未找到ANTIALIAS代码，可能已经被修改或使用了不同的版本")
    else:
        print("未找到ddddocr库路径，无法应用补丁")
else:
    print(f"当前Pillow版本 {Image.__version__} 不需要修补") 