import sys
from PIL import Image, ImageDraw, ImageFont
import io

def generate_id_card():
    """
    Generates an ID card image by overlaying text and a profile picture on a template.
    The final image is written to stdout as PNG bytes.
    """
    try:
        # --- 字体路径配置 ---
        # 请根据您的系统修改字体文件路径
        # 微软雅黑常规体 (用于主要信息)
        font_path = "C:/Windows/Fonts/msyh.ttc"
        # Courier New (用于身份证号码)
        mono_font_path = "C:/Windows/Fonts/cour.ttf"

        # 加载背景和头像模板
        background = Image.open("id_card_template.png")
        profile_pic = Image.open("profile_image.png")

        # 创建一个可绘制对象
        draw = ImageDraw.Draw(background)

        # 定义字体和颜色
        # 尝试加载系统字体，如果失败，则使用Pillow的默认字体
        try:
            main_font = ImageFont.truetype(font_path, size=22)
            id_font = ImageFont.truetype(mono_font_path, size=28)
        except IOError:
            main_font = ImageFont.load_default()
            id_font = ImageFont.load_default()
            print("Warning: Custom fonts not found, using default font.", file=sys.stderr)

        text_color = "#333333"

        # 绘制个人信息文本
        draw.text((205, 107), "张三", font=main_font, fill=text_color)
        draw.text((205, 150), "男", font=main_font, fill=text_color)
        draw.text((330, 150), "汉", font=main_font, fill=text_color)
        draw.text((205, 193), "1995", font=main_font, fill=text_color)
        draw.text((290, 193), "7", font=main_font, fill=text_color)
        draw.text((350, 193), "17", font=main_font, fill=text_color)
        
        # 绘制地址
        draw.text((205, 236), "四川省成都市武侯区益州", font=main_font, fill=text_color)
        draw.text((205, 266), "大道中段722号复城国际", font=main_font, fill=text_color)

        # 绘制身份证号码
        draw.text((360, 432), "513701199507176346", font=id_font, fill=text_color)

        # 将头像粘贴到背景上
        # 注意：这里的坐标和尺寸需要精确匹配模板
        background.paste(profile_pic, (650, 100))

        # 将最终图片保存到内存中的一个bytes buffer
        img_byte_arr = io.BytesIO()
        background.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()

        # 将图片数据写入标准输出
        sys.stdout.buffer.write(img_byte_arr)

    except FileNotFoundError as e:
        print(f"Error: Asset file not found. Make sure 'id_card_template.png' and 'profile_image.png' are in the root directory. Details: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    generate_id_card() 