{"version": 3, "file": "compile.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["compile.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAY,YAAY,EAAE,MAAM,UAAU,CAAC;AACzD,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,SAAS,EAAE,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,cAAc,CAAC;AACtD,OAAO,EACH,WAAW,EACX,mBAAmB,GACtB,MAAM,kCAAkC,CAAC;AAO1C;;;;;;GAMG;AACH,MAAM,UAAU,OAAO,CACnB,QAA+B,EAC/B,OAA2C,EAC3C,OAAuB;IAEvB,MAAM,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,OAAO,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,aAAa,CACzB,QAA+B,EAC/B,OAA2C,EAC3C,OAAuB;IAEvB,MAAM,KAAK,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACxE,OAAO,YAAY,CAAoB,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,mBAAmB,CAAC,CAAmB;IAC5C,OAAO,CACH,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM;QAC9B,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;YACf,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;gBAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;AACN,CAAC;AAED,MAAM,gBAAgB,GAAa,EAAE,IAAI,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC;AACrE,MAAM,yBAAyB,GAAqB;IAChD,IAAI,EAAE,qBAAqB;CAC9B,CAAC;AACF,MAAM,WAAW,GAAa;IAC1B,IAAI,EAAE,YAAY,CAAC,MAAM;IACzB,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,IAAI;CACb,CAAC;AAEF;;;GAGG;AACH,SAAS,UAAU,CACf,KAA2B,EAC3B,EAAE,OAAO,EAAsC,EAC/C,OAAgB;IAEhB,qDAAqD;IACrD,MAAM,UAAU,GAAG,CAAC,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;QACtC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxD,OAAO,CAAC,KAAK,mBAAmB,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAA,CAAC;IAEH,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;QACnB,IACI,CAAC,CAAC,MAAM,GAAG,CAAC;YACZ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,UAAU,EACvC;YACE,gCAAgC;SACnC;aAAM,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE;YACnD,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SAC/B;aAAM;YACH,SAAS;SACZ;QAED,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KAC1B;AACL,CAAC;AAED,MAAM,UAAU,YAAY,CACxB,KAA2B,EAC3B,OAA2C,EAC3C,OAAuB;;IAEvB,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAEzB,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,OAAO,CAAC;IACrC,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE9C,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9D,oCAAoC;IACpC,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE;QACpC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;KAC5C;SAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D,MAAM,IAAI,KAAK,CACX,mFAAmF,CACtF,CAAC;KACL;IAED,IAAI,sBAAsB,GAAG,KAAK,CAAC;IAEnC,MAAM,KAAK,GAAG,KAAK;SACd,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACX,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;YAE9B,IACI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM;gBAClC,KAAK,CAAC,IAAI,KAAK,OAAO,EACxB;gBACE,SAAS;aACZ;iBAAM,IACH,cAAc;gBACd,MAAM,CAAC,IAAI,KAAK,YAAY,CAAC,UAAU,EACzC;gBACE,KAAK,CAAC,CAAC,CAAC,GAAG,yBAAyB,CAAC;aACxC;iBAAM,IACH,MAAM,CAAC,IAAI,KAAK,YAAY,CAAC,QAAQ;gBACrC,MAAM,CAAC,IAAI,KAAK,YAAY,CAAC,OAAO,EACtC;gBACE,sBAAsB,GAAG,IAAI,CAAC;aACjC;SACJ;QAED,OAAO,YAAY,CACf,KAAK,EACL,OAAO,EACP,YAAY,CACf,CAAC;IACN,CAAC,CAAC;SACD,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;IAE7C,KAAK,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IAEtD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,YAAY,CACjB,KAAyB,EACzB,OAA2C,EAC3C,OAAgB;;IAEhB,OAAO,KAAK,CAAC,MAAM,CACf,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,CACf,QAAQ,KAAK,QAAQ,CAAC,SAAS;QAC3B,CAAC,CAAC,QAAQ,CAAC,SAAS;QACpB,CAAC,CAAC,sBAAsB,CAClB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EACP,YAAY,CACf,EACX,MAAA,OAAO,CAAC,QAAQ,mCAAI,QAAQ,CAAC,QAAQ,CACxC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAChB,CAA6B,EAC7B,CAA6B;IAE7B,IAAI,CAAC,KAAK,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,QAAQ,CAAC,QAAQ,EAAE;QACrD,OAAO,CAAC,CAAC;KACZ;IACD,IAAI,CAAC,KAAK,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,QAAQ,CAAC,QAAQ,EAAE;QACrD,OAAO,CAAC,CAAC;KACZ;IAED,OAAO,SAAS,OAAO,CAAC,IAAI;QACxB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC;AACN,CAAC"}