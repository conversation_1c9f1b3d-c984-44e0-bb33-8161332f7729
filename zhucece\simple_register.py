#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国稳定币APP注册系统 - 简化版
专注于验证码获取、识别和注册请求
"""

import requests
import json
import time
import hashlib
import base64
import logging
import ddddocr
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CryptoManager:
    """加密管理器 - 基于逆向分析"""

    def __init__(self):
        # 从逆向分析得到的AES密钥: "46505501ee188273"
        self.aes_key = "46505501ee188273".encode('utf-8')
        self.base_url = "https://zgwdb-app1.rizi666.com/dev-api"

    def aes_encrypt(self, data: dict) -> str:
        """AES加密 - ECB模式 PKCS7填充"""
        try:
            # 转换为JSON字符串 (与JS的JSON.stringify保持一致)
            json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)

            # 创建AES加密器 (ECB模式)
            cipher = AES.new(self.aes_key, AES.MODE_ECB)

            # 填充并加密
            padded_data = pad(json_str.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_data)

            # Base64编码
            encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')

            logger.info(f"AES加密成功: {encrypted_b64}")
            return encrypted_b64

        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            return ""

    def aes_decrypt(self, encrypted_data: str) -> dict:
        """AES解密"""
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)

            # 创建AES解密器
            cipher = AES.new(self.aes_key, AES.MODE_ECB)

            # 解密并去填充
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)

            # 转换为字典
            json_str = unpadded_data.decode('utf-8')
            result = json.loads(json_str)

            logger.info(f"AES解密成功: {result}")
            return result

        except Exception as e:
            logger.error(f"AES解密失败: {e}")
            return {}

    def generate_md5(self, data: str) -> str:
        """生成MD5哈希"""
        return hashlib.md5(data.encode('utf-8')).hexdigest()

class SimpleRegisterBot:
    """简化版注册机器人 - 增强反反爬能力"""

    def __init__(self):
        self.crypto = CryptoManager()
        self.session = requests.Session()

        # 设置会话的通用配置
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
        })

        # 初始化会话 - 获取必要的Cookie
        self._initialize_session()

        # 验证码识别器
        try:
            self.ocr = ddddocr.DdddOcr()
            logger.info("验证码识别器初始化成功")
        except Exception as e:
            logger.warning(f"验证码识别器初始化失败: {e}")
            self.ocr = None

    def _initialize_session(self):
        """初始化会话，获取必要的Cookie和建立连接"""
        try:
            logger.info("初始化会话，访问主页获取Cookie...")

            # 首先访问主页，建立会话
            main_page_url = "https://zgwdb-app1.rizi666.com/pages/login/login"
            headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Upgrade-Insecure-Requests": "1"
            }

            response = self.session.get(main_page_url, headers=headers, timeout=10)
            logger.info(f"主页访问状态码: {response.status_code}")
            logger.info(f"获取到的Cookie: {dict(self.session.cookies)}")

            # 如果没有获取到关键Cookie，尝试设置备用Cookie
            if not self.session.cookies.get('acw_tc'):
                logger.info("未获取到acw_tc Cookie，设置备用Cookie...")
                # 这些是从真实浏览器会话中提取的示例Cookie
                backup_cookies = {
                    'acw_tc': 'b482661517532890784836565e37aa615429b0c587bd4e60703e7996f1'
                }
                for name, value in backup_cookies.items():
                    self.session.cookies.set(name, value)

            return True

        except Exception as e:
            logger.error(f"会话初始化失败: {e}")
            return False

    def get_captcha(self):
        """获取验证码"""
        try:
            logger.info("=== 开始获取验证码 ===")

            # 当前时间戳 - 使用当前时间
            timestamp = int(time.time())
            logger.info(f"验证码请求时间戳: {timestamp}")

            # 请求数据
            request_data = {"time": timestamp}
            logger.info(f"请求数据: {request_data}")

            # AES加密请求数据
            encrypted_data = self.crypto.aes_encrypt(request_data)
            logger.info(f"AES加密后的数据: {encrypted_data}")

            # 生成MD5签名
            sign = self.crypto.generate_md5(f"time={timestamp}")
            logger.info(f"生成的签名: {sign}")

            # 构建请求头 - 完全按照真实请求（无Cookie）
            headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "content-type": "application/json",
                "origin": "https://zgwdb-app1.rizi666.com",
                "priority": "u=1, i",
                "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sign": sign,
                "token": "",
                "transfersecret": encrypted_data,
                "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
            }

            # 发送请求
            url = f"{self.crypto.base_url}/api/login/authccode.html"
            logger.info(f"请求URL: {url}")

            response = self.session.post(url, headers=headers, json=request_data, timeout=10)

            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"解析后的响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                    if result.get("code") == 200:
                        logger.info("✅ 验证码请求成功")
                        return True, result.get("data", {})
                    else:
                        logger.error(f"❌ 验证码请求失败: {result.get('message', '未知错误')}")
                        return False, {}
                except json.JSONDecodeError as e:
                    logger.error(f"❌ 响应JSON解析失败: {e}")
                    return False, {}
            else:
                logger.error(f"❌ HTTP请求失败: {response.status_code}")
                return False, {}

        except Exception as e:
            logger.error(f"❌ 验证码请求异常: {e}")
            return False, {}

    def recognize_captcha_from_base64(self, base64_data: str) -> str:
        """从Base64数据识别验证码"""
        try:
            if not self.ocr:
                logger.error("验证码识别器未初始化")
                return ""

            logger.info("开始识别Base64验证码")

            # 处理Base64数据
            if base64_data.startswith("data:image"):
                base64_data = base64_data.split(",")[1]

            # 解码图片
            image_bytes = base64.b64decode(base64_data)

            # 保存图片用于调试
            with open("captcha_debug.png", "wb") as f:
                f.write(image_bytes)
            logger.info("验证码图片已保存为 captcha_debug.png")

            # 识别验证码
            result = self.ocr.classification(image_bytes)
            logger.info(f"验证码识别结果: {result}")
            return result

        except Exception as e:
            logger.error(f"验证码识别异常: {e}")
            return ""

    def register_account(self, phone: str, password: str, captcha: str, captcha_key: str = ""):
        """注册账号"""
        try:
            logger.info("=== 开始注册账号 ===")
            logger.info(f"手机号: {phone}")
            logger.info(f"密码: {password}")
            logger.info(f"验证码: {captcha}")
            logger.info(f"验证码key: {captcha_key}")

            # 当前时间戳 - 使用当前时间（与验证码请求不同的时间）
            timestamp = int(time.time())
            logger.info(f"注册请求时间戳: {timestamp}")

            # 注册数据 - 基于抓包分析的格式
            register_data = {
                "mobile": phone,
                "password": password,
                "confirm_password": password,
                "invitation_code": "SGA5TU",  # 默认邀请码
                "code": captcha,
                "key": captcha_key,
                "time": timestamp
            }
            logger.info(f"注册数据: {register_data}")

            # AES加密注册数据
            encrypted_data = self.crypto.aes_encrypt(register_data)
            logger.info(f"AES加密后的数据: {encrypted_data}")

            # 生成MD5签名
            sign_str = f"mobile={phone}&password={password}&time={timestamp}"
            sign = self.crypto.generate_md5(sign_str)
            logger.info(f"生成的签名: {sign}")

            # 构建请求头 - 完全按照真实请求（无Cookie）
            headers = {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br, zstd",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "content-type": "application/json",
                "origin": "https://zgwdb-app1.rizi666.com",
                "priority": "u=1, i",
                "referer": "https://zgwdb-app1.rizi666.com/pages/login/login",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sign": sign,
                "token": "",
                "transfersecret": encrypted_data,
                "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
            }

            # 发送注册请求 - 请求体只包含时间戳
            url = f"{self.crypto.base_url}/api/login/register.html"
            logger.info(f"请求URL: {url}")
            
            request_body = {"time": timestamp}
            response = self.session.post(url, headers=headers, json=request_body, timeout=10)

            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"解析后的响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                    if result.get("code") == 200:
                        logger.info("✅ 注册请求成功")
                        return True, result
                    else:
                        logger.error(f"❌ 注册请求失败: {result.get('message', '未知错误')}")
                        return False, result
                except json.JSONDecodeError as e:
                    logger.error(f"❌ 响应JSON解析失败: {e}")
                    return False, {}
            else:
                logger.error(f"❌ HTTP请求失败: {response.status_code}")
                return False, {}

        except Exception as e:
            logger.error(f"❌ 注册请求异常: {e}")
            return False, {}

def main():
    """主函数 - 完整流程测试"""
    logger.info("🚀 启动中国稳定币APP注册测试")

    # 创建注册机器人
    bot = SimpleRegisterBot()

    # 步骤1: 获取验证码
    logger.info("\n" + "="*50)
    logger.info("步骤1: 获取验证码")
    logger.info("="*50)

    success, captcha_data = bot.get_captcha()

    if success:
        logger.info("✅ 验证码获取成功")
        
        # 步骤2: 识别验证码
        logger.info("\n" + "="*50)
        logger.info("步骤2: 识别验证码")
        logger.info("="*50)
        
        captcha_text = ""
        captcha_key = ""
        
        # 尝试从不同字段获取验证码图片
        if "image" in captcha_data:
            captcha_text = bot.recognize_captcha_from_base64(captcha_data["image"])
        elif "captcha" in captcha_data:
            captcha_text = bot.recognize_captcha_from_base64(captcha_data["captcha"])
        elif "data" in captcha_data:
            captcha_text = bot.recognize_captcha_from_base64(captcha_data["data"])
            
        # 获取验证码key
        if "key" in captcha_data:
            captcha_key = captcha_data["key"]

        if captcha_text:
            logger.info(f"✅ 验证码识别成功: {captcha_text}")

            # 等待几秒，模拟真实用户行为
            logger.info("等待3秒，模拟用户输入验证码的时间...")
            time.sleep(3)

            # 步骤3: 注册账号
            logger.info("\n" + "="*50)
            logger.info("步骤3: 注册账号")
            logger.info("="*50)

            # 使用测试数据
            test_phone = "***********"
            test_password = "test123456"

            success, result = bot.register_account(test_phone, test_password, captcha_text, captcha_key)

            if success:
                logger.info("✅ 注册成功")
                logger.info(f"注册结果: {result}")
            else:
                logger.error("❌ 注册失败")
                logger.error(f"失败原因: {result}")
        else:
            logger.error("❌ 验证码识别失败，请手动处理")
            logger.info(f"验证码数据: {captcha_data}")
    else:
        logger.error("❌ 验证码获取失败")

if __name__ == "__main__":
    main()
