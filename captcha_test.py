#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码接口专项测试
"""

import requests
import urllib3
import json
from decrypt_analysis import EncryptionSystem

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_captcha_api():
    print("🧪 验证码接口专项测试")
    print("=" * 40)
    
    # 初始化加密系统
    crypto = EncryptionSystem()
    
    # 使用您提供的真实cookie
    real_cookies = "acw_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881; cdn_sec_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881"
    
    # 测试数据（空对象，就像浏览器中的请求）
    test_data = {}
    
    # 加密和签名
    encrypted = crypto.encrypt_data(test_data)
    sign = crypto.generate_sign(test_data)
    
    print(f"📤 测试数据: {test_data}")
    print(f"🔐 加密结果: {encrypted}")
    print(f"✍️ 签名结果: {sign}")
    
    # 完全模拟浏览器的请求头
    headers = {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'content-type': 'application/json',
        'cookie': real_cookies,
        'is_app': 'false',
        'origin': 'https://ds-web1.yrpdz.com',
        'priority': 'u=1, i',
        'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'token': 'transfersecret',  # 使用原始的token值
        'sign': sign,
        'transfersecret': encrypted
    }
    
    # 请求体（完全按照浏览器格式）
    request_body = {encrypted: encrypted}
    
    print(f"\n📋 请求头信息:")
    for key, value in headers.items():
        if key in ['sign', 'transfersecret']:
            print(f"  {key}: {value}")
        elif key == 'cookie':
            print(f"  {key}: {value[:50]}...")
        else:
            print(f"  {key}: {value}")
    
    print(f"\n📦 请求体: {request_body}")
    
    try:
        # 发送请求
        response = requests.post(
            "https://ds-web1.yrpdz.com/dev-api/api/login/authccode.html",
            headers=headers,
            json=request_body,
            timeout=15,
            verify=False
        )
        
        print(f"\n📥 响应信息:")
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应头: {dict(response.headers)}")
        
        # 检查响应内容
        if response.text.strip().startswith('<!DOCTYPE html>'):
            print("❌ 返回HTML页面，可能被反爬虫拦截")
            print(f"HTML内容前200字符: {response.text[:200]}")
        else:
            print("✅ 返回非HTML响应")
            print(f"完整响应: {response.text}")
            
            # 尝试解密
            try:
                decrypted = crypto.decrypt_data(response.text)
                if decrypted:
                    print(f"🔓 解密成功: {decrypted}")
                else:
                    print("⚠️ 解密失败，可能是明文")
            except Exception as e:
                print(f"⚠️ 解密异常: {e}")
        
        return response.status_code == 200 and not response.text.strip().startswith('<!DOCTYPE html>')
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_different_tokens():
    """测试不同的token值"""
    print("\n🔄 测试不同Token值")
    print("-" * 30)
    
    tokens_to_test = [
        "transfersecret",
        "",
        "null",
        "undefined"
    ]
    
    crypto = EncryptionSystem()
    real_cookies = "acw_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881; cdn_sec_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881"
    
    for token in tokens_to_test:
        print(f"\n🧪 测试Token: '{token}'")
        
        test_data = {}
        encrypted = crypto.encrypt_data(test_data)
        sign = crypto.generate_sign(test_data)
        
        headers = {
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'content-type': 'application/json',
            'cookie': real_cookies,
            'is_app': 'false',
            'origin': 'https://ds-web1.yrpdz.com',
            'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'token': token,
            'sign': sign,
            'transfersecret': encrypted
        }
        
        request_body = {encrypted: encrypted}
        
        try:
            response = requests.post(
                "https://ds-web1.yrpdz.com/dev-api/api/login/authccode.html",
                headers=headers,
                json=request_body,
                timeout=10,
                verify=False
            )
            
            print(f"  状态码: {response.status_code}")
            
            if response.text.strip().startswith('<!DOCTYPE html>'):
                print("  ❌ 返回HTML")
            else:
                print("  ✅ 返回API响应")
                print(f"  响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")

if __name__ == "__main__":
    # 测试验证码接口
    success = test_captcha_api()
    
    if not success:
        # 如果失败，测试不同的token值
        test_different_tokens()
    
    print(f"\n🎯 测试完成")
    if success:
        print("✅ 验证码接口测试成功")
    else:
        print("❌ 验证码接口仍有问题，需要进一步分析")
