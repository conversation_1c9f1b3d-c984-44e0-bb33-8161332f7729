#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 代理配置脚本 - 用于配置51代理的API参数

import json
import os

def configure_proxy():
    """配置代理参数"""
    print("🌐 51代理配置向导")
    print("=" * 40)
    
    print("请输入您的51代理账号信息:")
    print("(可以在51代理后台 -> API提取 页面找到这些信息)")
    
    # 获取用户输入
    packid = input("\n📦 套餐ID (packid): ").strip()
    uid = input("👤 用户ID (uid): ").strip()
    access_name = input("🔑 权限校验名称 (accessName): ").strip()
    access_password = input("🔐 权限校验密码 (accessPassword): ").strip()
    
    print("\n📋 协议选择:")
    print("1. HTTP (推荐)")
    print("2. SOCKS5")
    protocol_choice = input("请选择协议 (1-2, 默认1): ").strip()
    protocol = 'socks5' if protocol_choice == '2' else 'http'
    
    time_duration = input(f"\n⏰ 代理时长(分钟, 默认10): ").strip()
    if not time_duration.isdigit():
        time_duration = "10"
    
    # 构造配置
    config = {
        'enabled': True,
        'packid': packid,
        'uid': uid,
        'access_name': access_name,
        'access_password': access_password,
        'protocol': protocol,
        'time_duration': int(time_duration)
    }
    
    # 显示配置摘要
    print("\n📋 配置摘要:")
    print(f"   套餐ID: {packid}")
    print(f"   用户ID: {uid}")
    print(f"   权限名称: {access_name}")
    print(f"   权限密码: {access_password[:10]}...")
    print(f"   协议: {protocol.upper()}")
    print(f"   时长: {time_duration}分钟")
    
    # 确认配置
    confirm = input("\n✅ 确认配置并测试? (Y/N, 默认Y): ").strip().upper()
    if confirm == 'N':
        print("❌ 配置已取消")
        return False
    
    # 测试代理
    print("\n🧪 正在测试代理配置...")
    success = test_proxy_config(config)
    
    if success:
        # 保存配置
        save_config_to_file(config)
        print("\n🎉 代理配置成功!")
        print("💡 现在可以运行主脚本使用代理功能")
        return True
    else:
        print("\n❌ 代理测试失败，请检查配置")
        return False

def test_proxy_config(config):
    """测试代理配置"""
    try:
        # 导入代理管理器
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from zhuccc1 import ProxyManager
        
        # 创建代理管理器
        proxy_manager = ProxyManager(
            packid=config['packid'],
            uid=config['uid'],
            access_name=config['access_name'],
            access_password=config['access_password'],
            protocol=config['protocol'],
            time_duration=config['time_duration']
        )
        
        # 获取代理
        proxy_dict = proxy_manager.get_proxy(force_new=True)
        
        if proxy_dict:
            print("✅ 代理获取成功")
            
            # 测试代理连接
            print("🔍 测试代理连接...")
            import requests
            
            test_url = "https://httpbin.org/ip"
            response = requests.get(test_url, proxies=proxy_dict, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 代理连接测试成功")
                print(f"   代理IP: {result.get('origin', '未知')}")
                return True
            else:
                print(f"❌ 代理连接测试失败: HTTP {response.status_code}")
                return False
        else:
            print("❌ 代理获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试异常: {e}")
        return False

def save_config_to_file(config):
    """保存配置到文件"""
    try:
        # 读取现有的zhuccc1.py文件
        with open('zhuccc1.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 构造新的配置字符串
        new_config = f"""PROXY_CONFIG = {{
    'enabled': {config['enabled']},
    'packid': "{config['packid']}",
    'uid': "{config['uid']}", 
    'access_name': "{config['access_name']}",
    'access_password': "{config['access_password']}",
    'protocol': '{config['protocol']}',
    'time_duration': {config['time_duration']}
}}"""
        
        # 替换配置
        import re
        pattern = r'PROXY_CONFIG = \{[^}]*\}'
        if re.search(pattern, content, re.DOTALL):
            new_content = re.sub(pattern, new_config, content, flags=re.DOTALL)
        else:
            print("❌ 未找到PROXY_CONFIG配置块")
            return False
        
        # 写回文件
        with open('zhuccc1.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("💾 配置已保存到 zhuccc1.py")
        return True
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def disable_proxy():
    """禁用代理功能"""
    try:
        # 读取现有的zhuccc1.py文件
        with open('zhuccc1.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 将enabled设置为False
        import re
        pattern = r"'enabled': True"
        new_content = re.sub(pattern, "'enabled': False", content)
        
        # 写回文件
        with open('zhuccc1.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 代理功能已禁用")
        return True
        
    except Exception as e:
        print(f"❌ 禁用代理失败: {e}")
        return False

def main():
    """主函数"""
    print("🌐 环球网站注册脚本 - 代理配置工具")
    print("🔧 用于配置51代理的API参数")
    print("=" * 50)
    
    print("请选择操作:")
    print("1. 配置并启用代理")
    print("2. 禁用代理功能")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        configure_proxy()
    elif choice == '2':
        disable_proxy()
    elif choice == '3':
        print("👋 退出配置工具")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 配置被用户中断")
    except Exception as e:
        print(f"\n💥 配置过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
