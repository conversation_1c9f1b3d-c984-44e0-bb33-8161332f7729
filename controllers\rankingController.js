const Deal = require('../models/dealModel');
const User = require('../models/userModel');

// @desc    热门活动排行榜
// @route   GET /api/rankings/hot-deals
// @access  Public
exports.getHotDeals = async (req, res) => {
  try {
    const deals = await Deal.find({ status: 'active' })
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name')
      .sort({ likes: -1, views: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      count: deals.length,
      data: deals
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    最新活动排行榜
// @route   GET /api/rankings/new-deals
// @access  Public
exports.getNewDeals = async (req, res) => {
  try {
    const deals = await Deal.find({ status: 'active' })
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      count: deals.length,
      data: deals
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    优惠最高排行榜
// @route   GET /api/rankings/best-discount
// @access  Public
exports.getBestDiscountDeals = async (req, res) => {
  try {
    const deals = await Deal.find({ 
      status: 'active',
      discountAmount: { $gt: 0 }
    })
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name')
      .sort({ discountAmount: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      count: deals.length,
      data: deals
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    成功率最高排行榜
// @route   GET /api/rankings/most-successful
// @access  Public
exports.getMostSuccessfulDeals = async (req, res) => {
  try {
    const deals = await Deal.find({ 
      status: 'active',
      successReports: { $gt: 0 }
    })
      .populate({
        path: 'author',
        select: 'username avatar creditScore level'
      })
      .populate('category', 'name');
    
    // 计算成功率并排序
    const dealsWithRate = deals.map(deal => {
      const totalReports = deal.successReports + deal.failureReports;
      const successRate = totalReports > 0 
        ? (deal.successReports / totalReports) * 100 
        : 0;
      
      return {
        ...deal.toObject(),
        successRate: Math.round(successRate)
      };
    }).sort((a, b) => b.successRate - a.successRate || b.successReports - a.successReports)
      .slice(0, 10);

    res.status(200).json({
      success: true,
      count: dealsWithRate.length,
      data: dealsWithRate
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    用户贡献排行榜
// @route   GET /api/rankings/top-contributors
// @access  Public
exports.getTopContributors = async (req, res) => {
  try {
    const users = await User.find()
      .select('username avatar contributionPoints level creditScore')
      .sort({ contributionPoints: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
};

// @desc    用户信用排行榜
// @route   GET /api/rankings/top-credit
// @access  Public
exports.getTopCreditUsers = async (req, res) => {
  try {
    const users = await User.find()
      .select('username avatar creditScore level contributionPoints')
      .sort({ creditScore: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err.message
    });
  }
}; 