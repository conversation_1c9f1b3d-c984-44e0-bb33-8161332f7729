{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../../src/encode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAExD,MAAM,YAAY,GAAG,sCAAsC,CAAC;AAE5D;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CAAC,KAAa;IACpC,OAAO,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AACD;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAa;IAC5C,OAAO,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc,EAAE,KAAa;IACnD,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QACxB,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,kDAAkD;YAClD,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC7C,MAAM,KAAK,GACP,OAAO,IAAI,CAAC,CAAC,KAAK,QAAQ;oBACtB,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,QAAQ;wBACjB,CAAC,CAAC,IAAI,CAAC,CAAC;wBACR,CAAC,CAAC,SAAS;oBACf,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAE/B,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,WAAW,IAAI,KAAK,CAAC;oBACrB,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;oBAClC,SAAS;gBACb,CAAC;YACL,CAAC;YAED,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,4EAA4E;QAC5E,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACrB,MAAM,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACtC,WAAW,IAAI,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YACxC,4CAA4C;YAC5C,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACJ,WAAW,IAAI,IAAI,CAAC;YACpB,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjD,CAAC"}