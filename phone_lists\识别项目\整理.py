import os
import shutil
from pathlib import Path
from cnocr import CnOcr
import cv2
import json
from datetime import datetime
import numpy as np

class IDCardOrganizer:
    def __init__(self, source_dir, output_dir):
        """
        初始化身份证图片整理器
        :param source_dir: 源图片目录
        :param output_dir: 输出目录
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)

        if self.output_dir.is_file():
            raise ValueError("错误：输出路径不能是文件，请提供一个目录路径。")

        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 创建正面和反面文件夹
        self.front_dir = self.output_dir / "正面"
        self.back_dir = self.output_dir / "反面"
        self.unknown_dir = self.output_dir / "未识别"

        self.front_dir.mkdir(exist_ok=True)
        self.back_dir.mkdir(exist_ok=True)
        self.unknown_dir.mkdir(exist_ok=True)

        # 初始化CnOcr引擎，并使用中文模型
        self.ocr = CnOcr(det_model_name='ch_PP-OCRv3_det', rec_model_name='ch_PP-OCRv3')

        self.img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']

        # 身份证正面关键词
        self.front_keywords = [
            '中华人民共和国', '居民身份证', '姓名', '性别', '民族',
            '出生', '住址', '公民身份号码', '身份证号码'
        ]

        # 身份证反面关键词
        self.back_keywords = [
            '签发机关', '有效期限', '公安局', '派出所',
            '有效期', '长期', '签发日期'
        ]
        
    def _preprocess_image(self, img_np):
        """对图像进行预处理，仅转换为灰度图"""
        if img_np is None:
            return None
        # 仅转换为灰度图，保留所有亮度信息
        gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
        return gray

    def is_image_file(self, file_path):
        """判断文件是否为图片"""
        return file_path.suffix.lower() in self.img_extensions

    def identify_id_card_side(self, ocr_results):
        """
        根据OCR结果识别身份证正反面
        :param ocr_results: OCR识别结果列表
        :return: 'front' (正面), 'back' (反面), 'unknown' (未识别)
        """
        if not ocr_results:
            return 'unknown'

        # 提取所有识别到的文字
        all_text = ' '.join([result.get('text', '') for result in ocr_results])

        front_score = 0
        back_score = 0

        # 检查正面关键词
        for keyword in self.front_keywords:
            if keyword in all_text:
                front_score += 1

        # 检查反面关键词
        for keyword in self.back_keywords:
            if keyword in all_text:
                back_score += 1

        # 判断正反面
        if front_score > back_score and front_score >= 2:
            return 'front'
        elif back_score > front_score and back_score >= 1:
            return 'back'
        else:
            return 'unknown'
    
    def process_single_image(self, img_path):
        """
        处理单个图片文件，识别身份证正反面并分类
        :param img_path: 图片路径
        :return: 识别结果和分类结果
        """
        img_path = Path(img_path)
        if not img_path.is_file():
            print(f"错误：文件不存在: {img_path}")
            return None

        if not self.is_image_file(img_path):
            print(f"错误：不是支持的图片格式: {img_path}")
            return None

        print(f"正在处理图片: {img_path.name}")

        try:
            # 预先读取图片为numpy数组
            img_np = self.imread_with_unicode(str(img_path))
            if img_np is None:
                print(f"错误：无法读取图片 {img_path.name}")
                return None

            # 对图像进行预处理
            processed_img_np = self._preprocess_image(img_np)

            # 将预处理后的图片数据传递给CnOcr
            result = self.ocr.ocr(processed_img_np)

            if result:
                # --- 结果后处理 ---
                filtered_results = []
                confidence_threshold = 0.5  # 降低置信度阈值以获取更多文字
                min_text_length = 1         # 降低最小文本长度

                for line in result:
                    confidence = line.get('score', 0.0)
                    text = "".join(line['text']).strip()

                    # 应用过滤规则
                    if confidence >= confidence_threshold and len(text) >= min_text_length:
                        filtered_results.append({
                            "text": text,
                            "confidence": float(confidence),
                            "position": line['position']
                        })

                if not filtered_results:
                    print(f"未在图片中识别到有效文字: {img_path.name}")
                    # 移动到未识别文件夹
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    print(f"图片已移动到未识别文件夹")
                    return None

                # 识别身份证正反面
                side = self.identify_id_card_side(filtered_results)

                # 根据识别结果选择目标文件夹
                if side == 'front':
                    target_dir = self.front_dir
                    side_name = "正面"
                elif side == 'back':
                    target_dir = self.back_dir
                    side_name = "反面"
                else:
                    target_dir = self.unknown_dir
                    side_name = "未识别"

                # 复制图片到对应文件夹
                target_path = target_dir / img_path.name
                # 如果文件名已存在，添加时间戳
                if target_path.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    target_path = target_dir / f"{timestamp}_{img_path.name}"

                shutil.copy2(img_path, target_path)

                print(f"识别为身份证{side_name}，已移动到: {target_path}")

                # 显示识别到的关键文字
                text_results = [{"text": r["text"], "confidence": r["confidence"]} for r in filtered_results]
                print("识别到的文字:")
                for item in text_results:
                    print(f"- {item['text']} (置信度: {item['confidence']:.4f})")

                return {"side": side, "texts": text_results, "target_path": str(target_path)}

            else:
                print(f"未在图片中识别到文字: {img_path.name}")
                # 移动到未识别文件夹
                shutil.copy2(img_path, self.unknown_dir / img_path.name)
                print(f"图片已移动到未识别文件夹")
                return None

        except Exception as e:
            print(f"处理图片 {img_path.name} 时出错: {str(e)}")
            return None
        
    def process_images(self):
        """批量处理所有图片文件，识别身份证正反面并分类"""
        image_files = []
        for file_path in self.source_dir.glob('**/*'):
            if file_path.is_file() and self.is_image_file(file_path):
                image_files.append(file_path)

        print(f"找到 {len(image_files)} 个图片文件")

        # 统计结果
        front_count = 0
        back_count = 0
        unknown_count = 0

        for idx, img_path in enumerate(image_files):
            print(f"\n处理图片 {idx+1}/{len(image_files)}: {img_path.name}")

            try:
                # 预先读取图片为numpy数组
                img_np = self.imread_with_unicode(str(img_path))
                if img_np is None:
                    print(f"  错误: 无法读取图片 {img_path.name}, 已跳过。")
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    unknown_count += 1
                    continue

                # 对图像进行预处理
                processed_img_np = self._preprocess_image(img_np)

                result = self.ocr.ocr(processed_img_np)

                if result:
                    # --- 结果后处理 ---
                    filtered_results = []
                    confidence_threshold = 0.5  # 降低置信度阈值
                    min_text_length = 1

                    for line in result:
                        confidence = line.get('score', 0.0)
                        text = "".join(line['text']).strip()

                        if confidence >= confidence_threshold and len(text) >= min_text_length:
                            filtered_results.append({
                                "text": text,
                                "confidence": float(confidence),
                                "position": line['position']
                            })

                    if not filtered_results:
                        print(f"  未在图片中识别到有效文字: {img_path.name}")
                        shutil.copy2(img_path, self.unknown_dir / img_path.name)
                        unknown_count += 1
                        continue

                    # 识别身份证正反面
                    side = self.identify_id_card_side(filtered_results)

                    # 根据识别结果选择目标文件夹
                    if side == 'front':
                        target_dir = self.front_dir
                        side_name = "正面"
                        front_count += 1
                    elif side == 'back':
                        target_dir = self.back_dir
                        side_name = "反面"
                        back_count += 1
                    else:
                        target_dir = self.unknown_dir
                        side_name = "未识别"
                        unknown_count += 1

                    # 复制图片到对应文件夹
                    target_path = target_dir / img_path.name
                    # 如果文件名已存在，添加时间戳
                    if target_path.exists():
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        target_path = target_dir / f"{timestamp}_{img_path.name}"

                    shutil.copy2(img_path, target_path)
                    print(f"  识别为身份证{side_name}，已移动到: {target_path.name}")

                else:
                    print(f"  未在图片中识别到文字: {img_path.name}")
                    shutil.copy2(img_path, self.unknown_dir / img_path.name)
                    unknown_count += 1

            except Exception as e:
                print(f"  处理图片 {img_path.name} 时出错: {str(e)}")
                shutil.copy2(img_path, self.unknown_dir / img_path.name)
                unknown_count += 1

        print(f"\n所有图片处理完成！")
        print(f"统计结果:")
        print(f"  身份证正面: {front_count} 张")
        print(f"  身份证反面: {back_count} 张")
        print(f"  未识别: {unknown_count} 张")
        print(f"结果保存在: {self.output_dir}")
        
    def _draw_ocr_results(self, original_img_path, output_img_path, ocr_result):
        """在图片上标注OCR识别结果"""
        try:
            # 使用 imread_with_unicode 替换直接读取，以支持中文路径
            img = self.imread_with_unicode(str(original_img_path))
            if img is None:
                print(f"  警告：无法读取图片文件进行标注: {original_img_path}")
                return
                
            for line in ocr_result:
                box = np.array(line['position']).astype(int)
                text = "".join(line['text'])
                
                cv2.polylines(img, [box], True, (0, 255, 0), 2)
                
                cv2.putText(img, text, (box[0, 0], box[0, 1] - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            annotated_path = output_img_path.parent / f"annotated_{output_img_path.name}"
            cv2.imwrite(str(annotated_path), img)
            
        except Exception as e:
            print(f"  标注图片时出错: {str(e)}")

    def imread_with_unicode(self, path):
        """使用 imdecode 解决中文路径读取问题"""
        try:
            with open(path, 'rb') as f:
                img_data = f.read()
            return cv2.imdecode(np.frombuffer(img_data, np.uint8), cv2.IMREAD_COLOR)
        except Exception as e:
            print(f"  使用imdecode读取图片时出错: {e}")
            return None

def main():
    print("身份证图片整理工具 (基于 CnOcr)")
    print("功能：自动识别身份证正面和反面，并分类整理到不同文件夹")

    source_path_str = input("请输入要处理的图片文件或文件夹的完整路径: ").strip()
    source_path = Path(source_path_str)

    if not source_path.exists():
        print(f"错误：路径不存在: {source_path_str}")
        return

    output_dir = input("请输入结果保存目录路径（默认为'./id_card_output'）: ") or "./id_card_output"

    try:
        if source_path.is_file():
            print("检测到输入为单个文件，开始处理...")
            organizer = IDCardOrganizer("", output_dir)
            result = organizer.process_single_image(source_path)
            if result:
                print(f"处理完成！图片已分类到: {result['target_path']}")
        elif source_path.is_dir():
            print("检测到输入为文件夹，开始批量处理...")
            organizer = IDCardOrganizer(source_path, output_dir)
            organizer.process_images()
        else:
            print(f"错误：路径既不是文件也不是文件夹: {source_path_str}")

    except ValueError as e:
        print(e)
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    main() 