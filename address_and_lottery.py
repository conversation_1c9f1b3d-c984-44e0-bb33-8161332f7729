#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 模块3+4：地址管理 + 抽奖活动模块

import os
import sys
import json
import time
import random
import requests
from datetime import datetime

# 配置
BASE_URL = "https://ysfp.huanqiu.com"
ACCOUNTS_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\ysfp_accounts.txt"

def load_accounts():
    """从文件加载账号信息"""
    accounts = []
    try:
        if os.path.exists(ACCOUNTS_FILE):
            with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            account = json.loads(line)
                            accounts.append(account)
                        except json.JSONDecodeError:
                            continue
        
        print(f"📋 加载了 {len(accounts)} 个账号")
        return accounts
        
    except Exception as e:
        print(f"❌ 加载账号失败: {e}")
        return []

def display_accounts(accounts):
    """显示账号列表"""
    if not accounts:
        print("❌ 没有找到任何账号")
        return
    
    print(f"\n📋 账号列表:")
    print("-" * 80)
    print(f"{'序号':<4} {'手机号':<12} {'姓名':<8} {'用户ID':<8} {'创建时间':<16} {'状态'}")
    print("-" * 80)
    
    for i, account in enumerate(accounts, 1):
        real_name = account.get('real_name', '未实名')
        user_id = account.get('user_id', 'N/A')
        create_time = account.get('create_time', 'N/A')[:16]
        status = "✅已实名" if real_name and real_name != '未实名' else "⚠️未实名"
        
        print(f"{i:<4} {account['phone']:<12} {real_name:<8} {user_id:<8} {create_time:<16} {status}")

def select_account(accounts):
    """选择账号"""
    if not accounts:
        return None
    
    display_accounts(accounts)
    
    try:
        choice = input(f"\n请选择账号 (1-{len(accounts)}, 0=退出): ").strip()
        
        if choice == '0':
            return None
        
        index = int(choice) - 1
        if 0 <= index < len(accounts):
            selected = accounts[index]
            print(f"✅ 已选择账号: {selected['phone']} ({selected.get('real_name', '未实名')})")
            return selected
        else:
            print("❌ 无效的选择")
            return None
            
    except ValueError:
        print("❌ 请输入有效的数字")
        return None

def create_session_with_token(token):
    """创建带token的会话"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': f'Bearer {token}',
        'Origin': BASE_URL,
        'Referer': f'{BASE_URL}/',
    })
    return session

def generate_address_info(real_name, phone):
    """生成地址信息"""
    provinces_cities = {
        "北京市": ["东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区"],
        "上海市": ["黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区"],
        "广东省": ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市"],
        "江苏省": ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市"],
        "浙江省": ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市"],
    }
    
    province = random.choice(list(provinces_cities.keys()))
    city = random.choice(provinces_cities[province])
    
    streets = ["中山路", "人民路", "解放路", "建设路", "文化路", "学府路", "科技路", "商业街", "工业路", "环城路"]
    street = random.choice(streets)
    
    building_num = random.randint(1, 999)
    unit = random.randint(1, 10)
    room = random.randint(101, 999)
    
    detailed_address = f"{street}{building_num}号{unit}单元{room}室"
    
    # 生成银行卡信息
    banks = ["工商银行", "建设银行", "农业银行", "中国银行", "交通银行", "招商银行", "浦发银行", "民生银行"]
    bank = random.choice(banks)
    
    # 生成银行卡号（简单模拟）
    card_prefixes = {
        "工商银行": "6222",
        "建设银行": "6227",
        "农业银行": "6228",
        "中国银行": "6216",
        "交通银行": "6222",
        "招商银行": "6225",
        "浦发银行": "6221",
        "民生银行": "6226"
    }
    
    prefix = card_prefixes.get(bank, "6222")
    card_number = prefix + ''.join([str(random.randint(0, 9)) for _ in range(15)])
    
    return {
        'name': real_name,
        'phone': phone,
        'province': province,
        'city': city,
        'district': city,  # 简化处理
        'address': detailed_address,
        'bank': bank,
        'card_number': card_number
    }

def add_address(session, address_info):
    """添加收货地址"""
    try:
        # 获取区域代码（简化处理，使用固定值）
        area_codes = {
            "北京市": "110000",
            "上海市": "310000", 
            "广东省": "440000",
            "江苏省": "320000",
            "浙江省": "330000"
        }
        
        area_code = area_codes.get(address_info['province'], "110000")
        
        data = {
            'name': address_info['name'],
            'phone': address_info['phone'],
            'province': address_info['province'],
            'city': address_info['city'],
            'area': address_info['district'],
            'address': address_info['address'],
            'areacode': area_code,
            'bank': address_info['bank'],
            'bankcard': address_info['card_number']
        }
        
        response = session.post(f"{BASE_URL}/api/user/address", data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return True, "地址添加成功"
            else:
                return False, result.get('msg', '地址添加失败')
        else:
            return False, f"请求失败: {response.status_code}"
            
    except Exception as e:
        return False, f"添加地址异常: {e}"

def get_lottery_config(session):
    """获取抽奖配置"""
    try:
        response = session.post(f"{BASE_URL}/api/turntable/config", data={})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return True, result['data']
            else:
                return False, result.get('msg', '获取抽奖配置失败')
        else:
            return False, f"请求失败: {response.status_code}"
            
    except Exception as e:
        return False, f"获取抽奖配置异常: {e}"

def do_lottery(session):
    """执行抽奖"""
    try:
        response = session.post(f"{BASE_URL}/api/turntable/lottery", data={})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return True, result['data']
            else:
                return False, result.get('msg', '抽奖失败')
        else:
            return False, f"请求失败: {response.status_code}"
            
    except Exception as e:
        return False, f"抽奖异常: {e}"

def get_lottery_records(session):
    """获取抽奖记录"""
    try:
        response = session.post(f"{BASE_URL}/api/turntable/lists", data={})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return True, result['data']
            else:
                return False, result.get('msg', '获取记录失败')
        else:
            return False, f"请求失败: {response.status_code}"
            
    except Exception as e:
        return False, f"获取记录异常: {e}"

def address_management_menu(account):
    """地址管理菜单"""
    print(f"\n📍 地址管理 - {account['phone']}")
    print("=" * 40)
    
    session = create_session_with_token(account['token'])
    
    while True:
        print(f"\n请选择操作:")
        print(f"1. 添加收货地址")
        print(f"2. 查看地址列表")
        print(f"0. 返回主菜单")
        
        choice = input(f"请选择 (0-2): ").strip()
        
        if choice == '1':
            # 添加地址
            real_name = account.get('real_name', '')
            if not real_name or real_name == '未实名':
                real_name = input("请输入收货人姓名: ").strip()
            
            print(f"📍 生成地址信息...")
            address_info = generate_address_info(real_name, account['phone'])
            
            print(f"📋 地址信息:")
            print(f"   姓名: {address_info['name']}")
            print(f"   手机: {address_info['phone']}")
            print(f"   地址: {address_info['province']} {address_info['city']} {address_info['address']}")
            print(f"   银行: {address_info['bank']}")
            print(f"   卡号: {address_info['card_number']}")
            
            confirm = input(f"\n确认添加此地址? (Y/N): ").strip().upper()
            if confirm == 'Y':
                success, msg = add_address(session, address_info)
                if success:
                    print(f"✅ {msg}")
                else:
                    print(f"❌ {msg}")
            
        elif choice == '2':
            print(f"📋 地址列表功能待实现...")
            
        elif choice == '0':
            break
        else:
            print(f"❌ 无效选择")

def lottery_menu(account):
    """抽奖菜单"""
    print(f"\n🎰 抽奖活动 - {account['phone']}")
    print("=" * 40)
    
    session = create_session_with_token(account['token'])
    
    while True:
        print(f"\n请选择操作:")
        print(f"1. 查看抽奖配置")
        print(f"2. 开始抽奖")
        print(f"3. 查看抽奖记录")
        print(f"4. 自动抽奖 (多次)")
        print(f"0. 返回主菜单")
        
        choice = input(f"请选择 (0-4): ").strip()
        
        if choice == '1':
            # 查看抽奖配置
            success, data = get_lottery_config(session)
            if success:
                print(f"🎰 抽奖配置:")
                print(f"   {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ {data}")
                
        elif choice == '2':
            # 单次抽奖
            success, data = do_lottery(session)
            if success:
                print(f"🎉 抽奖结果:")
                print(f"   {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ {data}")
                
        elif choice == '3':
            # 查看记录
            success, data = get_lottery_records(session)
            if success:
                print(f"📋 抽奖记录:")
                print(f"   {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ {data}")
                
        elif choice == '4':
            # 自动抽奖
            count = input(f"请输入抽奖次数 (默认5次): ").strip()
            try:
                count = int(count) if count else 5
            except ValueError:
                count = 5
            
            print(f"🎰 开始自动抽奖 {count} 次...")
            
            for i in range(count):
                print(f"第 {i+1}/{count} 次抽奖...")
                success, data = do_lottery(session)
                if success:
                    print(f"✅ 抽奖成功: {data}")
                else:
                    print(f"❌ 抽奖失败: {data}")
                
                if i < count - 1:  # 不是最后一次
                    time.sleep(2)  # 间隔2秒
                    
        elif choice == '0':
            break
        else:
            print(f"❌ 无效选择")

def main():
    """主函数"""
    print("🏠 地址管理 + 抽奖活动模块")
    print("=" * 60)
    
    # 加载账号
    accounts = load_accounts()
    if not accounts:
        print("❌ 没有找到任何账号，请先运行注册模块")
        return
    
    while True:
        print(f"\n📋 主菜单:")
        print(f"1. 地址管理")
        print(f"2. 抽奖活动") 
        print(f"3. 查看账号列表")
        print(f"0. 退出")
        
        choice = input(f"请选择功能 (0-3): ").strip()
        
        if choice == '1':
            # 地址管理
            account = select_account(accounts)
            if account:
                address_management_menu(account)
                
        elif choice == '2':
            # 抽奖活动
            account = select_account(accounts)
            if account:
                lottery_menu(account)
                
        elif choice == '3':
            # 查看账号列表
            display_accounts(accounts)
            
        elif choice == '0':
            break
        else:
            print(f"❌ 无效选择")
    
    print(f"\n🎉 程序结束!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
