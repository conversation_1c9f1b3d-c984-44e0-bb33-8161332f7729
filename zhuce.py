```python
import requests
import random
import string
from faker import Faker
import json
import uuid

# 初始化faker来生成中文数据
fake = Faker('zh_CN')

class DeviceGenerator:
    """设备信息生成器，为每个账号生成唯一的设备指纹"""

    def __init__(self):
        # iPhone型号列表
        self.iphone_models = [
            "iPhone12,1", "iPhone12,3", "iPhone12,5", "iPhone12,8",  # iPhone 11 系列
            "iPhone13,1", "iPhone13,2", "iPhone13,3", "iPhone13,4",  # iPhone 12 系列
            "iPhone14,2", "iPhone14,3", "iPhone14,4", "iPhone14,5",  # iPhone 13 系列
            "iPhone14,7", "iPhone14,8",  # iPhone 13 mini/Pro Max
            "iPhone15,2", "iPhone15,3", "iPhone15,4", "iPhone15,5",  # iPhone 14 系列
        ]

        # iOS版本列表
        self.ios_versions = [
            "15_0", "15_1", "15_2", "15_3", "15_4", "15_5", "15_6", "15_7",
            "16_0", "16_1", "16_2", "16_3", "16_4", "16_5", "16_6", "16_7",
            "17_0", "17_1", "17_2", "17_3", "17_4", "17_5"
        ]

        # Safari版本列表
        self.safari_versions = [
            "604.1", "605.1.15", "606.1.36", "607.1.40", "608.1.49",
            "609.1.20", "610.1.28", "611.1.21", "612.1.29", "613.1.17"
        ]

        # WebKit版本列表
        self.webkit_versions = [
            "605.1.15", "606.1.36", "607.1.40", "608.1.49", "609.1.20",
            "610.1.28", "611.1.21", "612.1.29", "613.1.17", "614.1.25"
        ]

    def generate_device_info(self):
        """生成一套完整的设备信息"""
        # 随机选择设备信息
        iphone_model = random.choice(self.iphone_models)
        ios_version = random.choice(self.ios_versions)
        safari_version = random.choice(self.safari_versions)
        webkit_version = random.choice(self.webkit_versions)

        # 生成设备ID和其他标识
        device_id = str(uuid.uuid4()).upper()
        session_id = str(uuid.uuid4())

        # 构建User-Agent
        user_agent = f"Mozilla/5.0 ({iphone_model}; CPU iPhone OS {ios_version} like Mac OS X) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{safari_version.split('.')[0]}.{safari_version.split('.')[1]} Mobile/15E148 Safari/{safari_version}"

        device_info = {
            'user_agent': user_agent,
            'device_id': device_id,
            'session_id': session_id,
            'iphone_model': iphone_model,
            'ios_version': ios_version.replace('_', '.'),
            'safari_version': safari_version,
            'webkit_version': webkit_version,
            'screen_width': random.choice([375, 390, 414, 428]),  # iPhone屏幕宽度
            'screen_height': random.choice([667, 812, 844, 896, 926]),  # iPhone屏幕高度
            'timezone': random.choice(['Asia/Shanghai', 'Asia/Beijing']),
            'language': 'zh-CN'
        }

        return device_info

    def get_headers(self, device_info, token=None, content_type='application/json'):
        """根据设备信息生成请求头"""
        headers = {
            'User-Agent': device_info['user_agent'],
            'Origin': 'https://www.wuyouvip.me',
            'Referer': 'https://www.wuyouvip.me/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        }

        if content_type:
            headers['Content-Type'] = content_type

        if token:
            headers['token'] = token

        return headers

def generate_phone_number():
    """生成一个随机的中国手机号码"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '185', '186', '187', '188', '189',
                '166', '176', '198', '199']
    return random.choice(prefixes) + "".join(random.choice(string.digits) for _ in range(8))

def generate_password(length=8):
    """生成一个随机密码"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for i in range(length))

def generate_id_card():
    """
    生成一个随机的、格式正确的中国身份证号码.
    """
    id_card = fake.ssn()
    
    birth_year = int(id_card[6:10])
    birth_month = int(id_card[10:12])
    birth_day = int(id_card[12:14])
    birth_date = f"{birth_year:04d}-{birth_month:02d}-{birth_day:02d}"

    sex_digit = int(id_card[16])
    sex = 1 if sex_digit % 2 != 0 else 2
    
    return id_card, f"{birth_date}", sex

def register(account, password, tjcode, device_info):
    """注册新用户"""
    url = "https://api.wuyouvip.top/api/login/register"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info)

    data = {
        "account": account,
        "password": password,
        "password_confirm": password,
        "smscode": "1234",
        "tjcode": tjcode,
        "channel": 3
    }
    print("正在尝试注册...")
    print(f"使用设备: {device_info['iphone_model']} iOS {device_info['ios_version']}")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"注册响应: {result}")
        if result.get('code') == 1:
             return True
        else:
            print(f"注册失败消息: {result.get('msg')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"注册请求失败: {e}")
        return False

def login(account, password, device_info):
    """登录并获取token"""
    url = "https://api.wuyouvip.top/api/login/account"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info)

    data = {
        "scene": "1",
        "account": account,
        "password": password,
        "code": "",
        "terminal": 3
    }
    print("正在尝试登录...")
    print(f"使用设备: {device_info['iphone_model']} iOS {device_info['ios_version']}")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"登录响应: {result}")
        if result.get('code') == 1 and 'data' in result and 'token' in result['data']:
            return result['data']['token']
        else:
            return None
    except requests.exceptions.RequestException as e:
        print(f"登录请求失败: {e}")
        return None

def verify_real_name(token, real_name, id_card, birth, sex, device_info):
    """进行实名认证"""
    url = "https://api.wuyouvip.top/api/user/setId2MetaVerify"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token)

    data = {
        "id_card": id_card,
        "real_name": real_name,
        "sex": sex,
        "birth": birth,
        "id_card_front": "",
        "id_card_back": ""
    }
    print("正在尝试实名认证...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"实名认证响应: {result}")
        return result.get('code') == 1
    except requests.exceptions.RequestException as e:
        print(f"实名认证请求失败: {e}")
        return False
        
def sign_in(token, device_info):
    """进行每日签到"""
    url = "https://api.wuyouvip.top/api/user/sign"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token, content_type=None)

    print("正在尝试签到...")
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"签到响应: {result}")
        if result.get('code') == 1:
            print("签到成功！")
        else:
            print(f"签到操作完成，消息: {result.get('msg')}")
    except requests.exceptions.RequestException as e:
        print(f"签到请求失败: {e}")

# --- 主程序 ---
def main():
    try:
        # 1. 生成设备信息
        device_generator = DeviceGenerator()
        device_info = device_generator.generate_device_info()
        print("🔧 生成的设备信息:")
        print(f"  设备型号: {device_info['iphone_model']}")
        print(f"  iOS版本: {device_info['ios_version']}")
        print(f"  屏幕尺寸: {device_info['screen_width']}x{device_info['screen_height']}")
        print(f"  设备ID: {device_info['device_id'][:8]}...{device_info['device_id'][-8:]}")
        print()

        # 2. 交互式输入邀请码
        invitation_code = input("请输入您的邀请码 (或直接按回车使用默认'453265'): ")
        if not invitation_code:
            invitation_code = "453265"

        # 3. 生成随机用户信息
        phone = generate_phone_number()
        password = generate_password()
        print(f"\n📱 生成的账号: {phone}")
        print(f"🔑 生成的密码: {password}\n")

        # 4. 注册
        if register(phone, password, invitation_code, device_info):
            print("✅ 注册成功！\n")

            # 5. 登录
            token = login(phone, password, device_info)
            if token:
                print(f"✅ 登录成功！获取到的Token: ...{token[-6:]}\n") # 为安全起见，只显示token最后几位

                # 6. 生成实名信息
                id_card, birth_date, sex = generate_id_card()
                name = fake.name()
                print("👤 生成的实名信息:")
                print(f"  姓名: {name}")
                print(f"  身份证号: {id_card}")
                print(f"  生日: {birth_date}")
                print(f"  性别: {'男' if sex == 1 else '女'}\n")

                # 7. 实名认证
                if verify_real_name(token, name, id_card, birth_date, sex, device_info):
                    print("✅ 实名认证成功！\n")

                    # 8. 签到
                    sign_in(token, device_info)
                    print("\n🎉 全部流程执行完毕！")

                    # 9. 保存账号信息（可选）
                    account_info = {
                        'phone': phone,
                        'password': password,
                        'token': token,
                        'real_name': name,
                        'id_card': id_card,
                        'device_info': device_info
                    }
                    print(f"\n💾 账号信息已生成，可保存备用")

                else:
                    print("❌ 实名认证失败。")
            else:
                print("❌ 登录失败，无法继续操作。")
        else:
            print("❌ 注册失败。")
    except Exception as e:
        print(f"\n脚本执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    main()