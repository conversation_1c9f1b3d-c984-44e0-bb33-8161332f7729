```python
import requests
import random
import string
from faker import Faker
import json

# 初始化faker来生成中文数据
fake = Faker('zh_CN')

def generate_phone_number():
    """生成一个随机的中国手机号码"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '185', '186', '187', '188', '189',
                '166', '176', '198', '199']
    return random.choice(prefixes) + "".join(random.choice(string.digits) for _ in range(8))

def generate_password(length=8):
    """生成一个随机密码"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for i in range(length))

def generate_id_card():
    """
    生成一个随机的、格式正确的中国身份证号码.
    """
    id_card = fake.ssn()
    
    birth_year = int(id_card[6:10])
    birth_month = int(id_card[10:12])
    birth_day = int(id_card[12:14])
    birth_date = f"{birth_year:04d}-{birth_month:02d}-{birth_day:02d}"

    sex_digit = int(id_card[16])
    sex = 1 if sex_digit % 2 != 0 else 2
    
    return id_card, f"{birth_date}", sex

def register(account, password, tjcode):
    """注册新用户"""
    url = "https://api.wuyouvip.top/api/login/register"
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Origin': 'https://www.wuyouvip.me',
        'Referer': 'https://www.wuyouvip.me/'
    }
    data = {
        "account": account,
        "password": password,
        "password_confirm": password,
        "smscode": "1234",
        "tjcode": tjcode,
        "channel": 3
    }
    print("正在尝试注册...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"注册响应: {result}")
        if result.get('code') == 1:
             return True
        else:
            print(f"注册失败消息: {result.get('msg')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"注册请求失败: {e}")
        return False

def login(account, password):
    """登录并获取token"""
    url = "https://api.wuyouvip.top/api/login/account"
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Origin': 'https://www.wuyouvip.me',
        'Referer': 'https://www.wuyouvip.me/'
    }
    data = {
        "scene": "1",
        "account": account,
        "password": password,
        "code": "",
        "terminal": 3
    }
    print("正在尝试登录...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"登录响应: {result}")
        if result.get('code') == 1 and 'data' in result and 'token' in result['data']:
            return result['data']['token']
        else:
            return None
    except requests.exceptions.RequestException as e:
        print(f"登录请求失败: {e}")
        return None

def verify_real_name(token, real_name, id_card, birth, sex):
    """进行实名认证"""
    url = "https://api.wuyouvip.top/api/user/setId2MetaVerify"
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Origin': 'https://www.wuyouvip.me',
        'Referer': 'https://www.wuyouvip.me/',
        'token': token
    }
    data = {
        "id_card": id_card,
        "real_name": real_name,
        "sex": sex,
        "birth": birth,
        "id_card_front": "",
        "id_card_back": ""
    }
    print("正在尝试实名认证...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"实名认证响应: {result}")
        return result.get('code') == 1
    except requests.exceptions.RequestException as e:
        print(f"实名认证请求失败: {e}")
        return False
        
def sign_in(token):
    """进行每日签到"""
    url = "https://api.wuyouvip.top/api/user/sign"
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Origin': 'https://www.wuyouvip.me',
        'Referer': 'https://www.wuyouvip.me/',
        'token': token
    }
    print("正在尝试签到...")
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"签到响应: {result}")
        if result.get('code') == 1:
            print("签到成功！")
        else:
            print(f"签到操作完成，消息: {result.get('msg')}")
    except requests.exceptions.RequestException as e:
        print(f"签到请求失败: {e}")

# --- 主程序 ---
def main():
    try:
        # 1. 交互式输入邀请码
        invitation_code = input("请输入您的邀请码 (或直接按回车使用默认'453265'): ")
        if not invitation_code:
            invitation_code = "453265"

        # 2. 生成随机用户信息
        phone = generate_phone_number()
        password = generate_password()
        print(f"\n生成的账号: {phone}")
        print(f"生成的密码: {password}\n")

        # 3. 注册
        if register(phone, password, invitation_code):
            print("✅ 注册成功！\n")
            
            # 4. 登录
            token = login(phone, password)
            if token:
                print(f"✅ 登录成功！获取到的Token: ...{token[-6:]}\n") # 为安全起见，只显示token最后几位
                
                # 5. 生成实名信息
                id_card, birth_date, sex = generate_id_card()
                name = fake.name()
                print("生成的实名信息:")
                print(f"  姓名: {name}")
                print(f"  身份证号: {id_card}")
                print(f"  生日: {birth_date}")
                print(f"  性别: {'男' if sex == 1 else '女'}\n")

                # 6. 实名认证
                if verify_real_name(token, name, id_card, birth_date, sex):
                    print("✅ 实名认证成功！\n")

                    # 7. 签到
                    sign_in(token)
                    print("\n🎉 全部流程执行完毕！")

                else:
                    print("❌ 实名认证失败。")
            else:
                print("❌ 登录失败，无法继续操作。")
        else:
            print("❌ 注册失败。")
    except Exception as e:
        print(f"\n脚本执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    main()
```