import requests
import random
import string
from faker import Faker
import uuid
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 初始化faker来生成中文数据
fake = Faker('zh_CN')

class DeviceGenerator:
    """设备信息生成器，为每个账号生成唯一的设备指纹"""

    def __init__(self):
        # iPhone型号列表
        self.iphone_models = [
            "iPhone12,1", "iPhone12,3", "iPhone12,5", "iPhone12,8",  # iPhone 11 系列
            "iPhone13,1", "iPhone13,2", "iPhone13,3", "iPhone13,4",  # iPhone 12 系列
            "iPhone14,2", "iPhone14,3", "iPhone14,4", "iPhone14,5",  # iPhone 13 系列
            "iPhone14,7", "iPhone14,8",  # iPhone 13 mini/Pro Max
            "iPhone15,2", "iPhone15,3", "iPhone15,4", "iPhone15,5",  # iPhone 14 系列
        ]

        # iOS版本列表
        self.ios_versions = [
            "15_0", "15_1", "15_2", "15_3", "15_4", "15_5", "15_6", "15_7",
            "16_0", "16_1", "16_2", "16_3", "16_4", "16_5", "16_6", "16_7",
            "17_0", "17_1", "17_2", "17_3", "17_4", "17_5"
        ]

        # Safari版本列表
        self.safari_versions = [
            "604.1", "605.1.15", "606.1.36", "607.1.40", "608.1.49",
            "609.1.20", "610.1.28", "611.1.21", "612.1.29", "613.1.17"
        ]

        # WebKit版本列表
        self.webkit_versions = [
            "605.1.15", "606.1.36", "607.1.40", "608.1.49", "609.1.20",
            "610.1.28", "611.1.21", "612.1.29", "613.1.17", "614.1.25"
        ]

    def generate_device_info(self):
        """生成一套完整的设备信息"""
        # 随机选择设备信息
        iphone_model = random.choice(self.iphone_models)
        ios_version = random.choice(self.ios_versions)
        safari_version = random.choice(self.safari_versions)
        webkit_version = random.choice(self.webkit_versions)

        # 生成设备ID和其他标识
        device_id = str(uuid.uuid4()).upper()
        session_id = str(uuid.uuid4())

        # 构建User-Agent
        user_agent = f"Mozilla/5.0 ({iphone_model}; CPU iPhone OS {ios_version} like Mac OS X) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{safari_version.split('.')[0]}.{safari_version.split('.')[1]} Mobile/15E148 Safari/{safari_version}"

        device_info = {
            'user_agent': user_agent,
            'device_id': device_id,
            'session_id': session_id,
            'iphone_model': iphone_model,
            'ios_version': ios_version.replace('_', '.'),
            'safari_version': safari_version,
            'webkit_version': webkit_version,
            'screen_width': random.choice([375, 390, 414, 428]),  # iPhone屏幕宽度
            'screen_height': random.choice([667, 812, 844, 896, 926]),  # iPhone屏幕高度
            'timezone': random.choice(['Asia/Shanghai', 'Asia/Beijing']),
            'language': 'zh-CN'
        }

        return device_info

    def get_headers(self, device_info, token=None, content_type='application/json'):
        """根据设备信息生成请求头"""
        headers = {
            'User-Agent': device_info['user_agent'],
            'Origin': 'https://www.wuyouvip.me',
            'Referer': 'https://www.wuyouvip.me/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site'
        }

        if content_type:
            headers['Content-Type'] = content_type

        if token:
            headers['token'] = token

        return headers

def generate_phone_number():
    """生成一个随机的中国手机号码"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '185', '186', '187', '188', '189',
                '166', '176', '198', '199']
    return random.choice(prefixes) + "".join(random.choice(string.digits) for _ in range(8))

def generate_password(length=8):
    """生成一个随机密码"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def generate_id_card():
    """
    生成一个随机的、格式正确的中国身份证号码.
    """
    id_card = fake.ssn()
    
    birth_year = int(id_card[6:10])
    birth_month = int(id_card[10:12])
    birth_day = int(id_card[12:14])
    birth_date = f"{birth_year:04d}-{birth_month:02d}-{birth_day:02d}"

    sex_digit = int(id_card[16])
    sex = 1 if sex_digit % 2 != 0 else 2
    
    return id_card, f"{birth_date}", sex

def register(account, password, tjcode, device_info):
    """注册新用户"""
    url = "https://api.wuyouvip.top/api/login/register"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info)

    data = {
        "account": account,
        "password": password,
        "password_confirm": password,
        "smscode": "1234",
        "tjcode": tjcode,
        "channel": 3
    }
    print("正在尝试注册...")
    print(f"使用设备: {device_info['iphone_model']} iOS {device_info['ios_version']}")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"注册响应: {result}")
        if result.get('code') == 1:
             return True
        else:
            print(f"注册失败消息: {result.get('msg')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"注册请求失败: {e}")
        return False

def login(account, password, device_info):
    """登录并获取token"""
    url = "https://api.wuyouvip.top/api/login/account"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info)

    data = {
        "scene": "1",
        "account": account,
        "password": password,
        "code": "",
        "terminal": 3
    }
    print("正在尝试登录...")
    print(f"使用设备: {device_info['iphone_model']} iOS {device_info['ios_version']}")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"登录响应: {result}")
        if result.get('code') == 1 and 'data' in result and 'token' in result['data']:
            return result['data']['token']
        else:
            return None
    except requests.exceptions.RequestException as e:
        print(f"登录请求失败: {e}")
        return None

def verify_real_name(token, real_name, id_card, birth, sex, device_info):
    """进行实名认证"""
    url = "https://api.wuyouvip.top/api/user/setId2MetaVerify"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token)

    data = {
        "id_card": id_card,
        "real_name": real_name,
        "sex": sex,
        "birth": birth,
        "id_card_front": "",
        "id_card_back": ""
    }
    print("正在尝试实名认证...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"实名认证响应: {result}")
        return result.get('code') == 1
    except requests.exceptions.RequestException as e:
        print(f"实名认证请求失败: {e}")
        return False
        
def sign_in(token, device_info):
    """进行每日签到"""
    url = "https://api.wuyouvip.top/api/user/sign"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token, content_type=None)

    print("正在尝试签到...")
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"签到响应: {result}")
        if result.get('code') == 1:
            print("签到成功！")
            return True
        else:
            print(f"签到操作完成，消息: {result.get('msg')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"签到请求失败: {e}")
        return False

def bind_bank_card(token, real_name, bank_name, bank_code, card_number, device_info):
    """绑定银行卡"""
    url = "https://api.wuyouvip.top/api/user/setBank"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token)
    headers['version'] = '1.9.0'  # 添加版本号

    data = {
        "type": "add",
        "bankname": bank_name,
        "bankcode": bank_code,
        "banktype": "1",
        "cardname": real_name,
        "cardnumber": card_number,
        "tron": ""
    }

    print("正在尝试绑定银行卡...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"绑卡响应: {result}")
        if result.get('code') == 1:
            print("✅ 银行卡绑定成功！")
            return True
        else:
            print(f"❌ 银行卡绑定失败: {result.get('msg')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"绑卡请求失败: {e}")
        return False

def withdraw_money(token, real_name, bank_name, card_number, amount, device_info):
    """提现操作"""
    url = "https://api.wuyouvip.top/api/recharge/refund"
    device_generator = DeviceGenerator()
    headers = device_generator.get_headers(device_info, token=token)
    headers['version'] = '1.9.0'  # 添加版本号

    data = {
        "order_amount": amount,
        "bankname": bank_name,
        "cardnumber": card_number,
        "cardname": real_name,
        "banktype": "人民币"
    }

    print(f"正在尝试提现 {amount} 元...")
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"提现响应: {result}")
        if result.get('code') == 1:
            order_id = result.get('data', {}).get('order_id', 'N/A')
            print(f"✅ 提现申请成功！订单号: {order_id}")
            return True, order_id
        else:
            print(f"❌ 提现失败: {result.get('msg')}")
            return False, None
    except requests.exceptions.RequestException as e:
        print(f"提现请求失败: {e}")
        return False, None

def get_bank_code(bank_name):
    """根据银行名称获取银行代码"""
    bank_codes = {
        "中国工商银行": "ICBC",
        "中国农业银行": "ABC",
        "中国银行": "BOC",
        "中国建设银行": "CCB",
        "交通银行": "BOCOM",
        "招商银行": "CMB",
        "中信银行": "CITIC",
        "中国光大银行": "CEB",
        "华夏银行": "HXB",
        "中国民生银行": "CMBC",
        "广发银行": "CGB",
        "平安银行": "PAB",
        "兴业银行": "CIB",
        "浦发银行": "SPDB",
        "邮储银行": "PSBC"
    }
    return bank_codes.get(bank_name, "ICBC")  # 默认工商银行

def process_single_account(invitation_code, fixed_name, fixed_id_card, fixed_birth, fixed_sex, bank_name, bank_code, card_number, withdraw_amount, account_index, total_accounts):
    """处理单个账号的完整流程"""
    thread_id = threading.current_thread().name
    print(f"\n[线程{thread_id}] 🚀 开始处理第 {account_index}/{total_accounts} 个账号")

    try:
        # 1. 生成设备信息
        device_generator = DeviceGenerator()
        device_info = device_generator.generate_device_info()
        print(f"[线程{thread_id}] 🔧 设备: {device_info['iphone_model']} iOS {device_info['ios_version']}")

        # 2. 生成随机用户信息
        phone = generate_phone_number()
        password = generate_password()
        print(f"[线程{thread_id}] 📱 账号: {phone} | 密码: {password}")

        # 3. 注册
        if not register(phone, password, invitation_code, device_info):
            print(f"[线程{thread_id}] ❌ 注册失败，跳过此账号")
            return None
        print(f"[线程{thread_id}] ✅ 注册成功！")

        # 4. 登录
        token = login(phone, password, device_info)
        if not token:
            print(f"[线程{thread_id}] ❌ 登录失败，跳过此账号")
            return None
        print(f"[线程{thread_id}] ✅ 登录成功！Token: ...{token[-6:]}")

        # 5. 实名认证
        if not verify_real_name(token, fixed_name, fixed_id_card, fixed_birth, fixed_sex, device_info):
            print(f"[线程{thread_id}] ❌ 实名认证失败，跳过此账号")
            return None
        print(f"[线程{thread_id}] ✅ 实名认证成功！")

        # 6. 签到
        if not sign_in(token, device_info):
            print(f"[线程{thread_id}] ⚠️ 签到失败，但继续执行")
        else:
            print(f"[线程{thread_id}] ✅ 签到成功！")

        # 7. 绑定银行卡
        if not bind_bank_card(token, fixed_name, bank_name, bank_code, card_number, device_info):
            print(f"[线程{thread_id}] ❌ 银行卡绑定失败，跳过此账号")
            return None

        # 8. 提现
        success, order_id = withdraw_money(token, fixed_name, bank_name, card_number, withdraw_amount, device_info)
        if not success:
            print(f"[线程{thread_id}] ❌ 提现失败")
            return None

        # 9. 返回账号信息
        account_info = {
            'phone': phone,
            'password': password,
            'token': token,
            'real_name': fixed_name,
            'id_card': fixed_id_card,
            'device_info': device_info,
            'order_id': order_id
        }

        print(f"[线程{thread_id}] 🎉 第 {account_index} 个账号处理完成！手机号: {phone} | 订单: {order_id}")

        return account_info

    except Exception as e:
        print(f"[线程{thread_id}] ❌ 处理第 {account_index} 个账号时发生错误: {e}")
        return None

# --- 主程序 ---
def main():
    try:
        print("🎯 批量注册+实名+绑卡+提现 自动化脚本")
        print("="*60)

        # 预设配置选项
        print("🚀 快速配置选项:")
        print("1. 使用预设配置 (邀请码:453265, 数量:3, 姓名:张三, 工商银行)")
        print("2. 自定义单一配置")
        print("3. 自定义多重配置 (多个名字+银行卡轮流使用)")

        choice = input("请选择 (1/2/3, 默认1): ").strip()

        # 通用输入函数
        def get_basic_config():
            invitation_code = input("请输入您的邀请码 (默认'453265'): ").strip()
            if not invitation_code:
                invitation_code = "453265"

            while True:
                try:
                    account_count = int(input("请输入要注册的账号数量: "))
                    if account_count > 0:
                        break
                    else:
                        print("请输入大于0的数字")
                except ValueError:
                    print("请输入有效的数字")

            while True:
                try:
                    thread_count = int(input("请输入线程数 (建议1-10): "))
                    if 1 <= thread_count <= 20:
                        break
                    else:
                        print("请输入1-20之间的数字")
                except ValueError:
                    print("请输入有效的数字")

            return invitation_code, account_count, thread_count

        if choice == "2":
            # 自定义单一配置
            invitation_code, account_count, thread_count = get_basic_config()
            use_multiple_configs = False

        elif choice == "3":
            # 自定义多重配置
            invitation_code, account_count, thread_count = get_basic_config()
            use_multiple_configs = True

        else:
            # 使用预设配置
            invitation_code = "453265"
            account_count = 3
            thread_count = 3
            use_multiple_configs = False
            print(f"✅ 使用预设配置: 邀请码={invitation_code}, 数量={account_count}, 线程数={thread_count}")

        # 配置实名和银行卡信息
        if use_multiple_configs:
            # 多重配置模式
            print("\n🔄 多重配置模式 - 设置多个名字和银行卡轮流使用")

            # 输入多个名字
            print("\n� 请输入多个姓名（用逗号分隔）:")
            names_input = input("姓名列表 (如: 张三,李四,王五): ").strip()
            name_list = [name.strip() for name in names_input.split(',') if name.strip()]
            while not name_list:
                names_input = input("至少输入一个姓名，请重新输入: ").strip()
                name_list = [name.strip() for name in names_input.split(',') if name.strip()]

            # 输入对应的银行信息
            print("\n🏦 请为每个姓名配置对应的银行卡信息:")
            person_configs = []
            for i, name in enumerate(name_list):
                print(f"\n为 '{name}' 配置银行卡:")
                bank_name = input("银行名称 (如：中国工商银行): ").strip()
                while not bank_name:
                    bank_name = input("银行名称不能为空，请重新输入: ").strip()

                card_number = input("银行卡号: ").strip()
                while not card_number:
                    card_number = input("银行卡号不能为空，请重新输入: ").strip()

                bank_code = get_bank_code(bank_name)
                person_configs.append({
                    'name': name,
                    'bank_name': bank_name,
                    'card_number': card_number,
                    'bank_code': bank_code
                })

            # 输入提现金额
            while True:
                try:
                    withdraw_amount = int(input("\n提现金额 (整数): "))
                    if withdraw_amount > 0:
                        break
                    else:
                        print("请输入大于0的金额")
                except ValueError:
                    print("请输入有效的数字")

            print(f"\n✅ 多重配置设置完成:")
            for i, config in enumerate(person_configs):
                print(f"{i+1}. {config['name']} -> {config['bank_name']} ({config['card_number'][:4]}****{config['card_number'][-4:]})")
            print(f"提现金额: {withdraw_amount} 元")

        elif choice == "2":
            # 单一自定义配置
            print("\n📝 请输入固定的实名信息（所有账号将使用相同信息）:")
            fixed_name = input("姓名: ").strip()
            while not fixed_name:
                fixed_name = input("姓名不能为空，请重新输入: ").strip()

            print("\n💳 请输入银行卡信息:")
            bank_name = input("银行名称 (如：中国工商银行): ").strip()
            while not bank_name:
                bank_name = input("银行名称不能为空，请重新输入: ").strip()

            card_number = input("银行卡号: ").strip()
            while not card_number:
                card_number = input("银行卡号不能为空，请重新输入: ").strip()

            while True:
                try:
                    withdraw_amount = int(input("提现金额 (整数): "))
                    if withdraw_amount > 0:
                        break
                    else:
                        print("请输入大于0的金额")
                except ValueError:
                    print("请输入有效的数字")

            # 转换为统一格式
            person_configs = [{
                'name': fixed_name,
                'bank_name': bank_name,
                'card_number': card_number,
                'bank_code': get_bank_code(bank_name)
            }]

        else:
            # 预设配置
            person_configs = [{
                'name': "张三",
                'bank_name': "中国工商银行",
                'card_number': "6214762314000058077",
                'bank_code': "ICBC"
            }]
            withdraw_amount = 6
            print(f"✅ 使用预设信息: 姓名={person_configs[0]['name']}, 银行={person_configs[0]['bank_name']}, 提现={withdraw_amount}元")

        # 6. 确认信息
        print(f"\n📋 确认信息:")
        print(f"邀请码: {invitation_code}")
        print(f"账号数量: {account_count}")
        print(f"线程数: {thread_count}")
        if use_multiple_configs:
            print(f"配置模式: 多重配置 ({len(person_configs)} 个配置)")
            for i, config in enumerate(person_configs):
                print(f"配置 {i+1}: {config['name']} -> {config['bank_name']} ({config['card_number'][:4]}****{config['card_number'][-4:]})")
        else:
            print(f"配置模式: 单一配置")
            config = person_configs[0]
            print(f"姓名: {config['name']}")
            print(f"银行: {config['bank_name']} ({config['card_number'][:4]}****{config['card_number'][-4:]})")
        print(f"提现金额: {withdraw_amount} 元")

        confirm = input("\n确认开始批量处理？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return

        # 7. 开始批量处理
        print(f"\n� 开始批量处理 {account_count} 个账号...")
        successful_accounts = []
        failed_count = 0

        # 创建任务列表
        tasks = []
        for i in range(1, account_count + 1):
            # 轮流选择配置（名字和银行卡是一一对应的）
            config_index = (i - 1) % len(person_configs)
            current_config = person_configs[config_index]

            # 为每个账号生成独立的身份证号
            id_card, birth_date, sex = generate_id_card()

            task = (invitation_code, current_config['name'], id_card, birth_date, sex,
                   current_config['bank_name'], current_config['bank_code'],
                   current_config['card_number'], withdraw_amount, i, account_count)
            tasks.append(task)

        # 使用线程池执行任务
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(process_single_account, *task): task[9]
                for task in tasks
            }

            # 收集结果
            for future in as_completed(future_to_index):
                account_index = future_to_index[future]
                try:
                    account_info = future.result()
                    if account_info:
                        successful_accounts.append(account_info)
                    else:
                        failed_count += 1
                except Exception as e:
                    print(f"❌ 账号 {account_index} 执行异常: {e}")
                    failed_count += 1

        # 8. 显示最终结果
        print(f"\n{'='*60}")
        print(f"🎉 批量处理完成！")
        print(f"✅ 成功: {len(successful_accounts)} 个")
        print(f"❌ 失败: {failed_count} 个")
        print(f"📊 成功率: {len(successful_accounts)/account_count*100:.1f}%")

        if successful_accounts:
            print(f"\n💾 成功账号列表:")
            for i, account in enumerate(successful_accounts, 1):
                print(f"{i:2d}. {account['phone']} | {account['password']} | 订单:{account['order_id']}")

    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 脚本执行过程中发生意外错误: {e}")

if __name__ == '__main__':
    main()