{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"command": "C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\.venv-clean\\Scripts\\python.exe", "args": ["C:\\Users\\<USER>\\Desktop\\hook\\zhuce\\ida-pro-mcp\\src\\ida_pro_mcp\\server.py", "--unsafe"], "timeout": 1800, "disabled": false, "env": {"IDA_MCP_ENABLE_ALL_TOOLS": "true"}}}, "ida_mcp_status": {"connection_status": "connected", "current_file": "libapp.so", "server_address": "127.0.0.1:13337", "last_updated": "2025-01-17"}, "available_tools": {"basic_info": [{"name": "get_metadata", "status": "working", "description": "Get metadata about the current IDB", "example": "获取当前文件的元数据信息"}, {"name": "get_current_address", "status": "working", "description": "Get the address currently selected by the user", "example": "获取用户当前选择的地址"}, {"name": "get_current_function", "status": "working", "description": "Get the function currently selected by the user", "example": "获取用户当前选择的函数信息"}], "function_tools": [{"name": "get_function_by_name", "status": "available_in_cursor", "description": "Get a function by its name", "example": "根据函数名获取函数信息"}, {"name": "get_function_by_address", "status": "available_in_cursor", "description": "Get a function by its address", "example": "根据地址获取函数信息"}, {"name": "list_functions", "status": "available_in_cursor", "description": "List all functions in the database", "example": "列出数据库中的所有函数"}, {"name": "decompile_function", "status": "available_in_cursor", "description": "Decompile a function at the given address", "example": "反编译指定地址的函数"}, {"name": "disassemble_function", "status": "available_in_cursor", "description": "Get assembly code for a function", "example": "获取函数的汇编代码"}, {"name": "rename_function", "status": "available_in_cursor", "description": "Rename a function", "example": "重命名函数"}, {"name": "set_function_prototype", "status": "available_in_cursor", "description": "Set a function's prototype", "example": "设置函数原型"}], "data_tools": [{"name": "convert_number", "status": "available_in_cursor", "description": "Convert a number to different representations", "example": "转换数字到不同进制表示"}, {"name": "list_strings", "status": "available_in_cursor", "description": "List all strings in the database", "example": "列出数据库中的所有字符串"}, {"name": "search_strings", "status": "available_in_cursor", "description": "Search for strings in the database", "example": "在数据库中搜索字符串"}], "analysis_tools": [{"name": "get_xrefs_to", "status": "available_in_cursor", "description": "Get all cross references to the given address", "example": "获取指向指定地址的所有交叉引用"}, {"name": "get_entry_points", "status": "available_in_cursor", "description": "Get all entry points in the database", "example": "获取数据库中的所有入口点"}], "modification_tools": [{"name": "set_comment", "status": "available_in_cursor", "description": "Set a comment for a given address", "example": "为指定地址设置注释"}, {"name": "rename_local_variable", "status": "available_in_cursor", "description": "Rename a local variable in a function", "example": "重命名函数中的局部变量"}, {"name": "rename_global_variable", "status": "available_in_cursor", "description": "Rename a global variable", "example": "重命名全局变量"}, {"name": "set_global_variable_type", "status": "available_in_cursor", "description": "Set a global variable's type", "example": "设置全局变量的类型"}, {"name": "set_local_variable_type", "status": "available_in_cursor", "description": "Set a local variable's type", "example": "设置局部变量的类型"}, {"name": "declare_c_type", "status": "available_in_cursor", "description": "Create or update a local type from a C declaration", "example": "从C声明创建或更新本地类型"}], "connection_tools": [{"name": "check_connection", "status": "available_in_cursor", "description": "Check if the IDA plugin is running", "example": "检查IDA插件是否正在运行"}]}, "usage_instructions": {"working_tools": "这3个工具已验证可以直接使用", "cursor_tools": "这些工具在Cursor中显示为可用，可以尝试使用", "note": "某些工具可能需要特定的参数或条件才能正常工作", "recommendation": "建议在Cursor中直接尝试使用这些工具进行逆向分析"}}