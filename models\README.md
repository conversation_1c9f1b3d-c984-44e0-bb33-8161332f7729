# Models 模块开发文档

这个 `models` 目录包含了可复用的核心功能模块，包括代理管理、短信验证码、设备指纹等功能。每个模块都经过精心设计，可以直接在新项目中复用。

## 📁 目录结构

```
models/
├── README.md              # 本文档
├── proxy_manager.py       # 51代理管理模块
├── proxy_91_manager.py    # 91代理管理模块  
├── phone.py              # 好猪马短信接码模块
├── haozu.py              # 椰子云短信接码模块
├── qianchuan.py          # 千川短信接码模块
└── __pycache__/          # Python缓存目录
```

## 🚀 快速开始

### 1. 复制模块到新项目
```bash
# 将整个 models 目录复制到您的新项目中
cp -r models/ /path/to/your/new/project/
```

### 2. 在新项目中导入使用
```python
# 导入所需模块
from models.proxy_manager import ProxyManager
from models.haozu import YeyeYunClient, YeyeYunConfig
from models.phone import HaozhumaClient, HaozhumaConfig

# 使用示例见各模块详细说明
```

---

## 📋 模块详细说明

### 🌐 代理管理模块

#### 1. proxy_manager.py - 51代理管理器

**功能描述：**
- 对接51代理(51daili.com)平台
- 自动获取和管理代理IP
- 支持HTTP/SOCKS5协议
- 自动处理代理过期和刷新
- 返回requests库可直接使用的代理字典

**主要类：**
- `ProxyManager`: 核心代理管理类

**使用示例：**
```python
from models.proxy_manager import ProxyManager

# 初始化代理管理器
proxy_client = ProxyManager(
    packid="2",                    # 您的套餐ID
    uid="42477",                   # 您的UID
    access_name="ab263263",        # 权限校验名称
    access_password="8C84B9BFE1774BFB2443DD34797EE4FB",  # 权限校验密码
    protocol='http',               # 'http' 或 'socks5'
    time_duration=3                # 代理时长(分钟)
)

# 获取代理
proxies = proxy_client.get_proxy(force_new=True)
if proxies:
    # 使用代理发送请求
    response = requests.get("https://httpbin.org/ip", proxies=proxies)
    print(response.text)

# 释放代理
proxy_client.release_proxy()
```

**配置参数：**
- `packid`: 51代理套餐ID
- `uid`: 用户ID
- `access_name`: API访问用户名
- `access_password`: API访问密码
- `protocol`: 代理协议类型
- `time_duration`: 代理使用时长

#### 2. proxy_91_manager.py - 91代理管理器

**功能描述：**
- 对接91代理(91http.com)平台
- 支持代理获取、测试、管理
- 内置目标网站连通性测试
- 自动重试和错误处理机制

**主要类：**
- `Proxy91Config`: 配置类
- `Proxy91Manager`: 核心管理类

**使用示例：**
```python
from models.proxy_91_manager import Proxy91Manager, Proxy91Config

# 配置91代理参数
config = Proxy91Config()
config.TRADE_NO = "B228657142146"      # 您的订单号
config.SECRET = "HBAOD3XOGsDQX21V"     # 您的密钥

# 初始化管理器
manager = Proxy91Manager(config)

# 预检查服务可用性
if manager.pre_check():
    # 获取代理
    proxy = manager.get_proxy(force_new=True)
    if proxy:
        # 使用代理
        response = requests.get("https://example.com", proxies=proxy)
```

**配置参数：**
- `TRADE_NO`: 91代理订单号
- `SECRET`: API密钥
- `NUM`: 每次获取IP数量
- `PROTOCOL`: 协议类型(1=HTTP, 2=SOCKS5)
- `TIME`: 代理时长(分钟)

---

### 📱 短信验证码模块

#### 1. haozu.py - 椰子云接码平台

**功能描述：**
- 对接椰子云(sqhyw.net)短信接码平台
- 支持实卡/虚卡选择
- 智能轮询获取验证码
- 自动拉黑和释放手机号
- 支持专属项目管理

**主要类：**
- `YeyeYunConfig`: 配置类
- `YeyeYunClient`: 核心客户端类

**使用示例：**
```python
from models.haozu import YeyeYunClient, YeyeYunConfig

# 配置椰子云参数
config = YeyeYunConfig()
config.USERNAME = "your_username"      # 椰子云用户名
config.PASSWORD = "your_password"      # 椰子云密码
config.PROJECT_ID = "866795"           # 专属项目ID

# 初始化客户端
client = YeyeYunClient(config)

# 登录
if client.login():
    # 获取手机号
    success, result = client.get_phone_number()
    if success:
        phone, location = result
        print(f"获取到手机号: {phone} ({location})")
        
        # 等待短信验证码
        sms_code = client.get_sms_code(phone, max_wait_sec=90)
        if sms_code:
            print(f"验证码: {sms_code}")
            # 使用验证码进行注册等操作...
```

**配置参数：**
- `USERNAME`: 椰子云用户名
- `PASSWORD`: 椰子云密码  
- `PROJECT_ID`: 专属项目ID
- `API_BASE_URL`: API服务器地址
- `CARD_TYPE`: 卡类型(0=全部, 1=实卡, 2=虚卡)

#### 2. phone.py - 好猪马接码平台

**功能描述：**
- 对接好猪马(haozhuma.com)短信接码平台
- 支持多种卡类型选择
- 耐心等待短信机制
- 自动账户余额查询

**主要类：**
- `HaozhumaConfig`: 配置类
- `HaozhumaClient`: 核心客户端类

**使用示例：**
```python
from models.phone import HaozhumaClient, HaozhumaConfig

# 配置好猪马参数
config = HaozhumaConfig()
config.USERNAME = "your_username"      # 好猪马用户名
config.PASSWORD = "your_password"      # 好猪马密码
config.PROJECT_ID = "1000"             # 项目ID

# 初始化客户端
client = HaozhumaClient(config)

# 登录并获取手机号
if client.login():
    success, result = client.get_phone_number(ascription=2)  # 2=实卡
    if success:
        phone, location = result
        
        # 获取验证码
        sms_code = client.get_sms_code(phone)
        if sms_code:
            print(f"验证码: {sms_code}")
```

#### 3. qianchuan.py - 千川接码平台

**功能描述：**
- 对接千川(qc86.shop)短信接码平台
- 支持自动重试机制
- 内置Token自动管理
- 支持手机号黑名单管理

**主要类：**
- `QianchuanConfig`: 配置类
- `QianchuanClient`: 核心客户端类

**使用示例：**
```python
from models.qianchuan import QianchuanClient, QianchuanConfig

# 配置千川参数
config = QianchuanConfig()
config.USERNAME = "bb263"                      # 千川用户名
config.PASSWORD = "111qqq"                     # 千川密码
config.CHANNEL_ID = "1523062517935378440"      # 渠道ID
config.OPERATOR = 0                            # 号码类型(0=全部, 4=非虚拟, 5=虚拟)

# 初始化客户端
client = QianchuanClient(config)

# 获取手机号
success, result = client.get_phone_number()
if success:
    phone, location = result
    
    # 获取验证码
    sms_code = client.get_sms_code(phone)
    if sms_code:
        print(f"验证码: {sms_code}")
        
    # 拉黑手机号
    client.add_to_blacklist(phone)
```

---

## 🔧 通用使用模式

### 1. 代理 + 短信组合使用

```python
from models.proxy_manager import ProxyManager
from models.haozu import YeyeYunClient, YeyeYunConfig

# 初始化代理
proxy_manager = ProxyManager(...)
proxies = proxy_manager.get_proxy()

# 初始化短信
sms_config = YeyeYunConfig()
sms_client = YeyeYunClient(sms_config)
sms_client.login()

# 获取手机号
success, result = sms_client.get_phone_number()
if success:
    phone, location = result
    
    # 使用代理进行注册请求
    register_data = {"phone": phone, "code": ""}
    response = requests.post(
        "https://example.com/register", 
        json=register_data,
        proxies=proxies
    )
    
    # 获取验证码
    sms_code = sms_client.get_sms_code(phone)
    if sms_code:
        # 完成注册
        register_data["code"] = sms_code
        response = requests.post(
            "https://example.com/verify",
            json=register_data, 
            proxies=proxies
        )
```

### 2. 多平台容错机制

```python
# 多个短信平台容错
sms_platforms = [
    (YeyeYunClient, YeyeYunConfig()),
    (HaozhumaClient, HaozhumaConfig()),
    (QianchuanClient, QianchuanConfig())
]

for ClientClass, config in sms_platforms:
    try:
        client = ClientClass(config)
        if hasattr(client, 'login'):
            if not client.login():
                continue
        
        success, result = client.get_phone_number()
        if success:
            phone, location = result
            sms_code = client.get_sms_code(phone)
            if sms_code:
                print(f"成功获取验证码: {sms_code}")
                break
    except Exception as e:
        print(f"平台 {ClientClass.__name__} 失败: {e}")
        continue
```

---

## ⚠️ 注意事项

### 1. 配置安全
- 不要在代码中硬编码敏感信息
- 建议使用环境变量或配置文件
- 定期更换API密钥

### 2. 错误处理
- 所有模块都有内置错误处理
- 建议在业务代码中添加额外的异常捕获
- 注意API调用频率限制

### 3. 资源管理
- 及时释放不用的代理和手机号
- 避免资源浪费和账户余额消耗
- 合理设置超时时间

### 4. 平台限制
- 各平台都有API调用频率限制
- 注意账户余额和套餐限制
- 遵守平台使用规则

---

## 🔄 版本更新

当需要更新模块时：

1. **备份当前版本**
   ```bash
   cp -r models/ models_backup_$(date +%Y%m%d)/
   ```

2. **更新模块文件**
   - 直接替换对应的 `.py` 文件
   - 检查配置参数是否有变化

3. **测试新版本**
   ```python
   # 运行模块自带的测试代码
   python models/proxy_manager.py
   python models/haozu.py
   ```

---

## 📞 技术支持

如果在使用过程中遇到问题：

1. **检查配置参数**是否正确
2. **查看控制台日志**了解详细错误信息
3. **测试网络连接**确保能访问对应平台
4. **检查账户余额**确保有足够的资源

---

## 🎯 实际应用场景

### 场景1: 批量账号注册
```python
from models.proxy_manager import ProxyManager
from models.haozu import YeyeYunClient, YeyeYunConfig
import requests

def batch_register(count=10):
    """批量注册账号示例"""
    # 初始化服务
    proxy_manager = ProxyManager(...)
    sms_client = YeyeYunClient(YeyeYunConfig())
    sms_client.login()

    success_count = 0
    for i in range(count):
        try:
            # 获取新代理
            proxies = proxy_manager.get_proxy(force_new=True)

            # 获取手机号
            success, result = sms_client.get_phone_number()
            if not success:
                continue

            phone, location = result

            # 发送注册请求
            register_data = {
                "phone": phone,
                "password": "123456",
                "username": f"user_{i}"
            }

            response = requests.post(
                "https://example.com/register",
                json=register_data,
                proxies=proxies,
                timeout=30
            )

            if response.status_code == 200:
                # 获取验证码
                sms_code = sms_client.get_sms_code(phone)
                if sms_code:
                    # 验证注册
                    verify_data = {"phone": phone, "code": sms_code}
                    verify_response = requests.post(
                        "https://example.com/verify",
                        json=verify_data,
                        proxies=proxies
                    )

                    if verify_response.status_code == 200:
                        success_count += 1
                        print(f"账号 {phone} 注册成功")

        except Exception as e:
            print(f"注册失败: {e}")
            continue

    print(f"批量注册完成，成功: {success_count}/{count}")
```

### 场景2: 多平台容错注册
```python
def register_with_fallback(target_url):
    """多平台容错注册"""
    # 代理平台列表
    proxy_managers = [
        ProxyManager(...),  # 51代理
        Proxy91Manager(...) # 91代理
    ]

    # 短信平台列表
    sms_platforms = [
        YeyeYunClient(YeyeYunConfig()),
        HaozhumaClient(HaozhumaConfig()),
        QianchuanClient(QianchuanConfig())
    ]

    for proxy_manager in proxy_managers:
        try:
            proxies = proxy_manager.get_proxy()
            if not proxies:
                continue

            for sms_client in sms_platforms:
                try:
                    if hasattr(sms_client, 'login'):
                        if not sms_client.login():
                            continue

                    success, result = sms_client.get_phone_number()
                    if success:
                        phone, location = result

                        # 尝试注册
                        if attempt_register(target_url, phone, proxies, sms_client):
                            return True

                except Exception as e:
                    print(f"短信平台失败: {e}")
                    continue

        except Exception as e:
            print(f"代理平台失败: {e}")
            continue

    return False

def attempt_register(url, phone, proxies, sms_client):
    """尝试注册单个账号"""
    try:
        # 发送注册请求
        response = requests.post(
            f"{url}/register",
            json={"phone": phone},
            proxies=proxies,
            timeout=30
        )

        if response.status_code == 200:
            # 获取验证码
            sms_code = sms_client.get_sms_code(phone)
            if sms_code:
                # 验证
                verify_response = requests.post(
                    f"{url}/verify",
                    json={"phone": phone, "code": sms_code},
                    proxies=proxies
                )
                return verify_response.status_code == 200
    except Exception as e:
        print(f"注册尝试失败: {e}")

    return False
```

---

## 🛠️ 高级功能

### 1. 自定义配置管理
```python
import json
import os

class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)

    def get_proxy_config(self, platform="51daili"):
        """获取代理配置"""
        return self.config.get("proxy", {}).get(platform, {})

    def get_sms_config(self, platform="yeyeyun"):
        """获取短信配置"""
        return self.config.get("sms", {}).get(platform, {})

# 使用示例
config_manager = ConfigManager()
proxy_config = config_manager.get_proxy_config("51daili")
sms_config = config_manager.get_sms_config("yeyeyun")
```

### 2. 日志记录系统
```python
import logging
from datetime import datetime

class ModuleLogger:
    """模块专用日志记录器"""

    def __init__(self, name="models", log_file="models.log"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def info(self, message):
        self.logger.info(message)

    def error(self, message):
        self.logger.error(message)

    def warning(self, message):
        self.logger.warning(message)

# 全局日志实例
logger = ModuleLogger()
```

### 3. 性能监控
```python
import time
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            print(f"[性能] {func.__name__} 执行时间: {duration:.2f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"[性能] {func.__name__} 执行失败，耗时: {duration:.2f}秒，错误: {e}")
            raise
    return wrapper

# 使用示例
@monitor_performance
def get_proxy_with_monitoring():
    proxy_manager = ProxyManager(...)
    return proxy_manager.get_proxy()
```

---

## 📋 快速参考

### 常用导入语句
```python
# 代理模块
from models.proxy_manager import ProxyManager
from models.proxy_91_manager import Proxy91Manager, Proxy91Config

# 短信模块
from models.haozu import YeyeYunClient, YeyeYunConfig
from models.phone import HaozhumaClient, HaozhumaConfig
from models.qianchuan import QianchuanClient, QianchuanConfig
```

### 标准初始化模板
```python
def init_services():
    """标准服务初始化模板"""
    # 代理服务
    proxy_manager = ProxyManager(
        packid="your_packid",
        uid="your_uid",
        access_name="your_access_name",
        access_password="your_access_password"
    )

    # 短信服务
    sms_config = YeyeYunConfig()
    sms_config.USERNAME = "your_username"
    sms_config.PASSWORD = "your_password"
    sms_config.PROJECT_ID = "your_project_id"

    sms_client = YeyeYunClient(sms_config)
    sms_client.login()

    return proxy_manager, sms_client
```

---

*最后更新时间: 2025-01-30*
*文档版本: v1.0*

---

## 📝 更新日志

- **v1.0** (2025-01-30): 初始版本，包含完整的模块文档和使用示例
```
