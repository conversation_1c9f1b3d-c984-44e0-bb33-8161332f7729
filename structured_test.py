#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化测试脚本
分步骤验证破解的加密系统
"""

import base64
import hashlib
import json
import requests
import urllib3
from urllib.parse import urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class StructuredTest:
    def __init__(self):
        self.base_url = "https://ds-web1.yrpdz.com"
        
        # 破解的加密参数
        self.aes_key = "wtBfKcqAMug1wbH8"
        self.sign_key = "xMfxOOyStUC3CtQqlMNqhKfZwszMUfI2EsvNGGc4GdfbmWlAywkuHmFIyHw6yDYY"
        
        # API接口
        self.apis = {
            "captcha": "/dev-api/api/login/authccode.html",
            "register": "/dev-api/api/login/register.html",
            "sms": "/dev-api/api/login/smscode.html"
        }
        
        # 基础请求头
        self.base_headers = {
            'accept': '*/*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'content-type': 'application/json',
            'cookie': 'acw_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881; cdn_sec_tc=b482662717537651904677618e12037b20fcd5bc2c9d26153142038881',
            'is_app': 'false',
            'origin': 'https://ds-web1.yrpdz.com',
            'referer': 'https://ds-web1.yrpdz.com/pages/login/login?code=21328050',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'token': 'transfersecret'
        }

    def encrypt_data(self, data):
        """AES加密"""
        try:
            json_str = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
            data_bytes = json_str.encode('utf-8')
            padded_data = pad(data_bytes, AES.block_size)
            cipher = AES.new(self.aes_key.encode('utf-8'), AES.MODE_ECB)
            encrypted = cipher.encrypt(padded_data)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"❌ 加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data):
        """AES解密"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            cipher = AES.new(self.aes_key.encode('utf-8'), AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            unpadded_data = unpad(decrypted, AES.block_size)
            json_str = unpadded_data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            print(f"❌ 解密失败: {e}")
            return None

    def generate_sign(self, data):
        """生成MD5签名"""
        try:
            sorted_data = dict(sorted(data.items()))
            query_string = urlencode(sorted_data, doseq=True)
            sign_string = f"{query_string}&key={self.sign_key}"
            return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        except Exception as e:
            print(f"❌ 签名生成失败: {e}")
            return None

    def test_step_1_encryption(self):
        """步骤1: 测试加密解密功能"""
        print("🔧 步骤1: 测试加密解密功能")
        print("-" * 40)
        
        test_data = {"test": "hello", "number": 123, "中文": "测试"}
        print(f"原始数据: {test_data}")
        
        # 加密
        encrypted = self.encrypt_data(test_data)
        if not encrypted:
            return False
        print(f"✅ 加密成功: {encrypted[:50]}...")
        
        # 解密
        decrypted = self.decrypt_data(encrypted)
        if not decrypted:
            return False
        print(f"✅ 解密成功: {decrypted}")
        
        # 验证一致性
        if decrypted == test_data:
            print("✅ 加密解密一致性验证通过")
            return True
        else:
            print("❌ 加密解密一致性验证失败")
            return False

    def test_step_2_signature(self):
        """步骤2: 测试签名生成"""
        print("\n🔐 步骤2: 测试签名生成")
        print("-" * 40)
        
        test_data = {"phone": "13800138000", "code": "1234"}
        print(f"测试数据: {test_data}")
        
        sign = self.generate_sign(test_data)
        if not sign:
            return False
        
        print(f"✅ 签名生成成功: {sign}")
        print(f"签名长度: {len(sign)} (MD5应为32位)")
        
        # 验证签名格式
        if len(sign) == 32 and all(c in '0123456789abcdef' for c in sign):
            print("✅ 签名格式验证通过")
            return True
        else:
            print("❌ 签名格式验证失败")
            return False

    def test_step_3_request_format(self):
        """步骤3: 测试请求格式构造"""
        print("\n📦 步骤3: 测试请求格式构造")
        print("-" * 40)
        
        test_data = {"action": "test"}
        
        # 加密数据
        encrypted = self.encrypt_data(test_data)
        if not encrypted:
            return False
        
        # 生成签名
        sign = self.generate_sign(test_data)
        if not sign:
            return False
        
        # 构造请求头
        headers = self.base_headers.copy()
        headers['sign'] = sign
        headers['transfersecret'] = encrypted
        
        # 构造请求体
        request_body = {encrypted: encrypted}
        
        print("✅ 请求头构造成功:")
        print(f"  - sign: {sign}")
        print(f"  - transfersecret: {encrypted[:30]}...")
        
        print("✅ 请求体构造成功:")
        print(f"  - 格式: {{加密数据: 加密数据}}")
        
        return True

    def test_step_4_network_connectivity(self):
        """步骤4: 测试网络连通性"""
        print("\n🌐 步骤4: 测试网络连通性")
        print("-" * 40)
        
        try:
            # 简单的GET请求测试连通性
            response = requests.get(
                self.base_url,
                timeout=5,
                verify=False,
                allow_redirects=True
            )
            print(f"✅ 网络连通性正常")
            print(f"  - 状态码: {response.status_code}")
            print(f"  - 服务器: {response.headers.get('server', 'Unknown')}")
            return True
            
        except Exception as e:
            print(f"❌ 网络连通性测试失败: {e}")
            return False

    def test_step_5_api_request(self):
        """步骤5: 测试实际API请求"""
        print("\n🚀 步骤5: 测试实际API请求")
        print("-" * 40)
        
        # 测试验证码接口
        test_data = {}
        
        try:
            # 加密和签名
            encrypted = self.encrypt_data(test_data)
            sign = self.generate_sign(test_data)
            
            if not encrypted or not sign:
                return False
            
            # 构造请求
            headers = self.base_headers.copy()
            headers['sign'] = sign
            headers['transfersecret'] = encrypted
            
            request_body = {encrypted: encrypted}
            
            print(f"请求URL: {self.base_url}{self.apis['captcha']}")
            print(f"请求头签名: {sign}")
            print(f"请求体大小: {len(json.dumps(request_body))} 字节")
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}{self.apis['captcha']}",
                headers=headers,
                json=request_body,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            print(f"✅ 请求发送成功")
            print(f"  - 状态码: {response.status_code}")
            print(f"  - 响应长度: {len(response.text)} 字节")
            print(f"  - 响应内容: {response.text[:200]}...")
            
            # 尝试解密响应
            if response.text:
                try:
                    decrypted_response = self.decrypt_data(response.text)
                    if decrypted_response:
                        print(f"✅ 响应解密成功: {decrypted_response}")
                    else:
                        print("⚠️ 响应可能不是加密格式")
                except:
                    print("⚠️ 响应解密失败，可能是明文或其他格式")
            
            return True
            
        except Exception as e:
            print(f"❌ API请求失败: {e}")
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始结构化测试")
        print("=" * 50)
        
        tests = [
            self.test_step_1_encryption,
            self.test_step_2_signature,
            self.test_step_3_request_format,
            self.test_step_4_network_connectivity,
            self.test_step_5_api_request
        ]
        
        results = []
        for i, test in enumerate(tests, 1):
            try:
                result = test()
                results.append(result)
                if result:
                    print(f"✅ 步骤{i} 通过")
                else:
                    print(f"❌ 步骤{i} 失败")
            except Exception as e:
                print(f"❌ 步骤{i} 异常: {e}")
                results.append(False)
        
        # 总结
        print("\n" + "=" * 50)
        print("🎯 测试总结")
        print("-" * 20)
        passed = sum(results)
        total = len(results)
        print(f"通过: {passed}/{total}")
        
        if passed == total:
            print("🎉 所有测试通过！加密系统完全可用！")
        elif passed >= 3:
            print("⚠️ 核心功能正常，网络问题需要解决")
        else:
            print("❌ 存在严重问题，需要进一步调试")
        
        return results


if __name__ == "__main__":
    tester = StructuredTest()
    tester.run_all_tests()
