{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"command": "C:\\Users\\<USER>\\Desktop\\hook\\注册机\\.venv\\Scripts\\python.exe", "args": ["c:\\Users\\<USER>\\Desktop\\hook\\注册机\\.venv\\Lib\\site-packages\\ida_pro_mcp\\server.py"], "timeout": 1800, "disabled": false, "autoApprove": ["check_connection", "get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_globals_filter", "list_globals", "list_imports", "list_strings_filter", "list_strings", "list_local_types", "decompile_function", "disassemble_function", "get_xrefs_to", "get_xrefs_to_field", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "get_global_variable_value_by_name", "get_global_variable_value_at_address", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type", "get_stack_frame_variables", "get_defined_structures", "rename_stack_frame_variable", "create_stack_frame_variable", "set_stack_frame_variable_type", "delete_stack_frame_variable", "read_memory_bytes", "data_read_byte", "data_read_word", "data_read_dword", "data_read_qword", "data_read_string"], "alwaysAllow": ["get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_globals_filter", "list_globals", "list_imports", "list_strings_filter", "list_strings", "list_local_types", "decompile_function", "disassemble_function", "get_xrefs_to", "get_xrefs_to_field", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "get_global_variable_value_by_name", "get_global_variable_value_at_address", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type", "get_stack_frame_variables", "get_defined_structures", "rename_stack_frame_variable", "create_stack_frame_variable", "set_stack_frame_variable_type", "delete_stack_frame_variable", "read_memory_bytes", "data_read_byte", "data_read_word", "data_read_dword", "data_read_qword", "data_read_string"]}, "promptx": {"command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@beta", "mcp-server"], "timeout": 1800, "disabled": false, "autoApprove": ["promptx_init", "promptx_hello", "promptx_action", "promptx_learn", "promptx_recall", "promptx_remember"], "alwaysAllow": ["promptx_init", "promptx_hello", "promptx_action", "promptx_learn", "promptx_recall", "promptx_remember"]}, "@agentdeskai/browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "timeout": 1800, "disabled": false}}}