#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# "鲲鹏通讯" - App逆向分析辅助工具
#
# 用于分析App的网络通信协议，特别是群聊消息和红包推送机制

import json
import base64
import hashlib
import time
import re
import socket
import struct
from typing import Dict, List, Optional
try:
    from Cryptodome.Cipher import AES
    from Cryptodome.Util.Padding import unpad, pad
except ImportError:
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import unpad, pad

# --- 1. 全局配置 ---
API_KEY_STRING = "212i919292901"
ACCOUNT_FILE = r"C:\Users\<USER>\Desktop\hook\zhuce\xianbao_data\zhanghao.txt"

# --- 2. 协议分析工具 ---
class ProtocolAnalyzer:
    def __init__(self):
        self.message_patterns = {
            'websocket_handshake': [
                b'GET /',
                b'Upgrade: websocket',
                b'Sec-WebSocket-Key:',
                b'Sec-WebSocket-Version: 13'
            ],
            'tcp_message': [
                b'\x00\x00\x00',  # 可能的消息长度前缀
                b'\x01\x02\x03',  # 可能的协议标识
            ],
            'json_message': [
                b'{"type":',
                b'{"messageType":',
                b'{"redPacket":',
                b'"redPacketId":'
            ]
        }

    def analyze_packet(self, data: bytes) -> Dict:
        """
        分析数据包类型和内容
        """
        result = {
            'type': 'unknown',
            'size': len(data),
            'hex': data.hex(),
            'ascii': data.decode('utf-8', errors='ignore'),
            'potential_messages': []
        }

        # 检测协议类型
        for protocol, patterns in self.message_patterns.items():
            for pattern in patterns:
                if pattern in data:
                    result['type'] = protocol
                    break

        # 提取可能的消息
        result['potential_messages'] = self.extract_messages(data)
        
        return result

    def extract_messages(self, data: bytes) -> List[str]:
        """
        从数据包中提取可能的消息内容
        """
        messages = []
        
        # 转换为ASCII文本
        text = data.decode('utf-8', errors='ignore')
        
        # 查找JSON结构
        json_patterns = [
            r'\{[^}]*"type"[^}]*\}',
            r'\{[^}]*"messageType"[^}]*\}',
            r'\{[^}]*"redPacket"[^}]*\}',
            r'\{[^}]*"id"[^}]*\}',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            messages.extend(matches)

        # 查找Base64编码的数据
        b64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        b64_matches = re.findall(b64_pattern, text)
        messages.extend(b64_matches)

        # 查找十六进制ID（如红包ID）
        hex_id_pattern = r'[a-f0-9]{24}'
        hex_matches = re.findall(hex_id_pattern, text, re.IGNORECASE)
        messages.extend(hex_matches)

        return messages

# --- 3. 网络代理服务器 ---
class ProxyServer:
    def __init__(self, listen_port: int = 8888, target_host: str = "**************", target_port: int = 5666):
        self.listen_port = listen_port
        self.target_host = target_host
        self.target_port = target_port
        self.analyzer = ProtocolAnalyzer()
        self.running = False

    def start_proxy(self):
        """
        启动代理服务器，拦截App与服务器的通信
        """
        print(f"🚀 启动代理服务器...")
        print(f"   监听端口: {self.listen_port}")
        print(f"   目标服务器: {self.target_host}:{self.target_port}")
        print(f"📝 请将App的服务器地址修改为: 127.0.0.1:{self.listen_port}")
        
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind(('127.0.0.1', self.listen_port))
            server_socket.listen(5)
            
            self.running = True
            print(f"✅ 代理服务器已启动，等待连接...")
            
            while self.running:
                client_socket, addr = server_socket.accept()
                print(f"📱 新连接来自: {addr}")
                
                # 为每个连接创建处理线程
                import threading
                thread = threading.Thread(target=self.handle_connection, args=(client_socket,))
                thread.daemon = True
                thread.start()
                
        except Exception as e:
            print(f"❌ 代理服务器启动失败: {e}")
        finally:
            server_socket.close()

    def handle_connection(self, client_socket):
        """
        处理单个客户端连接
        """
        try:
            # 连接到真实服务器
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.connect((self.target_host, self.target_port))
            
            print(f"🔗 已建立到真实服务器的连接")
            
            # 双向数据转发
            import threading
            
            # 客户端 -> 服务器
            def forward_client_to_server():
                while True:
                    try:
                        data = client_socket.recv(4096)
                        if not data:
                            break
                        
                        print(f"📤 客户端 -> 服务器: {len(data)} 字节")
                        self.analyze_and_log(data, "CLIENT_TO_SERVER")
                        
                        server_socket.send(data)
                    except:
                        break
            
            # 服务器 -> 客户端
            def forward_server_to_client():
                while True:
                    try:
                        data = server_socket.recv(4096)
                        if not data:
                            break
                        
                        print(f"📥 服务器 -> 客户端: {len(data)} 字节")
                        self.analyze_and_log(data, "SERVER_TO_CLIENT")
                        
                        client_socket.send(data)
                    except:
                        break
            
            t1 = threading.Thread(target=forward_client_to_server)
            t2 = threading.Thread(target=forward_server_to_client)
            t1.daemon = True
            t2.daemon = True
            t1.start()
            t2.start()
            
            t1.join()
            t2.join()
            
        except Exception as e:
            print(f"❌ 连接处理失败: {e}")
        finally:
            client_socket.close()
            server_socket.close()

    def analyze_and_log(self, data: bytes, direction: str):
        """
        分析并记录数据包
        """
        analysis = self.analyzer.analyze_packet(data)
        
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"\n[{timestamp}] {direction}")
        print(f"类型: {analysis['type']}")
        print(f"大小: {analysis['size']} 字节")
        
        if analysis['potential_messages']:
            print(f"可能的消息:")
            for i, msg in enumerate(analysis['potential_messages'], 1):
                print(f"  {i}. {msg}")
                
                # 检查是否为红包消息
                if self.is_redpacket_message(msg):
                    print(f"🎁 发现红包消息!")
                    redpacket_id = self.extract_redpacket_id(msg)
                    if redpacket_id:
                        print(f"🎯 红包ID: {redpacket_id}")
                        self.trigger_redpacket_collection(redpacket_id)

        # 保存到日志文件
        self.save_to_log(timestamp, direction, analysis)

    def is_redpacket_message(self, message: str) -> bool:
        """
        判断是否为红包消息
        """
        redpacket_keywords = [
            'redpacket', 'redPacket', '红包', 'packet',
            '"type":"redpacket"', '"messageType":"redpacket"'
        ]
        
        message_lower = message.lower()
        return any(keyword.lower() in message_lower for keyword in redpacket_keywords)

    def extract_redpacket_id(self, message: str) -> Optional[str]:
        """
        提取红包ID
        """
        patterns = [
            r'"redPacketId":\s*"([a-f0-9]{24})"',
            r'"id":\s*"([a-f0-9]{24})"',
            r'([a-f0-9]{24})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None

    def trigger_redpacket_collection(self, redpacket_id: str):
        """
        触发红包领取
        """
        print(f"🚨 触发红包领取: {redpacket_id}")
        
        # 这里可以自动调用红包领取工具
        try:
            import subprocess
            import sys
            
            python_exe = sys.executable
            cmd = f'echo "{redpacket_id}" | "{python_exe}" "zidong.py"'
            
            # 在后台执行
            subprocess.Popen(cmd, shell=True)
            print(f"✅ 红包领取工具已启动")
            
        except Exception as e:
            print(f"❌ 启动红包工具失败: {e}")

    def save_to_log(self, timestamp: str, direction: str, analysis: Dict):
        """
        保存分析结果到日志文件
        """
        log_entry = {
            'timestamp': timestamp,
            'direction': direction,
            'analysis': analysis
        }
        
        try:
            with open('network_analysis.log', 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        except Exception as e:
            print(f"❌ 保存日志失败: {e}")

    def stop_proxy(self):
        """
        停止代理服务器
        """
        self.running = False
        print("⏹️ 代理服务器已停止")

# --- 4. 主程序 ---
if __name__ == "__main__":
    print("🔍 鲲鹏通讯 - App逆向分析工具")
    print("=" * 50)
    
    choice = input("""
请选择分析模式:
  1. 启动网络代理 (拦截App通信)
  2. 分析已捕获的数据
  3. 协议特征分析
  Q. 退出

您的选择: """).strip().upper()
    
    if choice == '1':
        proxy = ProxyServer()
        try:
            proxy.start_proxy()
        except KeyboardInterrupt:
            proxy.stop_proxy()
            
    elif choice == '2':
        print("📋 请粘贴要分析的十六进制数据:")
        data_input = input().strip()
        
        try:
            data_bytes = bytes.fromhex(data_input.replace(' ', ''))
            analyzer = ProtocolAnalyzer()
            result = analyzer.analyze_packet(data_bytes)
            
            print("\n🔍 分析结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            
    elif choice == '3':
        print("📊 协议特征分析功能开发中...")
        print("💡 建议使用Wireshark捕获数据包进行分析")
    
    print("\n程序已退出")
