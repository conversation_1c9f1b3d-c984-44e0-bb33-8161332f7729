[project]
name = "ida-pro-mcp"
version = "1.4.0"
description = "Vibe reversing with IDA Pro"
readme = "README.md"
requires-python = ">=3.11"
authors = [{ name = "mrexodia" }]
keywords = ["ida", "mcp", "llm", "plugin"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
]
dependencies = [
    "mcp>=1.6.0",
]

[project.urls]
Repository = "https://github.com/mrexodia/ida-pro-mcp"
Issues = "https://github.com/mrexodia/ida-pro-mcp/issues"

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "mcp[cli]>=1.6.0",
]

[project.scripts]
ida-pro-mcp = "ida_pro_mcp.server:main"
idalib-mcp = "ida_pro_mcp.idalib_server:main"
