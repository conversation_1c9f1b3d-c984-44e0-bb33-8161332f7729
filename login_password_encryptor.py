#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 鲲鹏通讯登录密码加密算法实现

import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

def encrypt_login_password(password: str) -> str:
    """
    根据Hook日志实现的登录密码加密流程。
    流程: MD5( AES_CBC( key=MD5(password), iv=STATIC_IV, data=MD5(password) ) )
    
    :param password: 明文密码
    :return: 最终加密后，用于网络请求的密码凭据 (Hex格式)
    """
    
    # 固定的IV，来自Hook日志
    static_iv = bytes.fromhex('0102030405060708090a0b0c0d0e0f10')
    
    # 步骤 1: 对明文密码进行MD5哈希
    password_bytes = password.encode('utf-8')
    md5_hash_bytes = hashlib.md5(password_bytes).digest()
    print(f"[1] MD5(password)      : {md5_hash_bytes.hex()}")

    # 步骤 2: 使用步骤1的结果作为Key和Data，进行AES加密
    # Key 和 Data 都是第一次MD5的结果
    aes_key = md5_hash_bytes
    data_to_encrypt = md5_hash_bytes
    
    print(f"[2] AES Key            : {aes_key.hex()}")
    print(f"[2] AES IV             : {static_iv.hex()}")
    print(f"[2] Data to Encrypt    : {data_to_encrypt.hex()}")

    cipher = AES.new(aes_key, AES.MODE_CBC, static_iv)
    # 注意：原始的Java代码使用PKCS5Padding，在数据块正好是16字节倍数时也会填充。
    # PyCryptodome的pad函数在PKCS7模式下可以模拟此行为。
    padded_data = pad(data_to_encrypt, AES.block_size)
    aes_encrypted_bytes = cipher.encrypt(padded_data)
    print(f"[2] AES Encrypted Data : {aes_encrypted_bytes.hex()}")
    
    # 步骤 3: 对AES加密的结果再次进行MD5哈希
    final_md5_hash_bytes = hashlib.md5(aes_encrypted_bytes).digest()
    final_hex_digest = final_md5_hash_bytes.hex()
    print(f"[3] Final MD5 Hash     : {final_hex_digest}")
    
    return final_hex_digest

if __name__ == '__main__':
    # 在这里输入您的密码进行测试
    test_password = "111qqq"
    
    print(f"--- 计算密码 '{test_password}' 的加密凭据 ---")
    final_credential = encrypt_login_password(test_password)
    print("\n-------------------------------------------")
    print(f"密码 '{test_password}' 的最终加密结果 (Hex):")
    print(final_credential)
    print("-------------------------------------------\n")

    # 与Hook日志中的结果进行比对
    # 第二条MD5日志的签名结果
    expected_result_from_log = "aae20b05ab18f2a8662d8c584b9532c2"
    print(f"Hook日志中的期望结果: {expected_result_from_log}")
    if final_credential == expected_result_from_log:
        print(">>> 结果比对成功! 脚本正确实现了加密算法。")
    else:
        print(">>> 结果比对失败! 请检查算法实现。") 