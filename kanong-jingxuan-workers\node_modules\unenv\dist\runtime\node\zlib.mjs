import { notImplemented } from "../_internal/utils.mjs";
import { codes } from "./internal/zlib/codes.mjs";
import { BrotliCompress, BrotliDecompress, brotliCompress, brotliCompressSync, brotliDecompress, brotliDecompressSync, createBrotliCompress, createBrotliDecompress } from "./internal/zlib/formats/brotli.mjs";
import { Deflate, DeflateRaw, Inflate, InflateRaw, createDeflate, createDeflateRaw, createInflate, createInflateRaw, deflate, deflateRaw, deflateRawSync, deflateSync, inflate, inflateRaw, inflateRawSync, inflateSync } from "./internal/zlib/formats/deflate.mjs";
import { Gunzip, Gzip, createGunzip, createGzip, gunzip, gunzipSync, gzip, gzipSync } from "./internal/zlib/formats/gzip.mjs";
import { Unzip, createUnzip, unzip, unzipSync } from "./internal/zlib/formats/zip.mjs";
import { Z_NO_FLUSH, Z_PARTIAL_FLUSH, Z_SYNC_FLUSH, Z_FULL_FLUSH, Z_FINISH, Z_BLOCK, Z_OK, Z_STREAM_END, Z_NEED_DICT, Z_ERRNO, Z_STREAM_ERROR, Z_DATA_ERROR, Z_MEM_ERROR, Z_BUF_ERROR, Z_VERSION_ERROR, Z_NO_COMPRESSION, Z_BEST_SPEED, Z_BEST_COMPRESSION, Z_DEFAULT_COMPRESSION, Z_FILTERED, Z_HUFFMAN_ONLY, Z_RLE, Z_FIXED, Z_DEFAULT_STRATEGY, ZLIB_VERNUM, DEFLATE, INFLATE, GZIP, GUNZIP, DEFLATERAW, INFLATERAW, UNZIP, BROTLI_DECODE, BROTLI_ENCODE, Z_MIN_WINDOWBITS, Z_MAX_WINDOWBITS, Z_DEFAULT_WINDOWBITS, Z_MIN_CHUNK, Z_MAX_CHUNK, Z_DEFAULT_CHUNK, Z_MIN_MEMLEVEL, Z_MAX_MEMLEVEL, Z_DEFAULT_MEMLEVEL, Z_MIN_LEVEL, Z_MAX_LEVEL, Z_DEFAULT_LEVEL, BROTLI_OPERATION_PROCESS, BROTLI_OPERATION_FLUSH, BROTLI_OPERATION_FINISH, BROTLI_OPERATION_EMIT_METADATA, BROTLI_PARAM_MODE, BROTLI_MODE_GENERIC, BROTLI_MODE_TEXT, BROTLI_MODE_FONT, BROTLI_DEFAULT_MODE, BROTLI_PARAM_QUALITY, BROTLI_MIN_QUALITY, BROTLI_MAX_QUALITY, BROTLI_DEFAULT_QUALITY, BROTLI_PARAM_LGWIN, BROTLI_MIN_WINDOW_BITS, BROTLI_MAX_WINDOW_BITS, BROTLI_LARGE_MAX_WINDOW_BITS, BROTLI_DEFAULT_WINDOW, BROTLI_PARAM_LGBLOCK, BROTLI_MIN_INPUT_BLOCK_BITS, BROTLI_MAX_INPUT_BLOCK_BITS, BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING, BROTLI_PARAM_SIZE_HINT, BROTLI_PARAM_LARGE_WINDOW, BROTLI_PARAM_NPOSTFIX, BROTLI_PARAM_NDIRECT, BROTLI_DECODER_RESULT_ERROR, BROTLI_DECODER_RESULT_SUCCESS, BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT, BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT, BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION, BROTLI_DECODER_PARAM_LARGE_WINDOW, BROTLI_DECODER_NO_ERROR, BROTLI_DECODER_SUCCESS, BROTLI_DECODER_NEEDS_MORE_INPUT, BROTLI_DECODER_NEEDS_MORE_OUTPUT, BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE, BROTLI_DECODER_ERROR_FORMAT_RESERVED, BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE, BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET, BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME, BROTLI_DECODER_ERROR_FORMAT_CL_SPACE, BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE, BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT, BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1, BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2, BROTLI_DECODER_ERROR_FORMAT_TRANSFORM, BROTLI_DECODER_ERROR_FORMAT_DICTIONARY, BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS, BROTLI_DECODER_ERROR_FORMAT_PADDING_1, BROTLI_DECODER_ERROR_FORMAT_PADDING_2, BROTLI_DECODER_ERROR_FORMAT_DISTANCE, BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET, BROTLI_DECODER_ERROR_INVALID_ARGUMENTS, BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES, BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS, BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP, BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1, BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2, BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES, BROTLI_DECODER_ERROR_UNREACHABLE } from "./internal/zlib/constants.mjs";
export const constants = {
	Z_NO_FLUSH,
	Z_PARTIAL_FLUSH,
	Z_SYNC_FLUSH,
	Z_FULL_FLUSH,
	Z_FINISH,
	Z_BLOCK,
	Z_OK,
	Z_STREAM_END,
	Z_NEED_DICT,
	Z_ERRNO,
	Z_STREAM_ERROR,
	Z_DATA_ERROR,
	Z_MEM_ERROR,
	Z_BUF_ERROR,
	Z_VERSION_ERROR,
	Z_NO_COMPRESSION,
	Z_BEST_SPEED,
	Z_BEST_COMPRESSION,
	Z_DEFAULT_COMPRESSION,
	Z_FILTERED,
	Z_HUFFMAN_ONLY,
	Z_RLE,
	Z_FIXED,
	Z_DEFAULT_STRATEGY,
	ZLIB_VERNUM,
	DEFLATE,
	INFLATE,
	GZIP,
	GUNZIP,
	DEFLATERAW,
	INFLATERAW,
	UNZIP,
	BROTLI_DECODE,
	BROTLI_ENCODE,
	Z_MIN_WINDOWBITS,
	Z_MAX_WINDOWBITS,
	Z_DEFAULT_WINDOWBITS,
	Z_MIN_CHUNK,
	Z_MAX_CHUNK,
	Z_DEFAULT_CHUNK,
	Z_MIN_MEMLEVEL,
	Z_MAX_MEMLEVEL,
	Z_DEFAULT_MEMLEVEL,
	Z_MIN_LEVEL,
	Z_MAX_LEVEL,
	Z_DEFAULT_LEVEL,
	BROTLI_OPERATION_PROCESS,
	BROTLI_OPERATION_FLUSH,
	BROTLI_OPERATION_FINISH,
	BROTLI_OPERATION_EMIT_METADATA,
	BROTLI_PARAM_MODE,
	BROTLI_MODE_GENERIC,
	BROTLI_MODE_TEXT,
	BROTLI_MODE_FONT,
	BROTLI_DEFAULT_MODE,
	BROTLI_PARAM_QUALITY,
	BROTLI_MIN_QUALITY,
	BROTLI_MAX_QUALITY,
	BROTLI_DEFAULT_QUALITY,
	BROTLI_PARAM_LGWIN,
	BROTLI_MIN_WINDOW_BITS,
	BROTLI_MAX_WINDOW_BITS,
	BROTLI_LARGE_MAX_WINDOW_BITS,
	BROTLI_DEFAULT_WINDOW,
	BROTLI_PARAM_LGBLOCK,
	BROTLI_MIN_INPUT_BLOCK_BITS,
	BROTLI_MAX_INPUT_BLOCK_BITS,
	BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING,
	BROTLI_PARAM_SIZE_HINT,
	BROTLI_PARAM_LARGE_WINDOW,
	BROTLI_PARAM_NPOSTFIX,
	BROTLI_PARAM_NDIRECT,
	BROTLI_DECODER_RESULT_ERROR,
	BROTLI_DECODER_RESULT_SUCCESS,
	BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT,
	BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT,
	BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION,
	BROTLI_DECODER_PARAM_LARGE_WINDOW,
	BROTLI_DECODER_NO_ERROR,
	BROTLI_DECODER_SUCCESS,
	BROTLI_DECODER_NEEDS_MORE_INPUT,
	BROTLI_DECODER_NEEDS_MORE_OUTPUT,
	BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE,
	BROTLI_DECODER_ERROR_FORMAT_RESERVED,
	BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE,
	BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET,
	BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME,
	BROTLI_DECODER_ERROR_FORMAT_CL_SPACE,
	BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE,
	BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT,
	BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1,
	BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2,
	BROTLI_DECODER_ERROR_FORMAT_TRANSFORM,
	BROTLI_DECODER_ERROR_FORMAT_DICTIONARY,
	BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS,
	BROTLI_DECODER_ERROR_FORMAT_PADDING_1,
	BROTLI_DECODER_ERROR_FORMAT_PADDING_2,
	BROTLI_DECODER_ERROR_FORMAT_DISTANCE,
	BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET,
	BROTLI_DECODER_ERROR_INVALID_ARGUMENTS,
	BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES,
	BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS,
	BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP,
	BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1,
	BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2,
	BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES,
	BROTLI_DECODER_ERROR_UNREACHABLE
};
export { codes } from "./internal/zlib/codes.mjs";
export * from "./internal/zlib/formats/brotli.mjs";
export * from "./internal/zlib/formats/deflate.mjs";
export * from "./internal/zlib/formats/gzip.mjs";
export * from "./internal/zlib/formats/zip.mjs";
export const crc32 = /* @__PURE__ */ notImplemented("zlib.crc32");
export { Z_NO_FLUSH, Z_PARTIAL_FLUSH, Z_SYNC_FLUSH, Z_FULL_FLUSH, Z_FINISH, Z_BLOCK, Z_OK, Z_STREAM_END, Z_NEED_DICT, Z_ERRNO, Z_STREAM_ERROR, Z_DATA_ERROR, Z_MEM_ERROR, Z_BUF_ERROR, Z_VERSION_ERROR, Z_NO_COMPRESSION, Z_BEST_SPEED, Z_BEST_COMPRESSION, Z_DEFAULT_COMPRESSION, Z_FILTERED, Z_HUFFMAN_ONLY, Z_RLE, Z_FIXED, Z_DEFAULT_STRATEGY, ZLIB_VERNUM, DEFLATE, INFLATE, GZIP, GUNZIP, DEFLATERAW, INFLATERAW, UNZIP, Z_MIN_WINDOWBITS, Z_MAX_WINDOWBITS, Z_DEFAULT_WINDOWBITS, Z_MIN_CHUNK, Z_MAX_CHUNK, Z_DEFAULT_CHUNK, Z_MIN_MEMLEVEL, Z_MAX_MEMLEVEL, Z_DEFAULT_MEMLEVEL, Z_MIN_LEVEL, Z_MAX_LEVEL, Z_DEFAULT_LEVEL };
export default {
	Z_NO_FLUSH,
	Z_PARTIAL_FLUSH,
	Z_SYNC_FLUSH,
	Z_FULL_FLUSH,
	Z_FINISH,
	Z_BLOCK,
	Z_OK,
	Z_STREAM_END,
	Z_NEED_DICT,
	Z_ERRNO,
	Z_STREAM_ERROR,
	Z_DATA_ERROR,
	Z_MEM_ERROR,
	Z_BUF_ERROR,
	Z_VERSION_ERROR,
	Z_NO_COMPRESSION,
	Z_BEST_SPEED,
	Z_BEST_COMPRESSION,
	Z_DEFAULT_COMPRESSION,
	Z_FILTERED,
	Z_HUFFMAN_ONLY,
	Z_RLE,
	Z_FIXED,
	Z_DEFAULT_STRATEGY,
	ZLIB_VERNUM,
	DEFLATE,
	INFLATE,
	GZIP,
	GUNZIP,
	DEFLATERAW,
	INFLATERAW,
	UNZIP,
	Z_MIN_WINDOWBITS,
	Z_MAX_WINDOWBITS,
	Z_DEFAULT_WINDOWBITS,
	Z_MIN_CHUNK,
	Z_MAX_CHUNK,
	Z_DEFAULT_CHUNK,
	Z_MIN_MEMLEVEL,
	Z_MAX_MEMLEVEL,
	Z_DEFAULT_MEMLEVEL,
	Z_MIN_LEVEL,
	Z_MAX_LEVEL,
	Z_DEFAULT_LEVEL,
	constants,
	BrotliCompress,
	BrotliDecompress,
	brotliCompress,
	brotliCompressSync,
	brotliDecompress,
	brotliDecompressSync,
	createBrotliCompress,
	createBrotliDecompress,
	Deflate,
	DeflateRaw,
	Inflate,
	InflateRaw,
	createDeflate,
	createDeflateRaw,
	createInflate,
	createInflateRaw,
	deflate,
	deflateRaw,
	deflateRawSync,
	deflateSync,
	inflate,
	inflateRaw,
	inflateRawSync,
	inflateSync,
	Gunzip,
	Gzip,
	createGunzip,
	createGzip,
	gunzip,
	gunzipSync,
	gzip,
	gzipSync,
	Unzip,
	createUnzip,
	unzip,
	unzipSync,
	codes,
	crc32
};
