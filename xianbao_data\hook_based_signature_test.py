#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 基于Hook分析结果的签名算法测试

import base64
import hmac
import hashlib
import time
import requests
import json

def analyze_hook_data():
    """分析Hook捕获的数据"""
    print("🔍 Hook数据分析")
    print("=" * 60)
    
    # Hook捕获的数据
    hook_data = {
        "key_base64": "aWalJFOwhiebkOe2918880de2c",
        "key_hex": "6966a52453b086279b90e2918880de2c",
        "content_string": "212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158",
        "result_base64": "lU+LyT24m2lhW3YrR0reIw==",
        "result_hex": "954f8bc93db89b69615b762b474ade23"
    }
    
    print("📋 Hook捕获的原始数据:")
    print(f"   密钥(Base64): {hook_data['key_base64']}")
    print(f"   密钥(Hex): {hook_data['key_hex']}")
    print(f"   签名内容: {hook_data['content_string']}")
    print(f"   签名结果: {hook_data['result_base64']}")
    
    # 分析签名内容的结构
    content = hook_data['content_string']
    print(f"\n🔍 签名内容分析:")
    print(f"   总长度: {len(content)} 字符")
    
    # 尝试分解内容
    if content.startswith("212i919292901"):
        remaining = content[13:]  # 去掉API密钥
        print(f"   API密钥: 212i919292901")
        print(f"   剩余内容: {remaining}")
        
        # 进一步分析剩余内容
        # 从Hook堆栈看，这是在DataDownloadActivity中调用的
        # 可能包含: userId + access_token + language + 其他参数 + salt
        
        # 尝试识别用户ID (通常是数字)
        user_id_candidates = []
        for i in range(len(remaining)):
            for j in range(i+3, min(i+10, len(remaining)+1)):
                substr = remaining[i:j]
                if substr.isdigit() and len(substr) >= 4:
                    user_id_candidates.append((i, j, substr))
        
        print(f"   可能的用户ID: {user_id_candidates}")
        
        # 尝试识别时间戳 (13位数字)
        timestamp_candidates = []
        for i in range(len(remaining)):
            for j in range(i+13, min(i+14, len(remaining)+1)):
                substr = remaining[i:j]
                if substr.isdigit() and len(substr) == 13:
                    timestamp_candidates.append((i, j, substr))
        
        print(f"   可能的时间戳: {timestamp_candidates}")

def test_signature_algorithm():
    """测试签名算法"""
    print(f"\n🧪 测试签名算法")
    print("=" * 60)
    
    # 基于Hook数据重构签名
    api_key = "212i919292901"
    
    # 从Hook内容中提取的可能参数
    # 212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158
    # 分解: 212i919292901 + 107998 + 0a6781659c62493c8d1dc73ec71d57ba + zh + *********** + 1753676978158
    
    test_cases = [
        {
            "name": "Hook数据重构1",
            "key": "aWalJFOwhiebkOe2918880de2c",
            "content": "212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158",
            "expected": "lU+LyT24m2lhW3YrR0reIw=="
        },
        {
            "name": "分解参数测试",
            "key": "aWalJFOwhiebkOe2918880de2c", 
            "parts": ["212i919292901", "107998", "0a6781659c62493c8d1dc73ec71d57ba", "zh", "***********", "1753676978158"],
            "expected": "lU+LyT24m2lhW3YrR0reIw=="
        }
    ]
    
    for test in test_cases:
        print(f"\n📝 测试: {test['name']}")
        
        try:
            # 准备密钥
            key_bytes = base64.b64decode(test['key'])
            print(f"   密钥(Hex): {key_bytes.hex()}")
            
            # 准备内容
            if 'content' in test:
                content = test['content']
            else:
                content = "".join(test['parts'])
            
            print(f"   签名内容: {content}")
            print(f"   内容长度: {len(content)}")
            
            # 生成签名
            signature = hmac.new(key_bytes, content.encode('utf-8'), hashlib.md5).digest()
            signature_b64 = base64.b64encode(signature).decode('utf-8')
            
            print(f"   生成签名: {signature_b64}")
            print(f"   期望签名: {test['expected']}")
            print(f"   匹配结果: {'✅ 成功' if signature_b64 == test['expected'] else '❌ 失败'}")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def create_signature_function():
    """基于Hook分析创建正确的签名函数"""
    print(f"\n🔧 创建正确的签名函数")
    print("=" * 60)
    
    def generate_correct_signature(key_b64, user_id, access_token, language="zh", **kwargs):
        """
        基于Hook分析的正确签名算法
        Hook示例: 212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158
        """
        salt = str(int(time.time() * 1000))
        
        # 解码密钥
        key_bytes = base64.b64decode(key_b64)
        
        # 构建签名内容 (基于Hook观察到的格式)
        content_parts = [
            "212i919292901",  # API密钥
            str(user_id),     # 用户ID
            access_token,     # 访问令牌
            language,         # 语言
        ]
        
        # 添加其他参数
        for key in sorted(kwargs.keys()):
            content_parts.append(str(kwargs[key]))
        
        # 添加时间戳
        content_parts.append(salt)
        
        # 拼接内容
        content = "".join(content_parts)
        
        # 生成签名
        signature = hmac.new(key_bytes, content.encode('utf-8'), hashlib.md5).digest()
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        return salt, signature_b64, content
    
    # 测试新函数
    print("📝 测试新的签名函数:")
    
    # 使用Hook中的数据进行测试
    test_key = "aWalJFOwhiebkOe2918880de2c"
    test_user_id = "107998"
    test_access_token = "0a6781659c62493c8d1dc73ec71d57ba"
    test_language = "zh"
    
    # 模拟时间戳
    test_salt = "1753676978158"
    
    # 手动构建内容进行验证
    manual_content = f"212i919292901{test_user_id}{test_access_token}{test_language}{test_salt}"
    print(f"   手动构建内容: {manual_content}")
    print(f"   Hook捕获内容: 212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158")
    
    # 检查是否匹配
    hook_content = "212i9192929011079980a6781659c62493c8d1dc73ec71d57bazh***********1753676978158"
    if manual_content in hook_content:
        print("   ✅ 内容格式匹配!")
    else:
        print("   ❌ 内容格式不匹配，需要进一步分析")
        
        # 尝试找出差异
        print(f"   差异分析:")
        print(f"     手动长度: {len(manual_content)}")
        print(f"     Hook长度: {len(hook_content)}")
        
        # 可能还有其他参数，比如手机号
        # 从Hook内容看，可能包含手机号: ***********
        possible_phone = "***********"
        manual_content_with_phone = f"212i919292901{test_user_id}{test_access_token}{test_language}{possible_phone}{test_salt}"
        print(f"     包含手机号: {manual_content_with_phone}")
        
        if manual_content_with_phone == hook_content:
            print("     ✅ 包含手机号后完全匹配!")
            return generate_correct_signature
    
    return generate_correct_signature

def test_with_real_account():
    """使用真实账号数据测试"""
    print(f"\n🚀 使用真实账号数据测试")
    print("=" * 60)
    
    # 使用已知的账号信息
    account_info = {
        "userId": "107477",
        "access_token": "5e6f579c8ade4601ba410bfc8b244199",
        "httpKey": "hcCgwwhEHhNtf2YVdIj/Bg==",
        "phone": "***********"
    }
    
    print(f"📋 测试账号信息:")
    for key, value in account_info.items():
        print(f"   {key}: {value}")
    
    # 创建签名函数
    signature_func = create_signature_function()
    
    # 生成签名
    try:
        salt, signature, content = signature_func(
            account_info["httpKey"],
            account_info["userId"], 
            account_info["access_token"],
            language="zh",
            phone=account_info["phone"]
        )
        
        print(f"\n📝 生成的签名:")
        print(f"   时间戳: {salt}")
        print(f"   签名内容: {content}")
        print(f"   签名结果: {signature}")
        
        # 尝试用这个签名发起请求
        print(f"\n🌐 测试API请求:")
        test_api_request(account_info, salt, signature)
        
    except Exception as e:
        print(f"❌ 签名生成失败: {e}")

def test_api_request(account_info, salt, signature):
    """测试API请求"""
    try:
        # 构建请求参数
        params = {
            "id": "test_hongbao_id",
            "language": "zh",
            "salt": salt,
            "secret": signature
        }
        
        # 发起请求
        url = f"http://23.248.226.178:8095/hongbao/receive"
        headers = {
            "User-Agent": "okhttp/4.9.0",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        response = requests.post(url, data=params, headers=headers, timeout=10)
        
        print(f"   请求URL: {url}")
        print(f"   响应状态: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('resultCode') == 10110:
                print(f"   ⚠️  权限验证失败 - 签名算法仍需调整")
            elif result.get('resultCode') == 1:
                print(f"   ✅ 请求成功!")
            else:
                print(f"   ℹ️  其他响应: {result}")
        
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def main():
    print("🔍 基于Hook分析的签名算法测试")
    print("=" * 60)
    
    # 分析Hook数据
    analyze_hook_data()
    
    # 测试签名算法
    test_signature_algorithm()
    
    # 创建并测试签名函数
    create_signature_function()
    
    # 使用真实账号测试
    test_with_real_account()

if __name__ == "__main__":
    main()
